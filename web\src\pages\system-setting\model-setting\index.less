.modelSettingWrapper {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .headerLeft {
      flex: 1;
    }

    .headerRight {
      flex-shrink: 0;
    }
  }

  .providersSection {
    .sectionHeader {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        color: #666;
        font-weight: 500;
      }
    }
  }

  .modelCard {
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    .modelHeader {
      .modelIcon {
        font-size: 24px;
        margin-right: 12px;
      }

      .modelName {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 4px;
      }

      .modelType {
        color: #666;
        font-size: 12px;
        margin-right: 4px;
      }

      .modelValue {
        color: #333;
        font-size: 12px;
      }
    }
  }
}
