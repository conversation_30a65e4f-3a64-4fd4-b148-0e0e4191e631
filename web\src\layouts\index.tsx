import { Layout } from 'antd';
import React, { useEffect, useState } from 'react';
import { Outlet } from 'umi';
import '../locales/config';
import HeaderComponent from './components/header';
import SideBar from './components/sidebar/SideBar';

import styles from './index.less';

const { Content } = Layout;

// Helper functions for localStorage operations
const SIDEBAR_STORAGE_KEY = 'sidebar_state';

const getSidebarState = (): boolean => {
  try {
    const stored = localStorage.getItem(SIDEBAR_STORAGE_KEY);
    return stored !== null ? JSON.parse(stored) : false; // default to expanded (false = not collapsed)
  } catch {
    return false; // fallback to default if localStorage is not available
  }
};

const setSidebarState = (collapsed: boolean): void => {
  try {
    localStorage.setItem(SIDEBAR_STORAGE_KEY, JSON.stringify(collapsed));
  } catch {
    // Silently fail if localStorage is not available (e.g., private browsing)
  }
};

const App: React.FC = () => {
  // Initialize from localStorage if available, otherwise use default
  const [collapsed, setCollapsed] = useState(() => {
    // Only read from localStorage on client side
    if (typeof window !== 'undefined') {
      return getSidebarState();
    }
    return false; // default to expanded on server side
  });
  const [isMobile, setIsMobile] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [autoCollapsed, setAutoCollapsed] = useState(false);

  // Responsive behavior handler
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;

      // Mobile detection
      setIsMobile(width < 768);
      setShowSidebar(width >= 768);

      // Auto-collapse logic for desktop
      if (width < 1080 && !collapsed) {
        setCollapsed(true);
        setSidebarState(true); // Save auto-collapse to localStorage
        setAutoCollapsed(true);
      } else if (width >= 1080 && autoCollapsed) {
        const restoredState = getSidebarState(); // Restore user's preference
        setCollapsed(restoredState);
        setAutoCollapsed(false);
      }
    };

    // Initial setup
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [collapsed, autoCollapsed]);

  // Toggle mobile sidebar
  const toggleMobileSidebar = () => {
    if (isMobile) {
      setShowSidebar(!showSidebar);
    }
  };

  // Manual collapse handler
  const handleManualCollapse = (newCollapsed: boolean) => {
    setCollapsed(newCollapsed);
    // Save to localStorage
    setSidebarState(newCollapsed);
    // Clear auto-collapse state when manually toggling on desktop
    if (window.innerWidth >= 1080) {
      setAutoCollapsed(false);
    }
  };

  return (
    <div className={styles.highTechLayout}>
      <HeaderComponent
        toggleSidebar={toggleMobileSidebar}
        isMobile={isMobile}
      />

      <div className={styles.layoutContainer}>
        <SideBar
          collapsed={collapsed}
          setCollapsed={handleManualCollapse}
          visible={showSidebar}
          className={showSidebar ? styles.sidebarVisible : styles.sidebarHidden}
        />

        <Content
          className={`${styles.contentArea} ${collapsed || !showSidebar ? styles.contentExpanded : ''}`}
        >
          <Outlet />
        </Content>
      </div>

      {/* Mobile overlay for sidebar */}
      {isMobile && showSidebar && (
        <div className={styles.mobileOverlay} onClick={toggleMobileSidebar} />
      )}
    </div>
  );
};

export default App;
