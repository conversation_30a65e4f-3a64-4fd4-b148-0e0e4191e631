import {
  FileExcelOutlined,
  FileImageOutlined,
  FileOutlined,
  FilePdfOutlined,
  FilePptOutlined,
  FileTextOutlined,
  FileWordOutlined,
  PlayCircleOutlined,
} from '@ant-design/icons';
import { Card, Space, Typography } from 'antd';
import React from 'react';
import styles from './RecommendedResources.less';

const { Text, Paragraph } = Typography;

export interface ResourceItem {
  id: string;
  name: string;
  type: string;
  size: number;
  matched_keyword: string;
  keyword_weight: number;
  create_time: number;
  location: string;
}

interface RecommendedResourcesProps {
  resources: ResourceItem[];
  onResourceClick?: (resource: ResourceItem) => void;
}

// 获取文件类型对应的图标
const getFileIcon = (type: string, fileName: string) => {
  const lowerType = type.toLowerCase();
  const lowerName = fileName.toLowerCase();

  if (lowerType === 'pdf' || lowerName.endsWith('.pdf')) {
    return <FilePdfOutlined className={styles.fileIcon} />;
  }
  if (
    lowerType === 'document' ||
    lowerName.endsWith('.doc') ||
    lowerName.endsWith('.docx')
  ) {
    return <FileWordOutlined className={styles.fileIcon} />;
  }
  if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) {
    return <FileExcelOutlined className={styles.fileIcon} />;
  }
  if (lowerName.endsWith('.ppt') || lowerName.endsWith('.pptx')) {
    return <FilePptOutlined className={styles.fileIcon} />;
  }
  if (
    lowerType === 'visual' ||
    lowerName.endsWith('.mp4') ||
    lowerName.endsWith('.avi') ||
    lowerName.endsWith('.mov')
  ) {
    return <PlayCircleOutlined className={styles.fileIcon} />;
  }
  if (
    lowerName.endsWith('.jpg') ||
    lowerName.endsWith('.jpeg') ||
    lowerName.endsWith('.png') ||
    lowerName.endsWith('.gif')
  ) {
    return <FileImageOutlined className={styles.fileIcon} />;
  }
  if (lowerName.endsWith('.txt')) {
    return <FileTextOutlined className={styles.fileIcon} />;
  }
  return <FileOutlined className={styles.fileIcon} />;
};

// 获取文件类型对应的颜色
const getFileIconColor = (type: string, fileName: string) => {
  const lowerType = type.toLowerCase();
  const lowerName = fileName.toLowerCase();

  if (lowerType === 'pdf' || lowerName.endsWith('.pdf')) {
    return '#ff4d4f'; // 红色
  }
  if (
    lowerType === 'document' ||
    lowerName.endsWith('.doc') ||
    lowerName.endsWith('.docx')
  ) {
    return '#1890ff'; // 蓝色
  }
  if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) {
    return '#52c41a'; // 绿色
  }
  if (lowerName.endsWith('.ppt') || lowerName.endsWith('.pptx')) {
    return '#fa8c16'; // 橙色
  }
  if (
    lowerType === 'visual' ||
    lowerName.endsWith('.mp4') ||
    lowerName.endsWith('.avi') ||
    lowerName.endsWith('.mov')
  ) {
    return '#722ed1'; // 紫色
  }
  if (
    lowerName.endsWith('.jpg') ||
    lowerName.endsWith('.jpeg') ||
    lowerName.endsWith('.png') ||
    lowerName.endsWith('.gif')
  ) {
    return '#eb2f96'; // 粉色
  }
  return 'var(--primary-color)'; // 使用主题色
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

const RecommendedResources: React.FC<RecommendedResourcesProps> = ({
  resources,
  onResourceClick,
}) => {
  const handleResourceClick = (resource: ResourceItem) => {
    onResourceClick?.(resource);
  };

  // 确保 resources 是数组
  const safeResources = Array.isArray(resources) ? resources : [];

  return (
    <div className={styles.recommendedResources}>
      <div className={styles.sectionTitle}>推荐资源</div>
      <div className={styles.resourceList}>
        {safeResources.map((resource) => (
          <Card
            key={resource.id}
            className={styles.resourceCard}
            hoverable
            onClick={() => handleResourceClick(resource)}
          >
            <Space
              direction="vertical"
              size="small"
              style={{ width: '100%', height: '100%' }}
            >
              <Space align="start" style={{ width: '100%' }}>
                <div
                  style={{
                    color: getFileIconColor(resource.type, resource.name),
                  }}
                >
                  {getFileIcon(resource.type, resource.name)}
                </div>
                <div className={styles.contentSection}>
                  <Text
                    strong
                    ellipsis={{ tooltip: resource.name }}
                    className={styles.fileName}
                    style={{ width: '200px' }}
                  >
                    {resource.name}
                  </Text>
                  <Text type="secondary" className={styles.keyword}>
                    关键词: {resource.matched_keyword}
                  </Text>
                </div>
              </Space>
              <Paragraph ellipsis={{ rows: 1 }} className={styles.details}>
                {formatFileSize(resource.size)} •{' '}
                {formatDate(resource.create_time)}
              </Paragraph>
            </Space>
          </Card>
        ))}
      </div>

      {safeResources.length === 0 && (
        <div className={styles.emptyState}>
          <FileOutlined className={styles.emptyIcon} />
          <Text className={styles.emptyText}>暂无相关资源</Text>
        </div>
      )}
    </div>
  );
};

export default RecommendedResources;
