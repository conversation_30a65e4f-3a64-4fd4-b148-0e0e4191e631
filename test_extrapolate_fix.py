#!/usr/bin/env python3
"""Test script to verify the ExtrapolateService fixes."""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/ragflow')


def test_import():
    """Test that we can import the service without errors."""
    try:
        from api.services.extrapolate_service import ExtrapolateService, _UploadContext
        print("✓ Successfully imported ExtrapolateService and _UploadContext")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


def test_upload_context():
    """Test that _UploadContext can be instantiated."""
    try:
        from api.services.extrapolate_service import _UploadContext
        ctx = _UploadContext("test_kb", "test_doc", "test_job")
        print(
            f"✓ Successfully created _UploadContext: kb_id={ctx.kb_id}, doc_id={ctx.doc_id}")
        return True
    except Exception as e:
        print(f"✗ _UploadContext creation failed: {e}")
        return False


def test_type_annotations():
    """Test that type annotations are valid."""
    try:
        from api.services.extrapolate_service import ExtrapolateService
        import inspect

        # Get the _handle_file_upload method
        method = ExtrapolateService._handle_file_upload
        sig = inspect.signature(method)

        print(f"✓ Method signature: {sig}")
        print(f"✓ Return annotation: {sig.return_annotation}")
        return True
    except Exception as e:
        print(f"✗ Type annotation test failed: {e}")
        return False


if __name__ == "__main__":
    print("Testing ExtrapolateService fixes...")

    tests = [
        test_import,
        test_upload_context,
        test_type_annotations,
    ]

    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()

    print(f"Results: {passed}/{len(tests)} tests passed")

    if passed == len(tests):
        print("🎉 All tests passed! The fixes should work.")
    else:
        print("❌ Some tests failed. There may still be issues.")
