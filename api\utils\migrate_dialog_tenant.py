#!/usr/bin/env python3
"""
Data migration script to fix tenant_id in dialog table.

This script corrects dialog records where tenant_id was incorrectly set to user_id
instead of the actual tenant_id from user_tenant table.
"""

from db import StatusEnum, UserTenantRole
from db.services.user_service import UserTenantService, TenantService
from db.services.dialog_service import DialogService
import sys
import os

# Add the API directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def migrate_dialog_tenant_ids():
    """Migrate dialog tenant_ids from user_id to actual tenant_id."""

    print("Starting dialog tenant_id migration...")

    # Get all valid dialogs
    dialogs = DialogService.query(status=StatusEnum.VALID.value)

    updated_count = 0
    error_count = 0

    for dialog in dialogs:
        dialog_dict = dialog.to_dict()
        current_tenant_id = dialog_dict['tenant_id']

        print(
            f"Checking dialog {dialog_dict['id']} with tenant_id: {current_tenant_id}")

        # Check if current tenant_id is actually a user_id by looking for user_tenant relationships
        user_tenants = UserTenantService.model.select().where(
            (UserTenantService.model.user_id == current_tenant_id) &
            (UserTenantService.model.status == StatusEnum.VALID.value) &
            (UserTenantService.model.role.in_(
                [UserTenantRole.OWNER, UserTenantRole.ADMIN, UserTenantRole.NORMAL]))
        )

        user_tenant_list = list(user_tenants)

        if user_tenant_list:
            # This looks like a user_id, need to find the correct tenant_id
            # Use the first tenant the user belongs to (could be improved with more business logic)
            correct_tenant_id = user_tenant_list[0].tenant_id

            if current_tenant_id != correct_tenant_id:
                print(
                    f"  → Updating dialog {dialog_dict['id']}: {current_tenant_id} → {correct_tenant_id}")

                try:
                    # Update the dialog with correct tenant_id
                    DialogService.update_by_id(
                        dialog_dict['id'], {'tenant_id': correct_tenant_id})
                    updated_count += 1
                    print(
                        f"  ✓ Successfully updated dialog {dialog_dict['id']}")
                except Exception as e:
                    print(
                        f"  ✗ Error updating dialog {dialog_dict['id']}: {str(e)}")
                    error_count += 1
            else:
                print(
                    f"  → Dialog {dialog_dict['id']} already has correct tenant_id")
        else:
            # Check if current_tenant_id is a valid tenant
            e, tenant = TenantService.get_by_id(current_tenant_id)
            if e:
                print(
                    f"  → Dialog {dialog_dict['id']} already has valid tenant_id")
            else:
                print(
                    f"  ⚠ Warning: Dialog {dialog_dict['id']} has invalid tenant_id: {current_tenant_id}")
                error_count += 1

    print(f"\nMigration completed:")
    print(f"  - Updated: {updated_count} dialogs")
    print(f"  - Errors: {error_count} dialogs")

    return updated_count, error_count


if __name__ == "__main__":
    try:
        updated, errors = migrate_dialog_tenant_ids()
        if errors == 0:
            print("\n✓ Migration completed successfully!")
            sys.exit(0)
        else:
            print(f"\n⚠ Migration completed with {errors} errors")
            sys.exit(1)
    except Exception as e:
        print(f"\n✗ Migration failed: {str(e)}")
        sys.exit(1)
