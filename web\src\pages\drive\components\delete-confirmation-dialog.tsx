import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Trash2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';

interface DeleteConfirmationDialogProps {
  open: boolean;
  title: string;
  onConfirm: () => void;
  onCancel: () => void;
  loading?: boolean;
}

export function DeleteConfirmationDialog({
  open,
  title,
  onConfirm,
  onCancel,
  loading = false,
}: DeleteConfirmationDialogProps) {
  const { t } = useTranslation();

  return (
    <AlertDialog open={open} onOpenChange={(isOpen) => !isOpen && onCancel()}>
      <AlertDialogContent
        onSelect={(e) => e.preventDefault()}
        onClick={(e) => e.stopPropagation()}
        className="ht-modal-content"
      >
        <AlertDialogHeader className="ht-modal-header">
          <AlertDialogTitle className="ht-modal-title">
            <Trash2 className="h-5 w-5" />
            {title}
          </AlertDialogTitle>
        </AlertDialogHeader>
        <div className="ht-modal-body">
          <p className="text-sm text-gray-600">
            此操作无法撤销，确定要继续吗？
          </p>
        </div>
        <AlertDialogFooter className="ht-modal-footer">
          <AlertDialogCancel
            onClick={onCancel}
            className="ht-modal-btn secondary"
            disabled={loading}
          >
            {t('common.cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            className="ht-modal-btn primary bg-red-600 hover:bg-red-700"
            onClick={onConfirm}
            disabled={loading}
          >
            <Trash2 className="h-4 w-4" />
            {loading ? '删除中...' : t('common.ok')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
