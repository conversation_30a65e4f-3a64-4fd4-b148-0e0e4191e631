import { GenerateCube } from '@/components/generate-cube';
import HightLightMarkdown from '@/components/highlight-markdown';
import { useFetchLlmList } from '@/hooks/llm-hooks';
import { getV3SystemConfig } from '@/services/agent_service';
import { exportMarkdownToDocx } from '@/utils/markdown-export';
import { mdToXlxsForExtrapolate } from '@/utils/markdown-to-excel-utils';
import { convertAndSendQuestions } from '@/utils/question-util';
import {
  ArrowLeftOutlined,
  BulbOutlined,
  CloudUploadOutlined,
  FileExcelOutlined,
  FileWordOutlined,
} from '@ant-design/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Col, Row, Typography, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { useForm } from 'react-hook-form';
import { history } from 'umi';
import { z } from 'zod';
import ExtrapolateForm from '../form/extrapolate-form';
import styles from './index.less';

const { Title, Paragraph } = Typography;

// Define the form schema
const formSchema = z.object({
  question_type: z.enum(['same', 'custom']).default('same'),
  custom_question_type: z.string().optional(),
  structure: z.enum(['same', 'custom']).default('same'),
  custom_structure: z.string().optional(),
  knowledge_points: z.enum(['same_context', 'custom']).default('same_context'),
  custom_knowledge_points: z.string().optional(),
  difficulty: z.enum(['same', 'basic', 'medium', 'hard']).default('same'),
  quantity: z.number().min(1).max(50).default(5),
  sample_file: z.any().optional(),
  llm_id: z.string().min(1, { message: '请选择一个模型' }),
});

type FormValues = z.infer<typeof formSchema>;

const ExtrapolatePage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [statusMessage, setStatusMessage] = useState<string>('');
  const [markdownData, setMarkdownData] = useState<string | null>(null);
  const [fileList, setFileList] = useState<any[]>([]);
  const [forwardingToV3System, setForwardingToV3System] = useState(false);
  const { list: llmList } = useFetchLlmList();
  const [defaultLlmId, setDefaultLlmId] = useState<string>(
    'deepseek-r1:32b@Ollama',
  );

  // streaming helpers
  const thinkModeRef = useRef(false);

  // Get the first available CHAT model as default
  useEffect(() => {
    if (llmList && llmList.length > 0) {
      const chatModels = llmList.filter((llm: any) => llm.llm_type === 'CHAT');
      if (chatModels.length > 0) {
        setDefaultLlmId(chatModels[0].id.toString());
      }
    }
  }, [llmList]);

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      question_type: 'same',
      custom_question_type: '',
      structure: 'same',
      custom_structure: '',
      knowledge_points: 'same_context',
      custom_knowledge_points: '',
      difficulty: 'same',
      quantity: 5,
      sample_file: undefined,
      llm_id: defaultLlmId,
    },
  });

  // Update llm_id when defaultLlmId changes
  useEffect(() => {
    if (defaultLlmId) {
      form.setValue('llm_id', defaultLlmId);
    }
  }, [defaultLlmId, form]);

  const handleGoBack = () => {
    history.push('/workbench');
  };

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    setMarkdownData('');

    try {
      const formData = new FormData();

      formData.append('question_type', data.question_type);
      formData.append('custom_question_type', data.custom_question_type || '');
      formData.append('structure', data.structure);
      formData.append('custom_structure', data.custom_structure || '');
      formData.append('knowledge_points', data.knowledge_points);
      formData.append(
        'custom_knowledge_points',
        data.custom_knowledge_points || '',
      );
      formData.append('difficulty', data.difficulty);
      formData.append('quantity', data.quantity.toString());
      formData.append('llm_id', data.llm_id || 'deepseek-r1:32b@Ollama');

      if (data.sample_file) {
        formData.append('sample_file', data.sample_file as any);
      }

      const { getAuthorization } = await import('@/utils/authorization-util');

      const resp = await fetch('/v1/agent/extrapolate/start', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          Authorization: getAuthorization(),
        },
      });

      if (!resp.ok) {
        throw new Error(`HTTP ${resp.status}`);
      }

      const reader = resp.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let accumContent = '';

      const processBuffer = () => {
        let idx;
        while ((idx = buffer.indexOf('\n\n')) >= 0) {
          const rawEvent = buffer.slice(0, idx + 2);
          buffer = buffer.slice(idx + 2);

          let eventName = '';
          let dataStr = '';

          rawEvent
            .trim()
            .split('\n')
            .forEach((line) => {
              if (line.startsWith('event:')) {
                eventName = line.slice(6).trim();
              } else if (line.startsWith('data:')) {
                dataStr += line.slice(5).trim();
              }
            });

          if (!eventName || !dataStr) continue;

          try {
            const evtData = JSON.parse(dataStr);

            switch (eventName) {
              case 'stream': {
                let chunkText = evtData.chunk || '';

                // Filter think blocks
                if (thinkModeRef.current) {
                  const closeIdx = chunkText.indexOf('</think>');
                  if (closeIdx !== -1) {
                    chunkText = chunkText.slice(closeIdx + 8);
                    thinkModeRef.current = false;
                  } else {
                    chunkText = '';
                  }
                }

                const startIdx = chunkText.indexOf('<think>');
                if (startIdx !== -1) {
                  const beforeThink = chunkText.slice(0, startIdx);
                  const afterStart = chunkText.slice(startIdx + 7);
                  const endIdx = afterStart.indexOf('</think>');
                  if (endIdx !== -1) {
                    const afterThink = afterStart.slice(endIdx + 8);
                    chunkText = beforeThink + afterThink;
                  } else {
                    thinkModeRef.current = true;
                    chunkText = beforeThink;
                  }
                }

                accumContent += chunkText;
                flushSync(() => {
                  setMarkdownData(accumContent);
                });
                setLoading(false); // show content as soon as arrives
                break;
              }
              case 'waiting_embedding':
                setStatusMessage('示例文件向量化中...');
                setLoading(true);
                break;
              case 'generating':
                setStatusMessage('题目正在生成中...');
                setLoading(true);
                break;
              case 'embedding_done':
                setStatusMessage('向量化完成，开始生成题目...');
                break;
              case 'done':
                setLoading(false);
                message.success('举一反三题目生成成功！');
                break;
              case 'error':
                message.error(evtData.detail || '服务端错误');
                setLoading(false);
                break;
              default:
                break;
            }
          } catch (err) {
            console.error('Failed to parse SSE event', err, dataStr);
          }
        }
      };

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        processBuffer();
      }
    } catch (err: any) {
      console.error('Extrapolate generation failed', err);
      message.error(err.message || '生成举一反三题目失败');
      setLoading(false);
    }
  };

  const handleFormSubmit = () => {
    setLoading(true);
    form.handleSubmit(onSubmit)();
  };

  const handleExportToDocx = () => {
    if (markdownData) {
      try {
        const defaultFileName =
          '举一反三题目_' + new Date().toISOString().slice(0, 10);
        const fileName = `${defaultFileName}.docx`;
        exportMarkdownToDocx(markdownData, fileName);
      } catch (error) {
        console.error('导出文件错误:', error);
        message.error('导出文件失败');
      }
    }
  };

  const handleExportToExcel = () => {
    if (markdownData) {
      try {
        const defaultFileName =
          '举一反三题目_' + new Date().toISOString().slice(0, 10);

        // 使用markdown到Excel转换函数
        mdToXlxsForExtrapolate(markdownData, defaultFileName);
      } catch (error) {
        console.error('导出文件错误:', error);
        message.error('导出文件失败');
      }
    }
  };

  // 转发到3.0系统功能
  const handleForwardTo3System = async () => {
    if (!markdownData) {
      message.warning('没有可转发的内容');
      return;
    }

    setForwardingToV3System(true);

    try {
      // 动态获取V3系统配置
      const configResponse = await getV3SystemConfig();

      if (configResponse.code !== 0 || !configResponse.data) {
        message.error('获取V3系统配置失败');
        return;
      }

      const { endpoint, token } = configResponse.data;

      const success = await convertAndSendQuestions(
        markdownData,
        token,
        endpoint,
      );

      if (success) {
        message.success('题目已成功转发到3.0系统');
      }
    } catch (error) {
      console.error('转发到3.0系统失败:', error);
      message.error('转发到3.0系统失败，请重试');
    } finally {
      setForwardingToV3System(false);
    }
  };

  // 备用复制方法 - 保留作为后备选项
  const fallbackCopyToClipboard = (text: string) => {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      document.execCommand('copy');
      message.success('题目内容已复制到剪贴板，可以粘贴到3.0系统中');
    } catch (err) {
      message.error('复制失败，请手动选择和复制内容');
    } finally {
      document.body.removeChild(textArea);
    }
  };

  const uploadProps = {
    fileList,
    beforeUpload: (file: any) => {
      setFileList([file]);
      form.setValue('sample_file', file);
      return false; // Prevent automatic upload
    },
    onRemove: () => {
      setFileList([]);
      form.setValue('sample_file', undefined);
    },
  };

  return (
    <div className={`${styles.extrapolateContainer} main-content-container`}>
      {/* Page Header */}
      <div className={styles.pageHeader}>
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          className={styles.backButton}
        >
          返回
        </Button>

        <div className={styles.pageTitle}>
          <h2 className="main-page-title">举一反三</h2>
        </div>

        <Paragraph className={styles.pageDescription}>
          基于您提供的原始题目，智能生成相似结构和知识点的变式题目，帮助学生巩固知识点并提升解题能力。
        </Paragraph>
      </div>

      {/* Main Content */}
      <Row gutter={[24, 24]}>
        {/* Form Section */}
        <Col xs={24} lg={10}>
          <Card title="题目生成配置" className={styles.formCard}>
            <ExtrapolateForm form={form} uploadProps={uploadProps} />

            <div className={styles.formActions}>
              <Button
                type="primary"
                icon={<BulbOutlined />}
                size="large"
                loading={loading}
                onClick={handleFormSubmit}
                className={styles.generateButton}
              >
                {loading ? '生成中...' : '生成题目'}
              </Button>
            </div>
          </Card>
        </Col>

        {/* Results Section */}
        <Col xs={24} lg={14}>
          <Card
            title="生成结果"
            className={styles.resultCard}
            extra={
              markdownData && (
                <>
                  <Button
                    type="default"
                    icon={<FileWordOutlined size={14} />}
                    onClick={handleExportToDocx}
                    disabled={!markdownData}
                    style={{ marginRight: 16 }}
                  >
                    导出Word
                  </Button>
                  <Button
                    type="default"
                    icon={<FileExcelOutlined size={14} />}
                    onClick={handleExportToExcel}
                    disabled={!markdownData}
                    style={{ marginRight: 16 }}
                  >
                    导出Excel
                  </Button>
                  <Button
                    type="primary"
                    icon={<CloudUploadOutlined />}
                    onClick={handleForwardTo3System}
                    disabled={!markdownData}
                    loading={forwardingToV3System}
                  >
                    转发到3.0系统
                  </Button>
                </>
              )
            }
          >
            {loading ? (
              <div className={styles.loadingResult}>
                <div className={styles.loadingContainer}>
                  <GenerateCube color="#1890ff" size={12} />
                  <p
                    style={{
                      marginTop: '4px',
                      textAlign: 'center',
                      color: '#666',
                    }}
                  >
                    {statusMessage || '正在生成举一反三题目，请稍候...'}
                  </p>
                </div>
              </div>
            ) : markdownData ? (
              <div className={styles.resultContent}>
                <HightLightMarkdown>{markdownData}</HightLightMarkdown>
              </div>
            ) : (
              <div className={styles.emptyResult}>
                <div className={styles.placeholder}>
                  <BulbOutlined className={styles.placeholderIcon} />
                  <div>
                    配置完成后，点击&ldquo;生成题目&rdquo;开始创建举一反三题目
                  </div>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ExtrapolatePage;
