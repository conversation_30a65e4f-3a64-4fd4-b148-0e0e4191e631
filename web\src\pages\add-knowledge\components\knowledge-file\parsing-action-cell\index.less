@import '@/theme/high-tech-theme.less';

// More button (三点菜单按钮)
.moreButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #6b7280;

  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  &:focus {
    background-color: #f3f4f6;
    color: #374151;
  }
}

// 下拉菜单容器
.dropdownOverlay {
  :global {
    .ant-dropdown {
      border-radius: 8px;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
      border: 1px solid #e5e7eb;
      padding: 4px 0;
      min-width: 140px;
    }

    .ant-dropdown-menu {
      border-radius: 8px;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    .ant-dropdown-menu-item {
      padding: 0;
      margin: 0;
      border-radius: 0;

      &:hover {
        background-color: transparent;
      }
    }

    .ant-dropdown-menu-item-divider {
      margin: 4px 12px;
      border-top-color: #e5e7eb;
    }
  }
}

// 菜单项样式
.menuItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 0;

  &:hover {
    background-color: #f9fafb;
  }

  &:active {
    background-color: #f3f4f6;
  }
}

.menuIcon {
  font-size: 14px;
  color: #6b7280;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menuText {
  font-size: 14px;
  color: #374151;
  font-weight: 400;
  line-height: 1.5;
}

// 删除项的特殊样式
.deleteItem {
  .menuIcon,
  .menuText {
    color: #ef4444;
  }

  &:hover {
    background-color: #fef2f2;

    .menuIcon,
    .menuText {
      color: #dc2626;
    }
  }
}

// 禁用状态
.menuItem:global(.ant-dropdown-menu-item-disabled) {
  .menuIcon,
  .menuText {
    color: #d1d5db;
  }

  &:hover {
    background-color: transparent;
    cursor: not-allowed;
  }
}

// 兼容旧的按钮样式（如果还有地方在用）
.iconButton {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);

  &:hover {
    color: var(--primary-color);
    background-color: var(--primary-ultralight);
    transform: translateY(-2px);
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .moreButton {
    color: #9ca3af;

    &:hover,
    &:focus {
      background-color: rgba(75, 85, 99, 0.3);
      color: #f3f4f6;
    }
  }

  .dropdownOverlay {
    :global {
      .ant-dropdown {
        background-color: #374151;
        border-color: #4b5563;
      }

      .ant-dropdown-menu-item-divider {
        border-top-color: #4b5563;
      }
    }
  }

  .menuItem {
    &:hover {
      background-color: #4b5563;
    }

    &:active {
      background-color: #6b7280;
    }
  }

  .menuIcon {
    color: #9ca3af;
  }

  .menuText {
    color: #f3f4f6;
  }

  .deleteItem {
    .menuIcon,
    .menuText {
      color: #f87171;
    }

    &:hover {
      background-color: rgba(239, 68, 68, 0.1);

      .menuIcon,
      .menuText {
        color: #ef4444;
      }
    }
  }

  .iconButton {
    &:hover {
      background-color: rgba(59, 130, 246, 0.15);
    }
  }
}
