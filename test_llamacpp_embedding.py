#!/usr/bin/env python3
"""
测试Llama.cpp Embedding模型适配器
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rag.llm.embedding_model import LlamaCppEmbed

def test_llamacpp_embedding():
    """测试LlamaCpp embedding模型"""
    
    # 配置参数
    base_url = "http://192.168.3.124:8080"
    model_name = "llama-cpp-embedding"
    api_key = "dummy"  # llama.cpp通常不需要API key
    
    print(f"测试Llama.cpp Embedding模型")
    print(f"Base URL: {base_url}")
    print(f"Model Name: {model_name}")
    print("-" * 50)
    
    try:
        # 初始化模型
        embed_model = LlamaCppEmbed(
            key=api_key,
            model_name=model_name,
            base_url=base_url
        )
        print("✓ 模型初始化成功")
        
        # 测试单个文本embedding
        test_text = "从另一台主机发来的请求"
        print(f"\n测试文本: {test_text}")
        
        embedding, token_count = embed_model.encode_queries(test_text)
        print(f"✓ 单文本embedding成功")
        print(f"  - Embedding维度: {embedding.shape}")
        print(f"  - Token数量: {token_count}")
        print(f"  - Embedding前5个值: {embedding[:5]}")
        
        # 测试批量文本embedding
        test_texts = [
            "从另一台主机发来的请求",
            "这是第二个测试文本",
            "RAGFlow embedding测试"
        ]
        print(f"\n测试批量文本: {test_texts}")
        
        embeddings, total_tokens = embed_model.encode(test_texts)
        print(f"✓ 批量embedding成功")
        print(f"  - Embeddings形状: {embeddings.shape}")
        print(f"  - 总Token数量: {total_tokens}")
        print(f"  - 第一个embedding前5个值: {embeddings[0][:5]}")
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！Llama.cpp embedding适配器工作正常")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_llamacpp_embedding()
    sys.exit(0 if success else 1)
