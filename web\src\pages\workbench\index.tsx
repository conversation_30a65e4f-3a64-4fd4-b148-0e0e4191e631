import {
  AppstoreOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  BuildOutlined,
  BulbOutlined,
  <PERSON>Outlined,
  ExperimentOutlined,
  FileOutlined,
  FileSearchOutlined,
  FormatPainterOutlined,
  FormOutlined,
  PrinterOutlined,
  ProjectOutlined,
  RobotOutlined,
  ScheduleOutlined,
  ToolOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons';
import { Col, Divider, Row, Tabs, Typography } from 'antd';
import React, { useState } from 'react';
import { history } from 'umi';
import styles from './index.less';

const { Title, Paragraph } = Typography;

interface AppCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  link?: string;
}

const AppCard: React.FC<AppCardProps> = ({
  title,
  description,
  icon,
  link,
}) => {
  const handleClick = () => {
    if (link) {
      history.push(link);
    }
  };

  return (
    <div className={styles.appCard} onClick={handleClick}>
      <div className={styles.appIcon}>{icon}</div>
      <div className={styles.appTitle}>{title}</div>
      <div className={styles.appDescription}>{description}</div>
    </div>
  );
};

// App categories based on the image
interface CategoryProps {
  key: string;
  title: string;
  apps: AppCardProps[];
}

const WorkbenchPage: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('popular');

  // Define all apps
  const allApps = {
    aiQuestion: {
      title: 'AI出题',
      description: '人工智能辅助出题',
      icon: <FormatPainterOutlined />,
      link: '/agent/ai-question',
    },
    lessonPlan: {
      title: '教案生成',
      description: '生成教学计划和教案',
      icon: <ScheduleOutlined />,
      link: '/agent/lesson-plan-v2',
    },
    smartExam: {
      title: '智能组卷',
      description: '智能生成考试试卷',
      icon: <BuildOutlined />,
      link: '/agent/smart-exam',
    },
    unitAssignment: {
      title: '单元作业',
      description: '创建单元作业',
      icon: <FileOutlined />,
    },
    questionAnalysis: {
      title: '题目解析',
      description: '分析题目难度和知识点',
      icon: <ExperimentOutlined />,
    },
    similarQuestions: {
      title: '举一反三',
      description: '生成相似题目',
      icon: <BulbOutlined />,
      link: '/agent/extrapolate',
    },
    lectureMaterial: {
      title: '讲义生成',
      description: '快速生成教学讲义',
      icon: <PrinterOutlined />,
    },
    aiAssistant: {
      title: 'AI助手',
      description: '教学助手工具',
      icon: <RobotOutlined />,
    },
    randomPicker: {
      title: '随机点名',
      description: '课堂随机点名工具',
      icon: <AppstoreOutlined />,
    },
    resourceRecommendation: {
      title: '资源推荐',
      description: '教学资源智能推荐',
      icon: <CloudOutlined />,
    },
    trainingPlan: {
      title: '培训计划',
      description: '制定培训计划',
      icon: <ToolOutlined />,
    },
    workSummary: {
      title: '工作总结',
      description: '生成工作总结报告',
      icon: <UnorderedListOutlined />,
    },
    constructionPlan: {
      title: '施工方案',
      description: '创建施工计划方案',
      icon: <ProjectOutlined />,
    },
    feasibilityReport: {
      title: '可研报告',
      description: '生成项目可行性研究报告',
      icon: <ExperimentOutlined />,
    },
    meetingMinutes: {
      title: '会议纪要',
      description: '生成会议记录和纪要',
      icon: <FormOutlined />,
    },
    gradeAnalysis: {
      title: '成绩分析',
      description: '分析学生成绩数据',
      icon: <FileSearchOutlined />,
    },
    dataSummary: {
      title: '数据汇总',
      description: '汇总和处理数据',
      icon: <BarChartOutlined />,
    },
  };

  // Define categories according to requirements
  const categories: CategoryProps[] = [
    {
      key: 'popular',
      title: '热门应用',
      apps: [
        allApps.smartExam,
        allApps.aiQuestion,
        allApps.similarQuestions,
        allApps.lessonPlan,
      ],
    },
    {
      key: 'exam',
      title: '出题考试',
      apps: [
        allApps.smartExam,
        allApps.aiQuestion,
        // allApps.unitAssignment,
        // allApps.questionAnalysis,
        allApps.similarQuestions,
      ],
    },
    {
      key: 'teacher',
      title: '教师工具',
      apps: [
        allApps.lessonPlan,
        // allApps.lectureMaterial,
        // allApps.aiAssistant,
        // allApps.randomPicker,
        // allApps.resourceRecommendation,
      ],
    },
    // {
    //   key: 'plan',
    //   title: '计划方案',
    //   apps: [
    //     allApps.trainingPlan,
    //     allApps.workSummary,
    //     allApps.constructionPlan,
    //     allApps.feasibilityReport,
    //     allApps.meetingMinutes,
    //   ],
    // },
    // {
    //   key: 'data',
    //   title: '数据整理',
    //   apps: [allApps.gradeAnalysis, allApps.dataSummary],
    // },
  ];

  // Get the current active category data
  const activeData =
    categories.find((cat) => cat.key === activeCategory) || categories[0];

  // Create tab items for modern Tabs component
  const tabItems = categories.map((category) => ({
    key: category.key,
    label: category.title,
  }));

  return (
    <div className={`${styles.workbenchContainer} main-content-container`}>
      <div className={styles.heroSection}>
        <h2 className="main-page-title">教学应用平台</h2>
        <Paragraph className={styles.heroSubtitle}>
          职教专属的AI工作助手
        </Paragraph>
      </div>

      <Divider className={styles.sectionDivider} />

      <div className={styles.mainTabsContainer}>
        <Tabs
          defaultActiveKey="popular"
          onChange={(key) => setActiveCategory(key)}
          className={styles.mainTabs}
          items={tabItems}
        />

        <div className={styles.appGridContainer}>
          <Row gutter={[24, 24]}>
            {activeData.apps.map((app, index) => (
              <Col xs={24} sm={12} md={8} lg={6} key={index}>
                <AppCard
                  title={app.title}
                  description={app.description}
                  icon={app.icon}
                  link={app.link}
                />
              </Col>
            ))}
          </Row>
        </div>
      </div>

      {/*
      <div className={styles.statsSection}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={12} lg={6}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>25</div>
              <div className={styles.statLabel}>活跃项目</div>
            </div>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>142</div>
              <div className={styles.statLabel}>已处理文档</div>
            </div>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>8</div>
              <div className={styles.statLabel}>已部署模型</div>
            </div>
          </Col>
          <Col xs={24} md={12} lg={6}>
            <div className={styles.statCard}>
              <div className={styles.statValue}>93%</div>
              <div className={styles.statLabel}>系统效率</div>
            </div>
          </Col>
        </Row>
      </div>

      <div className={styles.recentActivitySection}>
        <div className={styles.sectionHeader}>
          <Title level={3} className={styles.sectionTitle}>
            最近活动
          </Title>
        </div>
        <div className={styles.activityCard}>
          <Timeline>
            <Timeline.Item>
              文档 '2023年教学总结.pdf' 已处理{' '}
              <span className={styles.timeLabel}>2小时前</span>
            </Timeline.Item>
            <Timeline.Item>
              模型 '教学助手' 已训练完成{' '}
              <span className={styles.timeLabel}>昨天</span>
            </Timeline.Item>
            <Timeline.Item>
              新建应用 '智能组卷-v2'{' '}
              <span className={styles.timeLabel}>3天前</span>
            </Timeline.Item>
            <Timeline.Item>
              知识库更新了15个新文档{' '}
              <span className={styles.timeLabel}>1周前</span>
            </Timeline.Item>
          </Timeline>
        </div>
      </div>
      */}
    </div>
  );
};

export default WorkbenchPage;
