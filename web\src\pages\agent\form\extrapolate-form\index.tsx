import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useFetchLlmList } from '@/hooks/llm-hooks';
import { InputNumber as AntdInputNumber, Radio, Upload } from 'antd';
import { UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import styles from './index.less';

// Custom InputNumber component that is compatible with the form
const InputNumber = ({ value, onChange, ...props }: any) => {
  return <AntdInputNumber value={value} onChange={onChange} {...props} />;
};

interface ExtrapolateFormProps {
  form: UseFormReturn<any>;
  uploadProps: any;
}

const ExtrapolateForm = ({ form, uploadProps }: ExtrapolateFormProps) => {
  const { t } = useTranslation();
  const { list: llmList } = useFetchLlmList();

  const llmOptions = llmList
    ?.filter((llm: any) => llm.llm_type === 'CHAT')
    ?.map((llm: any) => ({
      label: llm.name,
      value: llm.id,
    }));

  return (
    <Form {...form}>
      <form
        className="space-y-6"
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        {/* 题型选择 */}
        <FormField
          control={form.control}
          name="question_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>题型</FormLabel>
              <FormControl>
                <div className={styles.radioSelector}>
                  <Radio.Group
                    value={field.value}
                    onChange={field.onChange}
                    className={styles.radioGroup}
                  >
                    <Radio value="same" className={styles.radioOption}>
                      保持与原题相同的题型
                    </Radio>
                    <Radio value="custom" className={styles.radioOption}>
                      自定义填写
                    </Radio>
                  </Radio.Group>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 自定义题型输入框 */}
        {form.watch('question_type') === 'custom' && (
          <FormField
            control={form.control}
            name="custom_question_type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>自定义题型</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="bg-white"
                    placeholder="请输入自定义题型，如：选择题、填空题等"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* 结构选择 */}
        <FormField
          control={form.control}
          name="structure"
          render={({ field }) => (
            <FormItem>
              <FormLabel>结构</FormLabel>
              <FormControl>
                <div className={styles.radioSelector}>
                  <Radio.Group
                    value={field.value}
                    onChange={field.onChange}
                    className={styles.radioGroup}
                  >
                    <Radio value="same" className={styles.radioOption}>
                      保持与原题相同的结构
                    </Radio>
                    <Radio value="custom" className={styles.radioOption}>
                      自定义填写
                    </Radio>
                  </Radio.Group>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 自定义结构输入框 */}
        {form.watch('structure') === 'custom' && (
          <FormField
            control={form.control}
            name="custom_structure"
            render={({ field }) => (
              <FormItem>
                <FormLabel>自定义结构</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    className="bg-white"
                    placeholder="请描述期望的题目结构"
                    rows={3}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* 知识点选择 */}
        <FormField
          control={form.control}
          name="knowledge_points"
          render={({ field }) => (
            <FormItem>
              <FormLabel>知识点</FormLabel>
              <FormControl>
                <div className={styles.radioSelector}>
                  <Radio.Group
                    value={field.value}
                    onChange={field.onChange}
                    className={styles.radioGroup}
                  >
                    <Radio value="same_context" className={styles.radioOption}>
                      覆盖相同的知识点，但需改变具体情境
                    </Radio>
                    <Radio value="custom" className={styles.radioOption}>
                      自定义填写
                    </Radio>
                  </Radio.Group>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 自定义知识点输入框 */}
        {form.watch('knowledge_points') === 'custom' && (
          <FormField
            control={form.control}
            name="custom_knowledge_points"
            render={({ field }) => (
              <FormItem>
                <FormLabel>自定义知识点</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    className="bg-white"
                    placeholder="请描述期望覆盖的知识点"
                    rows={3}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* 难度选择 */}
        <FormField
          control={form.control}
          name="difficulty"
          render={({ field }) => (
            <FormItem>
              <FormLabel>难度</FormLabel>
              <FormControl>
                <div className={styles.radioSelector}>
                  <Radio.Group
                    value={field.value}
                    onChange={field.onChange}
                    className={styles.radioGroup}
                  >
                    <Radio value="same" className={styles.radioOption}>
                      难度需与原题一致
                    </Radio>
                    <Radio value="basic" className={styles.radioOption}>
                      基础
                    </Radio>
                    <Radio value="medium" className={styles.radioOption}>
                      中等
                    </Radio>
                    <Radio value="hard" className={styles.radioOption}>
                      困难
                    </Radio>
                  </Radio.Group>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 数量输入 */}
        <FormField
          control={form.control}
          name="quantity"
          render={({ field }) => (
            <FormItem>
              <FormLabel>数量</FormLabel>
              <FormControl>
                <InputNumber
                  value={field.value}
                  onChange={field.onChange}
                  min={1}
                  max={1000}
                  className="w-full"
                  placeholder="请输入生成题目的数量"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 示例试题上传 */}
        <FormField
          control={form.control}
          name="sample_file"
          render={({ field }) => (
            <FormItem>
              <FormLabel>示例试题</FormLabel>
              <FormControl>
                <div className={styles.fileUploadSection}>
                  <div className={styles.uploadSection}>
                    <p className={styles.uploadInfo}>
                      上传类型：Excel、CSV、TXT
                    </p>
                    <Upload.Dragger
                      {...uploadProps}
                      accept=".xlsx,.xls,.csv,.txt"
                      maxCount={1}
                      className="upload-dragger-custom"
                      style={{
                        border: 'none',
                        background: 'transparent',
                        padding: 0,
                        margin: 0,
                        width: '100%',
                        height: 'auto',
                      }}
                    >
                      <div className={styles.uploadBox}>
                        <div className={styles.uploadPlaceholder}>
                          <div className={styles.uploadIcon}></div>
                          <p className={styles.dragText}>
                            将文件拖拽到此处，或
                            <span className={styles.browseText}>点击上传</span>
                          </p>
                          <p className={styles.supportedFormats}>
                            支持上传 .xlsx .xls .csv .txt
                            格式，单文件大小不超过20MB
                          </p>
                        </div>
                      </div>
                    </Upload.Dragger>
                  </div>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
};

export default ExtrapolateForm;
