"""
通用LLM交互日志记录模块

支持记录流式和非流式LLM交互的详细信息，包括：
- 请求参数（system prompt, history, generation config）
- 响应内容（生成结果、token统计）
- 性能指标（耗时、吞吐量）
- 上下文信息（租户、用户、Agent类型）
"""

import json
import time
import uuid
import logging
from typing import Dict, Any, Optional, Generator, List, Union
from datetime import datetime
from functools import wraps
import threading
from contextlib import contextmanager


class LLMInteractionLogger:
    """LLM交互日志记录器"""

    def __init__(self, logger_name: str = "llm_interaction"):
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(logging.INFO)

        # 创建文件处理器，专门记录LLM交互日志
        if not self.logger.handlers:
            handler = logging.FileHandler(
                'logs/llm_interactions.log', encoding='utf-8')
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def log_interaction(self,
                        interaction_id: str,
                        agent_type: str,
                        tenant_id: str,
                        user_id: str,
                        llm_model: str,
                        request_data: Dict[str, Any],
                        response_data: Dict[str, Any],
                        performance_metrics: Dict[str, Any]):
        """
        记录LLM交互日志

        Args:
            interaction_id: 交互唯一标识
            agent_type: Agent类型 (ai_question, smart_exam, extrapolate, lesson_plan)
            tenant_id: 租户ID
            user_id: 用户ID  
            llm_model: 使用的LLM模型
            request_data: 请求数据
            response_data: 响应数据
            performance_metrics: 性能指标
        """
        log_entry = {
            "interaction_id": interaction_id,
            "timestamp": datetime.now().isoformat(),
            "agent_type": agent_type,
            "tenant_id": tenant_id,
            "user_id": user_id,
            "llm_model": llm_model,
            "request": request_data,
            "response": response_data,
            "performance": performance_metrics
        }

        self.logger.info(json.dumps(log_entry, ensure_ascii=False, indent=2))


class LLMInteractionTracker:
    """LLM交互跟踪器 - 用于跟踪单次交互的完整生命周期"""

    def __init__(self,
                 agent_type: str,
                 tenant_id: str,
                 user_id: str,
                 llm_model: str,
                 logger: LLMInteractionLogger):
        self.interaction_id = str(uuid.uuid4())
        self.agent_type = agent_type
        self.tenant_id = tenant_id
        self.user_id = user_id
        self.llm_model = llm_model
        self.logger = logger

        # 交互数据
        self.start_time = None
        self.end_time = None
        self.request_data = {}
        self.response_data = {"content": "", "chunks": [], "total_tokens": 0}
        self.error_info = None

    def start_interaction(self, system: str, history: List[Dict], gen_conf: Dict):
        """开始记录交互"""
        self.start_time = time.time()
        self.request_data = {
            "system_prompt": system,
            "history_length": len(history),
            # 只记录最后2条历史
            "history_sample": history[-2:] if len(history) >= 2 else history,
            "generation_config": gen_conf,
            "prompt_tokens": self._estimate_tokens(system, history)
        }

    def add_response_chunk(self, chunk: str):
        """添加响应片段（用于流式响应）"""
        self.response_data["chunks"].append({
            "timestamp": time.time(),
            "content": chunk,
            "chunk_size": len(chunk)
        })
        self.response_data["content"] += chunk

    def set_response_content(self, content: str):
        """设置完整响应内容（用于非流式响应）"""
        self.response_data["content"] = content

    def set_token_count(self, total_tokens: int):
        """设置token统计"""
        self.response_data["total_tokens"] = total_tokens

    def set_error(self, error: Exception):
        """设置错误信息"""
        self.error_info = {
            "type": type(error).__name__,
            "message": str(error),
            "timestamp": time.time()
        }

    def finish_interaction(self):
        """完成交互记录"""
        self.end_time = time.time()

        # 计算性能指标
        duration = self.end_time - self.start_time if self.start_time else 0
        response_tokens = self._estimate_response_tokens()

        performance_metrics = {
            "duration_seconds": duration,
            "prompt_tokens": self.request_data.get("prompt_tokens", 0),
            "response_tokens": response_tokens,
            "total_tokens": self.response_data["total_tokens"] or (
                self.request_data.get("prompt_tokens", 0) + response_tokens
            ),
            "tokens_per_second": response_tokens / duration if duration > 0 else 0,
            "chunk_count": len(self.response_data["chunks"]),
            "response_length": len(self.response_data["content"]),
            "has_error": self.error_info is not None
        }

        # 准备响应数据
        response_data = {
            "content_length": len(self.response_data["content"]),
            "content_preview": self.response_data["content"][:500] + "..." if len(self.response_data["content"]) > 500 else self.response_data["content"],
            "chunk_count": len(self.response_data["chunks"]),
            "total_tokens": self.response_data["total_tokens"],
            "error": self.error_info
        }

        # 记录日志
        self.logger.log_interaction(
            interaction_id=self.interaction_id,
            agent_type=self.agent_type,
            tenant_id=self.tenant_id,
            user_id=self.user_id,
            llm_model=self.llm_model,
            request_data=self.request_data,
            response_data=response_data,
            performance_metrics=performance_metrics
        )

        return self.interaction_id

    def _estimate_tokens(self, system: str, history: List[Dict]) -> int:
        """估算输入token数量"""
        try:
            from rag.nlp.tokenizer import num_tokens_from_string
            total_text = system + "\n" + "\n".join([
                f"{msg.get('role', '')}: {msg.get('content', '')}"
                for msg in history
            ])
            return num_tokens_from_string(total_text)
        except:
            # 简单估算：约4个字符=1个token
            total_text = system + "\n" + "\n".join([
                f"{msg.get('role', '')}: {msg.get('content', '')}"
                for msg in history
            ])
            return len(total_text) // 4

    def _estimate_response_tokens(self) -> int:
        """估算响应token数量"""
        try:
            from rag.nlp.tokenizer import num_tokens_from_string
            return num_tokens_from_string(self.response_data["content"])
        except:
            # 简单估算：约4个字符=1个token
            return len(self.response_data["content"]) // 4


# 全局日志记录器实例
_llm_logger = LLMInteractionLogger()


def create_llm_tracker(agent_type: str,
                       tenant_id: str,
                       user_id: str,
                       llm_model: str) -> LLMInteractionTracker:
    """创建LLM交互跟踪器"""
    return LLMInteractionTracker(
        agent_type=agent_type,
        tenant_id=tenant_id,
        user_id=user_id,
        llm_model=llm_model,
        logger=_llm_logger
    )


@contextmanager
def llm_interaction_context(agent_type: str,
                            tenant_id: str,
                            user_id: str,
                            llm_model: str,
                            system: str,
                            history: List[Dict],
                            gen_conf: Dict):
    """LLM交互上下文管理器"""
    tracker = create_llm_tracker(agent_type, tenant_id, user_id, llm_model)
    tracker.start_interaction(system, history, gen_conf)

    try:
        yield tracker
    except Exception as e:
        tracker.set_error(e)
        raise
    finally:
        tracker.finish_interaction()


def log_llm_stream_response(tracker: LLMInteractionTracker,
                            stream_generator: Generator) -> Generator:
    """
    包装流式响应生成器，记录每个响应片段

    Args:
        tracker: LLM交互跟踪器
        stream_generator: 原始流式响应生成器

    Yields:
        响应片段
    """
    total_tokens = 0

    try:
        for chunk in stream_generator:
            if isinstance(chunk, int):
                # 最后的token计数
                total_tokens = chunk
                tracker.set_token_count(total_tokens)
                yield chunk
            else:
                # 响应内容片段
                tracker.add_response_chunk(chunk)
                yield chunk

    except Exception as e:
        tracker.set_error(e)
        raise


def log_llm_response(tracker: LLMInteractionTracker,
                     response: Union[str, tuple]) -> Union[str, tuple]:
    """
    记录非流式响应

    Args:
        tracker: LLM交互跟踪器
        response: LLM响应（可能是字符串或(content, tokens)元组）

    Returns:
        原始响应
    """
    try:
        if isinstance(response, tuple):
            content, tokens = response
            tracker.set_response_content(content)
            tracker.set_token_count(tokens)
        else:
            tracker.set_response_content(response)

        return response

    except Exception as e:
        tracker.set_error(e)
        raise


# 装饰器形式的日志记录（用于简化集成）
def log_llm_interaction(agent_type: str):
    """
    装饰器：自动记录LLM交互日志

    使用方式：
    @log_llm_interaction("ai_question")
    def generate_questions(self, ...):
        ...
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # 尝试从self获取上下文信息
            tenant_id = getattr(self, '_tenant_id', 'unknown') or getattr(
                self._canvas, 'tenant_id', 'unknown')
            user_id = getattr(self, '_user_id', 'unknown') or getattr(
                self._canvas, 'user_id', 'unknown')
            llm_model = getattr(self._param, 'llm_id', 'unknown') if hasattr(
                self, '_param') else 'unknown'

            # 创建跟踪器
            tracker = create_llm_tracker(
                agent_type, tenant_id, user_id, llm_model)

            # 尝试从参数中提取LLM调用信息
            # 这个装饰器主要用于标记，具体的日志记录在LLM调用处进行

            try:
                result = func(self, *args, **kwargs)
                return result
            except Exception as e:
                tracker.set_error(e)
                tracker.finish_interaction()
                raise

        return wrapper
    return decorator
