@import '@/theme/high-tech-theme.less';

.navs {
  ul {
    padding: 0;
    list-style: none;
    display: flex;
  }

  li {
    margin-right: 1em;
  }
}

.layout {
  height: 100vh;
}

body {
  margin: 0;
}

.divider {
  margin: 0 !important;
}

.clickAvailable {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
}

.siteLayout {
  transition: margin-left 0.2s;
}

.highTechLayout {
  min-height: 100vh;
  background-color: var(--background-secondary);
  position: relative;
}

.layoutContainer {
  position: relative;
  display: flex;
}

.contentArea {
  flex: 1;
  // padding: var(--spacing-lg);
  transition: margin-left var(--transition-normal);
  overflow-x: hidden;
  overflow-y: auto;
  height: calc(100vh - var(--header-height) + 4px);
}

/* Mobile sidebar handling */
.sidebarVisible {
  transform: translateX(0);
}

.sidebarHidden {
  transform: translateX(-100%);
}

.mobileOverlay {
  position: fixed;
  top: var(--header-height);
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 900;
  backdrop-filter: blur(2px);
}

/* Responsive styles */
@media (max-width: 1080px) {
}

@media (max-width: 768px) {
  .contentArea {
    padding: var(--spacing-md);
  }
}
