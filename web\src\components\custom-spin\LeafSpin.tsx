import classNames from 'classnames';
import React from 'react';
import './LeafSpin.less';
import { LeafSpinProps } from './types';

const LeafSpin: React.FC<LeafSpinProps> = ({
  spinning = false,
  tip,
  size = 'default',
  className,
  style,
  children,
  text,
}) => {
  // 渲染旋转点loading动画
  const renderLeafLoading = () => (
    <div className="loading-content">
      <div className="loading-dots-container">
        <div className="c1"></div>
        <div className="c2"></div>
        <div className="c3"></div>
        <div className="c4"></div>
      </div>
      {text && <span className="loading-text">{text}</span>}
    </div>
  );

  // 如果不是loading状态，直接返回子组件
  if (!spinning) {
    return <>{children}</>;
  }

  // 如果没有子组件，只显示loading指示器
  if (!children) {
    return (
      <div
        className={classNames('leaf-spin-standalone', className)}
        style={style}
      >
        <div className={classNames('leaf-spin-indicator', `size-${size}`)}>
          {renderLeafLoading()}
        </div>
        {tip && <div className="leaf-spin-tip">{tip}</div>}
      </div>
    );
  }

  // 包装模式：有子组件时，创建遮罩层
  return (
    <div className={classNames('leaf-spin-container', className)} style={style}>
      <div className="leaf-spin-content">{children}</div>
      <div className="leaf-spin-overlay">
        <div className={classNames('leaf-spin-indicator', `size-${size}`)}>
          {renderLeafLoading()}
        </div>
        {tip && <div className="leaf-spin-tip">{tip}</div>}
      </div>
    </div>
  );
};

export default LeafSpin;
