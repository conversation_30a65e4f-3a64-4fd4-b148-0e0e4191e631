import FilePreview from '@/components/file-preview/FilePreview';
import HightLightMarkdown from '@/components/highlight-markdown';
import { useFetchLlmList } from '@/hooks/llm-hooks';
import {
  generateLessonPlan,
  searchRelatedFiles,
} from '@/services/agent_service';
import { exportMarkdownToDocx } from '@/utils/markdown-export';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  ScheduleOutlined,
} from '@ant-design/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Col, Row, Typography, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { history } from 'umi';
import { z } from 'zod';
import LessonPlanForm from '../form/lesson-plan-form';
import { INextOperatorForm } from '../interface';
import RecommendedResources, {
  ResourceItem,
} from './components/RecommendedResources';
import styles from './index.less';

const { Title, Paragraph } = Typography;

// Define the form schema - 修改验证逻辑，支持二选一
const formSchema = z
  .object({
    course_name: z.string().min(1, { message: '课程名称不能为空' }),
    lesson_type: z.string().min(1, { message: '课型不能为空' }),
    lesson_duration: z.number().min(1, { message: '课时不能为空' }),
    target_audience: z.string().min(1, { message: '受众群体不能为空' }),
    course_content: z.string().optional(), // 改为可选
    other_requirements: z.string().optional(),
    course_file: z.any().optional(),
    llm_id: z.string().min(1, { message: '请选择一个模型' }),
  })
  .refine(
    (data) => {
      // 自定义验证：课程内容和文件至少要有一个
      return (
        (data.course_content && data.course_content.trim() !== '') ||
        data.course_file
      );
    },
    {
      message: '请输入课程内容或上传课程材料文件',
      path: ['course_content'], // 错误会显示在course_content字段上
    },
  );

type FormValues = z.infer<typeof formSchema>;

const LessonPlanPage: React.FC = () => {
  // const { t } = useTranslation(); // 暂时注释掉未使用的变量
  const [loading, setLoading] = useState(false);
  const [generatedLessonPlan, setGeneratedLessonPlan] = useState<string | null>(
    null,
  );
  const [relatedFiles, setRelatedFiles] = useState<ResourceItem[]>([]);
  const [searchingFiles, setSearchingFiles] = useState(false);
  const { list: llmList } = useFetchLlmList();
  const [defaultLlmId, setDefaultLlmId] = useState<string>(
    'deepseek-r1:32b@Ollama',
  );
  const [fileList, setFileList] = useState<any[]>([]);

  // 文件预览相关状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<{
    name: string;
    type: string;
    location: string;
    size: number;
  } | null>(null);

  // Get the first available CHAT model as default
  useEffect(() => {
    if (llmList && llmList.length > 0) {
      const chatModels = llmList.filter((llm: any) => llm.llm_type === 'CHAT');
      if (chatModels.length > 0) {
        setDefaultLlmId(chatModels[0].id.toString());
      }
    }
  }, [llmList]);

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      course_name: '',
      lesson_type: '',
      lesson_duration: 40,
      target_audience: '',
      course_content: '',
      other_requirements: '',
      course_file: undefined,
      llm_id: defaultLlmId,
    },
  });

  // Update llm_id when defaultLlmId changes
  useEffect(() => {
    if (defaultLlmId) {
      form.setValue('llm_id', defaultLlmId);
    }
  }, [defaultLlmId, form]);

  const handleGoBack = () => {
    history.push('/workbench');
  };

  // 搜索相关文件的函数
  const searchForRelatedFiles = async (
    courseContent: string,
    llmId: string,
  ) => {
    if (!courseContent.trim()) {
      return;
    }

    setSearchingFiles(true);
    try {
      const response = await searchRelatedFiles({
        content: courseContent,
        llm_id: llmId,
        max_keywords: 5,
        max_results: 5,
        temperature: 0.3,
      });

      if (response && response.code === 0 && response.data) {
        // 转换文件数据为ResourceItem格式
        const resourceItems: ResourceItem[] = response.data.matched_files.map(
          (file: any) => ({
            id: file.id,
            name: file.name,
            type: file.type,
            size: file.size,
            matched_keyword: file.matched_keyword,
            keyword_weight: file.keyword_weight,
            create_time: Number(file.create_time),
            location: file.location || '',
          }),
        );

        setRelatedFiles(resourceItems);
        console.log('Found related files:', resourceItems);
      } else {
        setRelatedFiles([]);
      }
    } catch (error) {
      console.error('Error searching related files:', error);
      setRelatedFiles([]);
    } finally {
      setSearchingFiles(false);
    }
  };

  const onSubmit = async (data: FormValues) => {
    setGeneratedLessonPlan(null);
    setRelatedFiles([]);
    setLoading(true);

    try {
      console.log('Submitting LessonPlan Agent:', data);

      if (!data.llm_id) {
        throw new Error('请选择一个模型才能生成教案');
      }

      // 额外验证：确保课程内容或文件至少有一个
      if (!data.course_content?.trim() && !data.course_file) {
        throw new Error('请输入课程内容或上传课程材料文件');
      }

      // 准备API调用参数
      const requestData = {
        course_name: data.course_name,
        lesson_type: data.lesson_type,
        lesson_duration: data.lesson_duration,
        target_audience: data.target_audience,
        course_content: data.course_content || '',
        other_requirements: data.other_requirements || '',
        llm_id: data.llm_id,
      };

      let apiParams;

      // 如果有文件上传，使用FormData格式
      if (data.course_file) {
        const formData = new FormData();
        Object.entries(requestData).forEach(([key, value]) => {
          formData.append(key, value.toString());
        });
        formData.append('course_file', data.course_file);
        apiParams = formData;
      } else {
        // 否则使用JSON格式
        apiParams = requestData;
      }

      const response = await generateLessonPlan(apiParams);

      if (response && response.code === 0 && response.data) {
        setGeneratedLessonPlan(response.data.generated_lesson_plan);
        message.success('教案生成成功！');
        console.log(
          'Generated lesson plan:',
          response.data.generated_lesson_plan,
        );

        // 如果有课程内容，尝试搜索相关文件
        if (data.course_content?.trim()) {
          await searchForRelatedFiles(data.course_content, data.llm_id);
        }
      } else {
        throw new Error(response?.message || '生成教案失败，请重试');
      }
    } catch (error: any) {
      console.error('Error in lesson plan generation:', error);
      const errorMessage = error?.message || '生成教案失败，请重试';
      setGeneratedLessonPlan(`生成失败: ${errorMessage}`);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 添加新的表单提交处理函数，确保按钮点击后立即禁用
  const handleFormSubmit = () => {
    // 立即设置loading状态，禁用按钮
    setLoading(true);
    // 触发表单提交
    form.handleSubmit(onSubmit)();
  };

  const handleExportToDocx = () => {
    if (generatedLessonPlan) {
      try {
        const defaultFileName = '教案_' + new Date().toISOString().slice(0, 10);
        const fileName = `${defaultFileName}.docx`;
        exportMarkdownToDocx(generatedLessonPlan, fileName);
      } catch (error) {
        console.error('导出文件错误:', error);
        message.error('导出文件失败');
      }
    }
  };

  const handleResourceClick = (resource: ResourceItem) => {
    console.log('Clicked resource:', resource);

    // 设置预览文件信息
    setPreviewFile({
      name: resource.name,
      type: resource.type,
      location: resource.location,
      size: resource.size,
    });

    // 打开预览模态框
    setPreviewVisible(true);
  };

  // 关闭文件预览
  const handleClosePreview = () => {
    setPreviewVisible(false);
    setPreviewFile(null);
  };

  const uploadProps = {
    fileList,
    beforeUpload: (file: any) => {
      setFileList([file]);
      form.setValue('course_file', file);
      return false; // Prevent automatic upload
    },
    onRemove: () => {
      setFileList([]);
      form.setValue('course_file', undefined);
    },
  };

  // Cast the form to match INextOperatorForm interface expected by LessonPlanForm
  const lessonPlanForm = form as unknown as INextOperatorForm['form'];

  return (
    <div className={styles.lessonPlanContainer}>
      <div className={styles.pageHeader}>
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          className={styles.backButton}
        >
          返回
        </Button>
        <div className={styles.pageTitle}>
          <ScheduleOutlined className={styles.titleIcon} />
          <Title level={3}>教案生成</Title>
        </div>
        <Paragraph className={styles.pageDescription}>
          基于知识库，智能生成教学教案
        </Paragraph>
      </div>

      <Row gutter={24}>
        <Col span={10}>
          <Card className={styles.formCard} title="教案生成配置">
            <LessonPlanForm form={lessonPlanForm} uploadProps={uploadProps} />

            <div className={styles.formActions}>
              <Button
                type="primary"
                icon={<ScheduleOutlined />}
                size="large"
                loading={loading}
                onClick={handleFormSubmit}
                className={styles.generateButton}
              >
                一键生成
              </Button>
            </div>
          </Card>
        </Col>

        <Col span={14}>
          <Card
            className={styles.resultCard}
            title="生成结果"
            extra={
              generatedLessonPlan && (
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleExportToDocx}
                >
                  导出Word
                </Button>
              )
            }
          >
            {generatedLessonPlan ? (
              <div className={styles.resultContent}>
                {/* 左侧 Markdown 内容区域 */}
                <div className={styles.markdownSection}>
                  <HightLightMarkdown>{generatedLessonPlan}</HightLightMarkdown>
                </div>

                {/* 右侧推荐资源区域 */}
                <div className={styles.sourcesSection}>
                  <RecommendedResources
                    resources={relatedFiles}
                    onResourceClick={handleResourceClick}
                  />
                </div>
              </div>
            ) : (
              <div className={styles.emptyResult}>
                <div className={styles.placeholder}>
                  <ScheduleOutlined className={styles.placeholderIcon} />
                  <p>完成左侧表单配置后，点击"一键生成"按钮生成教案</p>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 文件预览组件 */}
      <FilePreview
        visible={previewVisible}
        onClose={handleClosePreview}
        file={previewFile}
      />
    </div>
  );
};

export default LessonPlanPage;
