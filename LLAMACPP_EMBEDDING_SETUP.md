# RAGFlow 集成 Llama.cpp Embedding 模型配置指南

## 概述

本指南介绍如何在 RAGFlow 中配置和使用部署在 192.168.3.124 主机上的 llama.cpp embedding 模型。

**✅ 已完成的工作：**

- 添加了完整的 Llama.cpp 支持，遵循 RAGFlow 现有的设计原则
- 无侵入式集成，不影响现有业务逻辑
- 支持前端 UI 配置，无需手动修改代码

## 前提条件

- RAGFlow 部署在 192.168.3.123 主机上
- Llama.cpp embedding 服务部署在 192.168.3.124:8080
- 两台主机之间网络连通

## 实现详情

### 1. 完整的系统集成

已按照 RAGFlow 的架构设计原则完成以下集成：

#### 后端集成：

- ✅ 添加了`LlamaCppEmbed`类到`rag/llm/embedding_model.py`
- ✅ 更新了`EmbeddingModel`字典注册
- ✅ 添加了后端 API 处理逻辑
- ✅ 更新了配置文件`conf/llm_factories.json`

#### 前端集成：

- ✅ 添加了`LLMFactory.LlamaCpp`枚举
- ✅ 更新了图标映射（使用现有的`llama-cpp.svg`）
- ✅ 添加到本地 LLM 工厂列表
- ✅ 配置了模型选项和文档链接

### 2. 在 RAGFlow 管理界面配置模型

现在您可以通过前端 UI 直接配置 Llama.cpp 模型：

1. **启动 RAGFlow 服务**
2. **登录管理界面**，进入 **用户设置** -> **模型管理**
3. **在"待添加模型"列表中找到"Llama.cpp"**
   - 您会看到带有 Llama.cpp 图标的卡片
   - 标签显示支持的模型类型：LLM, TEXT EMBEDDING
4. **点击"添加模型"按钮**
5. **在弹出的配置对话框中填写：**
   ```
   模型类型: embedding (选择 embedding 用于文档向量化)
   模型名称: llama-cpp-embedding (或自定义名称)
   API Base: http://192.168.3.124:8080
   API Key: dummy (任意值，llama.cpp 不验证 API key)
   ```
6. **点击确定保存配置**

### 3. 验证配置

运行集成测试脚本：

```bash
cd /path/to/ragflow
python test_llamacpp_integration.py
```

该脚本会验证：

- ✅ 配置文件完整性
- ✅ 后端代码集成
- ✅ 前端代码集成
- ✅ API 连接测试

### 4. 在知识库中使用

1. **创建或编辑知识库**
2. **在知识库配置中选择 embedding 模型**
   - 选择刚才配置的"<EMAIL>"
3. **上传文档进行测试**
   - 系统会自动使用您的 llama.cpp 服务进行向量化

## API 接口对比

### 您的 Llama.cpp API 格式：

**请求:**

```bash
curl --request POST \
  --url http://192.168.3.124:8080/embedding \
  --header "Content-Type: application/json" \
  --data '{"content": "从另一台主机发来的请求"}'
```

**响应:**

```json
[{"index":0,"embedding":[[-0.6760782599449158,1.340750813484192,...]]}]
```

### RAGFlow 适配器实现：

```python
class LlamaCppEmbed(Base):
    def encode(self, texts: list):
        embeddings = []
        for text in texts:
            response = requests.post(
                f"{self.base_url}/embedding",
                json={"content": text},
                headers={'Content-Type': 'application/json'}
            )
            result = response.json()
            embedding = result[0]["embedding"][0]
            embeddings.append(embedding)
        return np.array(embeddings), token_count
```

## 优势

1. **直接集成**: 无需修改 llama.cpp 服务，直接使用现有 API
2. **高性能**: 避免了额外的协议转换开销
3. **网络隔离**: 支持跨主机部署，满足您的架构需求
4. **兼容性**: 完全兼容 RAGFlow 的 embedding 接口规范

## 故障排除

### 常见问题

1. **连接超时**

   - 检查 192.168.3.123 到 192.168.3.124 的网络连通性
   - 确认防火墙设置允许 8080 端口访问

2. **响应格式错误**

   - 确认 llama.cpp 返回的 JSON 格式与预期一致
   - 检查 embedding 向量的维度

3. **性能问题**
   - 考虑调整批处理大小
   - 监控网络延迟和 llama.cpp 服务负载

### 调试命令

```bash
# 测试网络连通性
ping 192.168.3.124

# 测试API可用性
curl -X POST http://192.168.3.124:8080/embedding \
  -H "Content-Type: application/json" \
  -d '{"content": "test"}'

# 查看RAGFlow日志
docker logs ragflow-container-name
```

## 性能优化建议

1. **批处理优化**: 可以修改适配器支持批量请求以提高效率
2. **连接池**: 使用 requests.Session()复用连接
3. **缓存**: 对相同文本的 embedding 结果进行缓存
4. **负载均衡**: 如果需要，可以部署多个 llama.cpp 实例

## 总结

通过这个适配器，您可以：

- ✅ 直接使用 192.168.3.124 上的 llama.cpp embedding 服务
- ✅ 保持现有的网络架构不变
- ✅ 享受 RAGFlow 的完整功能
- ✅ 获得良好的性能表现

配置完成后，RAGFlow 将能够无缝调用您的 llama.cpp embedding 模型进行文档处理和检索。
