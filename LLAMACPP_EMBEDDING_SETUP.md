# RAGFlow集成Llama.cpp Embedding模型配置指南

## 概述

本指南介绍如何在RAGFlow中配置和使用部署在192.168.3.124主机上的llama.cpp embedding模型。

## 前提条件

- RAGFlow部署在192.168.3.123主机上
- Llama.cpp embedding服务部署在192.168.3.124:8080
- 两台主机之间网络连通

## 配置步骤

### 1. 代码修改已完成

已经为RAGFlow添加了`LlamaCppEmbed`适配器，支持直接调用您的llama.cpp embedding API。

### 2. 在RAGFlow管理界面配置模型

1. 登录RAGFlow管理界面
2. 进入 **设置** -> **模型管理** -> **Embedding模型**
3. 点击 **添加模型**
4. 填写以下配置：

```
模型提供商: Llama.cpp
模型名称: llama-cpp-embedding (或您喜欢的名称)
API Key: dummy (llama.cpp通常不需要真实的API key)
Base URL: http://192.168.3.124:8080
```

### 3. 测试配置

运行测试脚本验证配置：

```bash
cd /path/to/ragflow
python test_llamacpp_embedding.py
```

### 4. 在知识库中使用

1. 创建或编辑知识库
2. 在知识库设置中选择embedding模型为刚才配置的"llama-cpp-embedding"
3. 上传文档进行测试

## API接口对比

### 您的Llama.cpp API格式：

**请求:**
```bash
curl --request POST \
  --url http://192.168.3.124:8080/embedding \
  --header "Content-Type: application/json" \
  --data '{"content": "从另一台主机发来的请求"}'
```

**响应:**
```json
[{"index":0,"embedding":[[-0.6760782599449158,1.340750813484192,...]]}]
```

### RAGFlow适配器实现：

```python
class LlamaCppEmbed(Base):
    def encode(self, texts: list):
        embeddings = []
        for text in texts:
            response = requests.post(
                f"{self.base_url}/embedding",
                json={"content": text},
                headers={'Content-Type': 'application/json'}
            )
            result = response.json()
            embedding = result[0]["embedding"][0]
            embeddings.append(embedding)
        return np.array(embeddings), token_count
```

## 优势

1. **直接集成**: 无需修改llama.cpp服务，直接使用现有API
2. **高性能**: 避免了额外的协议转换开销
3. **网络隔离**: 支持跨主机部署，满足您的架构需求
4. **兼容性**: 完全兼容RAGFlow的embedding接口规范

## 故障排除

### 常见问题

1. **连接超时**
   - 检查192.168.3.123到192.168.3.124的网络连通性
   - 确认防火墙设置允许8080端口访问

2. **响应格式错误**
   - 确认llama.cpp返回的JSON格式与预期一致
   - 检查embedding向量的维度

3. **性能问题**
   - 考虑调整批处理大小
   - 监控网络延迟和llama.cpp服务负载

### 调试命令

```bash
# 测试网络连通性
ping 192.168.3.124

# 测试API可用性
curl -X POST http://192.168.3.124:8080/embedding \
  -H "Content-Type: application/json" \
  -d '{"content": "test"}'

# 查看RAGFlow日志
docker logs ragflow-container-name
```

## 性能优化建议

1. **批处理优化**: 可以修改适配器支持批量请求以提高效率
2. **连接池**: 使用requests.Session()复用连接
3. **缓存**: 对相同文本的embedding结果进行缓存
4. **负载均衡**: 如果需要，可以部署多个llama.cpp实例

## 总结

通过这个适配器，您可以：
- ✅ 直接使用192.168.3.124上的llama.cpp embedding服务
- ✅ 保持现有的网络架构不变
- ✅ 享受RAGFlow的完整功能
- ✅ 获得良好的性能表现

配置完成后，RAGFlow将能够无缝调用您的llama.cpp embedding模型进行文档处理和检索。
