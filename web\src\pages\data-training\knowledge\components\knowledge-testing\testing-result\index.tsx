import { ReactComponent as SelectedFilesCollapseIcon } from '@/assets/svg/selected-files-collapse.svg';
import Image from '@/components/image';
import { useTranslate } from '@/hooks/common-hooks';
import { ITestingChunk } from '@/interfaces/database/knowledge';
import {
  Badge,
  Card,
  Collapse,
  Empty,
  Flex,
  Pagination,
  PaginationProps,
  Popover,
  Space,
  Tooltip,
  Typography,
} from 'antd';
import camelCase from 'lodash/camelCase';
import SelectFiles from './select-files';

import {
  useSelectIsTestingSuccess,
  useSelectTestingResult,
} from '@/hooks/knowledge-hooks';
import { useGetPaginationWithRouter } from '@/hooks/logic-hooks';
import {
  BarChartOutlined,
  FileTextOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { useCallback, useState } from 'react';
import styles from './index.less';

const { Title, Text } = Typography;

const similarityList: Array<{
  field: keyof ITestingChunk;
  label: string;
  icon: React.ReactNode;
}> = [
  {
    field: 'similarity',
    label: 'Hybrid Similarity',
    icon: <BarChartOutlined />,
  },
  {
    field: 'term_similarity',
    label: 'Term Similarity',
    icon: <FileTextOutlined />,
  },
  {
    field: 'vector_similarity',
    label: 'Vector Similarity',
    icon: <SearchOutlined />,
  },
];

const ChunkTitle = ({ item }: { item: ITestingChunk }) => {
  const { t } = useTranslate('knowledgeDetails');
  return (
    <Flex gap={16} align="center">
      {similarityList.map((x) => (
        <Tooltip key={x.field} title={t(camelCase(x.field))}>
          <Badge
            count={
              <span className={styles.similarityCircle}>
                {((item[x.field] as number) * 100).toFixed(0)}%
              </span>
            }
            offset={[0, 0]}
          >
            <Text type="secondary">{x.icon}</Text>
          </Badge>
        </Tooltip>
      ))}
    </Flex>
  );
};

interface IProps {
  handleTesting: (documentIds?: string[]) => Promise<any>;
}

const TestingResult = ({ handleTesting }: IProps) => {
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  const { documents, chunks, total } = useSelectTestingResult();
  const { t } = useTranslate('knowledgeDetails');
  const { pagination, setPagination } = useGetPaginationWithRouter();
  const isSuccess = useSelectIsTestingSuccess();

  const onChange: PaginationProps['onChange'] = (pageNumber, pageSize) => {
    pagination.onChange?.(pageNumber, pageSize);
    handleTesting(selectedDocumentIds);
  };

  const onTesting = useCallback(
    (ids: string[]) => {
      setPagination({ page: 1 });
      handleTesting(ids);
    },
    [setPagination, handleTesting],
  );

  return (
    <section className={styles.testingResultWrapper}>
      <Collapse
        expandIcon={() => <SelectedFilesCollapseIcon />}
        className={styles.selectFilesCollapse}
        items={[
          {
            key: '1',
            label: (
              <Flex
                justify={'space-between'}
                align="center"
                className={styles.selectFilesTitle}
              >
                <Space>
                  <Badge
                    count={selectedDocumentIds?.length ?? 0}
                    color="var(--primary-color)"
                    showZero
                  />
                  <span>{t('filesSelected')}</span>
                  <Text type="secondary">({documents?.length ?? 0} total)</Text>
                </Space>
              </Flex>
            ),
            children: (
              <div>
                <SelectFiles
                  setSelectedDocumentIds={setSelectedDocumentIds}
                  handleTesting={onTesting}
                />
              </div>
            ),
          },
        ]}
      />

      <div className={styles.resultContainer}>
        <Title level={5} style={{ marginBottom: 16 }}>
          Testing Results
        </Title>

        {isSuccess && chunks.length > 0 ? (
          chunks?.map((x) => (
            <Card
              key={x.chunk_id}
              title={<ChunkTitle item={x} />}
              className={styles.chunkCard}
              size="small"
            >
              <Flex gap={16} align="flex-start">
                {x.img_id && (
                  <Popover
                    placement="left"
                    title="Image Preview"
                    content={
                      <Image id={x.img_id} className={styles.imagePreview} />
                    }
                  >
                    <div>
                      <Image id={x.img_id} className={styles.image} />
                    </div>
                  </Popover>
                )}
                <div style={{ flex: 1 }}>{x.content_with_weight}</div>
              </Flex>
            </Card>
          ))
        ) : isSuccess && chunks.length === 0 ? (
          <Empty
            description="No matches found. Try modifying your query or adjusting the similarity threshold."
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : null}
      </div>

      {total > 0 && (
        <Pagination
          {...pagination}
          size="small"
          total={total}
          onChange={onChange}
          showSizeChanger={false}
          style={{ textAlign: 'right', marginTop: 16 }}
        />
      )}
    </section>
  );
};

export default TestingResult;
