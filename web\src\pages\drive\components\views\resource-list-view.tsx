import { RenameDialog } from '@/components/rename-dialog';
import SvgIcon from '@/components/svg-icon';
import { UseRowSelectionType } from '@/hooks/logic-hooks/use-row-selection';
import { useFetchFileList } from '@/hooks/use-drive-request';
import { IFile } from '@/interfaces/database/file-manager';
import { formatFileSize } from '@/utils/common-util';
import { formatDate } from '@/utils/date';
import { getExtension } from '@/utils/document-util';
import { Flex, Table, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { EditTagsDialog } from '../../dialogs/edit-tags/edit-tags-dialog';
import { useEditTags } from '../../dialogs/edit-tags/use-edit-tags';
import { LinkToDatasetDialog } from '../../dialogs/link-to-dataset-dialog';
import { UseMoveDocumentShowType } from '../../features/file-operations/use-move-file';
import { useHandleConnectToKnowledge, useRenameCurrentFile } from '../../hooks';
import { useFilePreview } from '../../hooks/use-file-preview';
import { useResourceNavigation } from '../../hooks/use-resource-navigation';
import { isFolderType } from '../../utils';
import { ActionCell } from '../cards/action-cell';
import { TagsCell } from '../cells/tags-cell';
import { DriveFilePreview } from '../file-preview';

const { Text } = Typography;

type ResourceListViewProps = Pick<
  ReturnType<typeof useFetchFileList>,
  'files' | 'loading' | 'pagination' | 'setPagination' | 'total'
> &
  Pick<UseRowSelectionType, 'rowSelection' | 'setRowSelection'> &
  UseMoveDocumentShowType;

export function ResourceListView({
  files,
  loading,
  pagination,
  setPagination,
  total,
  rowSelection,
  setRowSelection,
  showMoveFileModal,
}: ResourceListViewProps) {
  const { t } = useTranslation('translation', {
    keyPrefix: 'fileManager',
  });
  const { navigateToFolder } = useResourceNavigation();

  // 文件预览功能
  const { filePreviewVisible, currentFile, showPreview, hidePreview } =
    useFilePreview();

  const {
    connectToKnowledgeVisible,
    hideConnectToKnowledgeModal,
    showConnectToKnowledgeModal,
    initialConnectedIds,
    onConnectToKnowledgeOk,
    connectToKnowledgeLoading,
  } = useHandleConnectToKnowledge();

  const {
    fileRenameVisible,
    showFileRenameModal,
    hideFileRenameModal,
    onFileRenameOk,
    initialFileName,
    fileRenameLoading,
  } = useRenameCurrentFile();

  const {
    editTagsVisible,
    editTagsLoading,
    currentFile: currentEditFile,
    showEditTagsModal,
    hideEditTagsModal,
    onEditTagsOk,
  } = useEditTags();

  const columns: ColumnsType<IFile> = useMemo(() => {
    return [
      {
        title: t('name'),
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        width: '40%',
        render: (text: string, record: IFile) => {
          const isFolder = isFolderType(record.type);
          const extension = isFolder ? 'folder' : getExtension(record.name);

          return (
            <Flex gap={'small'} align="center">
              <SvgIcon
                name={`file-icon/${extension}`}
                width={24}
                style={{ flexShrink: 0 }}
              />
              <Text
                ellipsis={{ tooltip: text }}
                style={{
                  color: isFolder ? '#1890ff' : undefined,
                  cursor: isFolder ? 'pointer' : 'default',
                }}
                onClick={() => {
                  if (isFolder) {
                    navigateToFolder(record.id);
                  }
                }}
              >
                {text}
              </Text>
            </Flex>
          );
        },
      },
      {
        title: t('size'),
        dataIndex: 'size',
        key: 'size',
        width: 100,
        sorter: true,
        render: (text: number, record: IFile) => {
          if (isFolderType(record.type)) return '';
          return formatFileSize(text);
        },
      },
      {
        title: '更新时间',
        dataIndex: 'update_time',
        key: 'update_time',
        width: 200,
        sorter: true,
        render: (text: string) => {
          return text ? formatDate(text) : '';
        },
      },
      {
        title: '标签',
        dataIndex: 'tags',
        key: 'tags',
        width: 200,
        render: (value: IFile['tags']) => {
          return <TagsCell value={value} />;
        },
      },
      {
        title: t('action'),
        key: 'action',
        width: 350,
        render: (_, record, index) => (
          <ActionCell
            row={{ original: record } as any}
            showConnectToKnowledgeModal={showConnectToKnowledgeModal}
            showFileRenameModal={showFileRenameModal}
            showEditTagsModal={showEditTagsModal}
            showMoveFileModal={showMoveFileModal}
            showPreview={showPreview}
          />
        ),
      },
    ];
  }, [
    t,
    navigateToFolder,
    showConnectToKnowledgeModal,
    showFileRenameModal,
    showEditTagsModal,
    showMoveFileModal,
    showPreview,
  ]);

  return (
    <>
      <Table
        dataSource={files}
        columns={columns}
        loading={loading}
        rowKey="id"
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
          onChange: (page, pageSize) => {
            setPagination({ page, pageSize });
          },
        }}
        rowSelection={{
          selectedRowKeys: Object.keys(rowSelection).filter(
            (key) => rowSelection[key],
          ),
          onChange: (selectedKeys) => {
            const newSelection: Record<string, boolean> = {};
            selectedKeys.forEach((key) => {
              newSelection[String(key)] = true;
            });
            setRowSelection(newSelection);
          },
          getCheckboxProps: (record) => ({
            disabled: record.source_type === 'knowledgebase',
          }),
        }}
      />

      {/* 文件预览对话框 */}
      <DriveFilePreview
        visible={filePreviewVisible}
        onClose={hidePreview}
        file={currentFile}
      />

      {connectToKnowledgeVisible && (
        <LinkToDatasetDialog
          hideModal={hideConnectToKnowledgeModal}
          initialConnectedIds={initialConnectedIds}
          onConnectToKnowledgeOk={onConnectToKnowledgeOk}
          loading={connectToKnowledgeLoading}
        />
      )}

      {editTagsVisible && (
        <EditTagsDialog
          visible={editTagsVisible}
          hideModal={hideEditTagsModal}
          onOk={onEditTagsOk}
          loading={editTagsLoading}
          file={currentEditFile}
        />
      )}

      {fileRenameVisible && (
        <RenameDialog
          hideModal={hideFileRenameModal}
          onOk={onFileRenameOk}
          initialName={initialFileName}
          loading={fileRenameLoading}
        />
      )}
    </>
  );
}
