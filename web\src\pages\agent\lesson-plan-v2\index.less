@import '@/theme/high-tech-theme.less';
@import '../lesson-plan/index.less';

.feedbackSection {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color-light);
}

.loadingState {
  text-align: center;
  padding: var(--spacing-xl);

  .statusInfo {
    font-size: 16px;
    font-weight: 500;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
  }

  .tip {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
}

/* 思考过程样式 */
.thinkingProcess {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #fafafa;
  border-left: 3px solid #e8e8e8;
  border-radius: 4px;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;

  &:empty {
    display: none;
  }
}

/* 正式内容容器 */
.formalContent {
  /* 继承现有的markdown样式 */
}

/* 结果内容区域布局 */
.resultContent {
  display: flex;
  gap: 16px;
  height: calc(100vh - 320px); /* 根据实际页面调整 */
  min-height: 500px;
}

/* 教案内容区域 */
.markdownSection {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
  border-right: 1px solid var(--border-color-light);
}

/* 推荐资源和反馈区域 */
.sourcesSection {
  width: 280px;
  overflow-x: hidden;
  overflow-y: auto;
  padding-left: 8px;
}
