import { ITenant, ITenantUser } from '@/interfaces/database/user-setting';
import {
  inviteTenantUser,
  listTenant,
  listTenantUser,
  registerAndAddTenantUser,
  searchUsers,
} from '@/services/user-service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { useTranslation } from 'react-i18next';

// 用户搜索结果接口
export interface ISearchUser {
  id: string;
  nickname: string;
  email: string;
  avatar?: string;
}

// 获取所有租户列表（基于当前用户的租户权限）
export const useListAllTenants = () => {
  const {
    data,
    isFetching: loading,
    refetch,
    error,
  } = useQuery<ITenant[]>({
    queryKey: ['listAllTenants'],
    initialData: [],
    gcTime: 0,
    queryFn: async () => {
      try {
        const { data } = await listTenant();
        return data?.data ?? [];
      } catch (error) {
        console.error('Error fetching tenants:', error);
        return [];
      }
    },
  });

  return { data, loading, refetch, error };
};

// 获取指定租户下的所有用户
export const useListTenantUsers = (tenantId?: string) => {
  const {
    data,
    isFetching: loading,
    refetch,
    error,
  } = useQuery<ITenantUser[]>({
    queryKey: ['listTenantUsers', tenantId],
    initialData: [],
    gcTime: 0,
    enabled: !!tenantId,
    queryFn: async () => {
      try {
        if (!tenantId) return [];
        const { data } = await listTenantUser(tenantId);
        return data?.data ?? [];
      } catch (error) {
        console.error(`Error fetching users for tenant ${tenantId}:`, error);
        return [];
      }
    },
  });

  return { data, loading, refetch, error };
};

// 搜索用户
export const useSearchUsers = (keyword?: string) => {
  const {
    data,
    isFetching: loading,
    refetch,
    error,
  } = useQuery<{ users: ISearchUser[]; total: number }>({
    queryKey: ['searchUsers', keyword],
    initialData: { users: [], total: 0 },
    gcTime: 0,
    enabled: !!keyword && keyword.length > 0,
    queryFn: async () => {
      try {
        if (!keyword) return { users: [], total: 0 };
        const { data } = await searchUsers({ keyword, page: 1, page_size: 20 });
        return data?.data ?? { users: [], total: 0 };
      } catch (error) {
        console.error('Error searching users:', error);
        return { users: [], total: 0 };
      }
    },
  });

  return { data, loading, refetch, error };
};

// 注册用户并添加到租户
export const useRegisterAndAddTenantUser = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
    error,
  } = useMutation({
    mutationKey: ['registerAndAddTenantUser'],
    mutationFn: async ({
      tenantId,
      userData,
    }: {
      tenantId: string;
      userData: { nickname: string; email: string; password: string };
    }) => {
      const { data } = await registerAndAddTenantUser(tenantId, userData);
      if (data.code === 0) {
        message.success(data.message || t('message.operated'));
        // 刷新用户列表
        queryClient.invalidateQueries({
          queryKey: ['listTenantUsers', tenantId],
        });
      }
      return data;
    },
  });

  return { data, loading, registerAndAddUser: mutateAsync, error };
};

// 邀请用户加入租户
export const useInviteTenantUser = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
    error,
  } = useMutation({
    mutationKey: ['inviteTenantUser'],
    mutationFn: async ({
      tenantId,
      email,
    }: {
      tenantId: string;
      email: string;
    }) => {
      const { data } = await inviteTenantUser(tenantId, email);
      if (data.code === 0) {
        message.success(t('message.operated'));
        // 刷新用户列表
        queryClient.invalidateQueries({
          queryKey: ['listTenantUsers', tenantId],
        });
      }
      return data;
    },
  });

  return { data, loading, inviteUser: mutateAsync, error };
};
