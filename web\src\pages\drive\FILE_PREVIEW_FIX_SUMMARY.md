# Drive File Preview Fix Summary

## 问题分析

原始的文件预览实现存在以下问题：

1. **认证问题**: 直接使用 `/api/drive/get/${fileId}` URL 在 `<img>`, `<iframe>`, `<video>` 等标签中无法正确处理身份验证
2. **Blob 响应处理**: Drive API 返回的是 blob 数据，需要特殊处理才能在浏览器中预览
3. **跨域和权限**: 浏览器无法直接访问需要认证的 API 端点

## 解决方案

采用"下载然后预览"的方式，类似于下载功能的实现：

### 核心改动

1. **使用 driveService.getFile()**: 通过已有的 service 方法获取文件 blob 数据
2. **创建 Blob URL**: 使用 `window.URL.createObjectURL()` 为 blob 数据创建临时 URL
3. **内存管理**: 正确管理 blob URL 的生命周期，避免内存泄漏

### 技术实现

#### 下载文件并创建 Blob URL

```typescript
const downloadFileBlob = async (fileId: string) => {
  try {
    setLoading(true);
    const { data } = await driveService.getFile(fileId);
    const url = window.URL.createObjectURL(data);
    setBlobUrl(url);
    return data;
  } catch (error) {
    console.error('Error downloading file:', error);
    message.error('无法下载文件进行预览');
    throw error;
  } finally {
    setLoading(false);
  }
};
```

#### 文档处理

- **Word 文档**: 直接使用 blob 数据，通过 `blob.arrayBuffer()` 获取 ArrayBuffer
- **Excel 文件**: 使用 blob 数据，避免额外的网络请求
- **文本文件**: 使用 `blob.text()` 直接获取文本内容

#### 内存管理

```typescript
// 清理函数
const resetState = () => {
  setContent(null);
  setPreviewType('unsupported');
  setLoading(false);
  if (blobUrl) {
    window.URL.revokeObjectURL(blobUrl);
    setBlobUrl('');
  }
};

// 组件卸载时清理
useEffect(() => {
  return () => {
    if (blobUrl) {
      window.URL.revokeObjectURL(blobUrl);
    }
  };
}, [blobUrl]);
```

## 优势

1. **安全性**: 通过已认证的 service 方法获取文件，确保权限控制
2. **兼容性**: 与现有下载功能使用相同的机制，确保一致性
3. **性能**: 一次下载，支持多种预览方式
4. **稳定性**: 正确处理错误和加载状态

## 文件修改

### web/src/pages/drive/components/file-preview/drive-file-preview.tsx

主要修改：

- 添加 `downloadFileBlob` 函数
- 更新文档处理函数使用 blob 数据
- 添加 blob URL 状态管理
- 更新 useEffect 逻辑
- 改进内存管理

## 测试要点

1. **各种文件格式**: 测试图片、PDF、Word、Excel、视频、文本文件
2. **权限验证**: 确保只有有权限的用户可以预览文件
3. **内存泄漏**: 验证 blob URL 正确释放
4. **错误处理**: 测试网络错误、文件不存在等情况
5. **加载状态**: 确认加载指示器正常显示

## 后续改进

1. **缓存机制**: 可考虑添加文件缓存，避免重复下载
2. **预加载**: 对小文件可实现预加载功能
3. **进度指示**: 为大文件下载添加进度指示器
4. **格式扩展**: 支持更多文件格式的预览

这个实现确保了文件预览功能的稳定性和安全性，解决了原始实现中的认证和 blob 处理问题。
