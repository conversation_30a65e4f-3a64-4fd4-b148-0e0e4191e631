from __future__ import annotations

import os
from celery import Celery


BROKER_URL = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")
BACKEND_URL = os.getenv("CELERY_BACKEND_URL", "redis://localhost:6379/1")

celery_app = Celery("ragflow", broker=BROKER_URL, backend=BACKEND_URL)
celery_app.conf.update(
    accept_content=["json"],
    task_serializer="json",
    result_serializer="json",
)
