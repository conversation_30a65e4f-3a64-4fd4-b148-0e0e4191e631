from __future__ import annotations

"""Prompt builder for Extrapolate agent."""

from typing import List

from .models import ExtrapolateContext


class ExtrapolatePromptBuilder:
    """Build markdown prompt that asks model to derive variants from sample questions."""

    def __init__(self, ctx: ExtrapolateContext):
        self.ctx = ctx

    def build(self) -> str:
        ctx = self.ctx
        parts: List[str] = [
            "你是一位擅长举一反三出题的专家教师，根据示例题目生成变式题。",
            "",
            "**生成要求：**",
            f"- 生成数量：{ctx.quantity} 题",
        ]

        # Question type
        if ctx.question_type_mode == "same":
            parts.append("- 题型与示例保持一致")
        else:
            parts.append(f"- 题型：{ctx.custom_question_type}")

        # Structure
        if ctx.structure_mode == "same":
            parts.append("- 结构与示例保持一致")
        else:
            parts.append(f"- 结构：{ctx.custom_structure}")

        # Knowledge points
        if ctx.knowledge_points_mode == "same_context":
            parts.append("- 保持相同知识点但改变情境")
        else:
            parts.append(f"- 知识点：{ctx.custom_knowledge_points}")

        # Difficulty
        diff_map = {
            "same": "与原题一致",
            "basic": "基础",
            "medium": "中等",
            "hard": "困难",
        }
        parts.append(f"- 难度：{diff_map.get(ctx.difficulty_mode, '与原题一致')}")

        parts.extend([
            "",
            "**示例试题：**",
            "```",
            ctx.sample_content.strip(),
            "```",
            "",
            "**输出要求：**",
            "1. 每题包含题干、选项（如适用）、正确答案、解析。",
            "2. 使用Markdown格式，题目之间用 `---` 分隔。",
            "3. 题号从1开始。",
            "",
            "请开始生成题目：",
        ])

        return "\n".join(parts)
