@import '@/theme/high-tech-theme.less';

.testingResultWrapper {
  height: 100%;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.resultHeader {
  margin-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: var(--spacing-md);

  .resultIcon {
    fill: #3b82f6;
  }

  .resultTitle {
    margin-bottom: 0;
    font-weight: 600;
    .gradient-text();
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .resultStats {
    margin-left: var(--spacing-md);
    font-size: 0.9rem;
  }
}

.resultContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding-right: var(--spacing-xs);
}

.chunkCard {
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
  background: var(--card-background);

  &:hover {
    border-color: var(--primary-color);
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
  }

  :global(.ant-card-head) {
    background: linear-gradient(
      135deg,
      var(--primary-ultralight) 0%,
      rgba(56, 189, 248, 0.05) 100%
    );
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    padding: var(--spacing-sm) var(--spacing-md);
    min-height: auto;
  }

  :global(.ant-card-head-title) {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0;
  }

  :global(.ant-card-body) {
    padding: var(--spacing-md);
    font-size: 0.9rem;
    line-height: 1.66;
    color: var(--text-primary);
  }
}

.similarityContainer {
  width: 100%;
}

.similarityItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.similarityValue {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 0.9rem;
}

.similarityLabel {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.2);
  }
}

.imagePreview {
  max-width: 300px;
  max-height: 400px;
  object-fit: contain;
  border-radius: var(--border-radius-md);
}

.emptyState {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  text-align: center;
  color: var(--text-color-secondary);
  height: 300px;

  .emptyIcon {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: var(--text-tertiary);
  }
}

.paginationContainer {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;

  :global(.ant-pagination) {
    .ant-pagination-item {
      border-color: var(--border-color);

      &:hover {
        border-color: var(--primary-color);
      }

      &.ant-pagination-item-active {
        background: var(--primary-color);
        border-color: var(--primary-color);
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      border-color: var(--border-color);

      &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }

    .ant-pagination-jump-prev,
    .ant-pagination-jump-next {
      color: var(--text-secondary);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .testingResultWrapper {
    padding: var(--spacing-md);
  }

  .chunkCard {
    margin-bottom: var(--spacing-sm);

    :global(.ant-card-body) {
      padding: var(--spacing-sm);
    }
  }

  .image {
    width: 40px;
    height: 40px;
  }

  .paginationContainer {
    justify-content: center;
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .testingResultWrapper {
    background-color: var(--neutral-800);

    .selectFilesCollapse {
      :global(.ant-collapse-header) {
        background-color: var(--neutral-700);
      }

      :global(.ant-collapse-content) {
        background-color: var(--neutral-800);
      }
    }

    .chunkCard {
      background-color: var(--neutral-800);

      :global(.ant-card-head) {
        background-color: var(--neutral-700);
      }
    }
  }
}
