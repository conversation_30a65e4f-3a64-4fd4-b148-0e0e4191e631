import { useLogin, useRegister } from '@/hooks/login-hooks';
import { rsaPsw } from '@/utils';
import { LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Form, Input } from 'antd';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'umi';

import styles from './index.less';

const Login = () => {
  const [title, setTitle] = useState('login');
  const navigate = useNavigate();
  const { login, loading: signLoading } = useLogin();
  const { register, loading: registerLoading } = useRegister();
  const { t } = useTranslation('translation', { keyPrefix: 'login' });
  const loading = signLoading || registerLoading;

  const changeTitle = () => {
    setTitle((title) => (title === 'login' ? 'register' : 'login'));
  };
  const [form] = Form.useForm();

  useEffect(() => {
    form.validateFields(['nickname']);
  }, [form]);

  const onCheck = async () => {
    try {
      const params = await form.validateFields();

      const rsaPassWord = rsaPsw(params.password) as string;

      if (title === 'login') {
        const code = await login({
          email: `${params.email}`.trim(),
          password: rsaPassWord,
        });
        if (code === 0) {
          navigate('/workbench');
        }
      } else {
        const code = await register({
          nickname: params.nickname,
          email: params.email,
          password: rsaPassWord,
        });
        if (code === 0) {
          setTitle('login');
        }
      }
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  };

  const navigateToRegister = () => {
    navigate('/register');
  };

  return (
    <div className={styles.loginPage}>
      <div className={styles.loginContainer}>
        <div className={styles.logoContainer}>
          <img src="/china-railways.svg" alt="Logo" className={styles.logo} />
          <h1 className={styles.platformTitle}>广州电务段职教大模型训练平台</h1>
        </div>

        <div className={styles.formContainer}>
          <Form form={form} layout="horizontal" name="dynamic_rule">
            <Form.Item
              name="email"
              className={styles.loginFormItem}
              initialValue=""
              rules={[{ required: true, message: t('emailPlaceholder') }]}
            >
              <Input
                size="large"
                type="email"
                placeholder={t('emailPlaceholder')}
                prefix={<UserOutlined className={styles.inputIcon} />}
              />
            </Form.Item>

            {title === 'register' && (
              <Form.Item
                name="nickname"
                className={styles.loginFormItem}
                rules={[{ required: true, message: t('nicknamePlaceholder') }]}
              >
                <Input size="large" placeholder={t('nicknamePlaceholder')} />
              </Form.Item>
            )}

            <Form.Item
              name="password"
              className={styles.loginFormItem}
              initialValue=""
              rules={[{ required: true, message: t('passwordPlaceholder') }]}
            >
              <Input.Password
                size="large"
                placeholder={t('passwordPlaceholder')}
                onPressEnter={onCheck}
                prefix={<LockOutlined className={styles.inputIcon} />}
              />
            </Form.Item>

            <Form.Item className={styles.loginButtonItem}>
              <Button
                type="primary"
                block
                size="large"
                onClick={onCheck}
                loading={loading}
                className={styles.loginButton}
              >
                登 录
              </Button>
            </Form.Item>

            <div className={styles.loginOptions}>
              <div className={styles.forgotPassword}>
                <span>忘记密码</span>
              </div>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;
