import { useTranslate } from '@/hooks/common-hooks';
import { ITestingChunk } from '@/interfaces/database/knowledge';
import { Badge, Flex, Tooltip, Typography } from 'antd';
import camelCase from 'lodash/camelCase';

import MessageItem from '@/components/message-item';
import { MessageType } from '@/constants/chat';
import {
  useFetchNextDialogList,
  useQueryTestCompletion,
  useQueryTestConversation,
} from '@/hooks/chat-hooks';
import { IMessage } from '@/pages/chat/interface';
import {
  BarChartOutlined,
  FileTextOutlined,
  MessageFilled,
  SearchOutlined,
  SmileOutlined,
} from '@ant-design/icons';
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';
import { v4 as uuid } from 'uuid';
import styles from './index.less';

const { Title, Text } = Typography;

const similarityList: Array<{
  field: keyof ITestingChunk;
  label: string;
  icon: React.ReactNode;
}> = [
  {
    field: 'similarity',
    label: 'Hybrid Similarity',
    icon: <BarChartOutlined />,
  },
  {
    field: 'term_similarity',
    label: 'Term Similarity',
    icon: <FileTextOutlined />,
  },
  {
    field: 'vector_similarity',
    label: 'Vector Similarity',
    icon: <SearchOutlined />,
  },
];

const ChunkTitle = ({ item }: { item: ITestingChunk }) => {
  const { t } = useTranslate('knowledgeDetails');
  return (
    <Flex gap={16} align="center">
      {similarityList.map((x) => (
        <Tooltip key={x.field} title={t(camelCase(x.field))}>
          <Badge
            count={
              <span className={styles.similarityCircle}>
                {((item[x.field] as number) * 100).toFixed(0)}%
              </span>
            }
            offset={[0, 0]}
          >
            <Text type="secondary">{x.icon}</Text>
          </Badge>
        </Tooltip>
      ))}
    </Flex>
  );
};

interface IProps {
  testQuestion: string;
  onTestStart?: () => void;
  onTestComplete?: () => void;
}

const QueryTestResult = ({
  testQuestion,
  onTestStart,
  onTestComplete,
}: IProps) => {
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [hasStartedTest, setHasStartedTest] = useState(false);

  const { data: dialogList } = useFetchNextDialogList(true); // 获取对话助手列表
  const { dialogId, conversationId, createConversation, setTestDialogId } =
    useQueryTestConversation();

  const { sendTestMessage, answer, done, stopOutputMessage } =
    useQueryTestCompletion();

  // 用于跟踪当前 assistant 占位消息的 id，便于后续替换为真实回答
  const placeholderIdRef = useRef<string>('');

  // Prevent duplicate sending in React 18 StrictMode (which mounts components twice in dev)
  const hasSentRef = useRef(false);

  // 初始化：设置第一个可用的对话助手
  useEffect(() => {
    if (dialogList && dialogList.length > 0 && !dialogId) {
      setTestDialogId(dialogList[0].id);
    }
  }, [dialogList, dialogId, setTestDialogId]);

  // 处理测试问题的发送
  const handleSendTestQuestion = useCallback(async () => {
    if (!testQuestion.trim() || !dialogId) return;

    setHasStartedTest(true);
    onTestStart?.();

    try {
      // 如果没有会话，先创建一个
      let currentConversationId = conversationId;
      if (!currentConversationId) {
        const conversation = await createConversation();
        if (!conversation) {
          console.error('Failed to create conversation');
          return;
        }
        currentConversationId = conversation.id;

        // 添加助手的开场白
        if (conversation.message && conversation.message.length > 0) {
          setMessages(
            conversation.message.map(
              (msg: any) =>
                ({
                  id: msg.id || uuid(),
                  role: msg.role,
                  content: msg.content,
                }) as IMessage,
            ),
          );
        }
      }

      // 添加用户问题到消息列表
      const userMessage: IMessage = {
        id: uuid(),
        role: MessageType.User,
        content: testQuestion,
      };

      // 创建 assistant 占位消息（空内容，稍后替换）
      const placeholderAssistant: IMessage = {
        id: uuid(),
        role: MessageType.Assistant,
        content: '',
      };

      placeholderIdRef.current = placeholderAssistant.id;

      setMessages((prev) => [...prev, userMessage, placeholderAssistant]);

      // 发送消息
      await sendTestMessage({
        conversationId: currentConversationId,
        question: testQuestion,
        messages: messages.concat(userMessage),
      });
    } catch (error) {
      console.error('Failed to send test message:', error);
      onTestComplete?.();
    }
  }, [
    testQuestion,
    dialogId,
    conversationId,
    createConversation,
    sendTestMessage,
    messages,
    onTestStart,
  ]);

  // 监听回答的变化
  useEffect(() => {
    if (answer && answer.answer) {
      setMessages((prev) => {
        // 尝试根据 answer.id 或占位 id 找到需要替换的 assistant 消息
        const targetIndex = prev.findIndex(
          (msg) =>
            msg.role === MessageType.Assistant &&
            (msg.id === answer.id || msg.id === placeholderIdRef.current),
        );

        const assistantMessage: IMessage = {
          id: answer.id || placeholderIdRef.current || uuid(),
          role: MessageType.Assistant,
          content: answer.answer,
          reference: answer.reference || { chunks: [], doc_aggs: [], total: 0 },
        };

        if (targetIndex >= 0) {
          // 更新现有占位/同 id 消息
          const newMessages = [...prev];
          newMessages[targetIndex] = assistantMessage;
          return newMessages;
        }

        // 如果没找到则追加
        return [...prev, assistantMessage];
      });
    }
  }, [answer]);

  // 监听完成状态
  useEffect(() => {
    if (done && hasStartedTest) {
      onTestComplete?.();
    }
  }, [done, hasStartedTest, onTestComplete]);

  // 当测试问题改变时，自动发送（防止严格模式下的重复调用）
  useEffect(() => {
    if (
      testQuestion &&
      testQuestion.trim() &&
      dialogId &&
      !hasStartedTest &&
      !hasSentRef.current
    ) {
      hasSentRef.current = true; // 标记已发送
      handleSendTestQuestion();
    }
  }, [testQuestion, dialogId, hasStartedTest, handleSendTestQuestion]);

  // 重置状态的函数
  const resetTest = useCallback(() => {
    setMessages([]);
    setHasStartedTest(false);
    stopOutputMessage();
    hasSentRef.current = false; // 重置发送标记
  }, [stopOutputMessage]);

  // 当测试问题变化时重置（使用 useLayoutEffect 以确保在发送请求之前执行）
  useLayoutEffect(() => {
    resetTest();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [testQuestion]);

  return (
    <section className={styles.testingResultWrapper}>
      <div className={styles.resultHeader}>
        <Flex justify="space-between" align="center">
          <Title level={4} className={styles.resultTitle}>
            <MessageFilled className={styles.resultIcon} />
            提问结果
          </Title>
          {messages.length > 0 && (
            <Text type="secondary" className={styles.resultStats}>
              {messages.length} 条消息
            </Text>
          )}
        </Flex>
      </div>

      <div className={styles.resultContainer}>
        {messages.length > 0 ? (
          <div className={styles.messagesContainer}>
            {messages.map((message, index) => (
              <MessageItem
                key={message.id}
                item={message}
                reference={
                  message.reference || { chunks: [], doc_aggs: [], total: 0 }
                }
                loading={
                  !done &&
                  index === messages.length - 1 &&
                  message.role === MessageType.Assistant
                }
                sendLoading={false}
                index={index}
              />
            ))}
          </div>
        ) : (
          <div className={styles.emptyState}>
            <SmileOutlined className={styles.emptyIcon} />
            <Text type="secondary">
              请在左侧输入测试文本并点击"开始测试"按钮进行提问
            </Text>
          </div>
        )}
      </div>
    </section>
  );
};

export default QueryTestResult;
