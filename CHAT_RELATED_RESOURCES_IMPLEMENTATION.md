# 聊天界面相关资源功能实现

## 功能概述

在聊天界面的右侧添加了「相关资源」列表，该功能会：

1. **动态搜索相关资源**：根据用户的最后一条提问内容，通过 `searchRelatedFiles` API 搜索相关文件
2. **汇聚聊天引用文档**：从聊天回复中的 `referenceDocumentList` 汇聚所有引用的文档
3. **固定位置显示**：相关资源列表位置固定，不随聊天内容滚动

## 技术实现

### 1. 组件结构

#### RelatedResources 组件 (`web/src/pages/chat/components/RelatedResources.tsx`)

**核心功能**：

- 接收用户查询文本和引用文档列表作为 props
- 使用 `searchRelatedFiles` API 搜索相关文件
- 合并并去重 API 搜索结果和聊天引用文档
- 提供刷新功能重新搜索

**主要特性**：

- 支持不同文件类型的图标显示（PDF、Word、Excel、PPT、图片、视频等）
- 显示匹配关键词和文件详细信息
- 区分资源来源（API 搜索 vs 聊天引用）
- 响应式加载状态和错误处理

### 2. 界面布局调整

#### ChatContainer 修改 (`web/src/pages/chat/chat-container/index.tsx`)

**新增功能**：

- 添加 `onUserQueryChange` 回调：当用户消息变化时通知父组件
- 添加 `onReferenceDocumentsChange` 回调：当引用文档变化时通知父组件
- 汇聚所有聊天回复中的引用文档（从 `conversation.reference` 和消息级别的 `reference`）

#### 主聊天页面修改 (`web/src/pages/chat/index.tsx`)

**布局更新**：

- 保持原有三列布局：左侧对话列表、中间聊天容器、右侧相关资源
- 添加状态管理用于存储当前用户查询和引用文档
- 处理 ChatContainer 的回调以更新相关资源组件的 props

### 3. 样式设计

#### 相关资源样式 (`web/src/pages/chat/components/RelatedResources.less`)

**设计特点**：

- 固定宽度 280px，与左侧对话列表保持视觉平衡
- 使用高科技主题的渐变色标题
- 卡片式资源展示，支持悬停效果
- 滚动条样式与整体主题一致
- 深色模式适配

#### 聊天容器样式调整

- 调整 `max-width` 以适应三列布局：`calc(100% - 540px)`
- 确保中间聊天区域有足够空间显示内容

### 4. 数据流

```
用户发送消息 → ChatContainer 检测到用户消息变化
→ 触发 onUserQueryChange 回调 → Chat 页面更新 currentUserQuery
→ RelatedResources 组件接收新查询 → 调用 searchRelatedFiles API
→ 显示搜索结果

聊天回复包含引用文档 → ChatContainer 汇聚引用文档
→ 触发 onReferenceDocumentsChange 回调 → Chat 页面更新 currentReferenceDocuments
→ RelatedResources 组件合并引用文档到资源列表
```

### 5. API 集成

**使用现有的 FileKeywordSearch Agent**：

- 端点：`/v1/agent/file_keyword_search/search`
- 参数：用户查询文本、LLM ID、最大关键词数、最大结果数等
- 返回：匹配的文件列表，包含文件基本信息和匹配关键词

### 6. 用户体验优化

**实时更新**：

- 用户发送新消息时自动搜索相关资源
- 聊天回复生成新的引用文档时自动更新列表

**加载状态**：

- 搜索过程中显示加载动画
- 搜索失败时显示错误信息和重试按钮

**交互反馈**：

- 资源卡片悬停效果
- 点击资源的处理（可扩展为打开文档预览等）

**空状态处理**：

- 无相关资源时显示友好的空状态提示

## 文件结构

```
web/src/pages/chat/
├── components/
│   ├── RelatedResources.tsx      # 相关资源组件
│   └── RelatedResources.less     # 相关资源样式
├── chat-container/
│   └── index.tsx                 # 聊天容器（已修改）
├── index.tsx                     # 主聊天页面（已修改）
└── index.less                    # 主聊天页面样式

web/src/services/
└── agent_service.ts              # API 服务（已包含 searchRelatedFiles）
```

## 后续扩展

1. **资源预览**：点击资源时可以打开文档预览
2. **资源筛选**：按文件类型、创建时间等筛选资源
3. **资源收藏**：用户可以收藏常用资源
4. **智能推荐**：基于用户历史行为优化推荐算法
5. **批量操作**：支持批量下载或分享资源

## 配置说明

- **默认 LLM ID**：当前使用 `qwen-plus`，可根据系统配置调整
- **搜索参数**：最大关键词数 5，最大结果数 10，可根据需要调整
- **布局尺寸**：左侧 260px，右侧 280px，可根据设计需求调整
