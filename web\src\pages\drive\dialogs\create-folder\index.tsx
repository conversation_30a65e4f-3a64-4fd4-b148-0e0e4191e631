import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LoadingButton } from '@/components/ui/loading-button';
import { IModalProps } from '@/interfaces/common';
import { TagRenameId } from '@/pages/add-knowledge/constant';
import { useTranslation } from 'react-i18next';
import '../../modal-styles.less';
import { CreateFolderForm } from './create-folder-form';

export function CreateFolderDialog({
  hideModal,
  onOk,
  loading,
}: IModalProps<any>) {
  const { t } = useTranslation();

  return (
    <Dialog open onOpenChange={hideModal}>
      <DialogContent className="ht-modal-content">
        <DialogHeader className="ht-modal-header">
          <DialogTitle className="ht-modal-title">
            {t('fileManager.newFolder')}
          </DialogTitle>
        </DialogHeader>
        <div className="ht-modal-body">
          <CreateFolderForm
            hideModal={hideModal}
            onOk={onOk}
          ></CreateFolderForm>
        </div>
        <DialogFooter className="ht-modal-footer">
          <LoadingButton
            type="submit"
            form={TagRenameId}
            loading={loading}
            className="ht-modal-btn primary"
          >
            {t('common.save')}
          </LoadingButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
