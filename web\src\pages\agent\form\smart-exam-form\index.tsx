import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  InputNumber as AntdInputNumber,
  Card,
  Col,
  Radio,
  Row,
  Select,
  Upload,
} from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { INextOperatorForm } from '../../interface';
import styles from './index.less';

// Custom InputNumber component that is compatible with the form
const InputNumber = ({ value, onChange, ...props }: any) => {
  return <AntdInputNumber value={value} onChange={onChange} {...props} />;
};

const { Option } = Select;

interface SmartExamFormProps extends INextOperatorForm {
  uploadProps?: any;
}

const SmartExamForm = ({ form, uploadProps }: SmartExamFormProps) => {
  const { t } = useTranslation();
  const [questionMethod, setQuestionMethod] = useState<'random' | 'by_type'>(
    'random',
  );

  // Handle switching between random and by-type question generation
  const handleQuestionMethodChange = (e: any) => {
    const method = e.target.value as 'random' | 'by_type';
    setQuestionMethod(method);

    if (method === 'random') {
      // Reset by-type counts when switching to random
      form.setValue('single_choice_count', 0);
      form.setValue('multiple_choice_count', 0);
      form.setValue('fill_blank_count', 0);
      form.setValue('true_false_count', 0);
      form.setValue('short_answer_count', 0);
      form.setValue('ordering_count', 0);
    } else {
      // Reset random count when switching to by-type
      form.setValue('question_count', 0);
    }
  };

  return (
    <Form {...form}>
      <form
        className="space-y-6"
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        {/* 名称 - Name */}
        <FormField
          control={form.control}
          name="exam_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('smartExam.nameTip')}>
                {t('smartExam.name', '名称')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t(
                    'smartExam.namePlaceholder',
                    '如：铁路信号工安全操作规程',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 知识点 - Knowledge Points */}
        <FormField
          control={form.control}
          name="knowledge_points"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('smartExam.knowledgePointsTip')}>
                {t('smartExam.knowledgePoints', '知识点')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder={t(
                    'smartExam.knowledgePointsPlaceholder',
                    '如：道岔转辙机，LKJ',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 出题方式选择器 - Question Method Selector */}
        <FormItem>
          <FormLabel>出题方式</FormLabel>
          <div className={styles.questionMethodSelector}>
            <Radio.Group
              value={questionMethod}
              onChange={handleQuestionMethodChange}
              className={styles.radioGroup}
            >
              <Radio value="random" className={styles.radioOption}>
                随机出题
              </Radio>
              <Radio value="by_type" className={styles.radioOption}>
                按题型出题
              </Radio>
            </Radio.Group>
          </div>
        </FormItem>

        {/* 随机出题数量 - Random Questions Count */}
        {questionMethod === 'random' && (
          <FormField
            control={form.control}
            name="question_count"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('smartExam.questionCount', '试题数量')}
                </FormLabel>
                <FormControl>
                  <InputNumber
                    value={field.value}
                    onChange={field.onChange}
                    min={5}
                    max={1000}
                    className={styles.fullWidthSelect}
                    placeholder="5~1000的数值"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* 按题型出题 - Questions by Type */}
        {questionMethod === 'by_type' && (
          <div className={styles.questionTypeSection}>
            <Card className={styles.questionTypeCard}>
              <Row gutter={[16, 16]}>
                {/* 单选题 - Single Choice */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="single_choice_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('smartExam.singleChoice', '单选题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 多选题 - Multiple Choice */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="multiple_choice_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('smartExam.multipleChoice', '多选题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 填空题 - Fill in the Blank */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="fill_blank_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('smartExam.fillBlank', '填空题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 判断题 - True/False */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="true_false_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('smartExam.trueFalse', '判断题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 简答题 - Short Answer */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="short_answer_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('smartExam.shortAnswer', '简答题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 排序题 - Ordering */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="ordering_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('smartExam.ordering', '排序题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>
              </Row>
            </Card>
          </div>
        )}

        {/* 岗位级别 - Position Level */}
        <FormField
          control={form.control}
          name="position_level"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('smartExam.positionLevelTip')}>
                {t('smartExam.positionLevel', '岗位级别')}
              </FormLabel>
              <FormControl>
                <Select
                  value={field.value}
                  onChange={field.onChange}
                  mode="tags"
                  placeholder={t(
                    'smartExam.positionLevelPlaceholder',
                    '请选择岗位级别，如：初级工/中级工/高级工/技师/高级技师/学标考标',
                  )}
                  className={styles.fullWidthSelect}
                >
                  <Option value="junior_worker">
                    {t('smartExam.juniorWorker', '初级工')}
                  </Option>
                  <Option value="intermediate_worker">
                    {t('smartExam.intermediateWorker', '中级工')}
                  </Option>
                  <Option value="senior_worker">
                    {t('smartExam.seniorWorker', '高级工')}
                  </Option>
                  <Option value="technician">
                    {t('smartExam.technician', '技师')}
                  </Option>
                  <Option value="senior_technician">
                    {t('smartExam.seniorTechnician', '高级技师')}
                  </Option>
                  <Option value="standard_exam">
                    {t('smartExam.standardExam', '学标考标')}
                  </Option>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 难度 - Difficulty */}
        <FormField
          control={form.control}
          name="difficulty"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('smartExam.difficultyTip')}>
                {t('smartExam.difficulty', '难度')}
              </FormLabel>
              <FormControl>
                <Select
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="难度等级"
                  className={styles.fullWidthSelect}
                >
                  <Option value="low">低</Option>
                  <Option value="medium">中</Option>
                  <Option value="high">高</Option>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 上传试题文件 - Upload Question File */}
        <FormField
          control={form.control}
          name="question_bank_file"
          render={({ field }) => (
            <FormItem>
              <FormLabel>上传试题文件</FormLabel>
              <FormControl>
                <div className={styles.fileUploadSection}>
                  <Upload.Dragger
                    {...uploadProps}
                    accept=".xlsx,.xls,.csv"
                    maxCount={1}
                    className="upload-dragger-custom"
                    style={{
                      border: 'none',
                      background: 'transparent',
                      padding: 0,
                      margin: 0,
                      width: '100%',
                      height: 'auto',
                    }}
                  >
                    <div className={styles.uploadBox}>
                      <div className={styles.uploadPlaceholder}>
                        <div className={styles.uploadIcon}></div>
                        <p className={styles.dragText}>
                          将文件拖拽到此处，或
                          <span className={styles.browseText}>点击上传</span>
                        </p>
                        <p className={styles.supportedFormats}>
                          支持 .xlsx .xls .csv 格式，单文件不超过 20MB
                        </p>
                      </div>
                    </div>
                  </Upload.Dragger>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
};

export default SmartExamForm;
