import { useTranslate } from '@/hooks/common-hooks';
import {
  useChunkIsTesting,
  useFetchDefaultDocumentListForRetrieval,
} from '@/hooks/knowledge-hooks';
import { IDocumentInfo } from '@/interfaces/database/document';
import {
  EditOutlined,
  FileTextOutlined,
  SendOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Form,
  Input,
  List,
  Space,
  Spin,
  Typography,
  message,
} from 'antd';
import { FormInstance } from 'antd/lib';

import styles from './index.less';

type FieldType = {
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  question: string;
};

interface IProps {
  form: FormInstance;
  handleTesting: (documentIds?: string[]) => Promise<any>;
  selectedDocumentIds: string[];
  setSelectedDocumentIds: (ids: string[]) => void;
}

const { Text } = Typography;

const TestingControl = ({
  form,
  handleTesting,
  selectedDocumentIds,
  setSelectedDocumentIds,
}: IProps) => {
  const question = Form.useWatch('question', { form, preserve: true });
  const loading = useChunkIsTesting();
  const { t } = useTranslate('knowledgeDetails');

  // 获取默认知识库的文档列表
  const { documents, loading: documentsLoading } =
    useFetchDefaultDocumentListForRetrieval();

  const buttonDisabled =
    !question ||
    (typeof question === 'string' && question.trim() === '') ||
    selectedDocumentIds.length === 0;

  const handleTestingClick = async () => {
    if (selectedDocumentIds.length === 0) {
      message.warning('请先选择要测试的文档');
      return;
    }
    await handleTesting(selectedDocumentIds);
  };

  // 处理单个文档选择
  const handleDocumentSelect = (docId: string, checked: boolean) => {
    if (checked) {
      setSelectedDocumentIds([...selectedDocumentIds, docId]);
    } else {
      setSelectedDocumentIds(selectedDocumentIds.filter((id) => id !== docId));
    }
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDocumentIds(documents.map((doc) => doc.id));
    } else {
      setSelectedDocumentIds([]);
    }
  };

  const isAllSelected =
    documents.length > 0 && selectedDocumentIds.length === documents.length;
  const isIndeterminate =
    selectedDocumentIds.length > 0 &&
    selectedDocumentIds.length < documents.length;

  return (
    <div className={styles.testingControlWrapper}>
      <Form
        name="testing"
        layout="vertical"
        form={form}
        initialValues={{
          similarity_threshold: 0.7,
          vector_similarity_weight: 0.5,
        }}
      >
        {/* 权重设置面板已移除，但初始值仍保留在表单中 */}

        {/* 文档选择部分 */}
        <Card
          size="small"
          className={styles.sectionCard}
          title={
            <div className={styles.sectionHeader}>
              <FileTextOutlined className={styles.sectionIcon} />
              <span>知识库文档</span>
            </div>
          }
        >
          <div className={styles.documentSection}>
            {documentsLoading ? (
              <div className={styles.loadingContainer}>
                <Spin size="small" />
                <Text type="secondary">加载文档列表中...</Text>
              </div>
            ) : documents.length > 0 ? (
              <div className={styles.documentList}>
                <div className={styles.selectAllContainer}>
                  <Checkbox
                    indeterminate={isIndeterminate}
                    checked={isAllSelected}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    <Text strong>
                      全选 ({selectedDocumentIds.length}/{documents.length})
                    </Text>
                  </Checkbox>
                </div>
                <List
                  size="small"
                  className={styles.documentListContent}
                  dataSource={documents}
                  renderItem={(doc: IDocumentInfo) => (
                    <List.Item key={doc.id} className={styles.documentItem}>
                      <Checkbox
                        checked={selectedDocumentIds.includes(doc.id)}
                        onChange={(e) =>
                          handleDocumentSelect(doc.id, e.target.checked)
                        }
                      >
                        <Space direction="vertical" size={0}>
                          <Text
                            ellipsis={{ tooltip: doc.name }}
                            className={styles.documentName}
                          >
                            {doc.name}
                          </Text>
                          <Text
                            type="secondary"
                            className={styles.documentInfo}
                          >
                            Chunks: {doc.chunk_num || 0} | Status: {doc.run}
                          </Text>
                        </Space>
                      </Checkbox>
                    </List.Item>
                  )}
                />
              </div>
            ) : (
              <div className={styles.emptyDocuments}>
                <Text type="secondary">暂无可选择的文档</Text>
              </div>
            )}
          </div>
        </Card>

        {/* 测试文本输入部分 */}
        <Card
          size="small"
          className={styles.sectionCard}
          title={
            <div className={styles.sectionHeader}>
              <EditOutlined className={styles.sectionIcon} />
              <span>测试文本</span>
            </div>
          }
        >
          <div className={styles.testingSection}>
            <Form.Item<FieldType>
              name={'question'}
              rules={[{ required: true, message: t('testTextPlaceholder') }]}
            >
              <Input.TextArea
                placeholder={t('testTextPlaceholder')}
                autoSize={{ minRows: 5, maxRows: 12 }}
                className={styles.testTextarea}
              />
            </Form.Item>

            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleTestingClick}
              disabled={buttonDisabled}
              loading={loading}
              className={styles.testButton}
              block
            >
              {t('testingLabel')}
            </Button>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default TestingControl;
