# Railway Electrical Signalling – Lesson-Plan Generation Agent

## 1 背景与目标

本 Agent 面向铁路电务职业教育领域，帮助企业培训讲师自动生成符合内部教学标准的**培训教案**（Lesson Plan）。目标：

1. 提升教案编写效率 ≥ 80 %。
2. 保证技术准确性与安全合规。
3. 提供低代码/可视化工作流，方便后期扩展。

---

## 2 整体架构

```
┌────────┐          WebSocket/SSE      ┌────────────┐
│ 前端 UI │  ──────────────────────▶  │ StreamViewer│
└────────┘                            └────────────┘
     │  表单 POST                        ▲
     ▼                                  │
┌─────────────────────────┐             │
│   FastAPI  Controller   │─────────────┘
├─────────────────────────┤  Canvas.run(stream=True)
│ CanvasBuilder  │ TaskSvc │───────────────────▶  Retrieval / Generate / Answer 组件
└─────────────────────────┘
```

- **Vector DB** (Qdrant/Milvus) 存储教案模板、规程、案例等 chunk。
- **LLM Service** (OpenAI GPT-4o 或私有 Qwen/Mistral) 负责正文生成与自校对。
- **Redis** 缓存任务状态，支持断线重连。
- **对象存储** (MinIO/Azure Blob) 保存生成后的 Markdown / 引用。

---

## 3 Canvas DSL（MVP）

```json
{
  "components": {
    "begin": {
      "obj": {
        "component_name": "Begin",
        "params": {
          "query": [
            { "key": "lesson_title", "name": "课题名称" },
            { "key": "lesson_content", "name": "授课内容" },
            { "key": "teaching_goal", "name": "教学目标" }
          ]
        }
      },
      "downstream": ["retrieval_0"],
      "upstream": []
    },
    "retrieval_0": {
      "obj": {
        "component_name": "Retrieval",
        "params": {
          "kb_ids": ["<TEMPLATE_KB_ID>"],
          "query": [{ "value": "{begin@lesson_content}" }],
          "top_n": 10,
          "similarity_threshold": 0.25
        }
      },
      "downstream": ["generate_0"],
      "upstream": ["begin"]
    },
    "generate_0": {
      "obj": {
        "component_name": "Generate",
        "params": {
          "llm_id": "gpt-4o@OpenAI",
          "prompt": "{prompt.lesson_plan}",
          "temperature": 0.7,
          "cite": true
        }
      },
      "downstream": ["answer_0"],
      "upstream": ["retrieval_0"]
    },
    "answer_0": { "obj": { "component_name": "Answer", "params": {} }, "downstream": [], "upstream": ["generate_0"] }
  }
}
```

- Prompt 模板 `{prompt.lesson_plan}` 见"Prompt 设计"章节。

---

## 4 Prompt 设计（示例）

```
你是一名资深铁路电务培训讲师，请根据下列信息编写完整教案：
- 课题：{begin@lesson_title}
- 授课目标：{begin@teaching_goal}
- 课程要点：{begin@lesson_content}

下面是参考模板片段：
{retrieval_0}

=== 输出要求 ===
1. 使用 Markdown 二级标题分块（导入 / 讲授 / 实践 / 总结 / 评价）。
2. 每段不少于 3 句话，必要时插入表格或示意图说明。
3. 最后列出 "扩展阅读" 列表。
```

---

## 5 落地路线图

| 里程碑         | 时间       | 关键交付物                                                |
| -------------- | ---------- | --------------------------------------------------------- |
| M0 需求澄清    | Week 0     | 教案样例库、字段字典、PRD                                 |
| M1 核心 MVP    | Week 3-5   | 前端表单 + 流式预览<br/>固定 Canvas 运行<br/>生成结果保存 |
| M2 强化 & 扩展 | Week 6-8   | Redis 任务持久化<br/>权限/RBAC、版本历史、监控指标        |
| M3 可视化编排  | Week 9-11  | Flow-Builder 拖拽 Canvas<br/>Prompt 可视编辑              |
| M4 多模型插件  | Week 12-14 | Tool-Call 插件、LoRA 微调、一键热更新                     |

---

## 6 健壮性设计

1. **超时/重试**：LLM 调用 `tenacity` 包包裹（3 次指数退避）；整体任务 120 s 硬超时。
2. **内存/Token 防护**：
   - Retrieval 限制 Top N 与 `MAX_CONTENT_LEN`；
   - Prompt 长度截断，超长加入 `[...]` 提示。
3. **异常语义化**：所有组件异常包装为 `{type, code, msg}`，前端统一弹窗。
4. **监控**：Prometheus counter (token_used)、histogram (duration)；OpenTelemetry trace Canvas 路径。

---

## 7 第一步（M1 Kick-off）

- [ ] **数据**：整理 ≥ 10 份电务教案，编写 chunk-builder 脚本并入库。
- [ ] **后端 POC**：实现 `CanvasBuilder` + 简易 `TaskService`，在命令行运行 `canvas.run(stream=True)` 验证流。
- [ ] **前端 POC**：`LessonForm` 组件 + `useLessonStream` hook；Markdown 实时渲染。
- [ ] **演示 & 审核**：SME 验证准确率，业务经理确认版式。

> 完成以上即可向管理层 Demo，并进入 M2 阶段。

---

## 8 成功指标（首季）

- 日活讲师 ≥ 20 人
- 保存教案转换率 ≥ 40 %
- SME 质量评分 ≥ 4/5
- P95 生成耗时 ≤ 45 s；失败率 < 1 %

---

_编写日期：{{ date }}_
