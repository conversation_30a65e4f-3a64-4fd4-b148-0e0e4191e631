───────────────────────────────────────  
Lesson-Plan-V2  最终实现方案  
───────────────────────────────────────

目录  
A. 角色与技术栈  
B. 工作流拓扑（DAG）  
C. 后端 API & SSE 事件规范  
D. 文件上传与临时知识库处理  
E. LessonPlanV2 组件核心逻辑  
F. 推荐资源检索并行化  
G. 会话 & 持久化数据模型  
H. 前端工作流 (lesson-plan-v2)  
I. 监控 / 安全 / 灰度

───────────────────────────────────────  
A. 角色与技术栈  
• 服务框架：FastAPI + Uvicorn + async-pg / peewee + Redis  
• 工作流编排：沿用 `agent.canvas.Canvas`（DSL-DAG），可平滑插入/替换组件  
• 异步任务：Celery 或 FastAPI 等后台 Task（embedding / 文件删除）  
• SSE：`text/event-stream`（FastAPI StreamingResponse）  
• LLM：deepseek-r1:32b@Ollama（chat_streamly）  
• Embedding：text-embedding-ada-002（示例）

───────────────────────────────────────  
B. 工作流拓扑（简化）

```
Begin
 └─▶ FileProcessor      (解析手输 or 文件内容; 生成清洗后文本)
     ├─▶ TemplateRetrieval (从 tenant-lesson-plan-template KB 抽样)
     │     └─▶ LessonPlanV2 (综合用户参数+模板+课程内容)
     │         └─▶ Answer  (流式输出教案)
     └─▶ ResourceSearch   (并行：检索推荐资源)
```

• **Begin** 挂载表单字段 (`class_type`,`class_hours`,…)  
• **FileProcessor**  
 – 手输内容：直接返回 DataFrame  
 – 文件：读取上传解析结果（见 §D）  
• **TemplateRetrieval**：检索 top-k 模板片段，供 LessonPlanV2 prompt  
• **LessonPlanV2**：Generate 子类，支持「迭代优化」模式  
• **ResourceSearch**：FileKeywordSearch + DriveService.get_by_pf_id

───────────────────────────────────────  
C. API & SSE 事件

1. POST `/api/agent/lesson-plan-v2/start`  
   RequestBody =

   ```json
   {
     "course_name": "...",
     "class_type": "讲授",
     "class_hours": 40,
     "target_audience": "...",
     "course_content_type": "manual|file",
     "course_content": "文本|file_id"
   }
   ```

   Response = `StreamingResponse`，每条 SSE 格式：

   ```
   event:<event_type>\n
   data:<json>\n\n
   ```

2. PUT `/api/agent/lesson-plan-v2/{session_id}/chat`  
   – body: { "feedback": "...", "satisfied": false }  
   – 第一次「满意/不满意」交互后进入下一轮 Canvas.run(stream=True)。

事件类型枚举（包括新增）

| event               | 典型 data 字段                                     |
| ------------------- | -------------------------------------------------- |
| init                | { "session_id": "..."}                             |
| waiting_embedding   | { "msg": "文件向量化中..." }                       |
| retrieving_template | { "msg": "模板检索中..." }                         |
| generating          | { "msg": "教案已开始生成..." }                     |
| stream              | { "chunk": "markdown partial ..." }                |
| recommendations     | { "resources": [ {id,name,matched_keyword,...} ] } |
| done                | { "doc_id": "...", "prompt_tokens":1234 }          |
| ask_feedback        | { "msg": "是否满意？" }                            |
| error               | { "detail": "..." }                                |

───────────────────────────────────────  
D. 文件上传 & 临时知识库

1. 前端选文件 ⇒ 直接调用 `/api/document/upload_and_parse`（已存在逻辑）
2. 后端流程
   ```python
   kb_id = ensure_user_kb(user_id)             # 不存在即创建
   doc_id = KnowledgebaseService.add_doc(kb_id,file)
   task_id = celery_embed.delay(kb_id, doc_id) # 异步 embedding
   session.jobs["embed_task"] = task_id
   ```
3. SSE `waiting_embedding` 持续轮询任务状态；完成后 Canvas 进入下一步。
4. 教案生成结束后，后台任务 `cleanup_kb_doc.delay(kb_id, doc_id, ttl=2h)` 删除文档并回收磁盘。

───────────────────────────────────────  
E. LessonPlanV2 组件关键点

• 输入参数：  
 `course_name, class_type, class_hours, target_audience, content_text, template_text`  
• Prompt 雏形：

```
system: 你是一位资深教学设计师...
user: <<template_text>>
      课程名称: {course_name}
      ...
      课程内容: {content_text}
      <<其它要求>>
```

• Stream 调用

```python
for delta in chat_mdl.chat_streamly(sys, msgs, {"max_tokens":16384,"yield_every_n_tokens":20}):
    yield {"event":"stream","data":delta}
```

• 多轮优化  
 – 判断 `history` 中是否有 `feedback`; prompt 追加  
 “## 用户反馈\n{feedback}\n## 请重新优化教案，保留原结构”

───────────────────────────────────────  
F. 推荐资源检索 ResourceSearch

• 从 `content_text` 提取关键词 → DriveService/Fuzzy 查询 → 取 top-N  
• 运行于与 LessonPlanV2 同层并行，结果写入 Canvas reference；  
• 推送 SSE `event:recommendations`

───────────────────────────────────────  
G. 数据模型

```sql
CREATE TABLE lesson_plan_sessions (
  id UUID PRIMARY KEY,
  user_id VARCHAR,
  version SMALLINT,        -- 2
  dsl JSONB,
  status VARCHAR,          -- running/done/errored
  iteration_count SMALLINT DEFAULT 0,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

CREATE TABLE lesson_plan_docs (
  id UUID PRIMARY KEY,
  session_id UUID,
  version SMALLINT,
  content_md TEXT,
  satisfied BOOLEAN,
  source_files JSONB,
  created_at TIMESTAMP
);
```

───────────────────────────────────────  
H. 前端 lesson-plan-v2

1. 表单 + 文件选择后：
   ```ts
   const resp = await fetch('/start', { body: formData })
   const sse = new EventSource(resp.url)
   sse.onmessage = (e) => handle(e.event, JSON.parse(e.data))
   ```
2. State Machine
   ```
   init -> waiting_embedding -> generating(stream...)
   -> done -> ask_feedback -> {satisfied|feedback}
   ```
3. recommendations 事件插入侧边栏 `RecommendedResources` 组件
4. 点击 “保存为 Word” ⇒ `/export/docx?doc_id=...`

───────────────────────────────────────  
I. 监控 / 安全 / 灰度

• Prometheus：embedding_time, generate_latency, tokens_used, sse_disconnects  
• Sentry：组件异常、文件解析失败  
• 速率：user 每小时 ≤3 份教案  
• 防御：上传文件大小 < 20 MB；mimetype 白名单；ClamAV 扫描  
• 灰度：HTTP Header `X-Agent-Version:2` 或用户白名单；v2 与 v1 会话表拆分

───────────────────────────────────────  
至此，Lesson-Plan-V2 的完整链路、接口、DAG、事件协议及运维配套均已闭环，可直接进入迭代开发。
