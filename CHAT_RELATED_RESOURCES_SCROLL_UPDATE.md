# 聊天界面相关资源滚动更新功能

## 功能概述

实现了右侧「相关资源」列表随着聊天容器滚动位置动态更新的功能。当用户滚动聊天记录时，相关资源列表会根据当前可见的问答内容实时更新，显示与当前可见消息相关的资源。

## 核心功能

### 1. 滚动监听机制

- **实时检测**：监听聊天容器的滚动事件
- **节流处理**：使用 200ms 节流避免频繁更新
- **视口中心定位**：找到最接近视口中心的消息作为当前焦点

### 2. 可见消息识别

- **元素引用管理**：为每条消息创建 DOM 引用
- **距离计算**：计算消息中心点与视口中心的距离
- **可见性检查**：确保消息在视口范围内

### 3. 上下文关联

- **用户消息关联**：根据可见的助手消息找到对应的用户问题
- **引用文档提取**：获取与当前可见消息相关的引用文档
- **智能后备**：当没有特定上下文时，使用最新的用户消息

## 技术实现

### 1. ChatContainer 滚动监听

```typescript
// 节流的滚动处理函数
const handleScroll = useCallback(
  throttle(() => {
    const container = messageContainerRef.current
    if (!container || messageRefs.current.size === 0) return

    const containerRect = container.getBoundingClientRect()
    const containerCenter = containerRect.top + containerRect.height / 2

    let closestMessageId = ''
    let minDistance = Infinity

    // 遍历所有消息元素，找到最接近视口中心的消息
    messageRefs.current.forEach((element, messageId) => {
      const elementRect = element.getBoundingClientRect()
      const elementCenter = elementRect.top + elementRect.height / 2
      const distance = Math.abs(elementCenter - containerCenter)

      // 确保元素在视口内
      const isVisible = elementRect.bottom > containerRect.top && elementRect.top < containerRect.bottom

      if (isVisible && distance < minDistance) {
        minDistance = distance
        closestMessageId = messageId
      }
    })

    if (closestMessageId && closestMessageId !== currentVisibleMessageId) {
      setCurrentVisibleMessageId(closestMessageId)
    }
  }, 200),
  [currentVisibleMessageId]
)
```

### 2. 消息引用管理

```typescript
// 设置消息元素的引用
const setMessageRef = useCallback((messageId: string, element: HTMLDivElement | null) => {
  if (element) {
    messageRefs.current.set(messageId, element);
  } else {
    messageRefs.current.delete(messageId);
  }
}, []);

// 在渲染时为每条消息设置引用
<div
  key={messageId}
  ref={(el) => setMessageRef(messageId, el)}
  className={currentVisibleMessageId === messageId ? styles.visibleMessage : ''}
>
```

### 3. 上下文数据提取

```typescript
// 根据当前可见的消息ID获取对应的用户消息
const currentVisibleUserMessage = useMemo(() => {
  if (!currentVisibleMessageId || !derivedMessages) return null

  const currentIndex = derivedMessages.findIndex((msg) => buildMessageUuidWithRole(msg) === currentVisibleMessageId)

  if (currentIndex === -1) return null

  // 如果当前可见的是助手消息，找到对应的用户消息
  if (derivedMessages[currentIndex].role === MessageType.Assistant) {
    // 向前查找最近的用户消息
    for (let i = currentIndex - 1; i >= 0; i--) {
      if (derivedMessages[i].role === MessageType.User) {
        return derivedMessages[i]
      }
    }
  } else if (derivedMessages[currentIndex].role === MessageType.User) {
    // 如果当前可见的就是用户消息，直接返回
    return derivedMessages[currentIndex]
  }

  return null
}, [currentVisibleMessageId, derivedMessages])
```

### 4. RelatedResources 增强显示

#### 当前问答指示器

```typescript
{
  userQuery && (
    <div className={styles.currentContext}>
      <div className={styles.contextHeader}>
        <InfoCircleOutlined className={styles.contextIcon} />
        <Text className={styles.contextLabel}>当前问答</Text>
      </div>
      <Tooltip title={userQuery}>
        <Text className={styles.contextText}>{truncateText(userQuery, 60)}</Text>
      </Tooltip>
      <div className={styles.resourceStats}>
        {resourceCounts.apiCount > 0 && <span className={styles.statItem}>搜索匹配: {resourceCounts.apiCount}</span>}
        {resourceCounts.referenceCount > 0 && (
          <span className={styles.statItem}>聊天引用: {resourceCounts.referenceCount}</span>
        )}
      </div>
    </div>
  )
}
```

## 视觉反馈

### 1. 当前可见消息标识

- **左侧边框**：为当前可见的消息添加渐变色左边框
- **平滑过渡**：使用 CSS 过渡效果实现流畅的视觉变化

```less
.visibleMessage {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(180deg, var(--primary-color) 0%, #38bdf8 100%);
    border-radius: 2px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
  }
}
```

### 2. 相关资源上下文信息

- **问答摘要**：显示当前问答的截取文本
- **资源统计**：分类显示 API 搜索和聊天引用的资源数量
- **智能提示**：根据状态显示不同的空状态提示

## 用户体验优化

### 1. 性能优化

- **节流处理**：滚动事件使用 200ms 节流，避免过度计算
- **DOM 引用缓存**：使用 Map 缓存消息元素引用，避免重复查找
- **条件更新**：只在可见消息真正发生变化时才触发更新

### 2. 交互体验

- **实时响应**：滚动时即时更新相关资源
- **上下文明确**：显示当前问答内容，用户能清楚知道资源对应的问题
- **平滑过渡**：所有视觉变化都有过渡效果

### 3. 边界处理

- **初始状态**：页面加载时默认显示最后一条消息的资源
- **空状态处理**：没有用户查询时显示友好提示
- **错误恢复**：搜索失败时提供重试选项

## 数据流更新

```
用户滚动聊天记录 → 触发节流的滚动处理函数
→ 计算最接近视口中心的消息 → 更新 currentVisibleMessageId
→ 根据可见消息提取用户问题和引用文档
→ 触发 onUserQueryChange 和 onReferenceDocumentsChange 回调
→ Chat 页面更新状态 → RelatedResources 组件接收新数据
→ 自动搜索相关资源并更新显示
```

## 配置参数

- **节流延迟**：200ms（可调整以平衡性能和响应速度）
- **文本截取长度**：60 字符（在 UI 中显示的问题文本长度）
- **搜索参数**：与之前保持一致（最大关键词 5 个，最大结果 10 个）

## 后续优化建议

1. **智能预加载**：可以预加载相邻消息的相关资源
2. **缓存机制**：为已搜索的查询建立本地缓存
3. **用户偏好**：允许用户选择是否启用滚动更新功能
4. **性能监控**：添加性能指标监控，优化滚动响应速度

这个实现确保了相关资源列表能够智能地跟随用户的阅读位置，提供与当前查看内容最相关的资源推荐。
