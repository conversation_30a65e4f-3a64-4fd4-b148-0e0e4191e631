import { useSetModalState } from '@/hooks/common-hooks';
import { IFile } from '@/interfaces/database/file-manager';
import { useCallback, useState } from 'react';

export interface DriveFilePreviewFile {
  name: string;
  type: string;
  location: string;
  size: number;
}

export const useFilePreview = () => {
  const {
    visible: filePreviewVisible,
    hideModal: hideFilePreviewModal,
    showModal: showFilePreviewModal,
  } = useSetModalState();

  const [currentFile, setCurrentFile] = useState<DriveFilePreviewFile | null>(
    null,
  );

  const showPreview = useCallback(
    (file: IFile) => {
      // 转换IFile到FilePreview需要的格式
      const previewFile: DriveFilePreviewFile = {
        name: file.name,
        type: file.type,
        location: file.id, // 使用文件ID作为location，用于API调用
        size: file.size || 0,
      };

      setCurrentFile(previewFile);
      showFilePreviewModal();
    },
    [showFilePreviewModal],
  );

  const hidePreview = useCallback(() => {
    setCurrentFile(null);
    hideFilePreviewModal();
  }, [hideFilePreviewModal]);

  return {
    filePreviewVisible,
    currentFile,
    showPreview,
    hidePreview,
  };
};

export type UseFilePreviewReturnType = ReturnType<typeof useFilePreview>;
