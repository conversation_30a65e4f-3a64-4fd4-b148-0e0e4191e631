import {
  TENANT_ROOT_FOLDER_NAME,
  YONTENET_DRIVE_FOLDER_NAME,
} from '@/constants/drive';
import { useFetchParentFolderList } from '@/hooks/use-drive-request';
import { useCallback } from 'react';
import { useSearchParams } from 'umi';

export const useResourceNavigation = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const getCurrentFolderId = useCallback(() => {
    return searchParams.get('folderId') || '';
  }, [searchParams]);

  const navigateToFolder = useCallback(
    (folderId: string) => {
      const newSearchParams = new URLSearchParams(searchParams);
      if (folderId) {
        newSearchParams.set('folderId', folderId);
      } else {
        newSearchParams.delete('folderId');
      }
      setSearchParams(newSearchParams);
    },
    [searchParams, setSearchParams],
  );

  const navigateToRoot = useCallback(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete('folderId');
    setSearchParams(newSearchParams);
  }, [searchParams, setSearchParams]);

  return {
    currentFolderId: getCurrentFolderId(),
    navigateToFolder,
    navigateToRoot,
  };
};

export const useResourceBreadcrumb = () => {
  const { currentFolderId } = useResourceNavigation();
  const parentFolderList = useFetchParentFolderList();

  const breadcrumbItems =
    parentFolderList.length <= 1
      ? []
      : parentFolderList
          .filter(
            (folder) =>
              folder.name !== TENANT_ROOT_FOLDER_NAME &&
              folder.name !== YONTENET_DRIVE_FOLDER_NAME,
          )
          .map((folder) => ({
            title: folder.name,
            folderId: folder.id,
          }));

  return {
    breadcrumbItems,
    currentFolderId,
  };
};
