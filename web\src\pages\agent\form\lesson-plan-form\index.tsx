import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { InputNumber as AntdInputNumber, Radio, Upload } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { INextOperatorForm } from '../../interface';
import styles from './index.less';

// Custom InputNumber component that is compatible with the form
const InputNumber = ({ value, onChange, ...props }: any) => {
  return <AntdInputNumber value={value} onChange={onChange} {...props} />;
};

interface LessonPlanFormProps extends INextOperatorForm {
  uploadProps: any;
}

const LessonPlanForm = ({ form, uploadProps }: LessonPlanFormProps) => {
  const { t } = useTranslation();
  const [contentInputMethod, setContentInputMethod] = useState<'text' | 'file'>(
    'text',
  );

  // 当切换输入方式时，清空相应字段
  const handleContentMethodChange = (e: any) => {
    const method = e.target.value;
    setContentInputMethod(method);

    if (method === 'text') {
      // 选择文本输入时，清空文件字段
      form.setValue('course_file', undefined);
      // 重置文件列表
      if (uploadProps.onRemove) {
        uploadProps.onRemove();
      }
    } else {
      // 选择文件上传时，清空文本字段
      form.setValue('course_content', '');
    }
  };

  return (
    <Form {...form}>
      <form
        className="space-y-6"
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        {/* 课程名称 - Course Name */}
        <FormField
          control={form.control}
          name="course_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('lessonPlan.courseNameTip')}>
                {t('lessonPlan.courseName', '课程名称')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'lessonPlan.courseNamePlaceholder',
                    '请输入课程名称，如：铁路信号工安全操作规程',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 课型 - Lesson Type */}
        <FormField
          control={form.control}
          name="lesson_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('lessonPlan.lessonTypeTip')}>
                {t('lessonPlan.lessonType', '课型')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'lessonPlan.lessonTypePlaceholder',
                    '请输入课型，如：新授课、复习课、习题课等',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 课时 - Lesson Duration */}
        <FormField
          control={form.control}
          name="lesson_duration"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('lessonPlan.lessonDurationTip')}>
                {t('lessonPlan.lessonDuration', '课时')} (分钟)
              </FormLabel>
              <FormControl>
                <InputNumber
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="请输入课时时长"
                  min={1}
                  max={300}
                  className={styles.fullWidthSelect}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 受众群体 - Target Audience */}
        <FormField
          control={form.control}
          name="target_audience"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('lessonPlan.targetAudienceTip')}>
                {t('lessonPlan.targetAudience', '受众群体')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'lessonPlan.targetAudiencePlaceholder',
                    '请描述受众群体，如：铁路电务信号工',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 课程内容输入方式选择 */}
        <FormItem>
          <FormLabel>课程内容输入方式</FormLabel>
          <div className={styles.contentMethodSelector}>
            <Radio.Group
              value={contentInputMethod}
              onChange={handleContentMethodChange}
              className={styles.radioGroup}
            >
              <Radio value="text" className={styles.radioOption}>
                手动输入课程内容
              </Radio>
              <Radio value="file" className={styles.radioOption}>
                上传课程材料文件
              </Radio>
            </Radio.Group>
          </div>
        </FormItem>

        {/* 课程内容 - Course Content (仅在选择文本输入时显示) */}
        {contentInputMethod === 'text' && (
          <FormField
            control={form.control}
            name="course_content"
            render={({ field }) => (
              <FormItem>
                <FormLabel tooltip={t('lessonPlan.courseContentTip')}>
                  {t('lessonPlan.courseContent', '课程内容或者提供内容简介')}
                </FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    className="bg-white"
                    placeholder={t(
                      'lessonPlan.courseContentPlaceholder',
                      '请输入或粘贴课程内容...',
                    )}
                    rows={6}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* 文件上传区域 - File Upload Area (仅在选择文件上传时显示) */}
        {contentInputMethod === 'file' && (
          <FormField
            control={form.control}
            name="course_file"
            render={({ field }) => (
              <FormItem>
                <FormLabel>课程材料</FormLabel>
                <FormControl>
                  <div className={styles.fileUploadSection}>
                    <div className={styles.uploadSection}>
                      <p className={styles.uploadInfo}>上传类型：word、txt</p>
                      <Upload.Dragger
                        {...uploadProps}
                        accept=".doc,.docx,.txt"
                        maxCount={1}
                        className="upload-dragger-custom"
                        style={{
                          border: 'none',
                          background: 'transparent',
                          padding: 0,
                          margin: 0,
                          width: '100%',
                          height: 'auto',
                        }}
                      >
                        <div className={styles.uploadBox}>
                          <div className={styles.uploadPlaceholder}>
                            <div className={styles.uploadIcon}></div>
                            <p className={styles.dragText}>
                              将文件拖拽到此处，或
                              <span className={styles.browseText}>
                                点击上传
                              </span>
                            </p>
                            <p className={styles.supportedFormats}>
                              支持上传 .doc .docx .txt 格式，单文件大小不超过2MB
                            </p>
                          </div>
                        </div>
                      </Upload.Dragger>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* 其他要求 - Other Requirements */}
        <FormField
          control={form.control}
          name="other_requirements"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('lessonPlan.otherRequirementsTip')}>
                {t('lessonPlan.otherRequirements', '其他要求')}
              </FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'lessonPlan.otherRequirementsPlaceholder',
                    '请输入其他要求（可选）...',
                  )}
                  rows={4}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
};

export default LessonPlanForm;
export type { LessonPlanFormProps };
