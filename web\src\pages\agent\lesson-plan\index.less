@import '@/theme/high-tech-theme.less';

.lessonPlanContainer {
  width: 100%;
}

.pageHeader {
  margin-bottom: var(--spacing-lg);

  .backButton {
    margin-bottom: var(--spacing-sm);
    padding: 0;
    display: flex;
    align-items: center;
    color: var(--text-secondary);

    &:hover {
      color: var(--primary-color);
    }
  }

  .pageTitle {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);

    .titleIcon {
      font-size: 28px;
      margin-right: var(--spacing-sm);
      color: var(--primary-color);
    }

    h3 {
      margin: 0;
      font-weight: 600;
      background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

  .pageDescription {
    color: var(--text-secondary);
    font-size: 1rem;
    max-width: 600px;
  }
}

.formCard {
  height: 100%;
  margin-bottom: var(--spacing-md);
  .high-tech-card();

  :global(.ant-card-head) {
    border-bottom: 1px solid var(--border-color-light);
    padding: var(--spacing-md) var(--spacing-lg);
  }

  :global(.ant-card-body) {
    padding: var(--spacing-lg);
  }
}

.formActions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);

  :global(.ant-btn-primary:not(:disabled):not(.ant-btn-disabled):hover) {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #faad14 100%);
    box-shadow:
      0 8px 32px 0 rgba(var(--primary-color-rgb), 0.6),
      0 0 20px 0 rgba(var(--primary-color-rgb), 0.4),
      0 0 40px 0 rgba(var(--accent-color-rgb), 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);

    // 启用发光边框
    &::before {
      opacity: 1;
      animation: borderGlow 2s ease-in-out infinite alternate;
    }

    // 添加发光文字效果
    color: #ffffff;
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.8),
      0 0 10px rgba(var(--primary-color-rgb), 0.6),
      0 0 15px rgba(var(--accent-color-rgb), 0.4);
  }

  .generateButton {
    min-width: 180px;
    height: 48px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: var(--border-radius-md);
    background: linear-gradient(
      90deg,
      var(--primary-color) 0%,
      var(--accent-color) 100%
    );
    border: none;
    box-shadow: 0 4px 14px 0 rgba(var(--primary-color-rgb), 0.4);
    transition: all var(--transition-normal);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px 0 rgba(var(--primary-color-rgb), 0.5);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

.resultCard {
  height: 100%;
  .high-tech-card();

  :global(.ant-card-head) {
    border-bottom: 1px solid var(--border-color-light);
    padding: var(--spacing-md) var(--spacing-lg);
  }

  :global(.ant-card-body) {
    padding: var(--spacing-lg);
    min-height: 500px;
    max-height: 700px;
    overflow-y: auto;
  }
}

.resultContent {
  display: flex;
  gap: var(--spacing-lg);
  height: 100%;
  overflow-x: hidden;

  .markdownSection {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */

    pre {
      font-family: var(--font-family);
      white-space: pre-wrap;
      word-wrap: break-word;
      line-height: 1.66;
      color: var(--text-primary);
      margin: 0;
      padding: 0;
    }
  }

  .sourcesSection {
    width: 200px;
    flex-shrink: 0;
    border-left: 1px solid var(--border-color);
    padding-left: var(--spacing-lg);
  }

  // 响应式布局：在小屏幕上垂直排列
  @media (max-width: 1200px) {
    flex-direction: column;

    .sourcesSection {
      width: 100%;
      border-left: none;
      border-top: 1px solid var(--border-color);
      padding-left: 0;
      padding-top: var(--spacing-lg);
      margin-top: var(--spacing-lg);

      .sourcesContainer {
        :global(.sourceCardGrid) {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: var(--spacing-md);
        }
      }
    }
  }
}

.emptyResult {
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  .placeholder {
    text-align: center;
    color: var(--text-tertiary);

    .placeholderIcon {
      font-size: 48px;
      margin-bottom: var(--spacing-md);
      display: block;
      opacity: 0.5;
    }

    p {
      font-size: 1rem;
      max-width: 240px;
      margin: 0 auto;
    }
  }
}
