@import '@/theme/high-tech-theme.less';

.recommendedResources {
  width: 200px;

  .sectionTitle {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: 14px;
    font-weight: 600;
    background: linear-gradient(
      90deg,
      var(--primary-color) 0%,
      var(--accent-color) 100%
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .resourceList {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .resourceCard {
    width: 200px;
    height: 120px;
    background: linear-gradient(
      135deg,
      var(--background-secondary) 0%,
      var(--background-tertiary) 100%
    );
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
    position: relative;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(
        90deg,
        var(--primary-color),
        var(--accent-color)
      );
      opacity: 0;
      transition: opacity var(--transition-normal);
    }

    &:hover {
      transform: translateY(-2px);
      border-color: var(--primary-color);
      box-shadow: var(--card-hover-shadow);

      &::before {
        opacity: 1;
      }

      .fileIcon {
        transform: scale(1.1);
      }

      .fileName {
        color: var(--primary-color);
      }
    }

    :global(.ant-card-body) {
      padding: var(--spacing-md) !important;
      height: 100%;
    }

    .fileIcon {
      font-size: 18px;
      transition: all var(--transition-normal);
    }

    .contentSection {
      flex: 1;
      min-width: 0;
    }

    .fileName {
      font-size: var(--font-size-base);
      font-weight: 600;
      color: var(--text-primary);
      line-height: 1.4;
      margin-bottom: 2px;
      display: block;
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      transition: color var(--transition-normal);
    }

    .keyword {
      font-size: var(--font-size-sm);
      color: var(--neutral-500);
      margin-top: 2px;
    }

    .details {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      margin-top: var(--spacing-xs);
      margin-bottom: 0;
    }
  }

  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl) var(--spacing-md);
    text-align: center;
    color: var(--text-tertiary);

    .emptyIcon {
      font-size: 32px;
      margin-bottom: var(--spacing-sm);
      opacity: 0.5;
    }

    .emptyText {
      font-size: var(--font-size-sm);
      color: var(--text-tertiary);
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .recommendedResources {
    width: 100%;

    .resourceList {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: var(--spacing-md);
    }
  }
}
