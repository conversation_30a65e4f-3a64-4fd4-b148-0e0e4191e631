from __future__ import annotations

"""Domain & parameter models for AI Question generation."""

from dataclasses import dataclass, field
from typing import Optional

from pydantic import BaseModel, Field, root_validator

from agent.common_exam.enums import Difficulty, Question<PERSON>eth<PERSON>, QuestionDistribution

# ------------------------------------------------------------------
# Context DTO (pure dataclass – for prompt builder)
# ------------------------------------------------------------------


@dataclass
class AIQuestionContext:
    knowledge_points: str
    objective: str | None = None
    exam_scene: str | None = None
    difficulty: Difficulty = Difficulty.MEDIUM
    question_method: QuestionMethod = QuestionMethod.RANDOM
    distribution: QuestionDistribution = field(
        default_factory=QuestionDistribution)

    # Retrieved contents
    template_content: str = ""
    user_kb_content: str = ""
    exam_kb_content: str = ""

    tenant_id: str | None = None
    user_id: str | None = None

    def total_questions(self) -> int:
        if self.question_method == QuestionMethod.RANDOM:
            return self.distribution.total or 10
        return self.distribution.total


# ------------------------------------------------------------------
# Pydantic parameter – external API layer
# ------------------------------------------------------------------


class AIQuestionParam(BaseModel):
    """Validated parameters for AI Question generator (replacement of legacy ComponentParam)."""

    knowledge_points: str = Field("", description="内容及知识点 (必填或退化后填)")
    exam_scene: str = ""
    objective: str = ""

    difficulty: Difficulty = Field(Difficulty.MEDIUM)
    llm_id: str = Field("deepseek-r1:32b@Ollama")

    question_method: QuestionMethod = Field(QuestionMethod.RANDOM)
    question_count: int = Field(10, ge=1)

    # by_type counts
    single_choice_count: int = Field(0, ge=0)
    multiple_choice_count: int = Field(0, ge=0)
    fill_blank_count: int = Field(0, ge=0)
    true_false_count: int = Field(0, ge=0)
    short_answer_count: int = Field(0, ge=0)
    ordering_count: int = Field(0, ge=0)

    # KB config
    exam_kb_id: Optional[str] = None
    user_kb_ids: list[str] = []
    exam_kb_top_n: int = Field(10, ge=1)
    other_kb_top_n: int = Field(10, ge=1)
    similarity_threshold: float = Field(0.2, ge=0.0, le=1.0)

    stream: bool = True

    class Config:
        validate_assignment = True
        use_enum_values = True

    # ------------------ validators ------------------

    @root_validator(skip_on_failure=True)
    def _fallback_knowledge_points(cls, values):
        if not values.get("knowledge_points"):
            for key in ("exam_scene", "objective"):
                if values.get(key):
                    values["knowledge_points"] = values[key]
                    break
        if not values.get("knowledge_points"):
            raise ValueError(
                "knowledge_points / exam_scene / objective must provide at least one non-empty value")
        return values

    @root_validator(skip_on_failure=True)
    def _check_counts(cls, values):
        method = values.get("question_method")
        if method == QuestionMethod.RANDOM:
            if values.get("question_count", 0) <= 0:
                raise ValueError(
                    "question_count must be > 0 when method is random")
        else:
            total = sum(values.get(f, 0) for f in [
                "single_choice_count",
                "multiple_choice_count",
                "fill_blank_count",
                "true_false_count",
                "short_answer_count",
                "ordering_count",
            ])
            if total <= 0:
                raise ValueError(
                    "At least one question type count must be > 0 when method is by_type")
        return values
