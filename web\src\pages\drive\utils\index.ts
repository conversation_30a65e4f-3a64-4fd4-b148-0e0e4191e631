export function isFolderType(type: string) {
  return type === 'folder';
}

export function isSupportedPreviewType(
  fileName: string,
  type: string,
): boolean {
  const lowerName = fileName.toLowerCase();
  const lowerType = type.toLowerCase();

  // 支持的文件类型
  const supportedExtensions = [
    // 图片
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.webp',
    // 文档
    '.pdf',
    '.doc',
    '.docx',
    '.txt',
    '.md',
    // 表格
    '.xls',
    '.xlsx',
    // 视频
    '.mp4',
    '.avi',
    '.mov',
    '.webm',
  ];

  // 支持的type类型
  const supportedTypes = ['visual', 'pdf', 'document'];

  // 检查文件扩展名
  const hasValidExtension = supportedExtensions.some((ext) =>
    lowerName.endsWith(ext),
  );

  // 检查文件类型
  const hasValidType = supportedTypes.includes(lowerType);

  return hasValidExtension || hasValidType;
}
