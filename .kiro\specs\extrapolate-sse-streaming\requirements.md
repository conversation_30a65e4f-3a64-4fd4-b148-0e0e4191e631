# Requirements Document

## Introduction

The ExtrapolateService (举一反三) currently has a basic streaming implementation but lacks the robust Server-Sent Events (SSE) streaming capability found in LessonPlanV2Service. This feature will refactor the ExtrapolateService to implement true streaming SSE output that provides real-time feedback to users during question generation, following the proven patterns established in LessonPlanV2Service.

## Requirements

### Requirement 1

**User Story:** As a user generating extrapolated questions, I want to see real-time streaming output during generation, so that I can monitor progress and receive immediate feedback.

#### Acceptance Criteria

1. WHEN a user submits an extrapolate request THEN the system SHALL return streaming SSE events with incremental content
2. WHEN content is being generated THEN the system SHALL send stream events at regular intervals (1-second buffer)
3. WHEN generation is complete THEN the system SHALL send a 'done' event to signal completion
4. WHEN an error occurs during generation THEN the system SHALL send an 'error' event with details

### Requirement 2

**User Story:** As a user uploading sample files for extrapolation, I want to see embedding progress feedback, so that I understand the system is processing my file.

#### Acceptance Criteria

1. WHEN a user uploads a sample file THEN the system SHALL provide embedding progress events
2. WHEN file embedding is in progress THEN the system SHALL send 'waiting_embedding' events with status messages
3. WHEN embedding completes THEN the system SHALL proceed to generation without user intervention
4. IF embedding times out THEN the system SHALL continue with generation using available content

### Requirement 3

**User Story:** As a developer maintaining the system, I want consistent SSE streaming patterns across all services, so that the codebase is maintainable and predictable.

#### Acceptance Criteria

1. WHEN implementing SSE streaming THEN the system SHALL use the same SSEStreamBuffer pattern as LessonPlanV2Service
2. WHEN handling Last-Event-ID headers THEN the system SHALL support SSE reconnection properly
3. WHEN processing streaming chunks THEN the system SHALL filter out think blocks like other services
4. WHEN generating events THEN the system SHALL follow the same event naming conventions (init, stream, done, error)

### Requirement 4

**User Story:** As a user with slow network connections, I want SSE reconnection support, so that I can resume streaming from where it left off if my connection drops.

#### Acceptance Criteria

1. WHEN a client sends Last-Event-ID header THEN the system SHALL resume streaming from that event ID
2. WHEN reconnecting THEN the system SHALL not duplicate previously sent events
3. WHEN event IDs are invalid THEN the system SHALL default to starting from the beginning
4. WHEN streaming resumes THEN the system SHALL maintain content continuity

### Requirement 5

**User Story:** As a system administrator, I want proper error handling and logging in streaming operations, so that I can troubleshoot issues effectively.

#### Acceptance Criteria

1. WHEN streaming errors occur THEN the system SHALL log detailed error information
2. WHEN file upload fails THEN the system SHALL return appropriate error responses before streaming starts
3. WHEN LLM generation fails THEN the system SHALL send error events through SSE
4. WHEN cleanup operations fail THEN the system SHALL log warnings without affecting user experience

### Requirement 6

**User Story:** As a user generating questions, I want the same file upload and cleanup behavior as lesson plans, so that I have a consistent experience across features.

#### Acceptance Criteria

1. WHEN uploading files THEN the system SHALL use the same upload context pattern as LessonPlanV2Service
2. WHEN files are processed THEN the system SHALL schedule automatic cleanup after 2 hours
3. WHEN generation completes THEN the system SHALL maintain cleanup job references for potential cancellation
4. WHEN cleanup executes THEN the system SHALL remove temporary knowledge base documents

### Requirement 7

**User Story:** As a frontend developer, I want consistent SSE event formats, so that I can reuse existing streaming UI components.

#### Acceptance Criteria

1. WHEN sending stream events THEN the system SHALL use the same JSON data format as other services
2. WHEN sending status events THEN the system SHALL include appropriate message fields
3. WHEN sending completion events THEN the system SHALL follow the established event sequence pattern
4. WHEN sending error events THEN the system SHALL include detail fields for error messages
