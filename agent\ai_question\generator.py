from __future__ import annotations

"""LLM generation wrapper for AI Question."""

import logging
from typing import Generator

from api.utils.errors import redact
from .models import AIQuestionContext
from .prompt_builder import AIQuestionPromptBuilder


class AIQuestionGenerator:
    """Generate AI questions using upstream LLM bundle."""

    def __init__(self, llm_bundle, stream: bool = True):
        self.llm_bundle = llm_bundle
        self.stream = stream

    # --------------------------------------------------
    def build_prompt(self, ctx: AIQuestionContext) -> str:
        prompt = AIQuestionPromptBuilder(ctx).build()
        logging.debug("[AIQuestionGenerator] prompt=%s", redact(prompt))
        return prompt

    # --------------------------------------------------
    def stream_generate(self, ctx: AIQuestionContext, llm_config: dict | None = None) -> Generator[str, None, None]:
        msg = self.build_prompt(ctx)
        llm_config = llm_config or {
            "max_tokens": 20480, "temperature": 0.3, "stream": True}

        if self.stream and hasattr(self.llm_bundle, "chat_streamly"):
            last = ""
            for chunk in self.llm_bundle.chat_streamly(msg, [], llm_config):
                if isinstance(chunk, int):
                    continue
                if not isinstance(chunk, str):
                    chunk = str(chunk)
                delta = chunk[len(last):]
                if delta:
                    yield delta
                    last = chunk
        else:
            response = self.llm_bundle.chat(msg, [], llm_config)
            if isinstance(response, tuple):
                response = response[0]
            yield str(response)
