import api from '@/utils/api';
import registerServer from '@/utils/register-server';
import request from '@/utils/request';

const {
  listDriveFile,
  removeDriveFile,
  uploadDriveFile,
  renameDriveFile,
  getAllDriveParentFolder,
  createDriveFolder,
  connectFileToKnowledge,
  getDriveFile,
  moveDriveFile,
  updateDriveFileTags,
} = api;

const methods = {
  listFile: {
    url: listDriveFile,
    method: 'get',
  },
  removeFile: {
    url: removeDriveFile,
    method: 'post',
  },
  uploadFile: {
    url: uploadDriveFile,
    method: 'post',
  },
  renameFile: {
    url: renameDriveFile,
    method: 'post',
  },
  getAllParentFolder: {
    url: getAllDriveParentFolder,
    method: 'get',
  },
  createFolder: {
    url: createDriveFolder,
    method: 'post',
  },
  connectFileToKnowledge: {
    url: connectFileToKnowledge,
    method: 'post',
  },
  getFile: {
    url: getDriveFile,
    method: 'get',
    responseType: 'blob',
  },
  moveFile: {
    url: moveDriveFile,
    method: 'post',
  },
  updateFileTags: {
    url: updateDriveFileTags,
    method: 'post',
  },
} as const;

const driveService = registerServer<keyof typeof methods>(methods, request);

// 重写 getFile 方法以支持路径参数
driveService.getFile = (fileId: string) => {
  return request.get(`${api.getDriveFile}/${fileId}`, {
    responseType: 'blob',
  });
};

export default driveService;
