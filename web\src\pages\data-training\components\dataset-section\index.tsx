import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Space, Typography } from 'antd';
import React, { useMemo, useState } from 'react';
import { DatasetTable } from './dataset-table';
import styles from './index.less';

const { Title, Text } = Typography;
const { Search } = Input;

interface DatasetItem {
  id: string;
  name: string;
  type: string;
  size: string;
  chunks: number;
  status: 'active' | 'inactive';
  uploadTime: string;
}

const DatasetSection: React.FC = () => {
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [rowSelection, setRowSelection] = useState({});

  // 模拟数据
  const mockData: DatasetItem[] = useMemo(
    () => [
      {
        id: '1',
        name: 'training_data_v1.pdf',
        type: 'PDF',
        size: '2.5MB',
        chunks: 156,
        status: 'active',
        uploadTime: '2024-01-15 10:30:00',
      },
      {
        id: '2',
        name: 'knowledge_base.docx',
        type: 'DOCX',
        size: '1.8MB',
        chunks: 89,
        status: 'active',
        uploadTime: '2024-01-14 15:20:00',
      },
      {
        id: '3',
        name: 'research_papers.txt',
        type: 'TXT',
        size: '3.2MB',
        chunks: 234,
        status: 'inactive',
        uploadTime: '2024-01-13 09:45:00',
      },
      {
        id: '4',
        name: 'technical_docs.md',
        type: 'MD',
        size: '1.1MB',
        chunks: 67,
        status: 'active',
        uploadTime: '2024-01-12 14:15:00',
      },
      {
        id: '5',
        name: 'user_manual.pdf',
        type: 'PDF',
        size: '4.7MB',
        chunks: 312,
        status: 'active',
        uploadTime: '2024-01-11 11:00:00',
      },
      {
        id: '6',
        name: 'api_documentation.md',
        type: 'MD',
        size: '800KB',
        chunks: 45,
        status: 'active',
        uploadTime: '2024-01-10 16:30:00',
      },
      {
        id: '7',
        name: 'system_requirements.docx',
        type: 'DOCX',
        size: '1.2MB',
        chunks: 78,
        status: 'inactive',
        uploadTime: '2024-01-09 14:20:00',
      },
      {
        id: '8',
        name: 'installation_guide.pdf',
        type: 'PDF',
        size: '3.8MB',
        chunks: 198,
        status: 'active',
        uploadTime: '2024-01-08 11:15:00',
      },
    ],
    [],
  );

  // 过滤数据
  const filteredData = useMemo(() => {
    if (!searchTerm) return mockData;
    return mockData.filter((item) =>
      item.name.toLowerCase().includes(searchTerm.toLowerCase()),
    );
  }, [mockData, searchTerm]);

  // 更新总数
  const paginationWithTotal = useMemo(() => {
    return {
      ...pagination,
      total: filteredData.length,
    };
  }, [pagination, filteredData]);

  const handlePaginationChange = (paginationInfo: {
    page: number;
    pageSize?: number;
  }) => {
    setPagination((prev) => ({
      ...prev,
      current: paginationInfo.page,
      pageSize: paginationInfo.pageSize || prev.pageSize,
    }));
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    // 重置到第一页
    setPagination((prev) => ({
      ...prev,
      current: 1,
    }));
  };

  return (
    <div className={styles.datasetWrapper}>
      <div className={styles.datasetHeader}>
        <div className={styles.titleSection}></div>

        <div className={styles.actionSection}>
          <Space>
            <Search
              placeholder="搜索文件名称"
              allowClear
              style={{ width: 250 }}
              prefix={<SearchOutlined />}
              onSearch={handleSearch}
              onChange={(e) => {
                if (!e.target.value) {
                  handleSearch('');
                }
              }}
            />

            <Button type="primary" icon={<PlusOutlined />}>
              新增文档
            </Button>
          </Space>
        </div>
      </div>

      <DatasetTable
        documents={filteredData}
        pagination={paginationWithTotal}
        setPagination={handlePaginationChange}
        rowSelection={rowSelection}
        setRowSelection={setRowSelection}
      />
    </div>
  );
};

export default DatasetSection;
