// 这是一个迁移示例，展示如何将 antd 的 Spin 组件替换为自定义 CustomSpin 组件

import CustomSpin from '@/components/custom-spin'; // 导入自定义组件
import { Flex, Typography } from 'antd'; // 移除 Spin 的导入
import React from 'react';

const { Title, Text } = Typography;

// ========== 迁移前（使用 antd Spin） ==========
/*
import { Flex, Spin, Typography } from 'antd';

const OldComponent = ({ loading, children }) => {
  return (
    <div>
      <Spin spinning={loading}>
        {children}
      </Spin>
    </div>
  );
};
*/

// ========== 迁移后（使用自定义 CustomSpin） ==========
interface MigrationExampleProps {
  loading: boolean;
  children: React.ReactNode;
}

const NewComponent: React.FC<MigrationExampleProps> = ({
  loading,
  children,
}) => {
  return (
    <div>
      <CustomSpin spinning={loading}>{children}</CustomSpin>
    </div>
  );
};

// ========== 实际项目中的迁移示例 ==========

// 1. 聊天容器示例
const ChatContainerExample = () => {
  const loading = false; // 示例状态
  const derivedMessages: Array<{ id: string; content: string }> = []; // 示例数据

  return (
    <Flex flex={1} vertical>
      <div className="messagesSection">
        {/* 原来的 antd Spin */}
        {/* <Spin spinning={loading}> */}

        {/* 替换为自定义 CustomSpin */}
        <CustomSpin spinning={loading}>
          {derivedMessages?.length > 0 ? (
            derivedMessages.map((message, i) => <div key={i}>消息内容 {i}</div>)
          ) : (
            <div className="emptyState">
              <Title level={4}>开启新的对话</Title>
              <Text type="secondary">在下方输入消息，开始聊天</Text>
            </div>
          )}
        </CustomSpin>
      </div>
    </Flex>
  );
};

// 2. 知识库加载示例
const KnowledgeChunkExample = () => {
  const loading = false; // 示例状态
  const data: Array<{ id: string; name: string }> = []; // 示例数据

  return (
    <div className="pageContent">
      {/* 原来的 antd Spin */}
      {/* <Spin spinning={loading} className="spin" size="large"> */}

      {/* 替换为自定义 CustomSpin */}
      <CustomSpin spinning={loading} className="spin" size="large">
        <div className="chunkContainer">
          {data.map((item, index) => (
            <div key={index}>Chunk 项目 {index}</div>
          ))}
        </div>
      </CustomSpin>
    </div>
  );
};

// 3. 带提示文本的示例
const KnowledgeListExample = () => {
  const loading = false; // 示例状态

  return (
    <div>
      {/* 原来的 antd Spin */}
      {/* <Spin spinning={loading} tip="Loading knowledge bases..."> */}

      {/* 替换为自定义 CustomSpin */}
      <CustomSpin spinning={loading} tip="Loading knowledge bases...">
        <div className="content">知识库列表内容</div>
      </CustomSpin>
    </div>
  );
};

// ========== 批量替换指南 ==========

/**
 * 批量替换步骤：
 *
 * 1. 更新导入语句：
 *    From: import\s*\{\s*([^}]*?)Spin([^}]*?)\}\s*from\s*['"]antd['"];?
 *    To:   import { $1$2 } from 'antd';\nimport CustomSpin from '@/components/custom-spin';
 *
 * 2. 替换组件使用：
 *    From: <Spin(\s+[^>]*)?>
 *    To:   <CustomSpin$1>
 *
 * 3. 替换闭合标签：
 *    From: </Spin>
 *    To:   </CustomSpin>
 */

export default NewComponent;
