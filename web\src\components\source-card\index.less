@import '@/theme/high-tech-theme.less';

.sourceCard {
  width: 100%;
  background: linear-gradient(
    135deg,
    var(--background-secondary) 0%,
    var(--background-tertiary) 100%
  );
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: all var(--transition-normal);
  backdrop-filter: blur(10px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--accent-color)
    );
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover {
    transform: translateY(-2px);
    border-color: var(--primary-color);
    box-shadow: var(--card-hover-shadow);

    &::before {
      opacity: 1;
    }
  }

  :global(.ant-card-body) {
    padding: var(--spacing-md);
  }

  .typeIcon {
    font-size: 18px;
    color: var(--primary-color);
  }

  .sourceContent {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0; // For text ellipsis to work
  }

  .titleText {
    display: block;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4;
  }

  .sourceText {
    font-size: var(--font-size-sm);
    color: var(--neutral-500);
    margin-top: 2px;
  }

  .description {
    margin-top: var(--spacing-xs);
    margin-bottom: 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}

// Source card grid
.sourceCardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  width: 100%;
}

// Source section
.sourceSection {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-color);

  .sectionTitle {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);

    .sectionIcon {
      margin-right: var(--spacing-xs);
      color: var(--primary-color);
    }

    h4 {
      margin: 0;
      background: linear-gradient(
        90deg,
        var(--primary-color) 0%,
        var(--accent-color) 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-weight: 600;
    }
  }
}
