@import '@/theme/high-tech-theme.less';

.knowledge {
  width: 100%;
  padding: var(--spacing-xl) 0;
  overflow: auto;
}

.topWrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 var(--spacing-xl) var(--spacing-xl);
  margin-bottom: var(--spacing-lg);

  .title {
    font-size: 24px;
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    margin-bottom: var(--spacing-xs);
  }

  .description {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;
    color: var(--text-secondary);
    max-width: 600px;
  }

  .topButton {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
    transition: all var(--transition-normal);

    &:hover {
      background: var(--primary-hover);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
    }
  }

  .filterButton {
    display: flex;
    align-items: center;
  }
}

.knowledgeCardContainer {
  padding: 0 var(--spacing-xl);
  overflow: auto;

  .knowledgeEmpty {
    width: 100%;
    padding: var(--spacing-xl) 0;
    color: var(--text-tertiary);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .topWrapper {
    flex-direction: column;
    gap: var(--spacing-lg);

    .title {
      font-size: 24px;
    }

    .description {
      font-size: 16px;
    }
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .knowledgeEmpty {
    opacity: 0.7;
  }
}
