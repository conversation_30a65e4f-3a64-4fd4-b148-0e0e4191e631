import {
  FileTextOutlined,
  LinkOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons';
import { Avatar, Card, Space, Typography } from 'antd';
import React from 'react';
import styles from './index.less';

const { Text, Paragraph } = Typography;

export interface SourceCardProps {
  title: string;
  source: string;
  sourceUrl: string;
  sourceIcon?: string;
  description?: string;
  type: 'document' | 'video' | 'webpage';
  onClick?: () => void;
}

const SourceCard: React.FC<SourceCardProps> = ({
  title,
  source,
  sourceUrl,
  sourceIcon,
  description,
  type,
  onClick,
}) => {
  const getIcon = () => {
    if (sourceIcon) {
      return <Avatar src={sourceIcon} size={24} />;
    }

    switch (type) {
      case 'document':
        return <FileTextOutlined className={styles.typeIcon} />;
      case 'video':
        return <VideoCameraOutlined className={styles.typeIcon} />;
      case 'webpage':
      default:
        return <LinkOutlined className={styles.typeIcon} />;
    }
  };

  return (
    <Card className={styles.sourceCard} onClick={onClick} hoverable>
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Space align="start" style={{ width: '100%' }}>
          {getIcon()}
          <div className={styles.sourceContent}>
            <Text
              strong
              ellipsis={{ tooltip: title }}
              style={{ width: '182px' }}
            >
              {title}
            </Text>
            <Text type="secondary" className={styles.sourceText}>
              {source}
            </Text>
          </div>
        </Space>
        {description && (
          <Paragraph ellipsis={{ rows: 2 }} className={styles.description}>
            {description}
          </Paragraph>
        )}
      </Space>
    </Card>
  );
};

export default SourceCard;
