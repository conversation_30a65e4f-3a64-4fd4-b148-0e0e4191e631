import { message } from 'antd';
import {
  BorderStyle,
  Document,
  HeadingLevel,
  Packer,
  Paragraph,
  Table,
  TableCell,
  TableRow,
  TextRun,
  UnderlineType,
  WidthType,
} from 'docx';
import { saveAs } from 'file-saver';
import * as showdown from 'showdown';

/**
 * Export markdown content to HTML that can be opened in Word
 * This method generates a properly formatted HTML file that Word can open correctly
 */
export const exportMarkdownToDocx = async (
  markdownContent: string,
  fileName: string = 'document.docx',
): Promise<void> => {
  try {
    // Make sure the filename has the correct extension
    const docxFileName = fileName.endsWith('.docx')
      ? fileName
      : `${fileName}.docx`;

    // First convert markdown to HTML for easier parsing
    const converter = new showdown.Converter({
      tables: true,
      tasklists: true,
      strikethrough: true,
    });
    converter.setFlavor('github');
    const htmlContent = converter.makeHtml(markdownContent);

    // Parse HTML and convert to DOCX elements
    const children = parseHtmlToDocxElements(htmlContent);

    // Create a new document with proper sections
    const doc = new Document({
      sections: [
        {
          properties: {
            page: {
              margin: {
                top: 1440, // 1 inch in twips
                right: 1440,
                bottom: 1440,
                left: 1440,
              },
            },
          },
          children: children,
        },
      ],
      styles: {
        paragraphStyles: [
          {
            id: 'Heading1',
            name: 'Heading 1',
            basedOn: 'Normal',
            next: 'Normal',
            quickFormat: true,
            run: {
              size: 36, // 18pt
              font: 'SimHei',
              bold: true,
            },
            paragraph: {
              spacing: {
                after: 200,
              },
            },
          },
          {
            id: 'Heading2',
            name: 'Heading 2',
            basedOn: 'Normal',
            next: 'Normal',
            quickFormat: true,
            run: {
              size: 32, // 16pt
              font: 'SimHei',
              bold: true,
            },
            paragraph: {
              spacing: {
                after: 120,
              },
            },
          },
          {
            id: 'Heading3',
            name: 'Heading 3',
            basedOn: 'Normal',
            next: 'Normal',
            quickFormat: true,
            run: {
              size: 28, // 14pt
              font: 'SimHei',
              bold: true,
            },
            paragraph: {
              spacing: {
                after: 100,
              },
            },
          },
          {
            id: 'CODE_BLOCK',
            name: 'Code Block',
            basedOn: 'Normal',
            next: 'Normal',
            quickFormat: true,
            run: {
              font: 'Courier New',
            },
            paragraph: {
              spacing: {
                line: 240, // 1 line spacing
              },
              border: {
                top: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
                bottom: {
                  style: BorderStyle.SINGLE,
                  size: 1,
                  color: '#DDDDDD',
                },
                left: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
                right: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
              },
              shading: {
                type: 'clear',
                fill: '#F5F5F5',
              },
            },
          },
        ],
        default: {
          document: {
            run: {
              font: 'SimSun',
              size: 24, // 12pt
            },
            paragraph: {
              spacing: {
                line: 360, // 1.5 line spacing
              },
            },
          },
        },
      },
    });

    // Generate DOCX file
    const buffer = await Packer.toBuffer(doc);
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    });

    // Save the file
    saveAs(blob, docxFileName);

    message.success(`已导出为 ${docxFileName}`);
  } catch (error) {
    console.error('Error exporting markdown to DOCX:', error);
    message.error('导出 DOCX 文件失败');
  }
};

/**
 * Parse HTML content and convert to DOCX elements
 */
function parseHtmlToDocxElements(html: string): (Paragraph | Table)[] {
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;

  const elements: (Paragraph | Table)[] = [];

  // Process each child node of the HTML
  for (let i = 0; i < tempDiv.children.length; i++) {
    const node = tempDiv.children[i];
    const nodeName = node.nodeName.toLowerCase();

    switch (nodeName) {
      case 'h1':
        elements.push(
          new Paragraph({
            text: node.textContent || '',
            heading: HeadingLevel.HEADING_1,
            style: 'Heading1',
          }),
        );
        break;

      case 'h2':
        elements.push(
          new Paragraph({
            text: node.textContent || '',
            heading: HeadingLevel.HEADING_2,
            style: 'Heading2',
          }),
        );
        break;

      case 'h3':
        elements.push(
          new Paragraph({
            text: node.textContent || '',
            heading: HeadingLevel.HEADING_3,
            style: 'Heading3',
          }),
        );
        break;

      case 'p':
        elements.push(createParagraphFromHtml(node));
        break;

      case 'ul':
        processListItems(node.children, elements, '•');
        break;

      case 'ol':
        processListItems(node.children, elements, 'number');
        break;

      case 'pre':
        // Handle code blocks
        const codeContent = node.textContent || '';
        elements.push(
          new Paragraph({
            text: codeContent,
            style: 'CODE_BLOCK',
          }),
        );
        break;

      case 'table':
        const table = processTable(node);
        if (table) {
          elements.push(table);
        }
        break;

      default:
        // For any other element, just add as a paragraph
        if (node.textContent && node.textContent.trim()) {
          elements.push(new Paragraph({ text: node.textContent }));
        }
        break;
    }
  }

  return elements;
}

/**
 * Process list items (ul or ol)
 */
function processListItems(
  items: HTMLCollection,
  elements: (Paragraph | Table)[],
  bullet: string,
  level: number = 0,
) {
  let index = 1;

  for (let i = 0; i < items.length; i++) {
    const item = items[i] as HTMLElement;
    if (item.nodeName.toLowerCase() !== 'li') continue;

    const prefix = bullet === 'number' ? `${index}. ` : `${bullet} `;
    const indentation = level * 18; // 18pt per level

    // Create a paragraph for the list item
    elements.push(
      new Paragraph({
        text: prefix + (item.textContent || ''),
        indent: {
          left: indentation,
          hanging: 20, // Hanging indent for the bullet
        },
      }),
    );

    index++;

    // Process nested lists
    const nestedLists = item.querySelectorAll(':scope > ul, :scope > ol');
    for (let j = 0; j < nestedLists.length; j++) {
      const nestedList = nestedLists[j];
      const isOrdered = nestedList.nodeName.toLowerCase() === 'ol';

      processListItems(
        nestedList.children,
        elements,
        isOrdered ? 'number' : '◦', // Use different bullet for nested lists
        level + 1,
      );
    }
  }
}

/**
 * Create a paragraph from HTML with text formatting
 */
function createParagraphFromHtml(node: Element): Paragraph {
  const textRuns: TextRun[] = [];

  // Process child nodes to handle formatting
  processTextNodes(node, textRuns);

  return new Paragraph({
    children: textRuns,
  });
}

/**
 * Process text nodes recursively to handle formatting
 */
function processTextNodes(node: Node, textRuns: TextRun[]) {
  if (node.nodeType === Node.TEXT_NODE) {
    // Text node, add as plain text
    if (node.textContent && node.textContent.trim()) {
      textRuns.push(new TextRun(node.textContent));
    }
    return;
  }

  if (node.nodeType !== Node.ELEMENT_NODE) return;

  const element = node as Element;
  const nodeName = element.nodeName.toLowerCase();

  // Format based on element type
  if (nodeName === 'strong' || nodeName === 'b') {
    // Bold text
    if (element.textContent) {
      textRuns.push(
        new TextRun({
          text: element.textContent,
          bold: true,
        }),
      );
    }
  } else if (nodeName === 'em' || nodeName === 'i') {
    // Italic text
    if (element.textContent) {
      textRuns.push(
        new TextRun({
          text: element.textContent,
          italics: true,
        }),
      );
    }
  } else if (nodeName === 'code') {
    // Inline code
    if (element.textContent) {
      textRuns.push(
        new TextRun({
          text: element.textContent,
          font: 'Courier New',
          shading: {
            type: 'clear',
            fill: '#F5F5F5',
          },
        }),
      );
    }
  } else if (nodeName === 'a') {
    // Link
    const href = element.getAttribute('href');
    if (element.textContent) {
      textRuns.push(
        new TextRun({
          text: element.textContent,
          color: '0000FF',
          underline: {
            type: UnderlineType.SINGLE,
          },
        }),
      );
    }
  } else if (nodeName === 'br') {
    // Line break
    textRuns.push(new TextRun({ text: '', break: 1 }));
  } else {
    // Process children recursively
    for (let i = 0; i < node.childNodes.length; i++) {
      processTextNodes(node.childNodes[i], textRuns);
    }
  }
}

/**
 * Process table element
 */
function processTable(tableElement: Element): Table | null {
  const rows = tableElement.querySelectorAll('tr');
  if (!rows.length) return null;

  const tableRows: TableRow[] = [];

  // Process each row
  rows.forEach((row) => {
    const cells = row.querySelectorAll('th, td');
    if (!cells.length) return;

    const tableCells: TableCell[] = [];

    // Process each cell
    cells.forEach((cell) => {
      const isHeader = cell.nodeName.toLowerCase() === 'th';

      tableCells.push(
        new TableCell({
          children: [createParagraphFromHtml(cell)],
          shading: isHeader
            ? {
                type: 'clear',
                fill: '#F2F2F2',
              }
            : undefined,
        }),
      );
    });

    tableRows.push(new TableRow({ children: tableCells }));
  });

  return new Table({
    rows: tableRows,
    width: {
      size: 100,
      type: WidthType.PERCENTAGE,
    },
    borders: {
      top: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
      bottom: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
      left: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
      right: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
      insideHorizontal: {
        style: BorderStyle.SINGLE,
        size: 1,
        color: '#DDDDDD',
      },
      insideVertical: { style: BorderStyle.SINGLE, size: 1, color: '#DDDDDD' },
    },
  });
}

/**
 * Enhanced conversion of markdown to HTML
 * More robust than the previous version
 */
const convertMarkdownToHtml = (markdown: string): string => {
  if (!markdown) return '';

  let html = markdown;

  // Handle headings (# Heading 1, ## Heading 2, etc.)
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  html = html.replace(/^#### (.*$)/gim, '<h4>$1</h4>');
  html = html.replace(/^##### (.*$)/gim, '<h5>$1</h5>');
  html = html.replace(/^###### (.*$)/gim, '<h6>$1</h6>');

  // Handle bold text (**text**)
  html = html.replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>');

  // Handle italic text (*text*)
  html = html.replace(/\*(.*?)\*/gim, '<em>$1</em>');

  // Handle code blocks (```code```)
  html = html.replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>');

  // Handle inline code (`code`)
  html = html.replace(/`(.*?)`/gim, '<code>$1</code>');

  // Better handling for lists
  // Unordered lists - handle multiple levels
  let tempHtml = '';
  const lines = html.split('\n');
  let inList = false;
  let listType = '';

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Check for unordered list items
    if (line.match(/^\s*[\-\*] (.*$)/)) {
      const content = line.replace(/^\s*[\-\*] /, '');

      if (!inList || listType !== 'ul') {
        // Start a new list if not in one
        if (inList) {
          tempHtml += `</${listType}>`;
        }
        tempHtml += '<ul>';
        listType = 'ul';
        inList = true;
      }

      tempHtml += `<li>${content}</li>`;
    }
    // Check for ordered list items
    else if (line.match(/^\s*\d+\. (.*$)/)) {
      const content = line.replace(/^\s*\d+\. /, '');

      if (!inList || listType !== 'ol') {
        // Start a new list if not in one
        if (inList) {
          tempHtml += `</${listType}>`;
        }
        tempHtml += '<ol>';
        listType = 'ol';
        inList = true;
      }

      tempHtml += `<li>${content}</li>`;
    }
    // Not a list item
    else {
      if (inList) {
        tempHtml += `</${listType}>`;
        inList = false;
      }
      tempHtml += line + '\n';
    }
  }

  // Close any open list
  if (inList) {
    tempHtml += `</${listType}>`;
  }

  html = tempHtml;

  // Handle paragraphs (text not otherwise formatted)
  // First, protect already processed HTML elements
  html = html.replace(/<([^>]+)>/g, '⟨$1⟩');

  // Now handle paragraphs for remaining plain text
  const paragraphs = html.split('\n\n');
  html = paragraphs
    .map((p) => {
      // Skip if it's just whitespace or already wrapped
      if (p.trim() === '' || p.trim().startsWith('⟨')) {
        return p;
      }
      return `<p>${p.trim()}</p>`;
    })
    .join('\n\n');

  // Restore protected HTML tags
  html = html.replace(/⟨([^>]+)⟩/g, '<$1>');

  // Handle line breaks within paragraphs
  html = html.replace(
    /\n(?!<\/?(p|h\d|ul|ol|li|pre|code|table|tr|td|th))/gi,
    '<br>\n',
  );

  // Clean up
  html = html.replace(/<p><\/p>/g, ''); // Remove empty paragraphs

  return html;
};

/**
 * More robust alternative using a third-party markdown conversion library
 * Requires additional dependencies to be installed:
 * npm install --save html-docx-js showdown
 */
/*
import { saveAs } from 'file-saver';
import * as showdown from 'showdown';
import * as htmlDocx from 'html-docx-js';

export const exportMarkdownToDocxWithLibrary = async (
  markdownContent: string,
  fileName: string = 'document.docx'
): Promise<void> => {
  try {
    // Make sure the filename has the correct extension
    const docxFileName = fileName.endsWith('.docx') ? fileName : `${fileName}.docx`;
    
    // Convert markdown to HTML using showdown
    const converter = new showdown.Converter({
      tables: true,
      tasklists: true,
      strikethrough: true,
    });
    const htmlContent = converter.makeHtml(markdownContent);
    
    // Create a full HTML document
    const fullHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="UTF-8">
          <title>${docxFileName}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 2cm; }
            h1, h2, h3 { margin-top: 1em; margin-bottom: 0.5em; }
            p { margin-bottom: 1em; }
            ul, ol { margin-bottom: 1em; padding-left: 2em; }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `;
    
    // Convert HTML to DOCX
    const docxBlob = htmlDocx.asBlob(fullHtml);
    
    // Save the file
    saveAs(docxBlob, docxFileName);
    
    message.success(`已导出为 ${docxFileName}`);
  } catch (error) {
    console.error('Error exporting markdown to DOCX:', error);
    message.error('导出 DOCX 文件失败');
  }
};
*/
