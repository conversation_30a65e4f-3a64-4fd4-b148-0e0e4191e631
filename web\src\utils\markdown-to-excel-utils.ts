import { message } from 'antd';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

interface ParsedQuestion {
  题型: string;
  题目内容: string;
  难易度: string;
  正确答案: string;
  答案A: string;
  答案B: string;
  答案C: string;
  答案D: string;
  答案E: string;
  答案F: string;
  答案G: string;
  解析: string;
}

/**
 * 解析markdown格式的题目内容为结构化数据
 * 支持多种格式变体以提高兼容性
 */
export const parseMarkdownQuestions = (
  markdownContent: string,
): ParsedQuestion[] => {
  if (!markdownContent || typeof markdownContent !== 'string') {
    return [];
  }

  const questions: ParsedQuestion[] = [];
  const rawBlocks = markdownContent.split(/\\n\\s*---\\s*\\n/);
  const questionBlocks: string[] = [];

  for (const block of rawBlocks) {
    const trimmedBlock = block.trim();
    const questionStartIndex = trimmedBlock.indexOf('####');
    if (questionStartIndex !== -1) {
      const questionBlock = trimmedBlock.substring(questionStartIndex);
      if (questionBlock.length > 10) {
        questionBlocks.push(questionBlock);
      }
    }
  }

  for (const block of questionBlocks) {
    let remainingBlock = block;
    const question: ParsedQuestion = {
      题型: '',
      题目内容: '',
      难易度: '',
      正确答案: '',
      答案A: '',
      答案B: '',
      答案C: '',
      答案D: '',
      答案E: '',
      答案F: '',
      答案G: '',
      解析: '',
    };

    // 提取题型 - 支持多种格式
    // 格式1: #### 1. 多选题 (AI出题)
    let typeMatch = remainingBlock.match(/####\s*\d+\.\s*([^\n]+)/);
    if (typeMatch) {
      question.题型 = typeMatch[1].trim();
    } else {
      // 格式2: #### 题目 1 （单选题）(AI出题)
      typeMatch = remainingBlock.match(
        /####\s*题目\s*\d+\s*[（(]([^）)]+)[）)]/,
      );
      if (typeMatch) {
        question.题型 = typeMatch[1].trim();
      } else {
        // 格式3: #### **多选题** (AI出题)
        typeMatch = remainingBlock.match(/####\s*\*\*([^*]+)\*\*/);
        if (typeMatch) {
          question.题型 = typeMatch[1].trim();
        } else {
          // 格式4: ### 1. 【单选题】 (举一反三)
          typeMatch = remainingBlock.match(/###\s*\d+\.\s*【([^】]+)】/);
          if (typeMatch) {
            question.题型 = typeMatch[1].trim();
          } else {
            // 格式5: #### 【单选题】 (新举一反三格式)
            typeMatch = remainingBlock.match(/####\s*【([^】]+)】/);
            if (typeMatch) {
              question.题型 = typeMatch[1].trim();
            }
          }
        }
      }
    }

    // 提取题目内容 - 支持多种格式
    let questionContent = '';

    // 方法1: 查找带编号的题目内容 **1. 题目内容**（AI出题格式）
    const numberedQuestionMatch = remainingBlock.match(
      /\*\*(\d+\.\s*[^*]+)\*\*/,
    );
    if (numberedQuestionMatch) {
      questionContent = numberedQuestionMatch[1].trim();
    }

    // 方法2: 查找第一个**包围的内容（旧格式，但要排除题型标题）
    if (!questionContent) {
      const contentMatches = remainingBlock.match(
        /\*\*([^*]+(?:\*(?!\*)[^*]*)*)\*\*/g,
      );
      if (contentMatches && contentMatches.length > 0) {
        // 遍历所有匹配，找到包含题目内容的那个（通常包含数字编号或较长内容）
        for (const match of contentMatches) {
          const content = match.replace(/\*\*/g, '').trim();
          // 跳过题型标题（通常比较短且不包含编号）
          if (content.length > 10 || /\d+\./.test(content)) {
            questionContent = content;
            break;
          }
        }
      }
    }

    // 方法3: 新举一反三格式 - 查找 **问题：** 后的内容
    if (!questionContent) {
      const problemMatch = remainingBlock.match(
        /\*\*问题[：:]\*\*\s*\n(.+?)(?=\n\*\*选项|\n\*\*答案|$)/s,
      );
      if (problemMatch) {
        let content = problemMatch[1].trim();
        // 清理可能的多余换行和空白，但保留必要的换行
        content = content.replace(/\n\s*\n/g, '\n').trim();
        questionContent = content;
      }
    }

    // 方法4: 举一反三格式 - 查找标题后到选项之间的内容
    if (!questionContent) {
      // 找到标题行后的内容（支持 ### 和 #### 两种格式）
      // 内容可能包含多行，直到遇到选项（A. B. C.）或答案标记
      const headerMatch = remainingBlock.match(
        /(?:###|####)[^\n]+\n(.+?)(?=\n[A-G]\.|$|\n\*\*答案|---)/s,
      );
      if (headerMatch) {
        let content = headerMatch[1].trim();
        // 清理可能的多余换行和空白，但保留必要的换行
        content = content.replace(/\n\s*\n/g, '\n').trim();
        // 移除可能的（   ）等标记，但保留题目中的（）
        content = content.replace(/\s*（\s*\）\s*$/, '');
        questionContent = content;
      }
    }

    question.题目内容 = questionContent;

    // 提取难易度 - 支持多种格式
    let difficultyMatch = remainingBlock.match(
      /[-*]\s*\*\*难易度[：:]\s*([^\*\n]+)\*\*/,
    );
    if (!difficultyMatch) {
      difficultyMatch = remainingBlock.match(
        /\*\*难易度[：:]\s*([^\*\n]+)\*\*/,
      );
    }
    // 新格式：难度：易（没有**包围）
    if (!difficultyMatch) {
      difficultyMatch = remainingBlock.match(/难度[：:]\s*([^\n]+)/);
    }
    if (difficultyMatch) {
      question.难易度 = difficultyMatch[1].trim();
    }

    // 提取正确答案 - 支持多种格式
    let answerMatch = remainingBlock.match(
      /\n\s*\*\*正确答案[：:]\*\*\s+([^\n]+)/,
    );
    // 如果没有匹配到，尝试匹配内容在**标签内的格式
    if (!answerMatch) {
      answerMatch = remainingBlock.match(/\n\s*\*\*正确答案[：:]([^*]+)\*\*/);
    }
    if (answerMatch) {
      question.正确答案 = answerMatch[1].trim();
      remainingBlock = remainingBlock.replace(answerMatch[0], '');
    }

    // 提取选项 - 支持多种格式
    // 格式1: - A. 选项内容
    let optionMatches = remainingBlock.match(/^[-*]\s*([A-G])\.\s*(.+)$/gm);
    // 格式2: A. 选项内容（直接开头）
    if (!optionMatches || optionMatches.length === 0) {
      optionMatches = remainingBlock.match(/^([A-G])\.\s*(.+)$/gm);
    }

    // 格式3: 新举一反三格式 - **选项：** 后的选项
    if (!optionMatches || optionMatches.length === 0) {
      // 首先找到 **选项：** 部分
      const optionsSection = remainingBlock.match(
        /\*\*选项[：:]\*\*\s*\n((?:[A-G]\..+\n?)+)/s,
      );
      if (optionsSection) {
        const optionsText = optionsSection[1];
        optionMatches = optionsText.match(/^([A-G])\.\s*(.+)$/gm);
      }
    }

    if (optionMatches) {
      for (const optionMatch of optionMatches) {
        let match = optionMatch.match(/^[-*]\s*([A-G])\.\s*(.+)$/);
        if (!match) {
          match = optionMatch.match(/^([A-G])\.\s*(.+)$/);
        }
        if (match) {
          const letter = match[1];
          const content = match[2].trim();
          question[`答案${letter}` as keyof ParsedQuestion] = content;
        }
      }
    }

    // 提取解析 - 支持多种格式
    const explanationMatch = remainingBlock.match(
      /\n\s*\*\*解析[：:]([\s\S]+)/,
    );
    if (explanationMatch) {
      let explanation = explanationMatch[1].trim();
      // Handles both "**解析：** content" and "**解析：content**"
      if (explanation.startsWith('**')) {
        explanation = explanation.substring(2).trim();
      }
      if (explanation.endsWith('**')) {
        explanation = explanation.slice(0, -2).trim();
      }

      // 清理markdown格式字符串
      explanation = explanation
        .replace(/\*\*\n\n---/g, '') // 移除 "**\n\n---"
        .replace(/\*\*/g, '') // 移除所有剩余的 **
        .replace(/\n\n---/g, '') // 移除 "\n\n---"
        .replace(/---$/g, '') // 移除结尾的 "---"
        .replace(/\n+/g, ' ') // 将多个换行符替换为单个空格
        .trim();

      question.解析 = explanation;
      remainingBlock = remainingBlock.replace(explanationMatch[0], '');
    }

    // 标准化题型名称
    if (question.题型) {
      const normalizedType = question.题型.replace(/[【】（()]/g, '').trim();
      if (normalizedType.includes('单选') || normalizedType.includes('选择')) {
        question.题型 = '单选题';
      } else if (normalizedType.includes('多选')) {
        question.题型 = '多选题';
      } else if (
        normalizedType.includes('判断') ||
        normalizedType.includes('是非')
      ) {
        question.题型 = '判断题';
      } else if (normalizedType.includes('填空')) {
        question.题型 = '填空题';
      } else if (
        normalizedType.includes('问答') ||
        normalizedType.includes('简答')
      ) {
        question.题型 = '问答题';
      } else if (normalizedType.includes('排序')) {
        question.题型 = '排序题';
      }
    }

    // 只有当题目内容不为空时才添加到结果中
    if (question.题目内容 || question.题型) {
      questions.push(question);
    }
  }

  return questions;
};

/**
 * 将markdown格式的题目转换为Excel文件并下载
 */
export const markdownToExcel = (
  markdownContent: string,
  fileName: string = 'questions.xlsx',
): void => {
  try {
    if (!markdownContent || markdownContent.trim() === '') {
      message.error('没有数据可导出');
      return;
    }

    // 解析markdown内容为结构化数据
    const questions = parseMarkdownQuestions(markdownContent);

    if (questions.length === 0) {
      console.error('解析失败，未找到有效的题目数据');
      console.log('原始内容长度:', markdownContent.length);
      console.log('原始内容预览:', markdownContent.slice(0, 300));
      console.log('是否包含 ###:', markdownContent.includes('###'));
      console.log('是否包含 ####:', markdownContent.includes('####'));
      console.log('是否包含【】:', markdownContent.includes('【'));

      // 调用测试函数进行详细调试
      console.log('开始详细调试...');
      testMarkdownParsing(markdownContent);

      message.error('未找到有效的题目数据，请检查控制台获取详细调试信息');
      return;
    }

    // 定义Excel表头
    const headers = [
      '题型',
      '题目内容',
      '难易度',
      '正确答案',
      '答案A',
      '答案B',
      '答案C',
      '答案D',
      '答案E',
      '答案F',
      '答案G',
      '解析',
    ];

    // 将数据转换为二维数组格式
    const data = [
      headers,
      ...questions.map((q) => [
        q.题型,
        q.题目内容,
        q.难易度,
        q.正确答案,
        q.答案A,
        q.答案B,
        q.答案C,
        q.答案D,
        q.答案E,
        q.答案F,
        q.答案G,
        q.解析,
      ]),
    ];

    // 创建工作簿和工作表
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // 设置列宽
    const columnWidths = [
      { wch: 10 }, // 题型
      { wch: 40 }, // 题目内容
      { wch: 8 }, // 难易度
      { wch: 12 }, // 正确答案
      { wch: 20 }, // 答案A
      { wch: 20 }, // 答案B
      { wch: 20 }, // 答案C
      { wch: 20 }, // 答案D
      { wch: 20 }, // 答案E
      { wch: 20 }, // 答案F
      { wch: 20 }, // 答案G
      { wch: 30 }, // 答案解析
    ];
    worksheet['!cols'] = columnWidths;

    // 设置单元格样式（表头加粗）
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let col = range.s.c; col <= range.e.c; col++) {
      const headerCell = worksheet[XLSX.utils.encode_cell({ r: 0, c: col })];
      if (headerCell) {
        headerCell.s = {
          font: { bold: true },
          alignment: { horizontal: 'center' },
        };
      }
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '考试题目');

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true,
    });

    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // 确保文件名有正确的扩展名
    const finalFileName = fileName.endsWith('.xlsx')
      ? fileName
      : `${fileName}.xlsx`;
    saveAs(blob, finalFileName);

    message.success(`成功导出 ${questions.length} 道题目到 ${finalFileName}`);
  } catch (error) {
    console.error('导出Excel失败:', error);
    message.error('导出Excel文件失败');
  }
};

/**
 * 解析新版markdown格式的题目内容为结构化数据
 * @param markdownContent
 */
export const parseMarkdownV2 = (markdownContent: string): ParsedQuestion[] => {
  if (!markdownContent || typeof markdownContent !== 'string') {
    return [];
  }

  const questions: ParsedQuestion[] = [];

  // 找到所有题目开始的位置（以#### 开头）
  const questionPattern = /####\s+\d+\.\s+/g;
  const matches: { index: number; match: string }[] = [];
  let match;

  while ((match = questionPattern.exec(markdownContent)) !== null) {
    matches.push({ index: match.index, match: match[0] });
  }

  // 如果没有找到编号题目，尝试查找其他格式的题目
  if (matches.length === 0) {
    const altPattern = /####\s+/g;
    while ((match = altPattern.exec(markdownContent)) !== null) {
      matches.push({ index: match.index, match: match[0] });
    }
  }

  // 基于找到的位置提取每个题目块
  const questionBlocks: string[] = [];
  for (let i = 0; i < matches.length; i++) {
    const start = matches[i].index;
    const end =
      i < matches.length - 1 ? matches[i + 1].index : markdownContent.length;
    const block = markdownContent.substring(start, end).trim();
    if (block.length > 10) {
      questionBlocks.push(block);
    }
  }

  for (const block of questionBlocks) {
    let remainingBlock = block;
    const question: ParsedQuestion = {
      题型: '',
      题目内容: '',
      难易度: '',
      正确答案: '',
      答案A: '',
      答案B: '',
      答案C: '',
      答案D: '',
      答案E: '',
      答案F: '',
      答案G: '',
      解析: '',
    };

    // 6. 解析 (from bottom up)
    const explanationMatch = remainingBlock.match(
      /\n\s*\*\*解析[：:]([\s\S]+)/,
    );
    if (explanationMatch) {
      let explanation = explanationMatch[1].trim();
      // Handles both "**解析：** content" and "**解析：content**"
      if (explanation.startsWith('**')) {
        explanation = explanation.substring(2).trim();
      }
      if (explanation.endsWith('**')) {
        explanation = explanation.slice(0, -2).trim();
      }

      // 清理markdown格式字符串
      explanation = explanation
        .replace(/\*\*\n\n---/g, '') // 移除 "**\n\n---"
        .replace(/\*\*/g, '') // 移除所有剩余的 **
        .replace(/\n\n---/g, '') // 移除 "\n\n---"
        .replace(/---$/g, '') // 移除结尾的 "---"
        .replace(/\n+/g, ' ') // 将多个换行符替换为单个空格
        .trim();

      question.解析 = explanation;
      remainingBlock = remainingBlock.replace(explanationMatch[0], '');
    }

    // 5. 正确答案
    let answerMatch = remainingBlock.match(
      /\n\s*\*\*正确答案[：:]\*\*\s+([^\n]+)/,
    );
    // 如果没有匹配到，尝试匹配内容在**标签内的格式
    if (!answerMatch) {
      answerMatch = remainingBlock.match(/\n\s*\*\*正确答案[：:]([^*]+)\*\*/);
    }
    if (answerMatch) {
      question.正确答案 = answerMatch[1].trim();
      remainingBlock = remainingBlock.replace(answerMatch[0], '');
    }

    // 4. 难易度
    const difficultyMatch = remainingBlock.match(
      /\n\s*\*\*难易度[：:]\*\*\s+([^\n]+)/,
    );
    if (difficultyMatch) {
      question.难易度 = difficultyMatch[1].trim();
      remainingBlock = remainingBlock.replace(difficultyMatch[0], '');
    }

    if (question.正确答案 === '错误') question.正确答案 = '错';
    if (question.正确答案 === '正确') question.正确答案 = '对';

    // 3. 选项
    const optionLines = remainingBlock.match(/^- .+/gm);
    if (optionLines) {
      if (optionLines.some((l) => l.trim() === '- 对' || l.trim() === '- 错')) {
        if (optionLines.some((l) => l.trim() === '- 对')) question.答案A = '对';
        if (optionLines.some((l) => l.trim() === '- 错')) question.答案B = '错';
      } else {
        for (const line of optionLines) {
          const choiceMatch = line.match(/^- ([A-G])\.\s*(.+)/);
          if (choiceMatch) {
            const letter = choiceMatch[1];
            const content = choiceMatch[2].trim();
            question[`答案${letter}` as keyof ParsedQuestion] = content;
          }
        }
      }
      remainingBlock = remainingBlock
        .replace(/^- .+/gm, '')
        .replace(/\n\s*\n/g, '\n');
    }

    // 1. 题型
    const typeMatch = remainingBlock.match(
      /####\s*\*\*(.*?)\*\*|####\s*\d+\.\s*(.*?)(?=\n|$)/,
    );
    if (typeMatch) {
      question.题型 = (typeMatch[1] || typeMatch[2] || '').trim();
      remainingBlock = remainingBlock.replace(typeMatch[0], '').trim();
    }

    // 2. 题目内容 is what's left
    question.题目内容 = remainingBlock
      .replace(/^\*\*/, '')
      .replace(/\*\*$/, '')
      .trim();

    // 标准化题型名称
    if (question.题型) {
      const normalizedType = question.题型.replace(/[【】（()]/g, '').trim();
      if (normalizedType.includes('单选')) {
        question.题型 = '单选题';
      } else if (normalizedType.includes('多选')) {
        question.题型 = '多选题';
      } else if (normalizedType.includes('判断')) {
        question.题型 = '判断题';
      }
    }

    if (question.题目内容 || question.题型) {
      questions.push(question);
    }
  }

  return questions;
};

/**
 * 将新版markdown格式的题目转换为Excel文件并下载
 */
export const markdownToExcelV2 = (
  markdownContent: string,
  fileName: string = 'questions.xlsx',
): void => {
  try {
    if (!markdownContent || markdownContent.trim() === '') {
      message.error('没有数据可导出');
      return;
    }

    // 解析markdown内容为结构化数据
    const questions = parseMarkdownV2(markdownContent);

    if (questions.length === 0) {
      console.error('V2解析失败，未找到有效的题目数据');
      console.log('V2 原始内容长度:', markdownContent.length);
      console.log('V2 原始内容预览:', markdownContent.slice(0, 500));
      message.error('未找到有效的题目数据，请检查markdown格式是否正确');
      return;
    }

    // 定义Excel表头
    const headers = [
      '题型',
      '题目内容',
      '难易度',
      '正确答案',
      '答案A',
      '答案B',
      '答案C',
      '答案D',
      '答案E',
      '答案F',
      '答案G',
      '解析',
    ];

    // 将数据转换为二维数组格式
    const data = [
      headers,
      ...questions.map((q) => [
        q.题型,
        q.题目内容,
        q.难易度,
        q.正确答案,
        q.答案A,
        q.答案B,
        q.答案C,
        q.答案D,
        q.答案E,
        q.答案F,
        q.答案G,
        q.解析,
      ]),
    ];

    // 创建工作簿和工作表
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // 设置列宽
    const columnWidths = [
      { wch: 10 }, // 题型
      { wch: 40 }, // 题目内容
      { wch: 8 }, // 难易度
      { wch: 12 }, // 正确答案
      { wch: 20 }, // 答案A
      { wch: 20 }, // 答案B
      { wch: 20 }, // 答案C
      { wch: 20 }, // 答案D
      { wch: 20 }, // 答案E
      { wch: 20 }, // 答案F
      { wch: 20 }, // 答案G
      { wch: 30 }, // 答案解析
    ];
    worksheet['!cols'] = columnWidths;

    // 设置单元格样式（表头加粗）
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let col = range.s.c; col <= range.e.c; col++) {
      const headerCell = worksheet[XLSX.utils.encode_cell({ r: 0, c: col })];
      if (headerCell) {
        headerCell.s = {
          font: { bold: true },
          alignment: { horizontal: 'center' },
        };
      }
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '考试题目');

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true,
    });

    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    const finalFileName = fileName.endsWith('.xlsx')
      ? fileName
      : `${fileName}.xlsx`;
    saveAs(blob, finalFileName);

    message.success(`成功导出 ${questions.length} 道题目到 ${finalFileName}`);
  } catch (error) {
    console.error('导出Excel失败 (V2):', error);
    message.error('导出Excel文件失败');
  }
};

/**
 * 测试markdown解析功能
 */
export const testMarkdownParsing = (markdownContent: string): void => {
  console.log('Testing markdown parsing...');
  const questions = parseMarkdownQuestions(markdownContent);
  console.log('Parsed questions:', questions);
  console.log(`Total questions parsed: ${questions.length}`);

  // 输出第一个题目的详细信息用于调试
  if (questions.length > 0) {
    console.log('First question details:', questions[0]);
  }
};

/**
 * 清洗单个内容项，移除markdown符号和多余空白
 */
const cleanContent = (content: string): string => {
  if (!content) return '';

  return (
    content
      .trim()
      // 移除markdown符号
      .replace(/^\*\*|\*\*$/g, '') // 移除开头和结尾的 **
      .replace(/^#{1,6}\s*/, '') // 移除开头的 # 标题符号
      .replace(/^---+|---+$/g, '') // 移除开头和结尾的 ---
      .replace(/[\r\n]+/g, ' ') // 将换行符替换为空格
      .replace(/\s+/g, ' ') // 合并多个空格为一个
      .trim()
  );
};

/**
 * 动态解析extrapolate格式的markdown内容
 */
const parseExtrapolateMarkdown = (
  markdownContent: string,
): { questions: any[]; columnNames: string[] } => {
  if (!markdownContent || typeof markdownContent !== 'string') {
    return { questions: [], columnNames: [] };
  }

  // 步骤1: 通过 "---" 分隔符拆分出每一行，放入一个数组
  const rows = markdownContent
    .replace(/\\n/g, '\n') // 处理转义的换行符
    .split(/\n\s*---+\s*\n/) // 用 --- 分隔
    .filter((row) => row.trim().length > 0);

  const questions: any[] = [];
  const allColumns = new Set<string>(); // 收集所有可能的列名

  // 步骤2 & 3: 遍历数组，去除无效内容，通过 "\n" 拆分成多列
  for (const row of rows) {
    const lines = row
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0 && !line.match(/^#+\s*$|^-+\s*$/)); // 过滤纯标题符号和分隔符行

    // 降低行数要求，只要有内容就尝试解析
    if (lines.length < 1) continue;

    const questionData: Record<string, string> = {};
    let hasValidContent = false;

    // 步骤4: 遍历每一行，根据"："进一步拆分
    for (const line of lines) {
      // 查找中文冒号或英文冒号
      const chineseColonIndex = line.indexOf('：');
      const englishColonIndex = line.indexOf(':');

      let colonIndex = -1;
      if (chineseColonIndex !== -1 && englishColonIndex !== -1) {
        // 如果两种冒号都存在，选择第一个出现的
        colonIndex = Math.min(chineseColonIndex, englishColonIndex);
      } else if (chineseColonIndex !== -1) {
        colonIndex = chineseColonIndex;
      } else if (englishColonIndex !== -1) {
        colonIndex = englishColonIndex;
      }

      // 放宽条件：允许冒号在位置1（比如"题目："这种情况）
      if (colonIndex >= 1) {
        const key = cleanContent(line.substring(0, colonIndex));
        const value = cleanContent(line.substring(colonIndex + 1));

        if (key && value) {
          questionData[key] = value;
          allColumns.add(key);
          hasValidContent = true;
        }
      }
    }

    // 只添加有有效内容的题目
    if (hasValidContent) {
      questions.push(questionData);
    }
  }

  // 步骤5: 筛选出Excel的列，并依次填充内容
  // 将收集到的所有列名转换为数组，并进行排序以保持一致性
  const columnNames = Array.from(allColumns).sort((a, b) => {
    // 定义常见列名的优先级排序
    const priority: Record<string, number> = {
      题型: 1,
      题目: 2,
      题目内容: 2,
      难易度: 3,
      难度: 3,
      正确答案: 4,
      答案A: 5,
      答案B: 6,
      答案C: 7,
      答案D: 8,
      答案E: 9,
      答案F: 10,
      答案G: 11,
      解析: 12,
    };

    const aPriority = priority[a] || 999;
    const bPriority = priority[b] || 999;

    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }

    return a.localeCompare(b);
  });

  console.log('动态解析结果:', {
    总题目数: questions.length,
    检测到的列: columnNames,
    数据预览: questions.slice(0, 2),
  });

  // 添加详细的调试信息
  if (questions.length === 0) {
    console.log('解析失败调试信息:');
    console.log('原始内容长度:', markdownContent.length);
    console.log('分割后的行数:', rows.length);
    console.log('前3行内容:', rows.slice(0, 3));

    // 分析每一行的解析情况
    rows.slice(0, 3).forEach((row, index) => {
      console.log(`第${index + 1}行分析:`);
      const lines = row
        .split('\n')
        .map((line) => line.trim())
        .filter((line) => line.length > 0 && !line.match(/^#+\s*$|^-+\s*$/));

      console.log('  有效行数:', lines.length);
      console.log('  行内容:', lines);

      lines.forEach((line, lineIndex) => {
        const chineseColonIndex = line.indexOf('：');
        const englishColonIndex = line.indexOf(':');
        console.log(`    第${lineIndex + 1}行: "${line}"`);
        console.log(
          `      中文冒号位置: ${chineseColonIndex}, 英文冒号位置: ${englishColonIndex}`,
        );
      });
    });
  }

  return { questions, columnNames };
};

/**
 * 将extrapolate格式的markdown转换为Excel文件并下载
 * 专门处理从markdown中提取的题目数据格式
 * @param markdownContent - extrapolate格式的markdown内容
 * @param fileName - 输出文件名，默认为'extrapolate_questions.xlsx'
 */
export const mdToXlxsForExtrapolate = (
  markdownContent: string,
  fileName: string = 'extrapolate_questions.xlsx',
): void => {
  try {
    if (!markdownContent || markdownContent.trim() === '') {
      message.error('没有数据可导出');
      return;
    }

    // 解析extrapolate格式的markdown内容
    const parseResult = parseExtrapolateMarkdown(markdownContent);
    const { questions, columnNames } = parseResult;

    if (questions.length === 0) {
      console.error('Extrapolate解析失败，未找到有效的题目数据');
      console.log('Extrapolate 原始内容长度:', markdownContent.length);
      console.log('Extrapolate 原始内容预览:', markdownContent.slice(0, 500));
      message.error('未找到有效的题目数据，请检查markdown格式是否正确');
      return;
    }

    // 使用动态解析出的列名作为表头
    const headers = columnNames;

    // 将数据转换为二维数组格式
    const data = [
      headers,
      ...questions.map(
        (question) => headers.map((header) => question[header] || ''), // 动态填充每列数据
      ),
    ];

    // 创建工作簿和工作表
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // 动态设置列宽
    const columnWidths = headers.map((header) => {
      // 根据列名设置不同的宽度
      if (header.includes('题目') || header === '题目内容') {
        return { wch: 50 }; // 题目内容列加宽
      } else if (header === '解析') {
        return { wch: 40 }; // 解析列加宽
      } else if (header.startsWith('答案')) {
        return { wch: 25 }; // 答案列
      } else if (header === '正确答案') {
        return { wch: 15 }; // 正确答案列
      } else if (header.includes('难度') || header === '难易度') {
        return { wch: 8 }; // 难度列
      } else {
        return { wch: 15 }; // 默认宽度
      }
    });
    worksheet['!cols'] = columnWidths;

    // 设置单元格样式（表头加粗）
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let col = range.s.c; col <= range.e.c; col++) {
      const headerCell = worksheet[XLSX.utils.encode_cell({ r: 0, c: col })];
      if (headerCell) {
        headerCell.s = {
          font: { bold: true },
          alignment: { horizontal: 'center' },
        };
      }
    }

    // 设置数据行的自动换行
    for (let row = 1; row <= questions.length; row++) {
      for (let col = 0; col < headers.length; col++) {
        const cell = worksheet[XLSX.utils.encode_cell({ r: row, c: col })];
        if (cell) {
          const header = headers[col];
          cell.s = {
            ...cell.s,
            alignment: {
              wrapText: true,
              vertical: 'top',
              // 题目内容左对齐，其他居中
              horizontal:
                header.includes('题目') || header === '题目内容'
                  ? 'left'
                  : 'center',
            },
          };
        }
      }
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Extrapolate题目');

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      cellStyles: true,
    });

    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    const finalFileName = fileName.endsWith('.xlsx')
      ? fileName
      : `${fileName}.xlsx`;
    saveAs(blob, finalFileName);

    message.success(`成功导出 ${questions.length} 道题目到 ${finalFileName}`);

    // 输出调试信息
    console.log('Extrapolate导出成功:', {
      总题目数: questions.length,
      检测到的列: columnNames,
      文件名: finalFileName,
      数据预览: questions.slice(0, 2),
    });
  } catch (error) {
    console.error('导出Excel失败 (Extrapolate):', error);
    message.error('导出Excel文件失败');
  }
};
