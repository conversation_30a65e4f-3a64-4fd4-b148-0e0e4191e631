// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/login","layout":false,"id":"1"},"2":{"path":"/register","layout":false,"id":"2"},"3":{"path":"/login-next","layout":false,"id":"3"},"4":{"path":"/chat/share","layout":false,"id":"4"},"5":{"path":"","layout":false,"parentId":"6","id":"5","originPath":"/"},"6":{"path":"/","isWrapper":true,"layout":false,"id":"6"},"7":{"path":"/","redirect":"/workbench","parentId":"5","id":"7"},"8":{"path":"/knowledge","parentId":"5","id":"8"},"9":{"path":"/knowledge-dataset","parentId":"5","id":"9"},"10":{"path":"/workbench","parentId":"5","id":"10"},"11":{"path":"/ask","parentId":"5","id":"11"},"12":{"path":"/train","parentId":"5","id":"12"},"13":{"path":"/training-method","parentId":"5","id":"13"},"14":{"path":"/data-training","parentId":"5","id":"14"},"15":{"path":"/inspect","parentId":"5","id":"15"},"16":{"path":"/retrieval-test","parentId":"5","id":"16"},"17":{"path":"/query-test","parentId":"5","id":"17"},"18":{"path":"/api-management","parentId":"5","id":"18"},"19":{"path":"/knowledge","parentId":"5","id":"19"},"20":{"path":"/knowledge/dataset","parentId":"19","id":"20"},"21":{"path":"/knowledge/dataset","parentId":"20","id":"21"},"22":{"path":"/knowledge/dataset/chunk","parentId":"20","id":"22"},"23":{"path":"/knowledge/configuration","parentId":"19","id":"23"},"24":{"path":"/knowledge/testing","parentId":"19","id":"24"},"25":{"path":"/knowledge/knowledgeGraph","parentId":"19","id":"25"},"26":{"path":"/data-training","parentId":"5","id":"26"},"27":{"path":"/data-training/dataset","parentId":"26","id":"27"},"28":{"path":"/data-training/dataset","parentId":"27","id":"28"},"29":{"path":"/data-training/dataset/chunk","parentId":"27","id":"29"},"30":{"path":"/data-training/configuration","parentId":"26","id":"30"},"31":{"path":"/data-training/testing","parentId":"26","id":"31"},"32":{"path":"/data-training/knowledgeGraph","parentId":"26","id":"32"},"33":{"path":"/chat","parentId":"5","id":"33"},"34":{"path":"/user-profile","parentId":"5","id":"34"},"35":{"path":"/user-setting","parentId":"5","id":"35"},"36":{"path":"/user-setting","redirect":"/user-setting/profile","parentId":"35","id":"36"},"37":{"path":"/user-setting/profile","parentId":"35","id":"37"},"38":{"path":"/user-setting/locale","parentId":"35","id":"38"},"39":{"path":"/user-setting/password","parentId":"35","id":"39"},"40":{"path":"/user-setting/model","parentId":"35","id":"40"},"41":{"path":"/user-setting/team","parentId":"35","id":"41"},"42":{"path":"/user-setting/system","parentId":"35","id":"42"},"43":{"path":"/user-setting/api","parentId":"35","id":"43"},"44":{"path":"/system-setting","parentId":"5","id":"44"},"45":{"path":"/system-setting/group-member","parentId":"5","id":"45"},"46":{"path":"/system-setting/model-setting","parentId":"5","id":"46"},"47":{"path":"/file","parentId":"5","id":"47"},"48":{"path":"/flow","parentId":"5","id":"48"},"49":{"path":"/flow/:id","parentId":"5","id":"49"},"50":{"path":"/search","parentId":"5","id":"50"},"51":{"path":"/document/:id","layout":false,"id":"51"},"52":{"path":"/*","layout":false,"id":"52"},"53":{"path":"/home","layout":false,"id":"53"},"54":{"path":"/home","parentId":"53","id":"54"},"55":{"path":"/datasets","layout":false,"id":"55"},"56":{"path":"/datasets","parentId":"55","id":"56"},"57":{"path":"/next-chats","layout":false,"id":"57"},"58":{"path":"/next-chats","parentId":"57","id":"58"},"59":{"path":"/next-chat","layout":false,"id":"59"},"60":{"path":"/next-searches","layout":false,"id":"60"},"61":{"path":"/next-searches","parentId":"60","id":"61"},"62":{"path":"/next-search","layout":false,"id":"62"},"63":{"path":"/agents","layout":false,"id":"63"},"64":{"path":"/agents","parentId":"63","id":"64"},"65":{"path":"/agent/:id","layout":false,"id":"65"},"66":{"path":"/agent-templates","layout":false,"id":"66"},"67":{"path":"/files","layout":false,"id":"67"},"68":{"path":"/files","parentId":"67","id":"68"},"69":{"path":"/resource","layout":false,"id":"69"},"70":{"path":"/resource","parentId":"69","id":"70"},"71":{"path":"/dataset","layout":false,"id":"71"},"72":{"path":"/dataset","redirect":"/dataset/dataset","parentId":"71","id":"72"},"73":{"path":"/dataset","layout":false,"id":"73"},"74":{"path":"/dataset/dataset/:id","parentId":"73","id":"74"},"75":{"path":"/dataset/setting/:id","parentId":"73","id":"75"},"76":{"path":"/dataset/testing/:id","parentId":"73","id":"76"},"77":{"path":"/chunk","layout":false,"id":"77"},"78":{"path":"/chunk","parentId":"77","id":"78"},"79":{"path":"/chunk/parsed/:id","parentId":"78","id":"79"},"80":{"path":"/chunk/chunk/:id","parentId":"78","id":"80"},"81":{"path":"/chunk/result/:id","parentId":"78","id":"81"},"82":{"path":"/chunk","layout":false,"id":"82"},"83":{"path":"/profile-setting","layout":false,"id":"83"},"84":{"path":"/profile-setting","redirect":"/profile-setting/profile","parentId":"83","id":"84"},"85":{"path":"/profile-setting/profile","parentId":"83","id":"85"},"86":{"path":"/profile-setting/team","parentId":"83","id":"86"},"87":{"path":"/profile-setting/plan","parentId":"83","id":"87"},"88":{"path":"/profile-setting/model","parentId":"83","id":"88"},"89":{"path":"/profile-setting/prompt","parentId":"83","id":"89"},"90":{"path":"/agent/:id","parentId":"@@/global-layout","id":"90"},"91":{"path":"/agent/ai-question","parentId":"@@/global-layout","id":"91"},"92":{"path":"/agent/lesson-plan","parentId":"@@/global-layout","id":"92"},"93":{"path":"/agent/smart-exam","parentId":"@@/global-layout","id":"93"},"94":{"path":"/agent/extrapolate","parentId":"@@/global-layout","id":"94"},"@@/global-layout":{"id":"@@/global-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__login__index" */'@/pages/login/index.tsx')),
'2': React.lazy(() => import(/* webpackChunkName: "p__register__index" */'@/pages/register/index.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__login-next__index" */'@/pages/login-next/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__chat__share__index" */'@/pages/chat/share/index.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'@/layouts/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "wrappers__auth" */'@/wrappers/auth.tsx')),
'7': React.lazy(() => import('./EmptyRoute')),
'8': React.lazy(() => import(/* webpackChunkName: "p__knowledge__index" */'@/pages/knowledge/index.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__resource__index" */'@/pages/resource/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__workbench__index" */'@/pages/workbench/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__chat__index" */'@/pages/chat/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__train__index" */'@/pages/train/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__training-method__index" */'@/pages/training-method/index.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__data-training__index" */'@/pages/data-training/index.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__inspect__index" */'@/pages/inspect/index.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__retrieval-test__index" */'@/pages/retrieval-test/index.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__query-test__index" */'@/pages/query-test/index.tsx')),
'18': React.lazy(() => import(/* webpackChunkName: "p__api-management__index" */'@/pages/api-management/index.tsx')),
'19': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__index" */'@/pages/add-knowledge/index.tsx')),
'20': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-dataset__index" */'@/pages/add-knowledge/components/knowledge-dataset/index.tsx')),
'21': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-file__index" */'@/pages/add-knowledge/components/knowledge-file/index.tsx')),
'22': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-chunk__index" */'@/pages/add-knowledge/components/knowledge-chunk/index.tsx')),
'23': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-setting__index" */'@/pages/add-knowledge/components/knowledge-setting/index.tsx')),
'24': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-testing__index" */'@/pages/add-knowledge/components/knowledge-testing/index.tsx')),
'25': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-graph__index" */'@/pages/add-knowledge/components/knowledge-graph/index.tsx')),
'26': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__index" */'@/pages/add-knowledge/index.tsx')),
'27': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-dataset__index" */'@/pages/add-knowledge/components/knowledge-dataset/index.tsx')),
'28': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-file__index" */'@/pages/add-knowledge/components/knowledge-file/index.tsx')),
'29': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-chunk__index" */'@/pages/add-knowledge/components/knowledge-chunk/index.tsx')),
'30': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-setting__index" */'@/pages/add-knowledge/components/knowledge-setting/index.tsx')),
'31': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-testing__index" */'@/pages/add-knowledge/components/knowledge-testing/index.tsx')),
'32': React.lazy(() => import(/* webpackChunkName: "p__add-knowledge__components__knowledge-graph__index" */'@/pages/add-knowledge/components/knowledge-graph/index.tsx')),
'33': React.lazy(() => import(/* webpackChunkName: "p__chat__index" */'@/pages/chat/index.tsx')),
'34': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-profile__index" */'@/pages/user-setting/setting-profile/index.tsx')),
'35': React.lazy(() => import(/* webpackChunkName: "p__user-setting__index" */'@/pages/user-setting/index.tsx')),
'36': React.lazy(() => import('./EmptyRoute')),
'37': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-profile__index" */'@/pages/user-setting/setting-profile/index.tsx')),
'38': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-locale__index" */'@/pages/user-setting/setting-locale/index.tsx')),
'39': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-password__index" */'@/pages/user-setting/setting-password/index.tsx')),
'40': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-model__index" */'@/pages/user-setting/setting-model/index.tsx')),
'41': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-team__index" */'@/pages/user-setting/setting-team/index.tsx')),
'42': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-system__index" */'@/pages/user-setting/setting-system/index.tsx')),
'43': React.lazy(() => import(/* webpackChunkName: "p__user-setting__setting-api__index" */'@/pages/user-setting/setting-api/index.tsx')),
'44': React.lazy(() => import(/* webpackChunkName: "p__system-setting__index" */'@/pages/system-setting/index.tsx')),
'45': React.lazy(() => import(/* webpackChunkName: "p__system-setting__index" */'@/pages/system-setting/index.tsx')),
'46': React.lazy(() => import(/* webpackChunkName: "p__system-setting__index" */'@/pages/system-setting/index.tsx')),
'47': React.lazy(() => import(/* webpackChunkName: "p__file-manager__index" */'@/pages/file-manager/index.tsx')),
'48': React.lazy(() => import(/* webpackChunkName: "p__flow__list__index" */'@/pages/flow/list/index.tsx')),
'49': React.lazy(() => import(/* webpackChunkName: "p__flow__index" */'@/pages/flow/index.tsx')),
'50': React.lazy(() => import(/* webpackChunkName: "p__search__index" */'@/pages/search/index.tsx')),
'51': React.lazy(() => import(/* webpackChunkName: "p__document-viewer__index" */'@/pages/document-viewer/index.tsx')),
'52': React.lazy(() => import(/* webpackChunkName: "p__404" */'@/pages/404.jsx')),
'53': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'54': React.lazy(() => import(/* webpackChunkName: "p__home__index" */'@/pages/home/<USER>')),
'55': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'56': React.lazy(() => import(/* webpackChunkName: "p__datasets__index" */'@/pages/datasets/index.tsx')),
'57': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'58': React.lazy(() => import(/* webpackChunkName: "p__next-chats__index" */'@/pages/next-chats/index.tsx')),
'59': React.lazy(() => import(/* webpackChunkName: "p__next-chats__chat__index" */'@/pages/next-chats/chat/index.tsx')),
'60': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'61': React.lazy(() => import(/* webpackChunkName: "p__next-searches__index" */'@/pages/next-searches/index.tsx')),
'62': React.lazy(() => import(/* webpackChunkName: "p__next-search__index" */'@/pages/next-search/index.tsx')),
'63': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'64': React.lazy(() => import(/* webpackChunkName: "p__agents__index" */'@/pages/agents/index.tsx')),
'65': React.lazy(() => import(/* webpackChunkName: "p__agent__index" */'@/pages/agent/index.tsx')),
'66': React.lazy(() => import(/* webpackChunkName: "p__agents__agent-templates" */'@/pages/agents/agent-templates.tsx')),
'67': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'68': React.lazy(() => import(/* webpackChunkName: "p__files__index" */'@/pages/files/index.tsx')),
'69': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'70': React.lazy(() => import(/* webpackChunkName: "p__resource__index" */'@/pages/resource/index.tsx')),
'71': React.lazy(() => import(/* webpackChunkName: "layouts__next" */'@/layouts/next.tsx')),
'72': React.lazy(() => import('./EmptyRoute')),
'73': React.lazy(() => import(/* webpackChunkName: "p__dataset__index" */'@/pages/dataset/index.tsx')),
'74': React.lazy(() => import(/* webpackChunkName: "p__dataset__dataset__index" */'@/pages/dataset/dataset/index.tsx')),
'75': React.lazy(() => import(/* webpackChunkName: "p__dataset__setting__index" */'@/pages/dataset/setting/index.tsx')),
'76': React.lazy(() => import(/* webpackChunkName: "p__dataset__testing__index" */'@/pages/dataset/testing/index.tsx')),
'77': React.lazy(() => import('./EmptyRoute')),
'78': React.lazy(() => import(/* webpackChunkName: "p__chunk__index" */'@/pages/chunk/index.tsx')),
'79': React.lazy(() => import(/* webpackChunkName: "p__chunk__parsed-result__index" */'@/pages/chunk/parsed-result/index.tsx')),
'80': React.lazy(() => import(/* webpackChunkName: "p__chunk__chunk-result__index" */'@/pages/chunk/chunk-result/index.tsx')),
'81': React.lazy(() => import(/* webpackChunkName: "p__chunk__result-view__index" */'@/pages/chunk/result-view/index.tsx')),
'82': React.lazy(() => import(/* webpackChunkName: "p__chunk__index" */'@/pages/chunk/index.tsx')),
'83': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__index" */'@/pages/profile-setting/index.tsx')),
'84': React.lazy(() => import('./EmptyRoute')),
'85': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__profile__index" */'@/pages/profile-setting/profile/index.tsx')),
'86': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__team__index" */'@/pages/profile-setting/team/index.tsx')),
'87': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__plan__index" */'@/pages/profile-setting/plan/index.tsx')),
'88': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__model__index" */'@/pages/profile-setting/model/index.tsx')),
'89': React.lazy(() => import(/* webpackChunkName: "p__profile-setting__prompt__index" */'@/pages/profile-setting/prompt/index.tsx')),
'90': React.lazy(() => import(/* webpackChunkName: "p__agent__index" */'@/pages/agent/index.tsx')),
'91': React.lazy(() => import(/* webpackChunkName: "p__agent__ai-question__index" */'@/pages/agent/ai-question/index.tsx')),
'92': React.lazy(() => import(/* webpackChunkName: "p__agent__lesson-plan__index" */'@/pages/agent/lesson-plan/index.tsx')),
'93': React.lazy(() => import(/* webpackChunkName: "p__agent__smart-exam__index" */'@/pages/agent/smart-exam/index.tsx')),
'94': React.lazy(() => import(/* webpackChunkName: "p__agent__extrapolate__index" */'@/pages/agent/extrapolate/index.tsx')),
'@@/global-layout': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'D:/Workspace/yontenet/ragflow_windsurf/web/src/layouts/index.tsx')),
},
  };
}
