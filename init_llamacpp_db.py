#!/usr/bin/env python3
"""
初始化 Llama.cpp 数据库配置
这个脚本需要在 RAGFlow 的 Docker 容器内运行
"""

import os
import sys

def init_llamacpp_db():
    """初始化 Llama.cpp 数据库配置"""
    
    print("初始化 Llama.cpp 数据库配置...")
    
    # 这个脚本应该在 RAGFlow 容器内运行
    # 用户可以通过以下命令执行：
    # docker exec -it ragflow-container python /ragflow/init_llamacpp_db.py
    
    try:
        # 设置环境变量
        os.environ.setdefault('PYTHONPATH', '/ragflow')
        
        # 导入 RAGFlow 模块
        sys.path.insert(0, '/ragflow')
        
        from api.db.services.llm_service import LLMService, LLMFactoriesService
        from api.db import StatusEnum
        
        print("1. 检查 Llama.cpp 工厂是否存在...")
        
        # 检查工厂是否存在
        factories = LLMFactoriesService.query(name="Llama.cpp")
        if not factories:
            print("   添加 Llama.cpp 工厂...")
            LLMFactoriesService.save(
                name="Llama.cpp",
                logo="",
                tags="LLM,TEXT EMBEDDING",
                status="1"
            )
            print("   ✓ Llama.cpp 工厂已添加")
        else:
            print("   ✓ Llama.cpp 工厂已存在")
        
        print("2. 检查 Llama.cpp 模型是否存在...")
        
        # 检查模型是否存在
        models = LLMService.query(fid="Llama.cpp")
        if not models:
            print("   添加 Llama.cpp 模型...")
            
            # 添加 embedding 模型
            LLMService.save(
                llm_name="llama-cpp-embedding",
                model_type="embedding",
                fid="Llama.cpp",
                max_tokens=8192,
                tags="TEXT EMBEDDING,8K",
                is_tools=False,
                status="1"
            )
            
            # 添加 chat 模型
            LLMService.save(
                llm_name="llama-cpp-chat",
                model_type="chat",
                fid="Llama.cpp",
                max_tokens=8192,
                tags="LLM,CHAT,8K",
                is_tools=False,
                status="1"
            )
            
            print("   ✓ Llama.cpp 模型已添加")
        else:
            print(f"   ✓ 找到 {len(models)} 个 Llama.cpp 模型")
        
        print("3. 数据库初始化完成！")
        print("\n现在用户可以在前端配置 Llama.cpp 模型了。")
        
        return True
        
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def print_usage():
    """打印使用说明"""
    
    print("""
使用说明：

这个脚本需要在 RAGFlow 的 Docker 容器内运行。

1. 找到 RAGFlow 容器：
   docker ps | grep ragflow

2. 进入容器并运行脚本：
   docker exec -it <container_name> python /ragflow/init_llamacpp_db.py

3. 或者将脚本复制到容器内：
   docker cp init_llamacpp_db.py <container_name>:/ragflow/
   docker exec -it <container_name> python /ragflow/init_llamacpp_db.py

4. 运行完成后，重启 RAGFlow 服务：
   docker restart <container_name>

注意：
- 请确保 RAGFlow 数据库服务正在运行
- 如果遇到权限问题，可能需要使用 root 用户运行
""")

if __name__ == "__main__":
    # 检查是否在容器内
    if os.path.exists('/ragflow'):
        success = init_llamacpp_db()
        sys.exit(0 if success else 1)
    else:
        print("错误：此脚本需要在 RAGFlow Docker 容器内运行")
        print_usage()
        sys.exit(1)
