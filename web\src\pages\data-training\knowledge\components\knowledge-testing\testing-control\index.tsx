import Rerank from '@/components/rerank';
import SimilaritySlider from '@/components/similarity-slider';
import { useTranslate } from '@/hooks/common-hooks';
import { useChunkIsTesting } from '@/hooks/knowledge-hooks';
import { ExperimentOutlined, SendOutlined } from '@ant-design/icons';
import { Button, Card, Divider, Flex, Form, Input, Typography } from 'antd';
import { FormInstance } from 'antd/lib';
import { LabelWordCloud } from './label-word-cloud';

import { CrossLanguageItem } from '@/components/cross-language-item';
import { UseKnowledgeGraphItem } from '@/components/use-knowledge-graph-item';
import styles from './index.less';

type FieldType = {
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  question: string;
};

interface IProps {
  form: FormInstance;
  handleTesting: () => Promise<any>;
}

const { Title, Paragraph, Text } = Typography;

const TestingControl = ({ form, handleTesting }: IProps) => {
  const question = Form.useWatch('question', { form, preserve: true });
  const loading = useChunkIsTesting();
  const { t } = useTranslate('knowledgeDetails');

  const buttonDisabled =
    !question || (typeof question === 'string' && question.trim() === '');

  return (
    <section className={styles.testingControlWrapper}>
      <div className={styles.titleSection}>
        <Title level={4} className={styles.title}>
          <ExperimentOutlined style={{ marginRight: 8 }} />
          {t('testing')}
        </Title>
        <Paragraph className={styles.description}>
          {t('testingDescription')}
        </Paragraph>
      </div>

      <Divider style={{ margin: '12px 0' }} />

      <section className={styles.formSection}>
        <Form
          name="testing"
          layout="vertical"
          form={form}
          initialValues={{
            similarity_threshold: 0.7,
            vector_similarity_weight: 0.5,
          }}
        >
          <SimilaritySlider isTooltipShown />
          <Rerank />
          <UseKnowledgeGraphItem filedName={['use_kg']} />
          <CrossLanguageItem name={'cross_languages'} />

          <Card
            size="small"
            title={t('testText')}
            className={styles.testCard}
            bordered
          >
            <Form.Item<FieldType>
              name={'question'}
              rules={[{ required: true, message: t('testTextPlaceholder') }]}
            >
              <Input.TextArea
                placeholder={t('testTextPlaceholder')}
                autoSize={{ minRows: 6 }}
                className={styles.testTextarea}
              />
            </Form.Item>

            <Flex justify="end">
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleTesting}
                disabled={buttonDisabled}
                loading={loading}
                className={styles.testButton}
              >
                {t('testingLabel')}
              </Button>
            </Flex>
          </Card>
        </Form>
      </section>

      <div className={styles.labelSection}>
        <Title level={5}>Term Distribution</Title>
        <LabelWordCloud />
      </div>
    </section>
  );
};

export default TestingControl;
