#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from flask import request
from api.db.services.conversation_service import ConversationService
from flask_login import login_required, current_user
from api.db.services.dialog_service import DialogService
from api.db import StatusEnum
from api.db.services.llm_service import TenantLLMService
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.user_service import TenantService, UserTenantService
from api import settings
from api.utils.api_utils import server_error_response, get_data_error_result, validate_request
from api.utils import get_uuid
from api.utils.api_utils import get_json_result
from api.utils.tenant_utils import get_tenant_with_fallback, user_accessible_dialog
from api.db.db_models import Dialog, Conversation, DB
import json
import os


@manager.route('/set', methods=['POST'])  # noqa: F821
@login_required
def set_dialog():
    req = request.json
    dialog_id = req.get("dialog_id")
    name = req.get("name", "New Dialog")
    description = req.get("description", "A helpful dialog")
    icon = req.get("icon", "")
    top_n = req.get("top_n", 6)
    top_k = req.get("top_k", 1024)
    rerank_id = req.get("rerank_id", "")
    if not rerank_id:
        req["rerank_id"] = ""
    similarity_threshold = req.get("similarity_threshold", 0.1)
    vector_similarity_weight = req.get("vector_similarity_weight", 0.3)
    llm_setting = req.get("llm_setting", {})
    default_prompt = {
        "system": """你是一个智能助手，请总结知识库的内容来回答问题，请列举知识库中的数据详细回答。当所有知识库内容都与问题无关时，你的回答必须包括"知识库中未找到您要的答案！"这句话。回答需要考虑聊天历史。
以下是知识库：
{knowledge}
以上是知识库。""",
        "prologue": "您好，我是您的助手小樱，长得可爱又善良，can I help you?",
        "parameters": [
            {"key": "knowledge", "optional": False}
        ],
        "empty_response": "Sorry! 知识库中未找到相关内容！"
    }
    prompt_config = req.get("prompt_config", default_prompt)

    if not prompt_config["system"]:
        prompt_config["system"] = default_prompt["system"]

    for p in prompt_config["parameters"]:
        if p["optional"]:
            continue
        if prompt_config["system"].find("{%s}" % p["key"]) < 0:
            return get_data_error_result(
                message="Parameter '{}' is not used".format(p["key"]))

    try:
        success, tenant = get_tenant_with_fallback(current_user.id)
        if not success:
            return get_data_error_result(message="Tenant not found!")
        kbs = KnowledgebaseService.get_by_ids(req.get("kb_ids", []))
        embd_ids = [TenantLLMService.split_model_name_and_factory(
            # remove vendor suffix for comparison
            kb.embd_id)[0] for kb in kbs]
        embd_count = len(set(embd_ids))
        if embd_count > 1:
            return get_data_error_result(message=f'Datasets use different embedding models: {[kb.embd_id for kb in kbs]}"')

        llm_id = req.get("llm_id", tenant.llm_id)
        if not dialog_id:
            dia = {
                "id": get_uuid(),
                "tenant_id": tenant.id,
                "name": name,
                "kb_ids": req.get("kb_ids", []),
                "description": description,
                "llm_id": llm_id,
                "llm_setting": llm_setting,
                "prompt_config": prompt_config,
                "top_n": top_n,
                "top_k": top_k,
                "rerank_id": rerank_id,
                "similarity_threshold": similarity_threshold,
                "vector_similarity_weight": vector_similarity_weight,
                "icon": icon
            }
            if not DialogService.save(**dia):
                return get_data_error_result(message="Fail to new a dialog!")
            return get_json_result(data=dia)
        else:
            del req["dialog_id"]
            if "kb_names" in req:
                del req["kb_names"]
            if not DialogService.update_by_id(dialog_id, req):
                return get_data_error_result(message="Dialog not found!")
            e, dia = DialogService.get_by_id(dialog_id)
            if not e:
                return get_data_error_result(message="Fail to update a dialog!")
            dia = dia.to_dict()
            dia.update(req)
            dia["kb_ids"], dia["kb_names"] = get_kb_names(dia["kb_ids"])
            return get_json_result(data=dia)
    except Exception as e:
        return server_error_response(e)


@manager.route('/get', methods=['GET'])  # noqa: F821
@login_required
def get():
    dialog_id = request.args["dialog_id"]
    try:
        # Check if user has access to this dialog
        if not user_accessible_dialog(current_user.id, dialog_id):
            return get_json_result(data=False, message="Only owner of dialog authorized for this operation.", code=settings.RetCode.OPERATING_ERROR)

        e, dia = DialogService.get_by_id(dialog_id)
        if not e:
            return get_data_error_result(message="Dialog not found!")
        dia = dia.to_dict()
        dia["kb_ids"], dia["kb_names"] = get_kb_names(dia["kb_ids"])
        return get_json_result(data=dia)
    except Exception as e:
        return server_error_response(e)


def get_kb_names(kb_ids):
    """Get knowledge base names from IDs."""
    if not kb_ids:
        return [], []

    kbs = KnowledgebaseService.get_by_ids(kb_ids)
    valid_kb_ids = []
    kb_names = []

    for kb in kbs:
        valid_kb_ids.append(kb.id)
        kb_names.append(kb.name)

    return valid_kb_ids, kb_names


def query_user_accessible_dialogs(tenant_id, user_id):
    """
    Query dialogs that are accessible to the user through join with conversation table.

    Args:
        tenant_id: The tenant ID to filter dialogs
        user_id: The user ID to filter conversations

    Returns:
        List of dialog dictionaries that the user has access to
    """
    with DB.connection_context():
        # Join Dialog and Conversation tables to get dialogs that have conversations for this user
        query = (Dialog
                 .select(Dialog)
                 .join(Conversation, on=(Dialog.id == Conversation.dialog_id))
                 .where(
                     (Dialog.tenant_id == tenant_id) &
                     (Dialog.status == StatusEnum.VALID.value) &
                     (Conversation.user_id == user_id)
                 )
                 .distinct()
                 .order_by(Dialog.create_time.desc()))

        return [d.to_dict() for d in query]


@manager.route('/list', methods=['GET'])  # noqa: F821
@login_required
def list_dialogs():
    try:
        # 获取当前租户信息
        success, tenant = get_tenant_with_fallback(current_user.id)
        if not success:
            return get_data_error_result(message="Tenant not found!")

        # 查询用户可访问的dialogs（通过连接查询）
        diags = query_user_accessible_dialogs(tenant.id, current_user.id)

        # 如果没有dialog，创建默认的铁路电务知识专家助手
        if len(diags) == 0:
            # 加载assistant.json配置
            assistant_config_path = os.path.join(
                os.path.dirname(__file__), 'preset', 'assistant.json')
            with open(assistant_config_path, 'r', encoding='utf-8') as f:
                assistant_config = json.load(f)

            # 查询当前租户的所有知识库ID
            kb_ids = []  # KnowledgebaseService.get_kb_ids(tenant.id)

            # 填充kb_ids到assistant配置中
            assistant_config["kb_ids"] = kb_ids

            # 创建默认dialog
            default_dialog = {
                "id": get_uuid(),
                "tenant_id": tenant.id,
                "name": assistant_config["name"],
                "kb_ids": assistant_config["kb_ids"],
                "description": assistant_config["description"],
                "llm_id": assistant_config.get("llm_id", tenant.llm_id),
                "llm_setting": assistant_config.get("llm_setting", {}),
                "prompt_config": assistant_config["prompt_config"],
                "top_n": assistant_config.get("top_n", 6),
                "top_k": assistant_config.get("top_k", 1024),
                "rerank_id": "",
                "similarity_threshold": assistant_config.get("similarity_threshold", 0.1),
                "vector_similarity_weight": assistant_config.get("vector_similarity_weight", 0.3),
                "icon": assistant_config.get("icon", "")
            }

            # 保存默认dialog
            if not DialogService.save(**default_dialog):
                return get_data_error_result(message="Failed to create default dialog!")

            # 创建默认conversation以建立用户与dialog的关联
            default_conversation = {
                "id": get_uuid(),
                "dialog_id": default_dialog["id"],
                "user_id": current_user.id,
                "name": "新会话",
                "message": [{"role": "assistant", "content": assistant_config["prompt_config"]["prologue"]}]
            }
            if not ConversationService.save(**default_conversation):
                return get_data_error_result(message="Failed to create default conversation!")

            # 重新查询用户可访问的dialogs
            diags = query_user_accessible_dialogs(tenant.id, current_user.id)

        # 为所有dialogs填充kb_names
        for d in diags:
            d["kb_ids"], d["kb_names"] = get_kb_names(d["kb_ids"])
        return get_json_result(data=diags)
    except Exception as e:
        return server_error_response(e)


@manager.route('/rm', methods=['POST'])  # noqa: F821
@login_required
@validate_request("dialog_ids")
def rm():
    req = request.json
    dialog_list = []
    tenants = UserTenantService.query(user_id=current_user.id)
    try:
        for id in req["dialog_ids"]:
            for tenant in tenants:
                if DialogService.query(tenant_id=tenant.tenant_id, id=id):
                    break
            else:
                return get_json_result(
                    data=False, message='Only owner of dialog authorized for this operation.',
                    code=settings.RetCode.OPERATING_ERROR)
            dialog_list.append({"id": id, "status": StatusEnum.INVALID.value})
        DialogService.update_many_by_id(dialog_list)
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)
