from __future__ import annotations

"""Extrapolate (举一反三) API service – streaming variant question generation."""

import json
import logging
import time
import traceback
import uuid
from typing import Dict, Generator, Optional, Tuple

from flask import Request, Response

from api.utils.api_utils import (
    get_data_error_result,
    get_data_from_request,
    get_json_result,
    server_error_response,
)
from api.utils.tenant_utils import get_tenant_with_fallback
from agent.extrapolate import ExtrapolateParam, ExtrapolateContext
from agent.extrapolate.file_parser import SampleFileParser
from agent.extrapolate.generator import ExtrapolateGenerator
from agent.common_exam.streamer import Streamer
from api.db.services.llm_service import LLMBundle
from api.db import LLMType
from agent.canvas import Canvas
from api.services.lesson_plan_v2_service import SSEStreamBuffer, MinimalCanvas


class ExtrapolatePerformanceMonitor:
    """Performance monitoring and structured logging for ExtrapolateService."""

    def __init__(self, user_id: str, tenant_id: Optional[str] = None, session_id: Optional[str] = None):
        self.user_id = user_id
        self.tenant_id = tenant_id
        self.session_id = session_id
        self.start_time = time.time()
        self.metrics = {
            'request_start_time': self.start_time,
            'file_upload_time': None,
            'embedding_wait_time': None,
            'generation_start_time': None,
            'generation_end_time': None,
            'total_chunks': 0,
            'total_content_length': 0,
            'error_count': 0,
            'consecutive_errors': 0,
            'embedding_checks': 0,
            'embedding_errors': 0,
            'think_blocks_filtered': 0,
            'buffer_flushes': 0,
            'cleanup_scheduled': False
        }

    def log_file_upload_start(self, filename: str, file_size: int):
        """Log structured information about file upload start."""
        logging.info(
            "[Extrapolate] File upload started - user_id=%s, session_id=%s, filename=%s, size=%d",
            self.user_id, self.session_id, filename, file_size
        )

    def log_file_upload_complete(self, duration: float, content_length: int, kb_id: str, doc_id: str):
        """Log structured information about successful file upload."""
        self.metrics['file_upload_time'] = duration
        logging.info(
            "[Extrapolate] File upload completed - user_id=%s, session_id=%s, duration=%.2fs, content_length=%d, kb_id=%s, doc_id=%s",
            self.user_id, self.session_id, duration, content_length, kb_id, doc_id
        )

    def log_file_upload_error(self, duration: float, error: Exception, filename: str = None):
        """Log structured information about file upload errors."""
        self.metrics['error_count'] += 1
        error_type = type(error).__name__
        logging.error(
            "[Extrapolate] File upload failed - user_id=%s, session_id=%s, duration=%.2fs, filename=%s, error_type=%s, error=%s",
            self.user_id, self.session_id, duration, filename, error_type, str(error), exc_info=True
        )

    def log_embedding_wait_start(self, kb_id: str, max_wait_time: int):
        """Log structured information about embedding wait start."""
        logging.info(
            "[Extrapolate] Embedding wait started - user_id=%s, session_id=%s, kb_id=%s, max_wait_time=%ds",
            self.user_id, self.session_id, kb_id, max_wait_time
        )

    def log_embedding_wait_complete(self, duration: float, kb_id: str, check_count: int):
        """Log structured information about successful embedding completion."""
        self.metrics['embedding_wait_time'] = duration
        self.metrics['embedding_checks'] = check_count
        logging.info(
            "[Extrapolate] Embedding completed - user_id=%s, session_id=%s, kb_id=%s, duration=%.2fs, checks=%d",
            self.user_id, self.session_id, kb_id, duration, check_count
        )

    def log_embedding_wait_timeout(self, duration: float, kb_id: str, check_count: int, error_count: int):
        """Log structured information about embedding timeout."""
        self.metrics['embedding_wait_time'] = duration
        self.metrics['embedding_checks'] = check_count
        self.metrics['embedding_errors'] = error_count
        logging.warning(
            "[Extrapolate] Embedding timeout - user_id=%s, session_id=%s, kb_id=%s, duration=%.2fs, checks=%d, errors=%d",
            self.user_id, self.session_id, kb_id, duration, check_count, error_count
        )

    def log_generation_start(self, llm_id: str, quantity: int, sample_content_length: int):
        """Log structured information about generation start."""
        self.metrics['generation_start_time'] = time.time()
        logging.info(
            "[Extrapolate] Generation started - user_id=%s, session_id=%s, tenant_id=%s, llm_id=%s, quantity=%d, sample_length=%d",
            self.user_id, self.session_id, self.tenant_id or "unknown", llm_id, quantity, sample_content_length
        )

    def log_generation_complete(self):
        """Log structured information about successful generation completion."""
        self.metrics['generation_end_time'] = time.time()
        generation_duration = self.metrics['generation_end_time'] - \
            self.metrics['generation_start_time']
        total_duration = self.metrics['generation_end_time'] - \
            self.metrics['request_start_time']

        logging.info(
            "[Extrapolate] Generation completed - user_id=%s, session_id=%s, generation_duration=%.2fs, total_duration=%.2fs, chunks=%d, content_length=%d, errors=%d",
            self.user_id, self.session_id, generation_duration, total_duration,
            self.metrics['total_chunks'], self.metrics['total_content_length'], self.metrics['error_count']
        )

    def log_chunk_processed(self, chunk_length: int, chunk_number: int, think_blocks_filtered: bool = False):
        """Log structured information about chunk processing."""
        self.metrics['total_chunks'] += 1
        self.metrics['total_content_length'] += chunk_length
        if think_blocks_filtered:
            self.metrics['think_blocks_filtered'] += 1

        # Log every 10th chunk for monitoring without spam
        if chunk_number % 10 == 0:
            logging.debug(
                "[Extrapolate] Chunk processed - user_id=%s, session_id=%s, chunk=%d, length=%d, total_length=%d, think_filtered=%s",
                self.user_id, self.session_id, chunk_number, chunk_length,
                self.metrics['total_content_length'], think_blocks_filtered
            )

    def log_chunk_error(self, error: Exception, chunk_number: int, consecutive_errors: int):
        """Log structured information about chunk processing errors."""
        self.metrics['error_count'] += 1
        self.metrics['consecutive_errors'] = consecutive_errors
        error_type = type(error).__name__

        logging.error(
            "[Extrapolate] Chunk error - user_id=%s, session_id=%s, chunk=%d, consecutive_errors=%d, error_type=%s, error=%s",
            self.user_id, self.session_id, chunk_number, consecutive_errors, error_type, str(error), exc_info=True
        )

    def log_buffer_flush(self, event_count: int):
        """Log structured information about buffer flushes."""
        self.metrics['buffer_flushes'] += 1
        logging.debug(
            "[Extrapolate] Buffer flushed - user_id=%s, session_id=%s, events=%d, total_flushes=%d",
            self.user_id, self.session_id, event_count, self.metrics['buffer_flushes']
        )

    def log_cleanup_scheduled(self, cleanup_job_id: str, kb_id: str, doc_id: str):
        """Log structured information about cleanup scheduling."""
        self.metrics['cleanup_scheduled'] = True
        logging.info(
            "[Extrapolate] Cleanup scheduled - user_id=%s, session_id=%s, job_id=%s, kb_id=%s, doc_id=%s",
            self.user_id, self.session_id, cleanup_job_id, kb_id, doc_id
        )

    def log_cleanup_error(self, operation: str, error: Exception, context: dict = None):
        """Log structured information about cleanup errors."""
        error_type = type(error).__name__
        context_str = ""
        if context:
            context_parts = [f"{k}={v}" for k,
                             v in context.items() if v is not None]
            context_str = f" ({', '.join(context_parts)})" if context_parts else ""

        logging.warning(
            "[Extrapolate] Cleanup error - user_id=%s, session_id=%s, operation=%s, error_type=%s, error=%s%s",
            self.user_id, self.session_id, operation, error_type, str(error), context_str, exc_info=True
        )

    def log_final_summary(self):
        """Log final performance summary with all metrics."""
        total_duration = time.time() - self.metrics['request_start_time']
        generation_duration = None
        if self.metrics['generation_start_time'] and self.metrics['generation_end_time']:
            generation_duration = self.metrics['generation_end_time'] - \
                self.metrics['generation_start_time']

        logging.info(
            "[Extrapolate] Session summary - user_id=%s, session_id=%s, tenant_id=%s, "
            "total_duration=%.2fs, generation_duration=%.2fs, file_upload_time=%.2fs, embedding_wait_time=%.2fs, "
            "chunks=%d, content_length=%d, errors=%d, embedding_checks=%d, embedding_errors=%d, "
            "think_blocks_filtered=%d, buffer_flushes=%d, cleanup_scheduled=%s",
            self.user_id, self.session_id, self.tenant_id or "unknown",
            total_duration,
            generation_duration if generation_duration else 0,
            self.metrics['file_upload_time'] if self.metrics['file_upload_time'] else 0,
            self.metrics['embedding_wait_time'] if self.metrics['embedding_wait_time'] else 0,
            self.metrics['total_chunks'], self.metrics['total_content_length'], self.metrics['error_count'],
            self.metrics['embedding_checks'], self.metrics['embedding_errors'],
            self.metrics['think_blocks_filtered'], self.metrics['buffer_flushes'], self.metrics['cleanup_scheduled']
        )

    def get_metrics(self) -> dict:
        """Get current performance metrics."""
        return self.metrics.copy()


class _UploadContext:
    """Metadata about an uploaded file so we can schedule cleanup later."""

    def __init__(self, kb_id: str, doc_id: str, cleanup_job_id: Optional[str]):
        self.kb_id = kb_id
        self.doc_id = doc_id
        self.cleanup_job_id = cleanup_job_id


class ExtrapolateService:
    """Business logic for /extrapolate API endpoints."""

    # ------------------------------
    # Helper: file upload & embedding
    # ------------------------------

    @staticmethod
    def _handle_cleanup_errors(operation: str, error: Exception, context: dict = None):
        """Handle cleanup errors gracefully without affecting user experience.

        This method logs cleanup errors with appropriate detail but ensures they
        don't propagate to affect the user's generation experience.

        Args:
            operation: Description of the cleanup operation that failed
            error: The exception that occurred
            context: Additional context information for logging
        """
        context_str = ""
        if context:
            context_parts = []
            for key, value in context.items():
                if value is not None:
                    context_parts.append(f"{key}={value}")
            if context_parts:
                context_str = f" ({', '.join(context_parts)})"

        logging.warning(
            "[Extrapolate] Cleanup operation failed - %s%s: %s",
            operation, context_str, error, exc_info=True
        )

        # Additional structured logging for monitoring/alerting
        logging.info(
            "[Extrapolate] Cleanup failure summary - operation=%s, error_type=%s, error_msg=%s%s",
            operation, type(error).__name__, str(error), context_str
        )

    @staticmethod
    def _handle_file_upload(user_id: str, sample_file, performance_monitor: Optional[ExtrapolatePerformanceMonitor] = None) -> Tuple[bool, str, Optional[_UploadContext]]:
        """Upload file to KB & kick off async embedding. Returns (ok, file_content, ctx)"""
        from api.db.services.knowledgebase_service import KnowledgebaseService
        from api.db.services.file_service import FileService
        from api.db.services.document_service import DocumentService, _get_or_create_kb_for_user
        from api.db.services.file2document_service import File2DocumentService
        from api.db.services.user_service import TenantService

        upload_start_time = time.time()
        kb_id = None
        doc_id = None

        # Get file size for monitoring
        file_size = 0
        if hasattr(sample_file, 'read'):
            file_size = len(sample_file.read())
            sample_file.seek(0)  # Reset after size check

        # Log upload start with performance monitoring
        if performance_monitor:
            performance_monitor.log_file_upload_start(
                sample_file.filename, file_size)
        else:
            logging.info(
                "[Extrapolate] Starting file upload for user %s, filename=%s, size=%d bytes",
                user_id, sample_file.filename, file_size)

        try:

            # 1) Find or create user temporary KB
            try:
                kb_id = _get_or_create_kb_for_user(user_id)
                ok, kb_obj = KnowledgebaseService.get_by_id(kb_id)
                if not ok or not kb_obj:
                    raise LookupError(
                        f"Cannot find knowledgebase with id: {kb_id}")
                logging.debug("[Extrapolate] Using knowledgebase: %s", kb_id)
            except Exception as e:
                logging.error(
                    "[Extrapolate] Failed to get/create knowledgebase for user %s: %s", user_id, e, exc_info=True)
                raise

            # 2) Upload file to storage / DB
            try:
                err_list, files = FileService.upload_document(
                    kb_obj, [sample_file], user_id)
                if err_list:
                    error_msg = "\n".join(err_list)
                    logging.error(
                        "[Extrapolate] File upload errors: %s", error_msg)
                    raise Exception(f"File upload failed: {error_msg}")
                if not files:
                    logging.error(
                        "[Extrapolate] No files uploaded successfully")
                    raise Exception("No files uploaded successfully")
                doc_id = files[0][0]["id"]
                logging.info(
                    "[Extrapolate] File uploaded successfully, doc_id: %s", doc_id)
            except Exception as e:
                logging.error(
                    "[Extrapolate] Failed to upload file to storage: %s", e, exc_info=True)
                raise

            # 3) Read local content for generation regardless of embedding
            try:
                sample_file.seek(0)
                try:
                    file_content = sample_file.read().decode("utf-8", errors="ignore")
                    logging.debug(
                        "[Extrapolate] File decoded as UTF-8, length: %d", len(file_content))
                except Exception:
                    sample_file.seek(0)
                    file_content = sample_file.read().decode("gbk", errors="ignore")
                    logging.debug(
                        "[Extrapolate] File decoded as GBK, length: %d", len(file_content))
            except Exception as e:
                logging.error(
                    "[Extrapolate] Failed to read file content: %s", e, exc_info=True)
                raise Exception(f"Failed to read file content: {str(e)}")

            # 4) Start async embedding
            embedding_start_time = time.time()
            try:
                _, tenant = TenantService.get_by_id(kb_obj.tenant_id)
                doc_dict = files[0][0]
                doc_dict["tenant_id"] = kb_obj.tenant_id
                info = {"run": 1, "progress": 0, "progress_msg": "",
                        "chunk_num": 0, "token_num": 0}
                DocumentService.update_by_id(doc_id, info)
                bucket, name = File2DocumentService.get_storage_address(
                    doc_id=doc_id)
                from api.db.services.task_service import queue_tasks
                queue_tasks(doc_dict, bucket, name, 0)
                embedding_setup_time = time.time() - embedding_start_time
                logging.info(
                    "[Extrapolate] Started async embedding task for doc_id: %s (setup took %.2fs)",
                    doc_id, embedding_setup_time)
            except Exception as e:
                logging.warning(
                    "[Extrapolate] Failed to start async embedding for doc_id %s: %s",
                    doc_id, e, exc_info=True)
                # Log additional structured information for monitoring
                logging.info(
                    "[Extrapolate] Embedding failure summary - doc_id=%s, kb_id=%s, user_id=%s, error_type=%s",
                    doc_id, kb_id, user_id, type(e).__name__)
                # Continue without embedding - not a fatal error

            # 5) Schedule cleanup 2h later
            cleanup_job_id = None
            try:
                from api.tasks.lesson_plan_tasks import cleanup_kb_doc
                cleanup_async = cleanup_kb_doc.apply_async(
                    args=[None, kb_id, [doc_id]], countdown=7200)
                cleanup_job_id = cleanup_async.id
                logging.info(
                    "[Extrapolate] Scheduled cleanup task: %s for kb=%s doc=%s",
                    cleanup_job_id, kb_id, doc_id)
            except Exception as e:
                ExtrapolateService._handle_cleanup_errors(
                    "cleanup task scheduling",
                    e,
                    {"kb_id": kb_id, "doc_id": doc_id, "user_id": user_id}
                )
                # Continue without cleanup scheduling - not a fatal error

            upload_total_time = time.time() - upload_start_time

            # Log successful upload with performance monitoring
            if performance_monitor:
                performance_monitor.log_file_upload_complete(
                    upload_total_time, len(file_content), kb_id, doc_id)
                if cleanup_job_id:
                    performance_monitor.log_cleanup_scheduled(
                        cleanup_job_id, kb_id, doc_id)
            else:
                logging.info(
                    "[Extrapolate] File upload completed successfully in %.2fs - content length: %d, kb_id: %s, doc_id: %s",
                    upload_total_time, len(file_content), kb_id, doc_id)

            return True, file_content, _UploadContext(kb_id, doc_id, cleanup_job_id)

        except Exception as e:
            upload_error_time = time.time() - upload_start_time

            # Log upload error with performance monitoring
            if performance_monitor:
                performance_monitor.log_file_upload_error(
                    upload_error_time, e, sample_file.filename if sample_file else None)
            else:
                logging.error(
                    "[Extrapolate] File upload failed after %.2fs for user %s, filename %s: %s",
                    upload_error_time, user_id, sample_file.filename if sample_file else "unknown", e, exc_info=True)

            # Best effort cleanup of any partially created resources
            try:
                if doc_id:
                    logging.info(
                        "[Extrapolate] Attempting cleanup of failed upload doc_id: %s", doc_id)
                    ok, doc_obj = DocumentService.get_by_id(doc_id)
                    if ok and doc_obj:
                        doc_tenant_id = DocumentService.get_tenant_id(doc_id)
                        DocumentService.remove_document(doc_obj, doc_tenant_id)
                        logging.info(
                            "[Extrapolate] Successfully cleaned up failed document: %s", doc_id)
                    else:
                        logging.warning(
                            "[Extrapolate] Could not find document to cleanup: %s", doc_id)
            except Exception as cleanup_err:
                ExtrapolateService._handle_cleanup_errors(
                    "failed upload document cleanup",
                    cleanup_err,
                    {"doc_id": doc_id, "user_id": user_id}
                )

            return False, "", None

    @staticmethod
    def _wait_for_embedding(kb_id: str, performance_monitor: Optional[ExtrapolatePerformanceMonitor] = None):
        """Yield SSE events while waiting for file embedding to finish (max 5 min).

        This simplified implementation mirrors LessonPlanV2Service to avoid the
        bug where we continued发送 waiting events即使向量化已完成。"""

        from api.db.services.knowledgebase_service import KnowledgebaseService
        from api.db.services.document_service import DocumentService

        max_wait_time = 60  # seconds (configurable)
        fast_checks = 10   # first 20s quick checks (2s interval)

        check_count = 0

        while time.time() - start_time < max_wait_time:
            try:
                done, _ = KnowledgebaseService.is_parsed_done(kb_id)

                # 快速判断：只要有任意文档 chunk_num>0 即可视为可用
                if not done:
                    # 获取首个文档 chunk_num
                    docs, _ = DocumentService.get_by_kb_id(
                        kb_id, 1, 1, "create_time", True, "", [], [])
                    if docs and docs[0].get("chunk_num", 0) > 0:
                        done = True

                if done:
                    # record finish
                    if performance_monitor:
                        duration = time.time() - start_time
                        performance_monitor.log_embedding_wait_complete(
                            duration, kb_id, 0)
                    break
                yield "event:waiting_embedding\ndata:" + json.dumps({"msg": "示例文件向量化中..."}) + "\n\n"
                time.sleep(2)

                check_count += 1
            except Exception as e:
                logging.warning(
                    "[Extrapolate] Error checking embedding status: %s", e)
                yield "event:waiting_embedding\ndata:" + json.dumps({"msg": "示例文件向量化中..."}) + "\n\n"
                time.sleep(2)

        # Additional log if timeout
        if time.time() - start_time >= max_wait_time and performance_monitor:
            performance_monitor.log_embedding_wait_timeout(
                time.time() - start_time, kb_id, 0, 0)

    # ------------------------------------------------------------------
    @staticmethod
    def build_start_response(req: Request, user) -> Response:
        request_start_time = time.time()
        user_id = getattr(user, 'id', 'unknown')

        try:
            # Extract and log request data
            try:
                data, files = ExtrapolateService._extract_request_data(req)
                logging.info(
                    "[Extrapolate] Request received from user %s - data keys: %s, files: %s",
                    user_id,
                    list(data.keys()),
                    list(files.keys()) if files else [],
                )
            except Exception as e:
                logging.error(
                    "[Extrapolate] Failed to extract request data for user %s: %s", user_id, e, exc_info=True)
                return get_json_result(data={}, code=400, message="请求数据格式错误")

            # Validate parameters
            try:
                param = ExtrapolateParam(**data)
                logging.debug(
                    "[Extrapolate] Parameters validated successfully for user %s", user_id)
            except Exception as ve:
                logging.warning(
                    "[Extrapolate] Parameter validation failed for user %s: %s", user_id, ve)
                return get_json_result(data={}, code=400, message=f"参数验证失败: {str(ve)}")

            # Handle file upload if present
            sample_file = files.get("sample_file") if files else None
            sample_content = ""
            upload_ctx = None

            if sample_file and sample_file.filename:
                logging.info(
                    "[Extrapolate] Processing uploaded file: %s for user %s", sample_file.filename, user_id)
                try:
                    # 1) Parse text content - will throw ValueError if format not supported
                    parser = SampleFileParser()
                    sample_content, _ = parser(
                        sample_file.filename, sample_file.read())
                    logging.debug(
                        "[Extrapolate] File parsed successfully, content length: %d", len(sample_content))

                    # 2) Reset file pointer and upload / vectorize
                    sample_file.seek(0)
                    # Initialize performance monitor for file upload tracking (tenant_id will be set later)
                    performance_monitor = ExtrapolatePerformanceMonitor(
                        user.id)
                    ok, _, upload_ctx = ExtrapolateService._handle_file_upload(
                        user.id, sample_file, performance_monitor)
                    if not ok:
                        logging.error(
                            "[Extrapolate] File upload failed for user %s, file: %s", user_id, sample_file.filename)
                        return get_json_result(data={}, code=500, message="文件上传解析失败")

                except ValueError as ve:
                    # Unsupported file type explicitly handled here
                    logging.warning("[Extrapolate] Unsupported file type for user %s, file: %s - %s",
                                    user_id, sample_file.filename, ve)
                    return get_json_result(data={}, code=400, message=f"不支持的文件格式: {str(ve)}")
                except Exception as e:
                    # General upload error handling
                    logging.error(
                        "[Extrapolate] File processing error for user %s, file: %s - %s",
                        user_id, sample_file.filename, e, exc_info=True)
                    return get_json_result(data={}, code=500, message="文件处理失败")
            else:
                sample_content = data.get("sample_content", "")
                logging.debug(
                    "[Extrapolate] Using provided sample content, length: %d", len(sample_content))

            # Validate sample content
            if not sample_content:
                logging.warning(
                    "[Extrapolate] No sample content provided for user %s", user_id)
                return get_json_result(data={}, code=400, message="示例试题文件或内容必填")

            # Get tenant information with enhanced error handling
            try:
                success, tenant = get_tenant_with_fallback(user.id)
                if not success or not tenant:
                    logging.error(
                        "[Extrapolate] Failed to determine tenant for user %s", user_id)
                    return get_data_error_result(message="无法确定用户租户信息")
                tenant_id = tenant.id
                logging.debug(
                    "[Extrapolate] Using tenant_id: %s for user %s", tenant_id, user_id)
            except Exception as e:
                # Enhanced tenant resolution error handling with detailed logging
                error_type = type(e).__name__
                logging.error(
                    "[Extrapolate] Tenant resolution error for user %s: %s (%s)",
                    user_id, str(e), error_type, exc_info=True)

                # Add structured logging for monitoring/alerting
                logging.info(
                    "[Extrapolate] Tenant resolution failure summary - user_id=%s, error_type=%s, error_msg=%s",
                    user_id, error_type, str(e))

                # Return user-friendly error message
                return get_data_error_result(message="租户信息获取失败，请联系系统管理员")

            # Initialize canvas with enhanced error handling
            try:
                canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user.id)
                logging.debug(
                    "[Extrapolate] Canvas initialized for user %s, tenant %s", user_id, tenant_id)
            except Exception as e:
                # Enhanced canvas initialization error handling
                error_type = type(e).__name__
                logging.error(
                    "[Extrapolate] Canvas initialization failed for user %s: %s (%s)",
                    user_id, str(e), error_type, exc_info=True)

                # Add structured logging for monitoring/alerting
                logging.info(
                    "[Extrapolate] Canvas initialization failure summary - user_id=%s, tenant_id=%s, error_type=%s, error_msg=%s",
                    user_id, tenant_id, error_type, str(e))

                # Return user-friendly error message
                return get_json_result(data={}, code=500, message="系统初始化失败，请稍后重试")

            # Start streaming generation with enhanced error handling
            try:
                # Initialize or reuse performance monitor for streaming
                if 'performance_monitor' not in locals():
                    performance_monitor = ExtrapolatePerformanceMonitor(
                        user.id, tenant_id)
                else:
                    # Update tenant_id if performance_monitor was created earlier without it
                    performance_monitor.tenant_id = tenant_id

                stream_gen = ExtrapolateService._generate_stream(
                    canvas,
                    param,
                    sample_content,
                    upload_ctx,
                    req.headers.get("Last-Event-ID", ""),
                    performance_monitor,
                )

                request_setup_time = time.time() - request_start_time
                logging.info("[Extrapolate] Starting stream generation for user %s (setup took %.2fs)",
                             user_id, request_setup_time)

                resp = Response(stream_gen, mimetype="text/event-stream")
                resp.headers["Cache-Control"] = "no-cache"
                resp.headers["X-Accel-Buffering"] = "no"
                return resp

            except Exception as e:
                # Enhanced stream generation setup error handling
                error_type = type(e).__name__
                logging.error(
                    "[Extrapolate] Stream generation setup failed for user %s: %s (%s)",
                    user_id, str(e), error_type, exc_info=True)

                # Add structured logging for monitoring/alerting
                logging.info(
                    "[Extrapolate] Stream generation setup failure summary - user_id=%s, tenant_id=%s, error_type=%s, error_msg=%s",
                    user_id, tenant_id, error_type, str(e))

                # Return user-friendly error message
                return get_json_result(data={}, code=500, message="生成流初始化失败，请稍后重试")

        except Exception as e:
            # Enhanced fatal error handling
            request_error_time = time.time() - request_start_time
            error_type = type(e).__name__
            logging.error(
                "[Extrapolate] Fatal error after %.2fs for user %s: %s (%s)",
                request_error_time, user_id, str(e), error_type, exc_info=True)

            # Add structured logging for monitoring/alerting
            logging.info(
                "[Extrapolate] Fatal error summary - user_id=%s, elapsed_time=%.2fs, error_type=%s, error_msg=%s",
                user_id, request_error_time, error_type, str(e))

            # Return user-friendly error message
            return server_error_response(e)

    # ------------------------------------------------------------------
    @staticmethod
    def _extract_request_data(req: Request) -> Tuple[Dict, Dict]:
        if req.content_type and "application/json" in (req.content_type or ""):
            data = get_data_from_request(req)
            files = {}
        else:
            data = req.form.to_dict()
            files = req.files
        return data, files

    # ------------------------------------------------------------------
    @staticmethod
    def _generate_stream(
        canvas: Canvas,
        param: ExtrapolateParam,
        sample_content: str,
        upload_ctx: Optional[_UploadContext],
        last_event_header: str,
        performance_monitor: Optional[ExtrapolatePerformanceMonitor] = None,
    ) -> Generator[str, None, None]:
        """Enhanced stream generation pipeline with proper SSE event sequencing.

        Implements the same event pattern as LessonPlanV2Service:
        1. init - Initialize streaming session
        2. waiting_embedding - File embedding progress (if file uploaded)
        3. generating - Content generation status
        4. stream - Incremental content chunks
        5. done - Generation completion
        6. error - Error conditions
        """
        stream_start_time = time.time()
        user_id = canvas.get_user_id()
        tenant_id = canvas.get_tenant_id()

        # Parse Last-Event-ID for reconnection support
        try:
            last_event_id = int(last_event_header) if last_event_header else -1
            if last_event_id > -1:
                logging.info(
                    "[Extrapolate] Reconnection detected for user %s, last_event_id: %d", user_id, last_event_id)
        except ValueError:
            last_event_id = -1
            logging.debug(
                "[Extrapolate] Invalid Last-Event-ID header: %s", last_event_header)

        # Build generation context
        try:
            # Generate session ID for streaming context
            session_id = str(uuid.uuid4())

            ctx = ExtrapolateContext(
                question_type_mode=param.question_type,
                structure_mode=param.structure,
                knowledge_points_mode=param.knowledge_points,
                difficulty_mode=param.difficulty,
                quantity=param.quantity,
            )
            ctx.sample_content = sample_content
            ctx.custom_question_type = param.custom_question_type
            ctx.custom_structure = param.custom_structure
            ctx.custom_knowledge_points = param.custom_knowledge_points
            ctx.tenant_id = tenant_id
            ctx.user_id = user_id

            # Set new streaming-related fields
            ctx.session_id = session_id
            ctx.upload_context = upload_ctx

            logging.debug("[Extrapolate] Generation context created for user %s: session_id=%s, quantity=%d, sample_content_length=%d, has_upload_context=%s",
                          user_id, session_id, param.quantity, len(sample_content), upload_ctx is not None)
        except Exception as e:
            logging.error(
                "[Extrapolate] Failed to create generation context for user %s: %s", user_id, e, exc_info=True)

            def _context_error_stream():
                yield f"event:error\ndata:{json.dumps({'detail': f'生成上下文创建失败: {str(e)}'})}\n\n"
            return _context_error_stream()

        # Initialize LLM and generator with enhanced error handling
        try:
            # First check if the LLM ID is valid for this tenant
            from api.db.services.llm_service import TenantLLMService
            model_config = TenantLLMService.get_model_config(
                tenant_id, LLMType.CHAT, param.llm_id)
            if not model_config:
                logging.error(
                    "[Extrapolate] LLM model '%s' not found or not available for tenant %s",
                    param.llm_id, tenant_id)

                def _llm_not_found_stream():
                    yield f"event:error\ndata:{json.dumps({'detail': f'指定的模型不可用或不存在，请选择其他模型'})}\n\n"
                return _llm_not_found_stream()

            # Try to initialize the LLM bundle
            llm_bundle = LLMBundle(tenant_id, LLMType.CHAT, param.llm_id)

            # Check if the LLM bundle was initialized correctly
            if not llm_bundle or not hasattr(llm_bundle, 'llm'):
                logging.error(
                    "[Extrapolate] LLM bundle initialization incomplete for user %s, tenant %s, llm_id %s",
                    user_id, tenant_id, param.llm_id)

                def _llm_incomplete_stream():
                    yield f"event:error\ndata:{json.dumps({'detail': f'模型初始化不完整，请稍后重试'})}\n\n"
                return _llm_incomplete_stream()

            # Initialize the generator
            generator = ExtrapolateGenerator(llm_bundle, stream=True)
            logging.info("[Extrapolate] LLM bundle initialized for user %s, tenant %s, llm_id: %s",
                         user_id, tenant_id, param.llm_id)
        except ImportError as e:
            # Handle missing dependencies
            error_type = type(e).__name__
            error_msg = str(e)
            logging.error(
                "[Extrapolate] Missing dependency for LLM initialization: %s (%s)",
                error_msg, error_type, exc_info=True)

            # Add structured logging for monitoring/alerting
            logging.info(
                "[Extrapolate] LLM dependency error - user_id=%s, tenant_id=%s, llm_id=%s, error_type=%s, error_msg=%s",
                user_id, tenant_id, param.llm_id, error_type, error_msg)

            def _llm_dependency_stream():
                yield f"event:error\ndata:{json.dumps({'detail': f'模型依赖缺失，请联系系统管理员'})}\n\n"
            return _llm_dependency_stream()
        except ValueError as e:
            # Handle invalid parameter values
            error_type = type(e).__name__
            error_msg = str(e)
            logging.error(
                "[Extrapolate] Invalid parameter for LLM initialization: %s (%s)",
                error_msg, error_type, exc_info=True)

            # Add structured logging for monitoring/alerting
            logging.info(
                "[Extrapolate] LLM parameter error - user_id=%s, tenant_id=%s, llm_id=%s, error_type=%s, error_msg=%s",
                user_id, tenant_id, param.llm_id, error_type, error_msg)

            def _llm_parameter_stream():
                yield f"event:error\ndata:{json.dumps({'detail': f'模型参数无效: {error_msg}'})}\n\n"
            return _llm_parameter_stream()
        except Exception as e:
            # General error handling
            error_type = type(e).__name__
            error_msg = str(e)
            logging.error(
                "[Extrapolate] Failed to initialize LLM bundle for user %s, tenant %s, llm_id %s: %s (%s)",
                user_id, tenant_id, param.llm_id, error_msg, error_type, exc_info=True)

            # Add structured logging for monitoring/alerting
            logging.info(
                "[Extrapolate] LLM initialization failure - user_id=%s, tenant_id=%s, llm_id=%s, error_type=%s, error_msg=%s",
                user_id, tenant_id, param.llm_id, error_type, error_msg)

            def _llm_error_stream():
                yield f"event:error\ndata:{json.dumps({'detail': f'模型初始化失败，请稍后重试或选择其他模型'})}\n\n"
            return _llm_error_stream()

        # Initialize SSE stream buffer with 1-second interval
        try:
            sse_buffer = SSEStreamBuffer(send_interval=1.0)
            logging.debug(
                "[Extrapolate] SSE buffer initialized for user %s", user_id)
        except Exception as e:
            logging.error(
                "[Extrapolate] Failed to initialize SSE buffer for user %s: %s", user_id, e, exc_info=True)

            def _buffer_error_stream():
                yield f"event:error\ndata:{json.dumps({'detail': f'流缓冲区初始化失败: {str(e)}'})}\n\n"
            return _buffer_error_stream()

        # Embedding wait generator if uploaded file present
        embedding_generator = None
        if upload_ctx is not None:
            try:
                embedding_generator = ExtrapolateService._wait_for_embedding(
                    upload_ctx.kb_id, performance_monitor)
                logging.info("[Extrapolate] Embedding wait generator created for user %s, kb_id: %s",
                             user_id, upload_ctx.kb_id)
            except Exception as e:
                logging.error("[Extrapolate] Failed to create embedding generator for user %s, kb_id %s: %s",
                              user_id, upload_ctx.kb_id, e, exc_info=True)
                # Continue without embedding - not fatal

        def _stream():
            generation_start_time = None
            chunk_count = 0
            total_content_length = 0

            try:
                # Event 1: Initialize streaming session with session_id
                yield f"event:init\ndata:{json.dumps({'session_id': session_id})}\n\n"
                logging.debug(
                    "[Extrapolate] Sent init event for user %s with session_id %s", user_id, session_id)

                # Event 2: Wait for embedding if file was uploaded
                if embedding_generator is not None:
                    embedding_wait_start = time.time()
                    try:
                        logging.info(
                            "[Extrapolate] Starting embedding wait for user %s", user_id)
                        for embedding_event in embedding_generator:
                            yield embedding_event

                        embedding_wait_time = time.time() - embedding_wait_start
                        logging.info("[Extrapolate] Embedding wait completed for user %s in %.2fs",
                                     user_id, embedding_wait_time)

                        # 通知前端向量化已完成
                        yield "event:embedding_done\ndata:{}\n\n"

                    except Exception as e:
                        embedding_wait_time = time.time() - embedding_wait_start
                        logging.warning(
                            "[Extrapolate] Embedding wait error for user %s after %.2fs: %s",
                            user_id, embedding_wait_time, e, exc_info=True)
                        # Continue with generation even if embedding fails

                # Event 3: Start content generation
                yield f"event:generating\ndata:{json.dumps({'msg': '题目正在生成中...'})}\n\n"
                generation_start_time = time.time()
                logging.info(
                    "[Extrapolate] Starting content generation for user %s", user_id)

                # Event 4: Stream incremental content chunks
                think_mode = False  # State for think block filtering
                consecutive_errors = 0
                max_consecutive_errors = 3
                retry_delay = 1.0  # seconds
                chunk_timeout = 30.0  # seconds
                last_chunk_time = time.time()

                try:
                    # Set up generator with timeout monitoring
                    generator_iter = generator.stream_generate(ctx)

                    while True:
                        try:
                            # Check for timeout
                            current_time = time.time()
                            if current_time - last_chunk_time > chunk_timeout:
                                logging.warning(
                                    "[Extrapolate] Generator timeout for user %s after %.2fs without chunks",
                                    user_id, chunk_timeout)

                                # Send a warning event but continue trying
                                yield f"event:warning\ndata:{json.dumps({'detail': '生成过程较慢，请耐心等待...'})}\n\n"

                                # Reset timeout counter
                                last_chunk_time = current_time

                            # Get next chunk with timeout handling
                            try:
                                chunk = next(generator_iter)
                            except StopIteration:
                                # Normal end of generation
                                break

                            # Reset error counter and update last chunk time
                            consecutive_errors = 0
                            last_chunk_time = time.time()
                            chunk_count += 1

                            # Apply think block filtering to maintain clean output
                            try:
                                filtered_chunk = ExtrapolateService._filter_think_blocks(
                                    chunk, think_mode)
                                chunk_text, think_mode = filtered_chunk
                            except Exception as filter_e:
                                error_type = type(filter_e).__name__
                                logging.warning(
                                    "[Extrapolate] Think block filtering error for user %s, chunk %d: %s (%s)",
                                    user_id, chunk_count, str(filter_e), error_type)
                                chunk_text = chunk  # Use unfiltered chunk as fallback

                            if chunk_text:  # Only process non-empty chunks
                                total_content_length += len(chunk_text)
                                try:
                                    event_data = sse_buffer.add_chunk(
                                        chunk_text, last_event_id)
                                    if event_data:
                                        yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"
                                except Exception as buffer_e:
                                    error_type = type(buffer_e).__name__
                                    logging.error(
                                        "[Extrapolate] SSE buffer error for user %s, chunk %d: %s (%s)",
                                        user_id, chunk_count, str(buffer_e), error_type, exc_info=True)

                                    # Try to send chunk directly as fallback
                                    yield f"event:stream\ndata:{json.dumps({'chunk': chunk_text})}\n\n"

                        except (KeyboardInterrupt, SystemExit):
                            # Re-raise critical exceptions
                            raise

                        except Exception as chunk_e:
                            error_type = type(chunk_e).__name__
                            consecutive_errors += 1

                            logging.error(
                                "[Extrapolate] Chunk processing error #%d for user %s: %s (%s)",
                                consecutive_errors, user_id, str(chunk_e), error_type, exc_info=True)

                            # Add structured logging for monitoring/alerting
                            logging.info(
                                "[Extrapolate] Chunk error summary - user_id=%s, tenant_id=%s, error_type=%s, error_msg=%s, consecutive=%d",
                                user_id, tenant_id, error_type, str(chunk_e), consecutive_errors)

                            if consecutive_errors >= max_consecutive_errors:
                                # Too many errors, abort generation
                                logging.error(
                                    "[Extrapolate] Too many consecutive errors (%d) for user %s, aborting generation",
                                    consecutive_errors, user_id)

                                yield f"event:error\ndata:{json.dumps({'detail': '生成过程中出现多次错误，请稍后重试'})}\n\n"
                                return

                            # For non-fatal errors, try to continue after a short delay
                            time.sleep(retry_delay)

                            # Send a warning to the client
                            if consecutive_errors == 1:  # Only send on first error
                                yield f"event:warning\ndata:{json.dumps({'detail': '生成过程中遇到问题，正在尝试恢复...'})}\n\n"

                    # Flush any remaining buffered content
                    try:
                        event_data = sse_buffer.flush()
                        if event_data:
                            yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"
                    except Exception as flush_e:
                        error_type = type(flush_e).__name__
                        logging.error(
                            "[Extrapolate] SSE buffer flush error for user %s: %s (%s)",
                            user_id, str(flush_e), error_type, exc_info=True)
                        # No fallback needed here, just log the error

                    generation_time = time.time() - generation_start_time if generation_start_time else 0
                    logging.info("[Extrapolate] Content generation completed for user %s: %.2fs, %d chunks, %d chars",
                                 user_id, generation_time, chunk_count, total_content_length)

                except Exception as gen_e:
                    # Handle fatal generation errors
                    generation_time = time.time() - generation_start_time if generation_start_time else 0
                    error_type = type(gen_e).__name__
                    error_msg = str(gen_e)

                    logging.error(
                        "[Extrapolate] Generation error for user %s after %.2fs (%d chunks): %s (%s)",
                        user_id, generation_time, chunk_count, error_msg, error_type, exc_info=True)

                    # Add structured logging for monitoring/alerting
                    logging.info(
                        "[Extrapolate] Generation failure summary - user_id=%s, tenant_id=%s, error_type=%s, error_msg=%s, elapsed_time=%.2fs, chunks=%d",
                        user_id, tenant_id, error_type, error_msg, generation_time, chunk_count)

                    # Provide a user-friendly error message based on error type
                    if "memory" in error_msg.lower() or "cuda" in error_msg.lower():
                        # Memory or GPU-related errors
                        error_detail = {
                            'detail': '系统资源不足，请稍后重试或减少生成数量',
                            'error_type': error_type,
                            'chunks_processed': chunk_count,
                            'generation_time': generation_time
                        }
                    elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                        # Timeout errors
                        error_detail = {
                            'detail': '生成超时，请稍后重试',
                            'error_type': error_type,
                            'chunks_processed': chunk_count,
                            'generation_time': generation_time
                        }
                    elif "token" in error_msg.lower() or "limit" in error_msg.lower():
                        # Token limit errors
                        error_detail = {
                            'detail': '内容过长超出模型限制，请减少示例内容或生成数量',
                            'error_type': error_type,
                            'chunks_processed': chunk_count,
                            'generation_time': generation_time
                        }
                    else:
                        # Generic error
                        error_detail = {
                            'detail': '生成过程中出错，请稍后重试',
                            'error_type': error_type,
                            'chunks_processed': chunk_count,
                            'generation_time': generation_time
                        }

                    yield f"event:error\ndata:{json.dumps(error_detail)}\n\n"
                    return

                # Log cleanup information for monitoring
                if upload_ctx and upload_ctx.cleanup_job_id:
                    logging.info("[Extrapolate] Cleanup scheduled for user %s: job_id=%s, kb=%s, doc=%s",
                                 user_id, upload_ctx.cleanup_job_id, upload_ctx.kb_id, upload_ctx.doc_id)
                elif upload_ctx:
                    logging.warning("[Extrapolate] Upload context exists but no cleanup job for user %s: kb=%s, doc=%s",
                                    user_id, upload_ctx.kb_id, upload_ctx.doc_id)

                # Event 5: Signal completion
                total_time = time.time() - stream_start_time
                logging.info("[Extrapolate] Stream generation completed for user %s: total_time=%.2fs, chunks=%d, content_length=%d",
                             user_id, total_time, chunk_count, total_content_length)
                yield "id:-2\nevent:done\ndata:{}\n\n"

            except Exception as e:
                total_time = time.time() - stream_start_time
                error_type = type(e).__name__
                error_msg = str(e)

                logging.error(
                    "[Extrapolate] Fatal stream generation error for user %s after %.2fs: %s (%s)",
                    user_id, total_time, error_msg, error_type, exc_info=True)

                # Add structured logging for monitoring/alerting
                logging.info(
                    "[Extrapolate] Fatal stream error summary - user_id=%s, tenant_id=%s, error_type=%s, error_msg=%s, elapsed_time=%.2fs, chunks=%d",
                    user_id, tenant_id, error_type, error_msg, total_time, chunk_count)

                # Implement graceful degradation for non-critical failures
                if "connection" in error_msg.lower() or "network" in error_msg.lower():
                    # Network-related errors
                    error_detail = {
                        'detail': '网络连接问题，请检查您的网络连接并重试',
                        'error_type': error_type,
                        'total_time': total_time,
                        'chunks_processed': chunk_count
                    }
                elif "permission" in error_msg.lower() or "access" in error_msg.lower():
                    # Permission-related errors
                    error_detail = {
                        'detail': '权限问题，请联系系统管理员',
                        'error_type': error_type,
                        'total_time': total_time,
                        'chunks_processed': chunk_count
                    }
                else:
                    # Generic error with technical details for support
                    error_detail = {
                        'detail': '流式生成过程中出现错误，请稍后重试',
                        'error_type': error_type,
                        # Generate unique error ID for support reference
                        'error_id': str(uuid.uuid4()),
                        'total_time': total_time,
                        'chunks_processed': chunk_count
                    }

                yield f"event:error\ndata:{json.dumps(error_detail)}\n\n"

        return _stream()

    @staticmethod
    def _filter_think_blocks(chunk: str, think_mode: bool) -> Tuple[str, bool]:
        """Filter think blocks from streaming chunks, maintaining state across boundaries.

        This implementation matches the frontend filtering logic exactly:
        - When in think_mode, look for the first </think> to exit
        - When not in think_mode, find <think> tags and their corresponding </think> tags
        - Handle nested think blocks by treating them as one continuous block

        Args:
            chunk: The text chunk to filter
            think_mode: Current state - True if we're inside a think block

        Returns:
            Tuple of (filtered_chunk, new_think_mode_state)
        """
        try:
            # Input validation
            if chunk is None:
                return "", think_mode

            # Handle non-string input gracefully
            if not isinstance(chunk, str):
                try:
                    chunk_text = str(chunk)
                    logging.warning(
                        "[Extrapolate] Non-string chunk converted to string: %s", type(chunk).__name__)
                except Exception:
                    logging.error(
                        "[Extrapolate] Failed to convert non-string chunk to string: %s", type(chunk).__name__)
                    return "", think_mode
            else:
                chunk_text = chunk

            # If we're already in think mode, look for closing tag
            if think_mode:
                close_idx = chunk_text.find('</think>')
                if close_idx != -1:
                    # Found closing tag, take text after it and continue processing
                    chunk_text = chunk_text[close_idx + 8:]
                    think_mode = False
                else:
                    # Still in think mode, filter out entire chunk
                    return "", think_mode

            # Process think blocks in the remaining text (matches frontend logic)
            start_idx = chunk_text.find('<think>')
            if start_idx != -1:
                before_think = chunk_text[:start_idx]
                after_start = chunk_text[start_idx + 7:]
                end_idx = after_start.find('</think>')
                if end_idx != -1:
                    # Complete think block found
                    after_think = after_start[end_idx + 8:]
                    # Recursively process any additional think blocks
                    remaining_text, remaining_mode = ExtrapolateService._filter_think_blocks(
                        before_think + after_think, False)
                    return remaining_text, remaining_mode
                else:
                    # Incomplete think block, enter think mode
                    think_mode = True
                    chunk_text = before_think

            return chunk_text, think_mode

        except Exception as e:
            # Catch any unexpected errors and log them
            error_type = type(e).__name__
            logging.error(
                "[Extrapolate] Think block filtering error: %s (%s)",
                str(e), error_type, exc_info=True)

            # Return original chunk as fallback with unchanged think_mode
            # This ensures we don't lose content even if filtering fails
            return chunk, think_mode
