#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import io
import logging
import pandas as pd
import re
from typing import Optional
from agent.component.base import ComponentBase, ComponentParamBase
from api.utils.llm_logger import llm_interaction_context, log_llm_response


class ExtrapolateParam(ComponentParamBase):
    """
    Define the Extrapolate component parameters.
    """

    def __init__(self):
        super().__init__()
        self.question_type = "same"  # same, custom
        self.custom_question_type = ""
        self.structure = "same"  # same, custom
        self.custom_structure = ""
        self.knowledge_points = "same_context"  # same_context, custom
        self.custom_knowledge_points = ""
        self.difficulty = "same"  # same, basic, medium, hard
        self.quantity = 5
        self.sample_file = None

        # 知识库检索相关参数
        self.enable_kb_retrieval = False  # 是否启用知识库检索
        self.kb_ids = []  # 知识库ID列表
        self.similarity_threshold = 0.2  # 相似度阈值
        self.keywords_similarity_weight = 0.3  # 关键词相似度权重
        self.top_n = 5  # 检索Top N个结果
        self.rerank_id = ""  # 重排序模型ID
        self.use_kg = False  # 是否使用知识图谱

    def check(self):
        self.check_positive_integer(self.quantity, "Quantity")
        if self.quantity > 100:
            raise ValueError("Quantity cannot exceed 100")

        if self.question_type == "custom" and not self.custom_question_type:
            raise ValueError(
                "Custom question type is required when question_type is 'custom'")

        if self.structure == "custom" and not self.custom_structure:
            raise ValueError(
                "Custom structure is required when structure is 'custom'")

        if self.knowledge_points == "custom" and not self.custom_knowledge_points:
            raise ValueError(
                "Custom knowledge points are required when knowledge_points is 'custom'")

        # 知识库相关参数验证
        if self.enable_kb_retrieval:
            if not self.kb_ids:
                raise ValueError(
                    "Knowledge base IDs are required when KB retrieval is enabled")

            self.check_decimal_float(
                self.similarity_threshold, "Similarity threshold")
            if not (0.0 <= self.similarity_threshold <= 1.0):
                raise ValueError(
                    "Similarity threshold must be between 0.0 and 1.0")

            self.check_decimal_float(
                self.keywords_similarity_weight, "Keywords similarity weight")
            if not (0.0 <= self.keywords_similarity_weight <= 1.0):
                raise ValueError(
                    "Keywords similarity weight must be between 0.0 and 1.0")

            self.check_positive_integer(self.top_n, "Top N")
            if self.top_n > 20:
                raise ValueError("Top N cannot exceed 20")


class Extrapolate(ComponentBase):
    component_name = "Extrapolate"

    def __init__(self, canvas, id, param: ComponentParamBase):
        super().__init__(canvas, id, param)
        self._excel_headers = []  # 存储Excel表头信息

    def _run(self, history, **kwargs):
        """
        Execute the extrapolate component to generate variant questions based on sample questions.
        """
        try:
            # Parse sample file content
            sample_content = self._parse_sample_file()

            # Generate questions using LLM
            response = self._generate_questions(sample_content, history)

            return Extrapolate.be_output(response)

        except Exception as e:
            logging.error(f"Extrapolate component error: {str(e)}")
            return Extrapolate.be_output(f"生成失败: {str(e)}")

    def _parse_sample_file(self) -> str:
        """
        Parse the uploaded sample file and convert to clean string format.
        Supports: .docx/.doc/.xlsx/.xls/.txt/.csv
        Other formats return empty string.
        Max length: 10240 Unicode characters.
        """
        if not self._param.sample_file:
            raise ValueError("示例试题文件是必需的")

        # 初始化表头信息
        self._excel_headers = []

        MAX_CONTENT_LENGTH = 10240

        try:
            # Get file content
            file_obj = self._param.sample_file

            # Handle Flask FileStorage object
            if hasattr(file_obj, 'read') and hasattr(file_obj, 'filename'):
                filename = file_obj.filename or 'sample.txt'
                file_content = file_obj.read()
                # Reset file pointer if possible
                if hasattr(file_obj, 'seek'):
                    file_obj.seek(0)
            else:
                # Handle direct file content
                filename = getattr(file_obj, 'name', 'sample.txt')
                file_content = file_obj if isinstance(
                    file_obj, bytes) else file_obj.read()

            # Determine file type
            file_ext = filename.lower().split(
                '.')[-1] if '.' in filename else 'txt'

            # Only process supported formats
            supported_formats = ['docx', 'doc', 'xlsx', 'xls', 'txt', 'csv']
            if file_ext not in supported_formats:
                logging.warning(
                    f"Unsupported file format: {file_ext}, returning empty string")
                self._excel_headers = []  # 重置表头信息
                return ""

            # Parse based on file type
            text_content = ""

            if file_ext in ['xlsx', 'xls']:
                text_content = self._parse_excel(file_content)
            elif file_ext == 'csv':
                text_content = self._parse_csv(file_content)
            elif file_ext == 'txt':
                text_content = self._parse_text(file_content)
            elif file_ext in ['docx', 'doc']:
                text_content = self._parse_word(file_content, file_ext)

            # Clean and limit the content
            cleaned_content = self._clean_content(text_content)

            # Limit to 2000 Unicode characters
            if len(cleaned_content) > MAX_CONTENT_LENGTH:
                cleaned_content = cleaned_content[:MAX_CONTENT_LENGTH] + "..."
                logging.info(
                    f"Content truncated to {MAX_CONTENT_LENGTH} characters")

            return cleaned_content

        except Exception as e:
            logging.error(f"Failed to parse sample file: {str(e)}")
            raise ValueError(f"解析示例试题文件失败: {str(e)}")

    def _parse_excel(self, file_content: bytes) -> str:
        """Parse Excel files (.xlsx/.xls)"""
        try:
            df = pd.read_excel(io.BytesIO(file_content))
            if df.empty:
                return ""

            # 提取表头信息
            headers = df.columns.tolist()
            self._excel_headers = [str(header) for header in headers]  # 存储表头信息

            # Convert DataFrame to readable text
            text_lines = []

            # 添加表头行
            header_text = " | ".join(self._excel_headers)
            text_lines.append(f"表头: {header_text}")

            for _, row in df.iterrows():
                row_text = " | ".join([str(cell)
                                      for cell in row.values if pd.notna(cell)])
                if row_text.strip():
                    text_lines.append(row_text.strip())

            return "\n".join(text_lines)

        except Exception as e:
            logging.error(f"Excel parsing error: {str(e)}")
            return ""

    def _parse_csv(self, file_content: bytes) -> str:
        """Parse CSV files"""
        try:
            # Try different encodings
            for encoding in ['utf-8', 'gbk', 'latin-1']:
                try:
                    df = pd.read_csv(io.BytesIO(file_content),
                                     encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                return ""

            if df.empty:
                return ""

            # 提取表头信息
            headers = df.columns.tolist()
            self._excel_headers = [str(header)
                                   for header in headers]  # CSV也使用相同的表头存储

            # Convert DataFrame to readable text
            text_lines = []

            # 添加表头行
            header_text = " | ".join(self._excel_headers)
            text_lines.append(f"表头: {header_text}")

            for _, row in df.iterrows():
                row_text = " | ".join([str(cell)
                                      for cell in row.values if pd.notna(cell)])
                if row_text.strip():
                    text_lines.append(row_text.strip())

            return "\n".join(text_lines)

        except Exception as e:
            logging.error(f"CSV parsing error: {str(e)}")
            return ""

    def _parse_text(self, file_content: bytes) -> str:
        """Parse text files (.txt)"""
        try:
            # 重置表头信息，因为文本文件没有结构化表头
            self._excel_headers = []

            # Try different encodings
            for encoding in ['utf-8', 'gbk', 'latin-1']:
                try:
                    text_content = file_content.decode(encoding)
                    return text_content
                except UnicodeDecodeError:
                    continue
            return ""

        except Exception as e:
            logging.error(f"Text parsing error: {str(e)}")
            return ""

    def _parse_word(self, file_content: bytes, file_ext: str) -> str:
        """Parse Word documents (.docx/.doc)"""
        try:
            # 重置表头信息，因为Word文档没有结构化表头
            self._excel_headers = []

            if file_ext == 'docx':
                return self._parse_docx(file_content)
            else:  # .doc
                return self._parse_doc(file_content)

        except Exception as e:
            logging.error(f"Word parsing error: {str(e)}")
            return ""

    def _parse_docx(self, file_content: bytes) -> str:
        """Parse .docx files"""
        try:
            from docx import Document

            doc = Document(io.BytesIO(file_content))
            text_content = []

            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())

            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = " | ".join(
                        [cell.text.strip() for cell in row.cells if cell.text.strip()])
                    if row_text:
                        text_content.append(row_text)

            return "\n".join(text_content)

        except ImportError:
            logging.warning(
                "python-docx not available, cannot parse .docx files")
            return ""
        except Exception as e:
            logging.error(f"DOCX parsing error: {str(e)}")
            return ""

    def _parse_doc(self, file_content: bytes) -> str:
        """Parse .doc files"""
        try:
            # Try using textract if available
            import textract
            text_content = textract.process(
                io.BytesIO(file_content)).decode('utf-8')
            return text_content

        except ImportError:
            try:
                # Try using python-docx2txt if available
                import docx2txt
                text_content = docx2txt.process(io.BytesIO(file_content))
                return text_content
            except ImportError:
                logging.warning(
                    "Neither textract nor docx2txt available, cannot parse .doc files")
                return ""
        except Exception as e:
            logging.error(f"DOC parsing error: {str(e)}")
            return ""

    def _clean_content(self, content: str) -> str:
        """Clean content by removing invalid characters and formatting properly"""
        if not content:
            return ""

        # Remove excessive whitespace and newlines
        content = re.sub(r'\n\s*\n', '\n', content)  # Remove empty lines
        # Replace multiple spaces with single space
        content = re.sub(r'\s+', ' ', content)
        content = re.sub(r'\t+', ' ', content)       # Replace tabs with spaces

        # Remove special characters that might cause issues - fixed regex pattern
        # Keep Chinese characters, letters, numbers, spaces, and common punctuation
        content = re.sub(
            r'[^\w\s\u4e00-\u9fff\u3400-\u4dbf\uff00-\uffef.,;:!?()（）【】《》""''、。，；：！？—…·+*/=<>\[\]{}|\\-]', '', content)

        # Split into lines and clean each line
        lines = content.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if line and len(line) > 2:  # Only keep meaningful lines
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _get_question_type_text(self) -> str:
        """Get question type text for prompt."""
        if self._param.question_type == "same":
            return "与示例试题相同的题型"
        else:
            return self._param.custom_question_type

    def _get_structure_text(self) -> str:
        """Get structure text for prompt."""
        if self._param.structure == "same":
            return "与示例试题相同的结构"
        else:
            return self._param.custom_structure

    def _get_knowledge_points_text(self) -> str:
        """Get knowledge points text for prompt."""
        if self._param.knowledge_points == "same_context":
            return "与示例试题相同的知识点"
        else:
            return self._param.custom_knowledge_points

    def _get_difficulty_text(self) -> str:
        """Get difficulty text for prompt."""
        difficulty_mapping = {
            "same": "与示例试题一致",
            "basic": "基础",
            "medium": "中等",
            "hard": "困难"
        }
        return difficulty_mapping.get(self._param.difficulty, "与示例试题一致")

    def _generate_questions(self, sample_content: str, history) -> str:
        """Generate questions using LLM with the provided prompt template."""
        try:
            if not sample_content.strip():
                raise ValueError("示例试题内容为空，无法生成新试题")

            # Get parameter texts
            question_type_text = self._get_question_type_text()
            structure_text = self._get_structure_text()
            knowledge_points_text = self._get_knowledge_points_text()
            difficulty_text = self._get_difficulty_text()

            # 构建表头相关的提示信息
            output_instruction = ""
            if self._excel_headers:
                header_text = "、".join(self._excel_headers)
                output_instruction = f"""
3. **输出格式要求**
   - 新试题用markdown格式输出
   - 试题的组成要素按照试题模板表头'{header_text}'的顺序，从上至下排列
   - 试题输出格式的样例为'题型： xxx\n\n题目： xxx\n\n正确答案： xxx\n\n答案A： xxx\n\n答案B： xxx\n\n答案C： xxx\n\n答案D： xxx\n\n答案E： xxx\n\n答案F： xxx\n\n解析： xxx\n\n'。 具体内容根据表头'{header_text}'确定
   - 每道试题之间用'\n\n---\n\n'分隔，最后一题末尾不加分隔符
   - 所有题型的试题，答案必须填充在'正确答案： xxx'这一列，且答案必须唯一
   - 判断题的正确答案为'正确'或'错误'"""

            # 检索相关知识内容
            # knowledge_content = ""
            # if self._param.enable_kb_retrieval and self._param.kb_ids:
            #     # 构建检索查询
            #     retrieval_query = f"{knowledge_points_text} {question_type_text}"
            #     if self._param.custom_knowledge_points:
            #         retrieval_query = self._param.custom_knowledge_points

            #     knowledge_content = self._retrieve_knowledge(retrieval_query)
            #     logging.info(f"Retrieved knowledge content length: {len(knowledge_content)}")

            # Build prompt using the provided template with knowledge base content
            prompt = f"""请基于我提供的试题模板，按照需求生成{self._param.quantity}道不同题型或结构的新试题。

--- 开始试题模板 ---
{sample_content}
--- 结束试题模板 ---

需求如下：
1. **试题模板分析**
   - 请先分析试题模板的以下特征：
     题型（单选/多选/判断/填空/问答/排序等）
     结构（题干/选项/答案/解析/错误选项分析/评分标准等）
     考查的知识点（如：应答器、转辙机、轨道电路等）
     题目结构（题干长度、选项数量、图表使用等）
     难度级别（基础/中等/困难）
     试题数量：{self._param.quantity}

2. **新试题生成要求**
  题型：{question_type_text}
  结构：{structure_text}
  知识点：{knowledge_points_text}
  难度：{difficulty_text}
  试题数量：{self._param.quantity}
  特别强调：需要覆盖试题模板中的所有题目，不可重复使用同一题干生成相同结构的试题，需要变换题型、描述方式、结构或答案顺序

{output_instruction}"""

  # - 注意事项：
  #   需提供正确答案和解析，在每道题的开始注明序号和题型
  #   保持题型的丰富性，避免只生成单一题型的试题
  #   不可重复使用同一题干生成相同结构的试题，需要变换题型、结构或答案顺序
  #   若试题涉及计算，确保新题的数值设定合理且答案唯一
  #   生成试题时，即使改变题型，核心知识点必须保留原义

            # Call LLM
            response = self._call_llm(prompt, history)

            if not response or not response.strip():
                raise ValueError("LLM返回了空的响应")

            return response

        except Exception as e:
            logging.error(f"Question generation error: {str(e)}")
            raise ValueError(f"生成试题失败: {str(e)}")

    def _call_llm(self, prompt: str, history) -> str:
        """Call LLM to generate questions based on the prompt."""
        try:
            # Get tenant_id from canvas
            tenant_id = self._canvas.get_tenant_id()

            # Import LLM services
            from api.db import LLMType
            from api.db.services.llm_service import LLMBundle

            # Create LLM bundle for chat
            chat_mdl = LLMBundle(tenant_id, LLMType.CHAT, self._param.llm_id)

            # Prepare messages for LLM
            messages = [
                {"role": "system", "content": "你是一位资深铁路电务领域的专家教师，专注于电务信号、通信设备的考试命题，并且擅于题型变换，能熟练运用举一反三的派生手法。请严格按照用户提供的要求生成高质量的试题。"},
                {"role": "user", "content": prompt}
            ]

            MAX_CONTENT_LENGTH = 4096 * 8

            # Include limited history if available
            if history:
                limited_history = history[-3:] if len(history) > 3 else history
                formatted_history = []
                for msg in limited_history:
                    if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                        content = msg['content']
                        if len(content) > MAX_CONTENT_LENGTH:
                            content = content[:MAX_CONTENT_LENGTH] + "..."
                        formatted_history.append({
                            'role': msg['role'],
                            'content': content
                        })
                messages = formatted_history + messages

            # Use LLM configuration without token limits (as requested)
            llm_config = {
                "temperature": 0.7,
                "max_tokens": MAX_CONTENT_LENGTH,
                "num_ctx": MAX_CONTENT_LENGTH
            }

            # Use LLM interaction context for logging
            with llm_interaction_context(
                agent_type="extrapolate",
                tenant_id=tenant_id,
                user_id=getattr(self._canvas, 'user_id', tenant_id),
                llm_model=self._param.llm_id,
                system=messages[0]["content"],
                history=messages[1:],
                gen_conf=llm_config
            ) as tracker:
                # Call LLM
                response = chat_mdl.chat(
                    messages[0]["content"],
                    messages[1:],
                    llm_config
                )

                # Handle different response formats
                if isinstance(response, tuple):
                    response = response[0]

                if not response or not isinstance(response, str):
                    raise ValueError("LLM返回了无效的响应")

                # Log the response
                log_llm_response(tracker, response)

                return response

        except Exception as e:
            logging.error(f"LLM call failed: {str(e)}")
            raise ValueError(f"调用语言模型失败: {str(e)}")

    def _retrieve_knowledge(self, query: str) -> str:
        """
        从知识库中检索相关内容

        Args:
            query: 检索查询

        Returns:
            检索到的知识内容字符串
        """
        if not self._param.enable_kb_retrieval or not self._param.kb_ids:
            return ""

        try:
            # 导入必要的服务
            from api.db.services.knowledgebase_service import KnowledgebaseService
            from api.db.services.llm_service import LLMBundle
            from api.db import LLMType
            from api import settings
            from rag.app.tag import label_question

            # 获取知识库信息
            kbs = KnowledgebaseService.get_by_ids(self._param.kb_ids)
            if not kbs:
                logging.warning(
                    f"No knowledge bases found for IDs: {self._param.kb_ids}")
                return ""

            # 验证嵌入模型一致性
            embd_nms = list(set([kb.embd_id for kb in kbs]))
            if len(embd_nms) > 1:
                logging.error("Knowledge bases use different embedding models")
                return ""

            # 创建嵌入模型
            tenant_id = self._canvas.get_tenant_id()
            embd_mdl = LLMBundle(tenant_id, LLMType.EMBEDDING, embd_nms[0])

            # 创建重排序模型（如果配置了）
            rerank_mdl = None
            if self._param.rerank_id:
                rerank_mdl = LLMBundle(
                    kbs[0].tenant_id, LLMType.RERANK, self._param.rerank_id)

            # 执行检索
            kbinfos = settings.retrievaler.retrieval(
                query,
                embd_mdl,
                [kb.tenant_id for kb in kbs],
                self._param.kb_ids,
                1,  # page
                self._param.top_n,
                self._param.similarity_threshold,
                1 - self._param.keywords_similarity_weight,
                aggs=False,
                rerank_mdl=rerank_mdl,
                rank_feature=label_question(query, kbs),
            )

            # 知识图谱检索（如果启用）
            if self._param.use_kg and kbs:
                ck = settings.kg_retrievaler.retrieval(
                    query,
                    [kb.tenant_id for kb in kbs],
                    self._param.kb_ids,
                    embd_mdl,
                    LLMBundle(kbs[0].tenant_id, LLMType.CHAT)
                )
                if ck["content_with_weight"]:
                    kbinfos["chunks"].insert(0, ck)

            # 格式化检索结果
            if not kbinfos["chunks"]:
                return ""

            # 提取检索内容并限制长度
            content_list = []
            total_chars = 0
            MAX_CONTENT_LENGTH = 4096 * 8  # 限制总长度避免prompt过长

            for chunk in kbinfos["chunks"]:
                if 'content_with_weight' not in chunk:
                    continue

                chunk_content = chunk['content_with_weight']

                # 检查是否会超出总长度限制
                if total_chars + len(chunk_content) > MAX_CONTENT_LENGTH:
                    if content_list:  # 已有内容
                        break
                    else:  # 第一个chunk太长，截取部分
                        chunk_content = chunk_content[:MAX_CONTENT_LENGTH-100] + "..."

                content_list.append(
                    f"[相关知识片段 {len(content_list)+1}]\n{chunk_content}")
                total_chars += len(chunk_content)

                if total_chars >= MAX_CONTENT_LENGTH:
                    break

            return "\n\n".join(content_list)

        except Exception as e:
            logging.error(
                f"Knowledge retrieval failed: {str(e)}", exc_info=True)
            return ""
