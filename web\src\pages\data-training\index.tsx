import DatasetSection from '@/pages/knowledge-base';
import { Divider } from 'antd';
import React from 'react';
import ChartSection from './components/chart-section';
import styles from './index.less';

const DataTrainingPage: React.FC = () => {
  return (
    <div className={`${styles.dataTrainingContainer} main-content-container`}>
      <div className={styles.header}>
        <h2 className="main-page-title">数据训练</h2>
      </div>

      {/* 上半部分 - 图表区域 (40% 高度) */}
      <ChartSection />

      <Divider className={styles.divider}></Divider>

      {/* 下半部分 - 数据集表格 (60% 高度) */}
      <DatasetSection />
    </div>
  );
};

export default DataTrainingPage;
