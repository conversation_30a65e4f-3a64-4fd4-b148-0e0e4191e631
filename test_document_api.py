#!/usr/bin/env python3
"""
Test script for the updated document API endpoints.
This script tests the new functionality where list_docs can work without specifying a dataset_id.
"""

import requests
import json
import os

# Configuration
HOST_ADDRESS = "http://localhost:9380"  # Update this to your actual server address
API_KEY = "your_api_key_here"  # Update this to your actual API key

def test_list_documents_with_default_kb():
    """Test the new /api/v1/documents endpoint that uses the default tenant knowledge base."""
    url = f"{HOST_ADDRESS}/api/v1/documents"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    print("Testing /api/v1/documents endpoint...")
    response = requests.get(url, headers=headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    return response.status_code == 200

def test_list_documents_with_default_dataset_id():
    """Test the updated /api/v1/datasets/default/documents endpoint."""
    url = f"{HOST_ADDRESS}/api/v1/datasets/default/documents"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    print("\nTesting /api/v1/datasets/default/documents endpoint...")
    response = requests.get(url, headers=headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    return response.status_code == 200

def test_list_documents_with_empty_dataset_id():
    """Test the updated /api/v1/datasets//documents endpoint."""
    url = f"{HOST_ADDRESS}/api/v1/datasets//documents"
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    print("\nTesting /api/v1/datasets//documents endpoint...")
    response = requests.get(url, headers=headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    return response.status_code == 200

if __name__ == "__main__":
    print("Testing updated document API endpoints...")
    print("=" * 50)
    
    # Update API_KEY from environment variable if available
    if os.getenv("RAGFLOW_API_KEY"):
        API_KEY = os.getenv("RAGFLOW_API_KEY")
    
    if API_KEY == "your_api_key_here":
        print("Please set your API key in the script or RAGFLOW_API_KEY environment variable")
        exit(1)
    
    results = []
    
    # Test the new endpoints
    results.append(test_list_documents_with_default_kb())
    results.append(test_list_documents_with_default_dataset_id())
    results.append(test_list_documents_with_empty_dataset_id())
    
    print("\n" + "=" * 50)
    print(f"Test Summary: {sum(results)}/{len(results)} tests passed") 