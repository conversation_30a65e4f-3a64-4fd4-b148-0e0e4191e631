from __future__ import annotations

"""Domain models for Extrapolate (举一反三) agent."""

from dataclasses import dataclass, field
from typing import Optional, TYPE_CHECKING

from pydantic import BaseModel, Field, root_validator

from agent.common_exam.enums import Difficulty, QuestionDistribution

# Import type for upload context to avoid circular imports
if TYPE_CHECKING:
    # Import only for static type checking to avoid circular runtime imports
    from api.services.extrapolate_service import _UploadContext


class QuestionVariantMode(str):
    SAME = "same"
    CUSTOM = "custom"


@dataclass
class ExtrapolateContext:
    question_type_mode: str  # same / custom
    structure_mode: str      # same / custom
    knowledge_points_mode: str  # same_context / custom
    difficulty_mode: str  # same / basic / medium / hard
    quantity: int

    sample_content: str = ""
    custom_question_type: str | None = None
    custom_structure: str | None = None
    custom_knowledge_points: str | None = None

    template_text: str = ""

    tenant_id: str | None = None
    user_id: str | None = None

    # New streaming-related fields for enhanced SSE support
    session_id: Optional[str] = None
    # Use module-level _UploadContext to prevent forward-reference import errors
    upload_context: Optional["_UploadContext"] = None


class ExtrapolateParam(BaseModel):
    """Validated parameters from API/form layer."""

    question_type: str = Field("same", description="题型设置 same/custom")
    custom_question_type: Optional[str] = None

    structure: str = Field("same")
    custom_structure: Optional[str] = None

    knowledge_points: str = Field("same_context")  # same_context / custom
    custom_knowledge_points: Optional[str] = None

    difficulty: str = Field("same")  # same/basic/medium/hard
    quantity: int = Field(5, ge=1, le=50)

    llm_id: str = Field("deepseek-r1:32b@Ollama")

    stream: bool = True

    class Config:
        validate_assignment = True

    # validators
    @root_validator(skip_on_failure=True)
    def _custom_checks(cls, values):
        if values.get("question_type") == "custom" and not values.get("custom_question_type"):
            raise ValueError(
                "custom_question_type is required when question_type is custom")
        if values.get("structure") == "custom" and not values.get("custom_structure"):
            raise ValueError(
                "custom_structure is required when structure is custom")
        if values.get("knowledge_points") == "custom" and not values.get("custom_knowledge_points"):
            raise ValueError(
                "custom_knowledge_points is required when knowledge_points is custom")
        return values
