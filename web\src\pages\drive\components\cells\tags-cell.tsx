import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from '@/components/ui/hover-card';
import { IFile } from '@/interfaces/database/file-manager';
import { useCallback } from 'react';

export function TagsCell({ value }: { value: IFile['tags'] }) {
  const renderTags = useCallback((tags: string[] = []) => {
    return tags.map((tag, index) => (
      <Badge
        key={index}
        variant={'outline'}
        className="inline-flex items-center px-3 py-1 bg-slate-100 text-slate-600 border-slate-200 rounded-2xl text-xs font-medium border hover:bg-slate-150 transition-colors"
        style={{
          backgroundColor: '#f1f5f9',
          color: '#64748b',
          borderColor: '#e2e8f0',
          borderRadius: '16px',
          fontSize: '12px',
          fontWeight: '500',
          padding: '4px 12px',
        }}
      >
        {tag}
      </Badge>
    ));
  }, []);

  const tags = Array.isArray(value) ? value : [];

  if (tags.length === 0) {
    return <span className="text-gray-400 text-sm"></span>;
  }

  return (
    <section className="flex gap-1 items-center max-w-full">
      <div className="flex gap-1 items-center flex-wrap">
        {renderTags(tags.slice(0, 2))}
      </div>

      {tags.length > 2 && (
        <HoverCard openDelay={300} closeDelay={100}>
          <HoverCardTrigger asChild>
            <Button
              variant={'ghost'}
              size={'sm'}
              className="px-2 py-1 h-6 text-xs text-gray-500 hover:bg-gray-100 transition-colors font-medium"
            >
              +{tags.length - 2}
            </Button>
          </HoverCardTrigger>
          <HoverCardContent
            className="flex gap-1 flex-wrap max-w-xs p-3"
            align="start"
          >
            <div className="text-xs text-gray-600 mb-2 font-medium">
              全部标签 ({tags.length})
            </div>
            <div className="flex gap-1 flex-wrap">{renderTags(tags)}</div>
          </HoverCardContent>
        </HoverCard>
      )}
    </section>
  );
}
