@import '@/theme/high-tech-theme.less';

.chatContainer {
  flex: 1;
  padding: 0;
  background: #ffffff;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  /* 顶部会话标题区域 */
  .conversationHeader {
    /* 原先被隐藏，改为可见并固定布局 */
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    height: 48px;
    padding: 16px 8px;

    .conversationTitle {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      padding: 8px 12px;
      color: var(--text-primary);
      background: #d6ebfa;
      border-radius: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .messageContainer {
    overflow-y: auto;
    padding: 0 8px;
    position: relative;
    z-index: 1;
    max-width: 760px;
    margin: 0 auto;
    flex: 1;
    min-height: 0;
    width: 100%;

    .messagesSection {
      /* 消息间距 */
      > div {
        margin-bottom: 32px;
        transition: all 0.3s ease;
      }

      /* 当前可见的消息样式 */
      .visibleMessage {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -8px;
          top: 0;
          bottom: 0;
          width: 3px;
          background: linear-gradient(
            180deg,
            var(--primary-color) 0%,
            #38bdf8 100%
          );
          border-radius: 3px;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }
    }
  }

  /* 分隔线 */
  .sectionDivider {
    margin: var(--spacing-lg) 0;
    border-color: var(--border-color);
  }

  /* 相关资源区域 */
  .resourcesSection {
    flex-shrink: 0;
    max-height: 300px;
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-md);

    .resourcesHeader {
      margin-bottom: var(--spacing-sm);

      .resourcesTitle {
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: 16px;
        color: var(--text-primary);

        .resourcesIcon {
          color: var(--accent-color);
          font-size: 16px;
        }
      }
    }

    .resourcesContainer {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);

      .resourceCard {
        padding: var(--spacing-md);
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);
        background: var(--card-background);
        transition: all 0.2s ease;

        &:hover {
          border-color: var(--primary-color);
          box-shadow: var(--card-hover-shadow);
        }

        .resourceTitle {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          font-weight: 500;
          color: var(--text-primary);
          margin-bottom: var(--spacing-xs);

          .resourceFileIcon {
            color: var(--primary-color);
            font-size: 14px;
          }

          span {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .resourceDescription {
          font-size: 12px;
          color: var(--text-secondary);
          line-height: 1.4;
        }
      }

      // 覆盖RetrievalDocuments组件的样式
      :global(.ant-collapse) {
        background: transparent;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius-md);

        :global(.ant-collapse-item) {
          border-bottom: none;

          :global(.ant-collapse-header) {
            background: var(--background-secondary);
            border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
            padding: var(--spacing-sm) var(--spacing-md);

            &:hover {
              background: var(--background-tertiary);
            }
          }

          :global(.ant-collapse-content) {
            border-top: 1px solid var(--border-color);

            :global(.ant-collapse-content-box) {
              padding: var(--spacing-md);
              max-height: 200px;
              overflow-y: auto;
            }
          }
        }
      }

      // 覆盖表格样式
      :global(.ant-table) {
        background: transparent;

        :global(.ant-table-thead) {
          display: none; // 隐藏表头
        }

        :global(.ant-table-tbody) {
          :global(.ant-table-row) {
            transition: all var(--transition-fast);

            &:hover {
              background-color: var(--primary-ultralight);
            }

            &.ant-table-row-selected {
              background-color: rgba(var(--primary-color-rgb), 0.1);
            }
          }
        }

        :global(.ant-table-cell) {
          border-bottom: 1px solid var(--border-color-light);
          padding: var(--spacing-xs) var(--spacing-sm);
        }
      }

      // 覆盖复选框样式
      :global(.ant-checkbox-wrapper) {
        :global(.ant-checkbox) {
          :global(.ant-checkbox-inner) {
            border-color: var(--border-color);
            border-radius: 3px;

            &:after {
              border-color: white;
            }
          }

          &.ant-checkbox-checked {
            :global(.ant-checkbox-inner) {
              background-color: var(--primary-color);
              border-color: var(--primary-color);
            }
          }
        }
      }
    }
  }

  .emptyState {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    text-align: center;
    color: #666;
    height: 300px;

    .emptyStateIcon {
      font-size: 32px;
      margin-bottom: 16px;
      color: #1890ff;
    }

    h4 {
      margin-bottom: 8px;
      color: #333;
      font-weight: 500;
    }
  }

  .messageInputWrapper {
    position: relative;
    z-index: 1;
    flex-shrink: 0;
    padding: 16px; // 增加顶部padding，确保总高度约108px
    max-width: 760px;
    margin: 0 auto;
    width: 100%;
    min-height: 108px;
  }

  /* Perplexity-inspired tabs */
  .responseTabs {
    margin: var(--spacing-md) 0 var(--spacing-lg) var(--spacing-md);
    max-width: 800px;

    :global(.ant-tabs-nav) {
      margin-bottom: 0;
    }

    :global(.ant-tabs-tab) {
      padding: var(--spacing-sm) var(--spacing-md);
      transition: all var(--transition-normal);

      &:hover {
        color: var(--primary-color);
      }
    }

    :global(.ant-tabs-tab-active) {
      .tabItem {
        color: var(--primary-color);
        font-weight: 500;
      }
    }

    :global(.ant-tabs-ink-bar) {
      background: var(--primary-color);
    }
  }

  .tabItem {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    .tabIcon {
      width: 16px;
      height: 16px;
    }

    .tabCount {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      background-color: var(--neutral-200);
      color: var(--neutral-700);
      border-radius: 999px;
      font-size: 12px;
      padding: 0 6px;
      margin-left: var(--spacing-xs);
      height: 18px;
      min-width: 18px;
    }
  }

  :global {
    .ant-btn {
      border-radius: 8px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
      }
    }

    .message-input-container {
      position: relative;
      z-index: 1;
      border-radius: 16px;
      background-color: #ffffff;
      box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.06),
        0 1px 2px rgba(0, 0, 0, 0.04);
      border: 1px solid #e5e7eb;
      transition: all 0.2s ease;
      max-width: 760px;
      margin: 0 auto;
      width: 100%;

      &:focus-within {
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.1),
          0 2px 4px rgba(0, 0, 0, 0.06);
        border-color: #d1d5db;
      }

      // 输入框内部样式
      .ant-input {
        border: none !important;
        box-shadow: none !important;
        background: transparent !important;
        padding: 16px 20px;
        font-size: 15px;
        line-height: 1.5;

        &::placeholder {
          color: #9ca3af;
        }

        &:focus {
          border: none !important;
          box-shadow: none !important;
        }
      }

      // 左侧加号按钮
      .left-action-button {
        padding: 8px;
        border: none;
        background: transparent;
        color: #6b7280;
        border-radius: 6px;
        margin-left: 12px;

        &:hover {
          background-color: #f3f4f6;
          color: #374151;
        }
      }

      // 右侧按钮组
      .right-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-right: 12px;

        .action-button {
          padding: 8px;
          border: none;
          background: transparent;
          color: #6b7280;
          border-radius: 6px;

          &:hover {
            background-color: #f3f4f6;
            color: #374151;
          }

          &.canvas-button {
            color: #1890ff;
            font-weight: 500;
            padding: 6px 12px;

            &:hover {
              background-color: #e6f7ff;
              color: #1890ff;
            }
          }
        }
      }
    }

    // 用户消息气泡样式
    .message-item-user {
      .message-content {
        background-color: #f1f3f4;
        border-radius: 20px;
        padding: 12px 16px;
        margin-left: auto;
        max-width: 80%;
        display: inline-block;
        color: #333;
        font-size: 15px;
        line-height: 1.5;
      }
    }

    // AI消息样式
    .message-item-assistant {
      .message-content {
        background: none;
        border: none;
        padding: 0;
        color: #333;
        font-size: 15px;
        line-height: 1.75;

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          color: #111;
          font-weight: 600;
          margin: 16px 0 8px 0;
        }

        p {
          margin-bottom: 12px;
        }

        ul,
        ol {
          margin: 12px 0;
          padding-left: 20px;
        }

        li {
          margin-bottom: 4px;
        }

        code {
          background-color: #f1f3f4;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 14px;
        }

        pre {
          background-color: #f8f9fa;
          padding: 16px;
          border-radius: 8px;
          overflow-x: auto;
          margin: 12px 0;
        }
      }
    }
  }
}
