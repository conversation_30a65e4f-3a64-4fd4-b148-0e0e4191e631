@import '@/theme/high-tech-theme.less';

// File upload section styling (exactly same as lesson-plan-form)
.fileUploadSection {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);

  .uploadSection {
    .uploadInfo {
      font-size: 0.9rem;
      color: var(--text-secondary);
      margin-bottom: var(--spacing-xs);
    }

    .uploadBox {
      border: 2px dashed var(--border-color);
      border-radius: var(--border-radius-md);
      padding: var(--spacing-lg);
      min-height: 120px;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--bg-subtle);
      transition: all var(--transition-normal);
      cursor: pointer;
      box-sizing: border-box;
      position: relative;

      &:hover {
        border-color: var(--primary-color);
        background-color: rgba(var(--primary-color-rgb), 0.02);
      }

      .uploadPlaceholder {
        text-align: center;

        .uploadIcon {
          width: 48px;
          height: 48px;
          margin: 0 auto var(--spacing-md);
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='none' stroke='%23a0aec0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: center;
        }

        .dragText {
          font-size: 1rem;
          color: var(--text-secondary);
          margin-bottom: var(--spacing-xs);

          .browseText {
            color: var(--primary-color);
            cursor: pointer;
            font-weight: 500;

            &:hover {
              text-decoration: underline;
            }
          }
        }

        .supportedFormats {
          font-size: 0.8rem;
          color: var(--text-tertiary);
        }
      }

      .ant-upload-drag-container {
        width: 100%;
        height: 100%;
      }
    }
  }

  // Upload.Dragger overrides to use our custom styling
  :global(.ant-upload-wrapper) {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;

    // Remove all default antd upload spacing
    &,
    & * {
      box-sizing: border-box !important;
    }

    &:hover {
      border: none !important;
      background: transparent !important;
    }

    :global(.ant-upload-drag-container) {
      padding: 0 !important;
      margin: 0 !important;
      width: 100% !important;
      height: 100% !important;
    }

    // Ensure the uploadBox fills the entire dragger area
    :global(.ant-upload-btn) {
      padding: 0 !important;
      width: 100% !important;
      height: 100% !important;
    }

    &.ant-upload-drag-hover {
      .uploadBox {
        border-color: var(--primary-color) !important;
        background-color: rgba(var(--primary-color-rgb), 0.02) !important;
      }
    }

    // Force the upload content to match our custom styling
    & > .ant-upload-btn {
      display: block !important;
      width: 100% !important;
      height: auto !important;
      padding: 0 !important;
      border: none !important;
      background: none !important;

      & > div {
        width: 100% !important;
      }
    }
  }
}

.extrapolateForm {
  .space-y-6 > * + * {
    margin-top: 1.5rem;
  }

  // Form field styling
  :global(.ant-form-item) {
    margin-bottom: var(--spacing-lg);
  }

  :global(.ant-form-item-label) {
    font-weight: 500;
    color: var(--text-primary);
  }

  // Radio group styling
  :global(.ant-radio-group) {
    width: 100%;
  }

  :global(.ant-radio-wrapper) {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);

    &:hover {
      background-color: rgba(var(--primary-color-rgb), 0.05);
    }
  }

  // Input styling (consistent with lesson-plan-form)
  :global(.ant-input),
  :global(.ant-input-number),
  :global(.ant-select-selector),
  :global(.ant-input-textarea) {
    border-radius: var(--border-radius-md) !important;
    border: 1px solid var(--border-color) !important;
    background-color: white !important;
    transition: all var(--transition-normal);

    &:hover,
    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1) !important;
    }
  }

  textarea.text-base,
  input.text-base {
    background-color: white !important;
  }

  // Upload component styling
  :global(.ant-upload) {
    width: 100%;
  }

  :global(.ant-upload-list) {
    margin-top: var(--spacing-sm);
  }

  // Textarea styling
  :global(.ant-input) {
    resize: vertical;
    min-height: 40px;
  }

  // Number input styling
  :global(.ant-input-number) {
    width: 100%;
  }

  // Select styling
  :global(.ant-select) {
    width: 100%;
  }

  :global(.ant-select-dropdown) {
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-lg);
  }

  // Form validation styling
  :global(.ant-form-item-has-error) {
    :global(.ant-input),
    :global(.ant-input-number),
    :global(.ant-select-selector) {
      border-color: var(--error-color);
    }
  }

  :global(.ant-form-item-explain-error) {
    color: var(--error-color);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
  }
}

// Radio Button 样式 - 与教案生成页面保持一致
.radioSelector {
  margin-top: var(--spacing-xs);

  .radioGroup {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);

    .radioOption {
      font-size: 14px;
      color: var(--text-primary);

      .ant-radio {
        .ant-radio-inner {
          border-color: var(--border-color);
          background-color: white;

          &::after {
            background-color: var(--primary-color);
          }
        }

        &.ant-radio-checked {
          .ant-radio-inner {
            border-color: var(--primary-color);
            background-color: white;
          }
        }
      }

      &:hover {
        .ant-radio {
          .ant-radio-inner {
            border-color: var(--primary-color);
          }
        }
      }
    }
  }
}
