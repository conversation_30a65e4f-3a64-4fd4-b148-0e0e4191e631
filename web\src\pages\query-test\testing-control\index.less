@import '@/theme/high-tech-theme.less';

.testingControlWrapper {
  height: 100%;
  padding: var(--spacing-md);
  background: var(--background-primary);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.sectionCard {
  margin-bottom: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  background: var(--card-background);
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    border-color: var(--primary-color);
    box-shadow: var(--card-hover-shadow);
  }

  :global(.ant-card-head) {
    background: linear-gradient(
      135deg,
      var(--primary-ultralight) 0%,
      rgba(56, 189, 248, 0.05) 100%
    );
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    padding: var(--spacing-sm) var(--spacing-md);
    min-height: auto;
  }

  .ant-card-body {
    padding: var(--spacing-md);
  }
}

.sectionCard:nth-child(1) {
  flex: 1 1 auto;
  min-height: 200px;
  overflow: hidden;
}

.sectionCard:nth-child(2) {
  flex: 0 0 auto;
  max-height: 300px;
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.headerRight {
  display: flex;
  align-items: center;
}

.sectionIcon {
  color: var(--primary-color);
  font-size: 16px;
}

.parameterSelect {
  margin: 0;

  .ant-form-item-label {
    display: none;
  }

  .ant-form-item-control {
    width: 100%;
  }

  .ant-select {
    font-size: var(--font-size-sm);
  }
}

// 自由度设置面板样式
.freedomSettings {
  .ant-form-item {
    margin-bottom: 0;
  }
}

.formItemGroup {
  margin-bottom: var(--spacing-md);

  &:last-child {
    margin-bottom: 0;
  }
}

.labelRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.itemLabel {
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--text-primary);
}

.switchItem {
  margin: 0;

  .ant-form-item-control-input {
    min-height: auto;
  }
}

.controlRow {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.sliderItem {
  flex: 1;
  margin: 0;

  .ant-form-item-control-input {
    min-height: auto;
  }
}

.numberItem {
  margin: 0;
  width: 80px;

  .ant-form-item-control-input {
    min-height: auto;
  }
}

// 滑块相关样式
.variableSlider {
  width: 100% !important;
  margin: 0 !important;

  .ant-slider-track {
    background: var(--primary-color);
  }

  .ant-slider-handle {
    border-color: var(--primary-color);

    &:hover,
    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 5px rgba(24, 144, 255, 0.12);
    }
  }
}

.sliderInputNumber {
  width: 100% !important;
}

// 测试文本面板样式
.testingSection {
  .ant-form-item {
    margin-bottom: var(--spacing-md);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.testTextarea {
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;

  &:focus,
  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

.testButton {
  width: 100%;
  height: 40px;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.15);

  &:hover:not(:disabled) {
    box-shadow: 0 4px 8px rgba(24, 144, 255, 0.25);
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.6;
  }
}

// 表单标签样式
.ant-form-item-label > label {
  color: var(--text-primary);
  font-weight: 500;
  font-size: var(--font-size-sm);
}

// Switch开关样式
.ant-switch {
  &.ant-switch-checked {
    background: var(--primary-color);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .testingControlWrapper {
    padding: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  .sectionCard {
    margin-bottom: var(--spacing-sm);

    .ant-card-body {
      padding: var(--spacing-sm);
    }
  }
}

@media (max-width: 768px) {
  .sectionHeader {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }

  .headerRight {
    width: 100%;
    justify-content: flex-end;
  }

  .parameterSelect {
    width: 100%;
  }

  .sliderInputNumber {
    width: 60px !important;
  }

  .controlRow {
    gap: var(--spacing-xs);
  }
}

/* 文档选择部分 */
.documentSection {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 150px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-sm);
}

.documentList {
  width: 100%;
  overflow: hidden;
  .documentListContent {
    max-height: 180px;
    overflow-y: auto;
  }
}

.selectAllContainer {
  padding: 0 0 var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-sm);
}

.documentItem {
  padding: var(--spacing-xs) 0 !important;
  border-bottom: 1px solid var(--neutral-100) !important;
  overflow: hidden;

  &:last-child {
    border-bottom: none !important;
  }

  :global(.ant-checkbox-wrapper) {
    width: 100%;
    overflow: hidden;
  }

  :global(.ant-space) {
    width: 100%;
    overflow: hidden;
  }
}

.documentName {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 400;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.documentInfo {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  width: 100%;
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.emptyDocuments {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-tertiary);
}
