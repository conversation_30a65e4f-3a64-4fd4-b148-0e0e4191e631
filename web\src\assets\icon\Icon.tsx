import { cn } from '@/lib/utils';
import Icon from '@ant-design/icons';
import { CustomIconComponentProps } from '@ant-design/icons/lib/components/Icon';
import { ChevronDown } from 'lucide-react';

type IconComponentProps = CustomIconComponentProps;
const currentColor = 'currentColor';
const ApiSvg = () => (
  <svg
    viewBox="0 0 1025 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
  >
    <path
      d="M1019.520374 56.394706L966.393742 3.257862c-2.001695-2.001695-4.503815-2.87999-7.1387-2.879991s-5.137004 1.000848-7.148912 2.879991L856.749851 98.61414C814.520204 69.916363 765.396963 55.638964 716.283935 55.638964c-64.156382 0-128.312765 24.428855-177.30324 73.429543L411.280694 256.758295c-3.880838 3.880838-3.880838 10.274008 0 14.154847L751.74254 611.364776c2.001695 2.001695 4.514028 2.890203 7.1387 2.890203 2.512332 0 5.137004-1.000848 7.1387-2.890203l127.689788-127.689788c86.338436-86.460989 96.489892-220.166077 30.454367-317.646604l95.356279-95.366491c3.880838-3.993178 3.880838-10.386348 0-14.267187zM833.321844 423.409656l-74.430391 74.440604-234.075818-234.075818 74.430391-74.430391c31.199896-31.199896 72.806566-48.490052 117.037909-48.490051 44.231342 0 85.705247 17.167603 117.037909 48.490051 31.199896 31.199896 48.490052 72.806566 48.490051 117.037909 0 44.231342-17.290155 85.705247-48.490051 117.027696zM594.987317 554.980283c-3.880838-3.880838-10.274008-3.880838-14.154847 0l-83.458446 83.458446-113.15707-113.146858 83.580998-83.580999c3.891051-3.891051 3.891051-10.284221 0-14.165059l-45.610061-45.610061c-3.880838-3.891051-10.274008-3.891051-14.154846 0l-83.580999 83.580998-53.882374-53.882374c-2.001695-2.001695-4.514028-2.87999-7.1387-2.87999-2.512332 0-5.137004 1.000848-7.138699 2.87999L128.725037 539.313952C42.386601 625.774941 32.235145 759.480028 98.27067 856.970768L2.914392 952.33726c-3.880838 3.880838-3.880838 10.274008 0 14.154846l53.136844 53.126632c2.001695 2.001695 4.514028 2.87999 7.1387 2.87999 2.634885 0 5.137004-1.000848 7.1387-2.87999l95.366491-95.356279c42.219434 28.697777 91.352888 42.985389 140.465916 42.985389 64.156382 0 128.312765-24.439068 177.30324-73.429543l127.689789-127.689788c3.880838-3.891051 3.880838-10.274008 0-14.154847l-53.882374-53.882374 83.580998-83.580998c3.880838-3.880838 3.880838-10.274008 0-14.154847l-45.865379-45.375168zM423.066186 833.665314c-31.199896 31.199896-72.806566 48.490052-117.037908 48.490051-44.231342 0-85.705247-17.167603-117.037909-48.490051-31.199896-31.199896-48.490052-72.806566-48.490052-117.037909 0-44.231342 17.167603-85.71546 48.490052-117.037909l74.430391-74.430391 234.075817 234.075818-74.430391 74.430391z m0 0"
      fill={currentColor}
    ></path>
  </svg>
);
const TeamSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22 21V19C22 17.1362 20.7252 15.5701 19 15.126M15.5 3.29076C16.9659 3.88415 18 5.32131 18 7C18 8.67869 16.9659 10.1159 15.5 10.7092M17 21C17 19.1362 17 18.2044 16.6955 17.4693C16.2895 16.4892 15.5108 15.7105 14.5307 15.3045C13.7956 15 12.8638 15 11 15H8C6.13623 15 5.20435 15 4.46927 15.3045C3.48915 15.7105 2.71046 16.4892 2.30448 17.4693C2 18.2044 2 19.1362 2 21M13.5 7C13.5 9.20914 11.7091 11 9.5 11C7.29086 11 5.5 9.20914 5.5 7C5.5 4.79086 7.29086 3 9.5 3C11.7091 3 13.5 4.79086 13.5 7Z"
      stroke={currentColor}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const ProfileSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M20 21C20 19.6044 20 18.9067 19.8278 18.3389C19.44 17.0605 18.4395 16.06 17.1611 15.6722C16.5933 15.5 15.8956 15.5 14.5 15.5H9.5C8.10444 15.5 7.40665 15.5 6.83886 15.6722C5.56045 16.06 4.56004 17.0605 4.17224 18.3389C4 18.9067 4 19.6044 4 21M16.5 7.5C16.5 9.98528 14.4853 12 12 12C9.51472 12 7.5 9.98528 7.5 7.5C7.5 5.01472 9.51472 3 12 3C14.4853 3 16.5 5.01472 16.5 7.5Z"
      stroke={currentColor}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const PasswordSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17 10V8C17 5.23858 14.7614 3 12 3C9.23858 3 7 5.23858 7 8V10M12 14.5V16.5M8.8 21H15.2C16.8802 21 17.7202 21 18.362 20.673C18.9265 20.3854 19.3854 19.9265 19.673 19.362C20 18.7202 20 17.8802 20 16.2V14.8C20 13.1198 20 12.2798 19.673 11.638C19.3854 11.0735 18.9265 10.6146 18.362 10.327C17.7202 10 16.8802 10 15.2 10H8.8C7.11984 10 6.27976 10 5.63803 10.327C5.07354 10.6146 4.6146 11.0735 4.32698 11.638C4 12.2798 4 13.1198 4 14.8V16.2C4 17.8802 4 18.7202 4.32698 19.362C4.6146 19.9265 5.07354 20.3854 5.63803 20.673C6.27976 21 7.11984 21 8.8 21Z"
      stroke={currentColor}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const LogOutSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16 17L21 12M21 12L16 7M21 12H9M9 3H7.8C6.11984 3 5.27976 3 4.63803 3.32698C4.07354 3.6146 3.6146 4.07354 3.32698 4.63803C3 5.27976 3 6.11984 3 7.8V16.2C3 17.8802 3 18.7202 3.32698 19.362C3.6146 19.9265 4.07354 20.3854 4.63803 20.673C5.27976 21 6.11984 21 7.8 21H9"
      stroke={currentColor}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const ModelProviderSvg = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M9.75 20.7496L11.223 21.5679C11.5066 21.7255 11.6484 21.8043 11.7986 21.8351C11.9315 21.8625 12.0685 21.8625 12.2015 21.8351C12.3516 21.8043 12.4934 21.7255 12.777 21.5679L14.25 20.7496M5.25 18.2496L3.82297 17.4568C3.52346 17.2904 3.37368 17.2072 3.26463 17.0889C3.16816 16.9842 3.09515 16.8601 3.05048 16.7249C3 16.5721 3 16.4008 3 16.0582V14.4996M3 9.4996V7.94104C3 7.5984 3 7.42708 3.05048 7.27428C3.09515 7.1391 3.16816 7.01502 3.26463 6.91033C3.37368 6.792 3.52345 6.70879 3.82297 6.54239L5.25 5.7496M9.75 3.2496L11.223 2.43128C11.5066 2.27372 11.6484 2.19494 11.7986 2.16406C11.9315 2.13672 12.0685 2.13672 12.2015 2.16406C12.3516 2.19494 12.4934 2.27372 12.777 2.43128L14.25 3.2496M18.75 5.7496L20.177 6.54239C20.4766 6.70879 20.6263 6.79199 20.7354 6.91033C20.8318 7.01502 20.9049 7.1391 20.9495 7.27428C21 7.42708 21 7.5984 21 7.94104V9.4996M21 14.4996V16.0582C21 16.4008 21 16.5721 20.9495 16.7249C20.9049 16.8601 20.8318 16.9842 20.7354 17.0889C20.6263 17.2072 20.4766 17.2904 20.177 17.4568L18.75 18.2496M9.75 10.7496L12 11.9996M12 11.9996L14.25 10.7496M12 11.9996V14.4996M3 6.9996L5.25 8.2496M18.75 8.2496L21 6.9996M12 19.4996V21.9996"
      stroke={currentColor}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
const PromptSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="1em"
    height="1em"
  >
    <path
      d="M509.952 260.864c-152.832 0-277.248 124.416-277.248 277.248 0 152.832 124.416 277.248 277.248 277.248 152.832 0 277.248-124.416 277.248-277.248C787.2 385.28 662.784 260.864 509.952 260.864zM509.952 746.24c-114.688 0-208.128-93.44-208.128-208.128 0-114.688 93.44-208.128 208.128-208.128s208.128 93.44 208.128 208.128C717.824 652.8 624.64 746.24 509.952 746.24z"
      fill={currentColor}
    ></path>
    <path
      d="M509.952 211.2c22.016 0 39.68-17.664 39.68-39.68L549.632 77.056c0-22.016-17.664-39.68-39.68-39.68s-39.68 17.664-39.68 39.68l0 94.464C470.272 193.28 487.936 211.2 509.952 211.2z"
      fill={currentColor}
    ></path>
    <path
      d="M134.144 538.88l-94.464 0c-22.016 0-39.68 17.664-39.68 39.68s17.664 39.68 39.68 39.68l94.464 0c22.016 0 39.68-17.664 39.68-39.68S155.904 538.88 134.144 538.88z"
      fill={currentColor}
    ></path>
    <path
      d="M984.32 538.88l-94.464 0c-22.016 0-39.68 17.664-39.68 39.68s17.664 39.68 39.68 39.68l94.464 0c22.016 0 39.68-17.664 39.68-39.68S1006.336 538.88 984.32 538.88z"
      fill={currentColor}
    ></path>
    <path
      d="M901.632 196.864c-15.616-15.616-40.704-15.616-56.064 0l-66.816 66.816c-15.616 15.616-15.616 40.704 0 56.064 7.68 7.68 17.92 11.52 28.16 11.52 10.24 0 20.224-3.84 28.16-11.52l66.816-66.816C916.992 237.568 916.992 212.224 901.632 196.864z"
      fill={currentColor}
    ></path>
    <path
      d="M241.152 263.424l-66.816-66.816c-15.616-15.616-40.704-15.616-56.064 0-15.616 15.616-15.616 40.704 0 56.064l66.816 66.816c7.68 7.68 17.92 11.52 28.16 11.52 10.24 0 20.224-3.84 28.16-11.52C256.768 304.128 256.768 279.04 241.152 263.424z"
      fill={currentColor}
    ></path>
    <path
      d="M605.184 866.56 414.72 866.56c-13.056 0-23.808 9.472-23.808 21.248s10.752 21.248 23.808 21.248l190.464 0c13.056 0 23.808-9.472 23.808-21.248S618.24 866.56 605.184 866.56z"
      fill={currentColor}
    ></path>
    <path
      d="M577.536 944.384l-135.168 0c-13.056 0-23.808 9.472-23.808 21.248s10.752 21.248 23.808 21.248l135.168 0c13.056 0 23.808-9.472 23.808-21.248S590.592 944.384 577.536 944.384z"
      fill={currentColor}
    ></path>
  </svg>
);
const WikipediaSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
  >
    <path
      d="M515.84 559.744c-39.936 82.432-94.592 194.048-121.728 244.394667-26.282667 45.824-48.085333 39.722667-65.365333 1.237333-59.989333-141.696-183.168-390.144-241.109334-529.450667-10.709333-25.642667-18.816-42.112-26.410666-48.597333-7.722667-6.4-23.637333-10.24-47.872-11.562667C4.394667 214.741333 0 212.565333 0 208.981333v-19.413333l2.218667-1.92c39.424-0.213333 230.442667 0 230.442666 0l2.176 1.92v18.517333c0 5.077333-3.2 7.509333-9.6 7.509334l-24.064 1.322666c-20.693333 1.237333-31.018667 6.997333-31.018666 18.602667 0 5.76 2.261333 14.08 7.082666 25.642667 46.165333 112.896 205.568 448.896 205.568 448.896l5.802667 1.962666 102.869333-205.226666-20.565333-45.525334-70.741333-139.264s-13.568-27.904-18.261334-37.205333c-31.061333-61.568-30.378667-64.768-61.738666-68.992-8.832-0.981333-13.354667-2.133333-13.354667-6.357333v-19.968l2.56-1.92h183.125333l4.821334 1.578666v19.242667c0 4.48-3.242667 6.4-9.685334 6.4l-13.141333 2.005333c-33.792 2.602667-28.202667 16.256-5.802667 60.672l67.498667 138.752 75.008-149.504c12.501333-27.306667 9.941333-34.176 4.736-40.405333-2.986667-3.584-13.013333-9.386667-34.645333-10.24l-8.576-0.896a9.728 9.728 0 0 1-6.186667-2.176 6.4 6.4 0 0 1-2.858667-5.504v-18.218667l2.602667-1.92c53.205333-0.341333 172.501333 0 172.501333 0l2.517334 1.92v18.602667c0 5.162667-2.517333 7.594667-8.234667 7.594667-27.562667 1.28-33.365333 4.053333-43.648 18.730666-5.12 7.936-16 25.130667-27.562667 44.330667l-98.176 182.314667-2.773333 5.76 119.125333 243.712 7.253334 2.048 187.562666-445.354667c6.570667-18.005333 5.504-30.805333-2.730666-38.186667-8.405333-7.338667-14.762667-11.648-36.565334-12.586666l-17.92-0.682667a10.88 10.88 0 0 1-6.485333-1.92c-1.834667-1.237333-3.072-3.2-3.072-5.077333v-18.602667l2.517333-1.92h211.669334l1.749333 1.92v18.645333c0 5.077333-3.157333 7.68-8.917333 7.68-27.648 1.28-48.085333 7.68-61.568 17.962667-13.397333 10.88-23.765333 26.282667-31.402667 45.525333 0 0-172.501333 395.008-231.509333 526.464-22.4 42.965333-44.928 39.125333-64.128-1.322666-24.362667-49.962667-75.648-161.536-112.896-243.626667l2.261333-1.536z"
      fill={currentColor}
    ></path>
  </svg>
);
const KeywordSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
  >
    <path
      d="M419.999992 631.999988v-136.699998c74.443999-50.009999 119.999998-134.033997 119.999997-223.299995C539.999989 123.113998 418.885992 0 269.999995 0S0 123.113998 0 271.999995c0 89.267998 45.555999 173.291997 119.999998 223.299995V871.999983c0 7.968 3.164 15.586 8.789999 21.21l119.999998 121.999997A29.903999 29.903999 0 0 0 269.999995 1023.99998a29.899999 29.899999 0 0 0 21.209999-8.79l119.999998-121.999997c11.718-11.718 11.718-30.703999 0-42.422L372.421993 811.999984l38.789999-38.789999c11.718-11.718 11.718-30.703999 0-42.421999L372.421993 691.999986l38.789999-38.789999A29.979999 29.979999 0 0 0 419.999992 631.999988z m-149.999997-329.999994c-49.627999 0-89.999998-40.371999-89.999999-89.999998s40.371999-89.999998 89.999999-89.999998 89.999998 40.371999 89.999998 89.999998-40.371999 89.999998-89.999998 89.999998z"
      fill={currentColor}
    ></path>
    <path
      d="M933.999982 361.999993c-39.059999 0-72.047999 25.156-84.473999 59.999999H753.999985a30.011999 30.011999 0 0 0-26.835999 16.581999L675.455987 541.999989H509.99999c-16.582 0-29.999999 13.418-29.999999 30s13.418 29.999999 29.999999 29.999999h183.999996a30.011999 30.011999 0 0 0 26.836-16.581999L772.543985 481.999991h76.981998c12.426 34.843999 45.413999 59.999999 84.473999 59.999998 49.627999 0 89.999998-40.371999 89.999998-89.999998s-40.371999-89.999998-89.999998-89.999998zM933.999982 601.999988c-39.059999 0-72.047999 25.156-84.473999 59.999999H509.99999c-16.582 0-29.999999 13.418-29.999999 29.999999s13.418 29.999999 29.999999 30h339.525993c12.426 34.843999 45.413999 59.999999 84.473999 59.999999 49.627999 0 89.999998-40.371999 89.999998-89.999999s-40.371999-89.999998-89.999998-89.999998zM933.999982 841.999984c-39.059999 0-72.047999 25.156-84.473999 59.999998h-76.981998l-51.707999-103.417998A30.011999 30.011999 0 0 0 693.999986 781.999985h-183.999996c-16.582 0-29.999999 13.418-29.999999 29.999999s13.418 29.999999 29.999999 30h165.455997l51.707999 103.417998C732.261986 955.583981 742.631985 963.999981 753.999985 963.999981h95.525998c12.426 34.843999 45.413999 59.999999 84.473999 59.999999 49.627999 0 89.999998-42.371999 89.999998-91.999998s-40.371999-89.999998-89.999998-89.999998zM933.999982 121.999998c-39.059999 0-72.047999 25.156-84.473999 59.999998H753.999985c-10.02 0-19.394 5.01-24.959999 13.36L617.945988 361.999993H569.999989c-16.582 0-29.999999 13.418-30 29.999999s13.418 29.999999 30 30h63.999999c10.02 0 19.394-5.01 24.959999-13.36L770.053985 241.999995h79.471998c12.426 34.843999 45.413999 59.999999 84.473999 59.999999 49.627999 0 89.999998-40.371999 89.999998-89.999998s-40.371999-89.999998-89.999998-89.999998z"
      fill={currentColor}
    ></path>
  </svg>
);
const GitHubSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
  >
    <path
      fill={currentColor}
      d="M938.666667 512a426.666667 426.666667 0 0 1-291.84 404.48 22.186667 22.186667 0 0 1-19.2-2.986667 21.76 21.76 0 0 1-8.96-17.493333v-113.92a170.666667 170.666667 0 0 0-21.333334-87.893333 10.666667 10.666667 0 0 1 0-11.52 11.52 11.52 0 0 1 8.533334-5.973334c104.106667-10.666667 162.133333-52.053333 162.133333-164.693333a200.96 200.96 0 0 0-50.773333-143.36 183.466667 183.466667 0 0 0 8.106666-51.2 184.746667 184.746667 0 0 0-6.4-46.08 20.906667 20.906667 0 0 0-22.613333-15.36 189.866667 189.866667 0 0 0-104.106667 50.346667 422.826667 422.826667 0 0 0-160.426666 0A189.866667 189.866667 0 0 0 327.68 256a20.906667 20.906667 0 0 0-22.613333 15.36A184.746667 184.746667 0 0 0 298.666667 317.44a183.466667 183.466667 0 0 0 8.106666 51.2A200.96 200.96 0 0 0 256 512c0 118.613333 64.426667 158.293333 182.613333 168.106667a158.293333 158.293333 0 0 0-29.44 65.28v5.12a29.013333 29.013333 0 0 0 0 5.973333 25.173333 25.173333 0 0 1-27.306666 21.76 42.666667 42.666667 0 0 1-18.346667-5.12 227.84 227.84 0 0 1-60.586667-53.76 430.506667 430.506667 0 0 0-34.133333-34.56 116.906667 116.906667 0 0 0-25.173333-16.64 20.906667 20.906667 0 0 0-20.48 0 21.333333 21.333333 0 0 0-9.813334 17.92v2.56a21.333333 21.333333 0 0 0 9.813334 17.92 193.706667 193.706667 0 0 1 39.253333 44.8 282.026667 282.026667 0 0 0 67.84 73.386667 105.813333 105.813333 0 0 0 59.733333 17.92h15.36V896a21.76 21.76 0 0 1-8.96 17.493333 22.186667 22.186667 0 0 1-19.2 2.986667A426.666667 426.666667 0 1 1 938.666667 512z"
    ></path>
  </svg>
);
const QWeatherSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
  >
    <path
      fill={currentColor}
      d="M953.6 736c-57.6 51.2-89.6 89.6-166.4 102.4 166.4-160 185.6-422.4 44.8-608-64-89.6-166.4-147.2-275.2-172.8-108.8-19.2-224 0-326.4 57.6-96 57.6-172.8 147.2-204.8 256-32 108.8-32 224 19.2 326.4 44.8 102.4 128 185.6 230.4 236.8 102.4 44.8 217.6 57.6 326.4 25.6 96 6.4 192-12.8 275.2-57.6 70.4-38.4 102.4-96 147.2-160l-70.4-6.4z m-537.6 172.8c-83.2-12.8-166.4-51.2-224-108.8-121.6-128-140.8-326.4-32-467.2 51.2-70.4 128-121.6 211.2-140.8 76.8-19.2 166.4-12.8 243.2 25.6 76.8 38.4 140.8 102.4 172.8 185.6 32 76.8 38.4 166.4 12.8 249.6-25.6 83.2-83.2 153.6-153.6 198.4-25.6 0-44.8-6.4-70.4-6.4-83.2-19.2-153.6-70.4-204.8-134.4-25.6-32-44.8-76.8-44.8-121.6 0-38.4 12.8-70.4 32-96 32-38.4 76.8-57.6 121.6-57.6 12.8 0 25.6 0 38.4 6.4 6.4 0 12.8 0 12.8 6.4 25.6 0 44.8 19.2 57.6 38.4 25.6 44.8 12.8 96-25.6 128-32 12.8-64 19.2-89.6 6.4h-12.8c0 6.4-6.4 6.4 0 12.8 19.2 32 51.2 51.2 89.6 51.2 25.6 0 51.2-6.4 76.8-19.2 57.6-32 96-89.6 96-153.6 0-38.4-12.8-70.4-32-102.4 0 0 0-6.4-6.4-6.4 0 0 0-6.4-6.4-6.4-51.2-64-128-102.4-217.6-102.4-83.2 0-153.6 32-211.2 89.6-83.2 83.2-102.4 211.2-44.8 320 51.2 89.6 128 160 217.6 204.8h-6.4z"
    ></path>
  </svg>
);

const SemicolonSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="200"
    height="200"
  >
    <path
      d="M506.88 249.059556a89.884444 89.884444 0 0 0 60.074667-21.845334 87.950222 87.950222 0 0 0 23.210666-61.44 79.189333 79.189333 0 0 0-83.285333-83.285333c-24.576 0-45.056 6.826667-60.074667 23.210667-16.384 15.018667-23.210667 35.498667-23.210666 60.074666 0 24.576 6.826667 45.056 23.210666 61.44 15.018667 13.653333 35.498667 21.845333 60.074667 21.845334zM414.037333 967.224889a262.030222 262.030222 0 0 0 141.994667-88.746667c35.498667-46.421333 53.248-99.669333 53.248-159.744 0-39.594667-9.557333-70.997333-27.306667-95.573333a89.543111 89.543111 0 0 0-75.093333-38.229333c-27.306667 0-47.786667 6.826667-62.805333 23.210666-17.749333 15.018667-25.941333 35.498667-25.941334 61.44 0 23.210667 8.192 43.690667 24.576 60.074667a79.416889 79.416889 0 0 0 58.709334 24.576 78.506667 78.506667 0 0 0 30.037333-5.461333c0 32.768-9.557333 62.805333-30.037333 91.477333a190.008889 190.008889 0 0 1-87.381334 60.074667v66.901333z"
      fill={currentColor}
    ></path>
  </svg>
);

const CommaSvg = () => (
  <svg
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="200"
    height="200"
  >
    <path
      fill={currentColor}
      d="M701.312 416.064C701.312 327.68 629.76 256 541.312 256c-88.32 0-160 71.68-160 160.064s71.68 160.064 160 160.064c10.368 0 20.352-1.216 30.144-3.072-27.136 78.592-88.32 99.392-166.4 120.32L434.688 736c228.288-40.576 269.184-268.48 266.688-266.496C707.328 452.672 701.312 434.88 701.312 416.064z"
    ></path>
  </svg>
);

const RetrievalResultSvg = () => (
  <svg
    viewBox="0 0 20 20"
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
  >
    <path d="M7 .995C7 .445 7.447 0 7.999 0H19c.552 0 .999.456.999.995v8.01c0 .55-.447.995-.999.995H8C7.447 10 7 9.544 7 9.005zm0 11.003c0-.551.447-.998.999-.998H19c.552 0 .999.446.999.998v7.004c0 .551-.447.998-.999.998H8A.998.998 0 0 1 7 19.002zM0 .992C0 .444.451 0 .99 0h4.02c.546 0 .99.455.99.992v18.016a.996.996 0 0 1-.99.992H.99a.999.999 0 0 1-.99-.992z"></path>
  </svg>
);

export const ApiIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={ApiSvg} {...props} />
);
export const TeamIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={TeamSvg} {...props} />
);
export const ProfileIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={ProfileSvg} {...props} />
);
export const PasswordIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={PasswordSvg} {...props} />
);
export const LogOutIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={LogOutSvg} {...props} />
);
export const ModelProviderIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={ModelProviderSvg} {...props} />
);
export const PromptIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={PromptSvg} {...props} />
);
export const WikipediaIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={WikipediaSvg} {...props} />
);
export const KeywordIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={KeywordSvg} {...props} />
);
export const GitHubIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={GitHubSvg} {...props} />
);
export const QWeatherIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={QWeatherSvg} {...props} />
);

export const SemicolonIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={SemicolonSvg} {...props} />
);

export const CommaIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={CommaSvg} {...props} />
);

export const RetrievalResultIcon = (props: Partial<IconComponentProps>) => (
  <Icon component={RetrievalResultSvg} {...props} />
);

export function SideDown({ className }: { className?: string }) {
  return (
    <ChevronDown
      className={cn(
        'transition-transform group-data-[state=open]/collapsible:rotate-180',
        className,
      )}
    />
  );
}
