import { message } from 'antd';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

/**
 * Clean CSV data by removing markdown code block delimiters and other formatting
 */
export const cleanCsvData = (rawData: string): string => {
  if (!rawData || typeof rawData !== 'string') {
    return '';
  }

  let cleaned = rawData;

  // Remove markdown code block delimiters (case insensitive)
  cleaned = cleaned.replace(/^\s*```\s*csv\s*\n?/i, '');
  cleaned = cleaned.replace(/\n?\s*```\s*$/i, '');

  // Remove leading and trailing \n characters specifically
  cleaned = cleaned.replace(/^\n+/, '').replace(/\n+$/, '');

  // Remove any remaining leading/trailing whitespace
  cleaned = cleaned.trim();

  return cleaned;
};

/**
 * Parse CSV data into a 2D array for Excel export
 * Specifically designed for the format from the backend
 */
export const parseCsvToArray = (csvString: string): string[][] => {
  if (!csvString || csvString.trim() === '') {
    return [];
  }

  // Split into lines using \n as delimiter (as specified)
  const lines = csvString.split('\n').filter((line) => line.trim() !== '');

  const result: string[][] = [];

  for (const line of lines) {
    // Split each line by comma delimiter (as specified)
    // Handle quoted fields properly for Chinese content
    const row: string[] = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
        // Don't include the quote character in the output
      } else if (char === ',' && !inQuotes) {
        row.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last field
    row.push(current.trim());

    // Only add non-empty rows
    if (row.length > 0 && row.some((cell) => cell.trim() !== '')) {
      result.push(row);
    }
  }

  console.log('CSV parsing result:', {
    totalLines: lines.length,
    parsedRows: result.length,
    firstRow: result[0] || [],
    columnsCount: result[0]?.length || 0,
  });

  return result;
};

/**
 * Convert CSV string to structured Markdown format
 * Converts exam questions from CSV to a formatted exam layout
 */
export const csvToMarkdown = (csvString: string): string => {
  if (!csvString || csvString.trim() === '') {
    return '';
  }

  try {
    // Parse CSV data into 2D array
    const data = parseCsvToArray(csvString);

    if (data.length === 0) {
      return '';
    }

    console.log('Converting to Markdown - Data structure:', {
      rows: data.length,
      headers: data[0],
      sampleRow: data[1] || [],
    });

    // Expected CSV columns based on the response format:
    // 题型,题目内容,选项A,选项B,选项C,选项D,选项E,正确答案,解析
    const headers = data[0];
    const questionRows = data.slice(1); // Skip header row

    // Find column indexes
    const getColumnIndex = (columnName: string): number => {
      return headers.findIndex(
        (header) => header.includes(columnName) || header === columnName,
      );
    };

    const typeIndex = getColumnIndex('题型');
    const contentIndex = getColumnIndex('题目内容');
    const optionAIndex = getColumnIndex('选项A');
    const optionBIndex = getColumnIndex('选项B');
    const optionCIndex = getColumnIndex('选项C');
    const optionDIndex = getColumnIndex('选项D');
    const optionEIndex = getColumnIndex('选项E');
    const answerIndex = getColumnIndex('正确答案');
    const explanationIndex = getColumnIndex('解析');

    console.log('Column indexes:', {
      typeIndex,
      contentIndex,
      optionAIndex,
      optionBIndex,
      optionCIndex,
      optionDIndex,
      optionEIndex,
      answerIndex,
      explanationIndex,
    });

    let markdown = '\n\n### 铁路信号工安全操作规程考题\n\n';

    questionRows.forEach((row, index) => {
      const questionNumber = index + 1;
      const questionType = row[typeIndex] || '';
      const questionContent = row[contentIndex] || '';
      const optionA = row[optionAIndex] || '';
      const optionB = row[optionBIndex] || '';
      const optionC = row[optionCIndex] || '';
      const optionD = row[optionDIndex] || '';
      const optionE = row[optionEIndex] || '';
      const correctAnswer = row[answerIndex] || '';
      const explanation = row[explanationIndex] || '';

      // Add question header
      markdown += `#### ${questionNumber}. ${questionType}\n`;
      markdown += `**${questionContent}**\n\n`;

      // Add options (only if they exist)
      if (optionA.trim()) markdown += `- A. ${optionA}  \n`;
      if (optionB.trim()) markdown += `- B. ${optionB}  \n`;
      if (optionC.trim()) markdown += `- C. ${optionC}  \n`;
      if (optionD.trim()) markdown += `- D. ${optionD}  \n`;
      if (optionE.trim()) markdown += `- E. ${optionE}  \n`;

      markdown += '\n';

      // Add correct answer
      if (correctAnswer.trim()) {
        markdown += `**正确答案：${correctAnswer}**  \n`;
      }

      // Add explanation
      if (explanation.trim()) {
        markdown += `**解析：**${explanation}\n`;
      }

      // Add separator between questions (except for the last one)
      if (index < questionRows.length - 1) {
        markdown += '\n---\n\n';
      }
    });

    console.log('Generated markdown preview:', markdown.slice(0, 500) + '...');
    return markdown;
  } catch (error) {
    console.error('Error converting CSV to Markdown:', error);
    return `CSV转换错误: ${error instanceof Error ? error.message : '未知错误'}`;
  }
};

/**
 * Convert CSV string to Excel XLSX file and download using XLSX library
 * Properly handles Chinese characters and creates real XLSX files
 */
export const csvToExcel = (
  csvString: string,
  fileName: string = 'export.xlsx',
): void => {
  if (!csvString || csvString.trim() === '') {
    message.error('没有数据可导出');
    return;
  }

  try {
    // Parse CSV data into 2D array
    const data = parseCsvToArray(csvString);

    if (data.length === 0) {
      message.error('没有有效数据可导出');
      return;
    }

    console.log('Parsed data for Excel export:', {
      rows: data.length,
      columns: data.length > 0 ? data[0].length : 0,
      sampleData: data.slice(0, 2), // Show first 2 rows
    });

    // Create a new workbook
    const workbook = XLSX.utils.book_new();

    // Convert 2D array to worksheet
    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // Set column widths to accommodate Chinese characters
    const columnWidths =
      data[0]?.map((_, colIndex) => {
        const maxLength = Math.max(
          ...data.map((row) => {
            const cellValue = row[colIndex] || '';
            // Count Chinese characters as 2 units, others as 1
            return cellValue.split('').reduce((acc, char) => {
              return acc + (/[\u4e00-\u9fff]/.test(char) ? 2 : 1);
            }, 0);
          }),
        );
        return { wch: Math.min(Math.max(maxLength, 10), 50) }; // Min 10, max 50
      }) || [];

    worksheet['!cols'] = columnWidths;

    // Add header row styling
    if (data.length > 0) {
      const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!worksheet[cellAddress]) continue;

        // Style the header row
        worksheet[cellAddress].s = {
          font: { bold: true, color: { rgb: '000000' } },
          fill: { fgColor: { rgb: 'E0E0E0' } },
          alignment: { horizontal: 'center', vertical: 'center' },
        };
      }
    }

    // Add the worksheet to the workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, '考试题目');

    // Generate the Excel file buffer
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array',
      compression: true,
    });

    // Create blob and download
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });

    // Ensure filename has correct extension
    const xlsxFileName = fileName.endsWith('.xlsx')
      ? fileName
      : `${fileName}.xlsx`;

    saveAs(blob, xlsxFileName);
    message.success('Excel文件已下载');
  } catch (error) {
    console.error('Error exporting Excel file:', error);
    message.error('导出Excel文件失败');
  }
};

/**
 * Test and verify CSV parsing with sample data
 * This function can be used for debugging and verification
 */
export const verifyCsvParsing = (
  csvString: string,
): {
  success: boolean;
  rows: number;
  columns: number;
  sample: string[][];
  headers: string[];
} => {
  try {
    const data = parseCsvToArray(csvString);

    return {
      success: true,
      rows: data.length,
      columns: data.length > 0 ? data[0].length : 0,
      sample: data.slice(0, 3), // First 3 rows for verification
      headers: data[0] || [], // First row as headers
    };
  } catch (error) {
    console.error('CSV parsing verification failed:', error);
    return {
      success: false,
      rows: 0,
      columns: 0,
      sample: [],
      headers: [],
    };
  }
};

/**
 * Debug function to test the complete workflow
 * This simulates the exact process used in the main component
 */
export const testCsvWorkflow = (rawResponse: string): void => {
  console.log('=== Testing Complete CSV Workflow ===');

  console.log('1. Raw response sample:');
  console.log(rawResponse.slice(0, 200) + '...');

  console.log('\n2. Cleaning CSV data...');
  const cleaned = cleanCsvData(rawResponse);
  console.log('Cleaned sample:', cleaned.slice(0, 200) + '...');

  console.log('\n3. Parsing to array...');
  const verification = verifyCsvParsing(cleaned);
  console.log('Parsing verification:', verification);

  console.log('\n4. Converting to Markdown...');
  const markdown = csvToMarkdown(cleaned);
  console.log('Markdown sample:', markdown.slice(0, 300) + '...');

  console.log('\n=== Workflow Test Complete ===');
};
