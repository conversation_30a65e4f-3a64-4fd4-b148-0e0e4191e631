@import '@/theme/high-tech-theme.less';

.tag {
  height: 40px;
  padding: 0 30px;
  margin: 0 5px;
  border: 1px solid #000;
  border-radius: 10px;
  cursor: pointer;
}

.checked {
  color: #1677ff;
  border-color: #1677ff;
}

.logoWrapper {
  .pointerCursor;
}

.appIcon {
  vertical-align: middle;
  max-width: 36px;
}

.appName {
  vertical-align: middle;
  font-family: var(--font-family);
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.radioGroup {
  & label {
    height: 40px;
    line-height: 40px;
    border: 0 !important;
    background-color: rgba(249, 249, 249, 1);
    font-weight: @fontWeight700;
    color: rgba(29, 25, 41, 1);

    &::before {
      display: none !important;
    }
  }

  :global(.ant-radio-button-wrapper-checked) {
    border-radius: 6px !important;

    & a {
      color: white;
    }
  }

  :global(.ant-radio-button-wrapper-checked.dark) {
    border-radius: 0px !important;

    & a {
      color: white;
    }
  }

  :global(.ant-radio-button-wrapper-checked.dark.first) {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;

    & a {
      color: white;
    }
  }

  :global(.ant-radio-button-wrapper-checked.dark.last) {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;

    & a {
      color: white;
    }
  }
}

.radioGroupDark {
  & label {
    height: 40px;
    line-height: 40px;
    border: 0 !important;
    background-color: rgba(249, 249, 249, 0.25);
    font-weight: @fontWeight700;
    color: rgba(29, 25, 41, 1);

    &::before {
      display: none !important;
    }
  }

  :global(.ant-radio-button-wrapper-checked) {
    border-radius: 6px !important;

    & a {
      color: white;
    }
  }

  :global(.ant-radio-button-wrapper-checked.dark) {
    border-radius: 0px !important;

    & a {
      color: white;
    }
  }

  :global(.ant-radio-button-wrapper-checked.dark.first) {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;

    & a {
      color: white;
    }
  }

  :global(.ant-radio-button-wrapper-checked.dark.last) {
    border-top-right-radius: 6px !important;
    border-bottom-right-radius: 6px !important;

    & a {
      color: white;
    }
  }
}

.ant-radio-button-wrapper-checked {
  border-radius: 6px !important;
}

.radioButtonIcon {
  vertical-align: middle;
  max-width: 15px;
  max-height: 15px;
}

.highTechHeader {
  z-index: 1000;
  height: var(--header-height);
  padding: 0 var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-primary);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-bottom: 1px solid var(--border-color);
  transition: all var(--transition-normal);

  /* Glass effect for high-tech feel */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.7);
  }
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.menuToggle {
  font-size: 1.2rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-primary);
}

.brandLogo {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);

  .logoIcon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
  }

  .logoText {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    // background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
    //-webkit-background-clip: text;
    // background-clip: text;
    display: inline-block;
  }
}

.headerRight {
  display: flex;
  align-items: center;
}

/* Dark mode adjustments */
:global(.dark) {
  .highTechHeader {
    &::before {
      background-color: rgba(15, 23, 42, 0.7);
    }
  }
}
