# 用户头像下拉菜单功能实现

## 功能概述

实现了全局header中个人头像的下拉菜单功能，包含以下特性：

### 🎯 **主要功能**

1. **下拉菜单**：鼠标悬停头像显示下拉菜单
2. **菜单项**：

   - 用户设置（带用户图标）
   - 分割线
   - 退出登录（带退出图标）

3. **交互式箭头**：头像右侧显示箭头图标

   - 默认状态：向下箭头
   - 悬停状态：向上箭头
   - 平滑过渡动画

4. **路由跳转**：点击"用户设置"跳转到 `/user-profile`
5. **确认对话框**：点击"退出登录"显示确认对话框

## 📁 **修改的文件**

### 1. 用户组件 (`web/src/layouts/components/user/index.tsx`)

- 添加了 `Dropdown` 组件包装头像
- 实现了菜单项配置
- 添加了退出登录确认对话框
- 集成了 `useLogout` hook

### 2. 路由配置 (`web/src/routes.ts`)

- 添加了 `/user-profile` 路由
- 映射到 `@/pages/user-setting/setting-profile` 组件

## 🔧 **技术实现**

### **组件结构**

```tsx
<Dropdown
  menu={{ items: menuItems }}
  placement="bottomRight"
  trigger={['hover']}
  onOpenChange={setDropdownOpen}
>
  <div className={styles.clickAvailable}>
    <Space size={4} align="center">
      <Avatar />
      {dropdownOpen ? <UpOutlined /> : <DownOutlined />}
    </Space>
  </div>
</Dropdown>
```

### **菜单配置**

```tsx
const menuItems: MenuProps['items'] = [
  {
    key: 'profile',
    label: '用户信息',
    icon: <UserOutlined />,
    onClick: handleUserProfile,
  },
  {
    type: 'divider',
  },
  {
    key: 'logout',
    label: '退出登录',
    icon: <LogoutOutlined />,
    onClick: handleLogout,
  },
];
```

### **确认对话框**

```tsx
const handleLogout = () => {
  confirm({
    title: '退出登录',
    icon: <ExclamationCircleOutlined />,
    content: '您确定要退出登录吗？',
    okText: '确定',
    cancelText: '取消',
    onOk() {
      logout();
    },
  });
};
```

## 🚀 **使用的Hooks**

- `useFetchUserInfo()`: 获取用户信息
- `useLogout()`: 处理退出登录逻辑
- `useNavigate()`: 路由导航

## ✅ **功能验证**

### **测试步骤**

1. 登录系统
2. 点击右上角头像
3. 验证下拉菜单显示
4. 点击"用户信息"验证跳转到 `/user-profile`
5. 点击"退出登录"验证确认对话框
6. 确认退出验证登录状态清除

### **预期行为**

- ✅ 头像点击显示下拉菜单
- ✅ 菜单包含图标和文本
- ✅ 用户信息跳转到正确页面
- ✅ 退出登录显示确认对话框
- ✅ 确认退出清除登录状态并跳转到登录页

## 🎨 **UI/UX特性**

- **位置**：下拉菜单在头像右下方显示
- **触发**：鼠标悬停触发（更流畅的交互）
- **视觉反馈**：
  - 悬停时容器背景色变化
  - 箭头图标动态切换（下→上）
  - 平滑的过渡动画效果
- **图标**：每个菜单项都有对应的图标
- **分割线**：用户设置和退出登录之间有分割线
- **确认**：退出登录有二次确认机制

## 📋 **依赖组件**

- `antd`: Dropdown, Avatar, Modal, Icon组件
- `@ant-design/icons`: UserOutlined, LogoutOutlined, ExclamationCircleOutlined
- `umi`: useNavigate hook
- 自定义hooks: useLogout, useFetchUserInfo

## 🔄 **集成说明**

该功能完全集成到现有的布局系统中，不影响其他功能：

- 保持原有的头像显示逻辑
- 复用现有的用户信息获取机制
- 使用现有的退出登录流程
- 遵循现有的路由配置模式
