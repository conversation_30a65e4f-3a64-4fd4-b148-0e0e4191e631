import { parseMarkdownQuestions } from './markdown-to-excel-utils';

// 您提供的最新格式数据
const testData = `
 \n\n### 铁路信号工安全操作规程考试试题\n\n---\n\n#### **多选题**\n\n**1. 在进行铁路信号设备维护作业前，必须执行哪些准备工作？（   ）**  \n难度：易  \n正确答案：A,B,C,D  \nA. 确认作业区域已停电并挂接地线  \nB. 检查工具和仪表是否符合安全要求  \nC. 穿戴好防护服、绝缘鞋和安全帽  \nD. 设置好警示标志和防护措施  \n\n---\n\n**2. 在铁路信号工操作中，哪些行为属于严重违规？（   ）**  \n难度：难  \n正确答案：A,C,D  \nA. 未断电情况下进行设备内部作业  \nB. 使用不合格的工具或仪表  \nC. 擅自修改设备参数或程序  \nD. 未按照规程检查设备状态  \n\n---\n\n#### **单选题**\n\n**3. 在铁路信号工操作中，发现设备异常时应采取的首要措施是：（   ）**  \n难度：中  \n正确答案：B  \nA. 立即停止作业并撤离现场  \nB. 按照规程切断电源并报告上级  \nC. 尝试自行修复设备故障  \nD. 继续完成当前任务后再处理  \n\n---\n\n**4. 铁路信号工在进行高空作业时，必须使用的安全装备是：（   ）**  \n难度：易  \n正确答案：A  \nA. 安全带和防坠器  \nB. 绝缘手套  \nC. 防护眼镜  \nD. 防毒面具  \n\n---\n\n#### **判断题**\n\n**5. 在铁路信号工操作中，可以使用金属工具敲击设备内部的电气元件。（   ）**  \n难度：难  \n正确答案：错  \n解析：禁止使用金属工具直接接触电气元件，以免造成短路或触电事故。\n\n---\n\n**6. 铁路信号工在进行带电作业时，必须由两人以上共同完成，且至少有一人负责监护。（   ）**  \n难度：中  \n正确答案：对  \n解析：带电作业必须严格执行双人操作制度，确保安全措施到位。\n\n---\n\n#### **多选题**\n\n**7. 在铁路信号设备维护中，哪些情况需要立即停止作业并报告？（   ）**  \n难度：难  \n正确答案：A,B,C,D  \nA. 设备出现异常声响或异味  \nB. 发现线路或设备存在严重安全隐患  \nC. 作业区域突然停电  \nD. 操作人员感到身体不适  \n\n---\n\n#### **单选题**\n\n**8. 铁路信号工在进行设备调试时，必须遵循的程序是：（   ）**  \n难度：中  \n正确答案：C  \nA. 直接调整参数至目标值  \nB. 先断电再进行调试  \nC. 按照规程逐步测试并记录数据  \nD. 立即通知上级等待指示  \n\n---\n\n#### **判断题**\n\n**9. 铁路信号工在作业过程中，可以使用手机或其他电子设备进行通信。（   ）**  \n难度：易  \n正确答案：错  \n解析：禁止在作业过程中使用非防爆电子设备，以免引发安全事故。\n\n---\n\n#### **多选题**\n\n**10. 在铁路信号工操作中，哪些行为可能导致触电事故？（   ）**  \n难度：难  \n正确答案：A,B,C,D  \nA. 未断电情况下接触带电设备  \nB. 使用损坏的绝缘工具  \nC. 操作时未穿戴防护装备  \nD. 忽视警示标志和安全提示  \n\n---\n\n以上试题涵盖了铁路信号工操作考试的核心内容，重点考察考生对安全操作规程的理解和应用能力。
`;

export const testLatestFormat = () => {
  console.log('开始测试最新格式的markdown解析...');
  console.log('原始数据长度:', testData.length);

  // 1. 先查看分割结果
  console.log('\n=== 分割测试 ===');
  const typeBlocks = testData.split(/(?=####\s*\*\*[^*]+\*\*)/);
  console.log('按题型分割后的块数:', typeBlocks.length);

  typeBlocks.forEach((block, index) => {
    console.log(`\n块 ${index + 1}:`, block.substring(0, 100) + '...');

    // 在每个题型块内按题目分割
    const questionsInType = block.split(/(?=\*\*\d+\.\s*)/);
    console.log(`  块内题目数量: ${questionsInType.length}`);

    questionsInType.forEach((q, qIndex) => {
      if (qIndex > 0) {
        // 跳过第一个块（题型标题）
        console.log(`    题目 ${qIndex}:`, q.substring(0, 50) + '...');
      }
    });
  });

  // 2. 解析测试
  console.log('\n=== 解析测试 ===');
  const questions = parseMarkdownQuestions(testData);
  console.log(`解析出的题目数量: ${questions.length}`);

  questions.forEach((q, index) => {
    console.log(`\n题目 ${index + 1}:`);
    console.log('  题型:', q.题型);
    console.log('  题目内容:', q.题目内容.substring(0, 80) + '...');
    console.log('  难易度:', q.难易度);
    console.log('  正确答案:', q.正确答案);
    console.log('  选项A:', q.答案A);
    console.log('  选项B:', q.答案B);
    console.log('  选项C:', q.答案C);
    console.log('  选项D:', q.答案D);
    console.log('  解析:', q.解析);
  });

  return questions;
};

// 在浏览器环境中添加到window对象以便调试
if (typeof window !== 'undefined') {
  (window as any).testLatestFormat = testLatestFormat;
}
