from __future__ import annotations

"""Core generation engine.

Takes an `ExamContext`, builds prompt via PromptBuilder, and streams content
via provided `llm_bundle` (wrapper over ChatModel).  No external IO.
"""

import logging
from typing import Generator, Iterable, Any
from api.utils.errors import redact

from .models import ExamContext
from .prompt_builder import MarkdownPromptBuilder, PromptBuilder


class SmartExamGenerator:
    """Generate smart exam paper using upstream LLM model."""

    def __init__(
        self,
        llm_bundle,
        prompt_builder_cls: type[PromptBuilder] = MarkdownPromptBuilder,
        stream: bool = True,
    ) -> None:
        self.llm_bundle = llm_bundle
        self.prompt_builder_cls = prompt_builder_cls
        self.stream = stream

    # --------------------------------------------------
    def build_prompt(self, ctx: ExamContext) -> str:
        builder = self.prompt_builder_cls(ctx)
        prompt = builder.build()
        logging.debug("[SmartExamGenerator] prompt=%s", redact(prompt))
        return prompt

    # --------------------------------------------------
    def stream_generate(self, ctx: ExamContext, llm_config: dict | None = None) -> Generator[str, None, None]:
        """Yield incremental content from LLM."""
        msg = self.build_prompt(ctx)
        llm_config = llm_config or {
            "max_tokens": 8192, "temperature": 0.3, "stream": True}

        if self.stream and hasattr(self.llm_bundle, "chat_streamly"):
            last = ""
            for chunk in self.llm_bundle.chat_streamly(msg, [], llm_config):
                if isinstance(chunk, int):
                    continue
                if not isinstance(chunk, str):
                    chunk = str(chunk)
                delta = chunk[len(last):]
                if delta:
                    yield delta
                    last = chunk
        else:
            response = self.llm_bundle.chat(msg, [], llm_config)
            if isinstance(response, tuple):
                response = response[0]
            yield str(response)
