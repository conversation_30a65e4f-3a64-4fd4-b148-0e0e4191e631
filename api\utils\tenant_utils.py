#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from api.db.db_models import Tenant
from api.db import UserTenantRole


class TenantProxy:
    """A proxy class that provides unified access to tenant data.

    This class wraps either a Tenant model object or a tenant dictionary,
    providing attribute-style access to tenant properties regardless of the
    underlying data type.

    Attributes:
        _data: The underlying tenant data (either dict or Tenant object)
        _is_dict: Flag indicating if the data is a dictionary
    """

    def __init__(self, tenant_data):
        """Initialize the proxy with tenant data.

        Args:
            tenant_data: Either a Tenant model object or a dictionary containing tenant data
        """
        self._data = tenant_data
        self._is_dict = isinstance(tenant_data, dict)

    def __getattr__(self, name):
        """Provide attribute-style access to tenant properties.

        Args:
            name: The attribute name to access

        Returns:
            The value of the requested attribute

        Raises:
            AttributeError: If the attribute doesn't exist
        """
        if self._is_dict:
            if name in self._data:
                return self._data[name]
            # Handle common field mappings for dict format
            elif name == 'tenant_id' and 'tenant_id' in self._data:
                return self._data['tenant_id']
            else:
                raise AttributeError(
                    f"'{type(self._data).__name__}' object has no attribute '{name}'")
        else:
            return getattr(self._data, name)

    def get(self, key, default=None):
        """Get a value with a default fallback.

        Args:
            key: The key to look up
            default: Default value if key doesn't exist

        Returns:
            The value for the key or the default value
        """
        if self._is_dict:
            return self._data.get(key, default)
        else:
            return getattr(self._data, key, default)

    @property
    def id(self):
        """Get the tenant ID."""
        if self._is_dict:
            return self._data.get('tenant_id', self._data.get('id'))
        else:
            return self._data.id

    @property
    def llm_id(self):
        """Get the LLM ID."""
        if self._is_dict:
            return self._data.get('llm_id')
        else:
            return self._data.llm_id

    @property
    def embd_id(self):
        """Get the embedding model ID."""
        if self._is_dict:
            return self._data.get('embd_id')
        else:
            return self._data.embd_id

    @property
    def name(self):
        """Get the tenant name."""
        if self._is_dict:
            return self._data.get('name')
        else:
            return self._data.name

    def to_dict(self):
        """Convert the tenant data to a dictionary.

        Returns:
            Dictionary representation of the tenant data
        """
        if self._is_dict:
            return self._data.copy()
        else:
            return self._data.to_dict()


def normalize_tenant(tenant_data):
    """Convert tenant data to a unified TenantProxy object.

    This function takes either a Tenant model object or a tenant dictionary
    and returns a TenantProxy that provides consistent attribute access.

    Args:
        tenant_data: Either a Tenant model object or a dictionary

    Returns:
        TenantProxy: A proxy object providing unified access to tenant data

    Example:
        # Works with model objects
        e, tenant_obj = TenantService.get_by_id(user_id)
        normalized = normalize_tenant(tenant_obj)

        # Works with dictionaries  
        tenant_list = TenantService.get_joined_tenants_by_user_id(user_id)
        normalized = normalize_tenant(tenant_list[0])

        # Both provide the same interface
        llm_id = normalized.llm_id
        name = normalized.name
    """
    return TenantProxy(tenant_data)


def get_tenant_with_fallback(user_id):
    """Get tenant data with automatic fallback logic and role-based priority.

    This function implements the standard pattern for getting tenant data:
    1. Try to get tenant by user_id (returns model object)
    2. If that fails, get joined tenants (returns dict list) and select by role priority
    3. Role priority: normal -> admin -> owner (normal has highest priority)
    4. Return normalized TenantProxy for consistent access

    Args:
        user_id: The user ID to get tenant data for

    Returns:
        tuple: (success: bool, tenant: TenantProxy or None)

    Example:
        success, tenant = get_tenant_with_fallback(current_user.id)
        if success:
            llm_id = tenant.llm_id  # Works regardless of underlying data type
        else:
            return get_data_error_result(message="Tenant not found!")
    """
    from api.db.services.user_service import TenantService
    from api.db import UserTenantRole

    # TODO: 去除默认逻辑，按照role优先级获取用户从属的租户
    # Try to get tenant by user_id first
    # e, tenant = TenantService.get_by_id(user_id)
    # if e:
    #     return True, normalize_tenant(tenant)

    # Fallback to joined tenants with role-based priority
    tenant_list = TenantService.get_info_by(user_id)
    if len(tenant_list) == 0:
        return False, None

    # If only one tenant, return it
    if len(tenant_list) == 1:
        return True, normalize_tenant(tenant_list[0])

    # Multiple tenants found, select by role priority: normal -> admin -> owner
    role_priority = {
        UserTenantRole.NORMAL: 1,
        UserTenantRole.ADMIN: 2,
        UserTenantRole.OWNER: 3
    }

    # Sort tenants by role priority (lower number = higher priority)
    sorted_tenants = sorted(
        tenant_list, key=lambda t: role_priority.get(t.get('role'), 999))

    return True, normalize_tenant(sorted_tenants[0])


def user_accessible_kb(user_id, kb_id):
    """Check if a user can access a specific knowledge base.

    This function checks if a user belongs to the tenant that owns 
    the knowledge base with OWNER, ADMIN, or NORMAL role.

    Args:
        user_id: The user ID to check
        kb_id: The knowledge base ID to check access for

    Returns:
        bool: True if user has access, False otherwise
    """
    from api.db.services.knowledgebase_service import KnowledgebaseService

    # Use the existing accessible method from KnowledgebaseService
    # which has been updated to include all required roles
    return KnowledgebaseService.accessible(kb_id, user_id)


def user_accessible_dialog(user_id, dialog_id):
    """Check if a user can access a specific dialog.

    This function checks if a user belongs to the tenant that owns 
    the dialog with OWNER, ADMIN, or NORMAL role.

    Args:
        user_id: The user ID to check
        dialog_id: The dialog ID to check access for

    Returns:
        bool: True if user has access, False otherwise
    """
    from api.db.services.dialog_service import DialogService
    from api.db.services.user_service import UserTenantService
    from api.db import StatusEnum

    # First get the dialog to find its tenant
    e, dialog = DialogService.get_by_id(dialog_id)
    if not e:
        return False

    # Check if user has access to the tenant that owns this dialog
    # Look for user-tenant relationship with OWNER, ADMIN, or NORMAL role
    user_tenant = UserTenantService.model.select().where(
        (UserTenantService.model.user_id == user_id) &
        (UserTenantService.model.tenant_id == dialog.tenant_id) &
        (UserTenantService.model.status == StatusEnum.VALID.value) &
        (UserTenantService.model.role.in_(
            [UserTenantRole.OWNER, UserTenantRole.ADMIN, UserTenantRole.NORMAL]))
    ).first()

    return user_tenant is not None
