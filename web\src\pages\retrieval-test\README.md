# 检索测试模块重构说明

## 重构内容

本次重构主要针对检索测试模块（`web/src/pages/retrieval-test`）的前端界面进行了布局和样式优化。

### 主要变更

1. **布局重构**

   - 采用左右两栏响应式布局
   - 左侧控制面板：固定宽度320px（移动端自适应）
   - 右侧结果区域：自适应宽度（最小300px）

2. **左侧控制面板分为三个部分**

   - **权重设置**：包含相似度阈值和关键字相似度权重（已隐藏重排序、知识图谱、跨语言检索）
   - **文档选择**：可多选的文档列表，显示文档名称和命中数（高度已增加）
   - **测试文本输入**：文本输入框和测试按钮（高度已增加，文本框支持6-12行）

3. **右侧结果区域**

   - 显示检索结果标题和统计信息
   - 结果卡片展示，包含相似度指标
   - 分页控件

4. **样式优化**
   - 采用高科技风格设计
   - 使用渐变色和阴影效果
   - 响应式设计，支持移动端
   - 统一的卡片样式和交互效果
   - 去除左侧面板滚动条，采用固定高度布局

### 最新调整（v2.0）

1. **简化权重设置**

   - 隐藏了 Rerank 模型配置
   - 隐藏了使用知识图谱选项
   - 隐藏了跨语言搜索配置
   - 只保留相似度阈值和关键字相似度权重

2. **增强用户体验**
   - 文档选择区域高度增加到 200px，提供更好的浏览体验
   - 测试文本输入区域高度增加到 220px，支持更多文本输入
   - 去除左侧面板滚动条，采用弹性布局自动分配空间
   - 文本框支持 6-12 行自适应高度

### 文件结构

```
web/src/pages/retrieval-test/
├── index.tsx                    # 主页面组件
├── index.less                   # 主页面样式
├── testing-control/
│   ├── index.tsx               # 左侧控制面板（已简化）
│   ├── index.less              # 控制面板样式（增加高度，去除滚动条）
│   └── label-word-cloud.tsx    # 词云组件（保留）
└── testing-result/
    ├── index.tsx               # 右侧结果区域
    ├── index.less              # 结果区域样式
    └── select-files.tsx        # 文档选择组件（已移至左侧）
```

### 布局分配

- **权重设置卡片**：固定高度，包含相似度滑块
- **文档选择卡片**：弹性布局（flex: 1），最小高度 200px
- **测试文本卡片**：弹性布局（flex: 1），最小高度 220px

### 响应式设计

- **桌面端（>1024px）**：左侧320px，右侧自适应，无滚动条
- **平板端（768px-1024px）**：左侧280px，右侧自适应
- **移动端（<768px）**：上下布局，恢复滚动条以适应小屏幕

### 使用说明

1. 在左侧"权重设置"部分调整相似度阈值和关键字权重
2. 在"文档选择"部分选择要检索的文档（支持多选）
3. 在"测试文本输入"部分输入查询文本（支持6-12行）
4. 点击"测试"按钮执行检索
5. 在右侧查看检索结果

### 技术特点

- 使用CSS变量实现主题化
- 采用Flexbox弹性布局
- 支持暗色模式
- 优化的滚动条样式（仅在文档列表中保留）
- 平滑的动画过渡效果
- 自适应高度文本框
