import {
  IFetchFileListResult,
  IFolder,
} from '@/interfaces/database/file-manager';
import driveService from '@/services/drive-service';
import { downloadFileFromBlob } from '@/utils/file-util';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useDebounce } from 'ahooks';
import { message } from 'antd';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'umi';
import {
  useGetPaginationWithRouter,
  useHandleSearchChange,
} from './logic-hooks';
import { useSetPaginationParams } from './route-hook';

export const enum DriveApiAction {
  UploadFile = 'uploadDriveFile',
  FetchFileList = 'fetchDriveFileList',
  MoveFile = 'moveDriveFile',
  CreateFolder = 'createDriveFolder',
  FetchParentFolderList = 'fetchDriveParentFolderList',
  DeleteFile = 'deleteDriveFile',
}

export const useGetFolderId = () => {
  const [searchParams] = useSearchParams();
  const id = searchParams.get('folderId') as string;

  return id ?? '';
};

export const useFetchPureFileList = () => {
  const { mutateAsync, isPending: loading } = useMutation({
    mutationKey: [DriveApiAction.FetchFileList],
    gcTime: 0,

    mutationFn: async (parentId: string) => {
      const { data } = await driveService.listFile({
        parent_id: parentId,
      });

      return data;
    },
  });

  return { loading, fetchList: mutateAsync };
};

export const useDeleteFile = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: [DriveApiAction.DeleteFile],
    mutationFn: async (params: { fileIds: string[]; parentId: string }) => {
      const { data } = await driveService.removeFile({
        file_ids: params.fileIds,
      });
      if (data.code === 0) {
        message.success(t('message.deleted'));
        setPaginationParams(1);
        queryClient.invalidateQueries({
          queryKey: [DriveApiAction.FetchFileList],
        });
      }
      return data.code;
    },
  });

  return { data, loading, deleteFile: mutateAsync };
};

export const useMoveFile = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: [DriveApiAction.MoveFile],
    mutationFn: async (params: {
      src_file_ids: string[];
      dest_file_id: string;
    }) => {
      const { data } = await driveService.moveFile(params);
      if (data.code === 0) {
        message.success(t('message.moved'));
        setPaginationParams(1);
        queryClient.invalidateQueries({
          queryKey: [DriveApiAction.FetchFileList],
        });
      }
      return data.code;
    },
  });

  return { data, loading, moveFile: mutateAsync };
};

export const useUploadFile = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: [DriveApiAction.UploadFile],
    mutationFn: async (params: {
      fileList: File[];
      parentId: string;
      tags?: string[];
    }) => {
      const fileList = params.fileList;
      const formData = new FormData();
      formData.append('parent_id', params.parentId);

      // 添加tags参数
      if (params.tags && params.tags.length > 0) {
        formData.append('tags', JSON.stringify(params.tags));
      }

      fileList.forEach((file: any) => {
        formData.append('file', file);
      });
      try {
        const ret = await driveService.uploadFile(formData);
        if (ret?.data.code === 0) {
          message.success(t('message.uploaded'));
          setPaginationParams(1);
          queryClient.invalidateQueries({
            queryKey: [DriveApiAction.FetchFileList],
          });
        }
        return ret?.data?.code;
      } catch (error) {
        console.log('🚀 ~ useUploadFile ~ error:', error);
      }
    },
  });

  return { data, loading, uploadFile: mutateAsync };
};

export const useCreateFolder = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: [DriveApiAction.CreateFolder],
    mutationFn: async (params: { parentId: string; name: string }) => {
      const { data } = await driveService.createFolder({
        ...params,
        type: 'folder',
      });
      if (data.code === 0) {
        message.success(t('message.created'));
        setPaginationParams(1);
        queryClient.invalidateQueries({
          queryKey: [DriveApiAction.FetchFileList],
        });
      }
      return data.code;
    },
  });

  return { data, loading, createFolder: mutateAsync };
};

export const useFetchParentFolderList = () => {
  const id = useGetFolderId();
  const { data } = useQuery<IFolder[]>({
    queryKey: [DriveApiAction.FetchParentFolderList, id],
    initialData: [],
    enabled: !!id,
    queryFn: async () => {
      const { data } = await driveService.getAllParentFolder({
        fileId: id,
      });

      return data?.data?.parent_folders?.toReversed() ?? [];
    },
  });

  return data;
};

export const useRenameFile = () => {
  const { setPaginationParams } = useSetPaginationParams();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const {
    data,
    isPending: loading,
    mutateAsync,
  } = useMutation({
    mutationKey: ['renameDriveFile'],
    mutationFn: async (params: { fileId: string; name: string }) => {
      const { data } = await driveService.renameFile({
        file_id: params.fileId,
        name: params.name,
      });
      if (data.code === 0) {
        message.success(t('message.renamed'));
        setPaginationParams(1);
        queryClient.invalidateQueries({
          queryKey: [DriveApiAction.FetchFileList],
        });
      }
      return data.code;
    },
  });

  return { data, loading, renameFile: mutateAsync };
};

export const useFetchFileList = () => {
  const { searchString, handleInputChange } = useHandleSearchChange();
  const { pagination, setPagination } = useGetPaginationWithRouter();
  const id = useGetFolderId();
  const debouncedSearchString = useDebounce(searchString, { wait: 500 });

  const { data, isFetching: loading } = useQuery<IFetchFileListResult>({
    queryKey: [
      DriveApiAction.FetchFileList,
      {
        id,
        debouncedSearchString,
        ...pagination,
      },
    ],
    initialData: { files: [], parent_folder: {} as IFolder, total: 0 },
    gcTime: 0,
    queryFn: async () => {
      const { data } = await driveService.listFile({
        parent_id: id,
        keywords: debouncedSearchString,
        page_size: pagination.pageSize,
        page: pagination.current,
      });

      return data?.data;
    },
  });

  const onInputChange: React.ChangeEventHandler<HTMLInputElement> = useCallback(
    (e) => {
      setPagination({ page: 1 });
      handleInputChange(e);
    },
    [handleInputChange, setPagination],
  );

  return {
    ...data,
    searchString,
    handleInputChange: onInputChange,
    pagination: { ...pagination, total: data?.total },
    setPagination,
    loading,
  };
};

export const useDownloadFile = () => {
  const { mutateAsync, isPending: loading } = useMutation({
    mutationKey: ['downloadDriveFile'],
    mutationFn: async (params: { id: string; filename: string }) => {
      const { data } = await driveService.getFile(params.id);
      downloadFileFromBlob(data, params.filename);
    },
  });

  return { loading, downloadFile: mutateAsync };
};
