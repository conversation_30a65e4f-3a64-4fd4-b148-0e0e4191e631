import EditTag from '@/components/edit-tag';
import { ButtonLoading } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { IModalProps } from '@/interfaces/common';
import '@/pages/drive/modal-styles.less';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { FileUploader } from '../file-uploader';

export interface FileUploadData {
  files: File[];
  tags: string[];
}

export function FileUploadDialog({
  hideModal,
  onOk,
  loading,
}: IModalProps<FileUploadData>) {
  const { t } = useTranslation();

  const FormSchema = z.object({
    files: z.array(z.any()).min(1, t('common.pleaseSelect')),
    tags: z.array(z.string()).min(1, t('common.pleaseInput')),
  });

  type FormData = z.infer<typeof FormSchema>;

  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      files: [],
      tags: [],
    },
  });

  const handleOk = useCallback(async () => {
    const isValid = await form.trigger();
    if (!isValid) return;

    const formData = form.getValues();
    onOk?.({
      files: formData.files,
      tags: formData.tags,
    });
  }, [form, onOk]);

  return (
    <Dialog open onOpenChange={hideModal}>
      <DialogContent className="ht-modal-content">
        <DialogHeader className="ht-modal-header">
          <DialogTitle className="ht-modal-title">
            {t('fileManager.uploadFile')}
          </DialogTitle>
        </DialogHeader>

        <div className="ht-modal-body">
          <Form {...form}>
            <div className="space-y-6">
              <FormField
                control={form.control}
                name="files"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('fileManager.selectFiles')}</FormLabel>
                    <FormControl>
                      <FileUploader
                        maxFileCount={20}
                        maxSize={20 * 1024 * 1024}
                        onValueChange={field.onChange}
                        value={field.value}
                        accept={{ '*': [] }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('fileManager.tags')} *</FormLabel>
                    <FormControl>
                      <EditTag value={field.value} onChange={field.onChange} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </Form>
        </div>

        <DialogFooter className="ht-modal-footer">
          <ButtonLoading
            type="submit"
            onClick={handleOk}
            loading={loading}
            className="ht-modal-btn primary"
          >
            {t('common.save')}
          </ButtonLoading>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
