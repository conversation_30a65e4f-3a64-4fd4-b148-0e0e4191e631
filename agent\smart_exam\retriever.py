from __future__ import annotations

"""Retriever abstraction used by Smart Exam service.

This module *only* describes interface & orchestrates high-level steps.
Actual vectorstore implementation will be plugged in later.
"""

import logging
from typing import Optional, Sequence, Dict, Any

from .models import RetrieveResult


# ------------------------------------------------------------------
# Configurable constants (Step-3 magic numbers removal)
# ------------------------------------------------------------------

from api import settings


MAX_CONTENT_LENGTH: int = getattr(
    settings, "SMART_EXAM_MAX_CONTENT_LENGTH", 20000)
MAX_CHUNK_LENGTH: int = getattr(settings, "SMART_EXAM_MAX_CHUNK_LENGTH", 2000)
FALLBACK_QUERY: str = getattr(settings, "SMART_EXAM_FALLBACK_QUERY", "试题")
FALLBACK_THRESHOLD: float = getattr(
    settings, "SMART_EXAM_FALLBACK_THRESHOLD", 0.05)


class Retriever:
    """Thin wrapper around an injected retrieval engine (e.g. settings.retrievaler)."""

    def __init__(self, retrievaler):
        """
        Parameters
        ----------
        retrievaler
            An object exposing a ``retrieval(query, emb_model, tenant_ids, kb_ids, page, top_n, similarity_threshold, ...)`` method.
            We keep it duck-typed to decouple from concrete impl.
        """
        self._retrievaler = retrievaler

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------
    def retrieve(
        self,
        *,
        tenant_id: str,
        kb_id: str,
        query: str,
        top_n: int = 15,
        similarity_threshold: float = 0.1,
        emb_model=None,
        vector_similarity_weight: float = 0.5,
    ) -> RetrieveResult:
        """High-level retrieval with fallback & chunk compaction.

        This implements the logic previously residing in
        `SmartExam._retrieve_from_kb` so that it can be re-used by other
        services/components.
        """
        if not kb_id:
            logging.warning("[Retriever] Empty kb_id, skip retrieval")
            return RetrieveResult(content="", chunks=[])

        kb_obj = self._load_kb_meta(kb_id)
        if not kb_obj:
            return RetrieveResult(content="", chunks=[])

        actual_tenant_id = kb_obj.tenant_id

        emb_model = self._resolve_embd_model(
            kb_obj, actual_tenant_id, emb_model)
        if emb_model is None:
            return RetrieveResult(content="", chunks=[])

        chunks = self._query_chunks(
            query,
            emb_model,
            actual_tenant_id,
            kb_id,
            top_n,
            similarity_threshold,
            vector_similarity_weight,
        )

        if not chunks:
            # fallback query attempt already inside _query_chunks
            return RetrieveResult(content="", chunks=[])

        content = self._compact_chunks(chunks)
        return RetrieveResult(content=content, chunks=chunks)

    # ------------------------------------------------------------------
    # Internals
    # ------------------------------------------------------------------

    @staticmethod
    def _load_kb_meta(kb_id: str):
        """Return KB object or None."""
        from api.db.services.knowledgebase_service import KnowledgebaseService

        try:
            ok, kb_obj = KnowledgebaseService.get_by_id(kb_id)
            if not ok or not kb_obj:
                logging.warning("[Retriever] KB not found: %s", kb_id)
                return None
            return kb_obj
        except Exception as e:
            logging.error("[Retriever] Error loading KB %s: %s",
                          kb_id, e, exc_info=True)
            return None

    @staticmethod
    def _resolve_embd_model(kb_obj, tenant_id: str, emb_model):
        """Return an initialized LLMBundle for embedding or None."""
        if emb_model is not None:
            return emb_model

        from api.db import LLMType
        from api.db.services.llm_service import LLMBundle

        embd_id = kb_obj.embd_id or ""
        if not embd_id:
            try:
                emb_model = LLMBundle(tenant_id, LLMType.EMBEDDING)
                embd_id = emb_model.llm_name
            except Exception:
                embd_id = ""
        if not embd_id:
            logging.warning(
                "[Retriever] No embedding model for tenant %s", tenant_id)
            return None

        return LLMBundle(tenant_id, LLMType.EMBEDDING, embd_id)

    def _query_chunks(
        self,
        query: str,
        emb_model,
        tenant_id: str,
        kb_id: str,
        top_n: int,
        threshold: float,
        vector_similarity_weight: float,
    ):
        """Run retrieval; include fallback if primary empty."""
        try:
            res = self._retrievaler.retrieval(
                query,
                emb_model,
                [tenant_id],
                [kb_id],
                1,
                top_n,
                threshold,
                vector_similarity_weight=vector_similarity_weight,
                aggs=False,
            )
        except Exception as e:
            logging.error("[Retriever] Retrieval error: %s", e, exc_info=True)
            return []

        chunks = res.get("chunks") if res else []
        if chunks:
            return chunks

        # fallback
        logging.info(
            "[Retriever] Primary retrieval empty; fallback with query '%s'", FALLBACK_QUERY)
        try:
            res_fb = self._retrievaler.retrieval(
                FALLBACK_QUERY,
                emb_model,
                [tenant_id],
                [kb_id],
                1,
                top_n,
                FALLBACK_THRESHOLD,
                vector_similarity_weight=0.3,
                aggs=False,
            )
            return res_fb.get("chunks") if res_fb and res_fb.get("chunks") else []
        except Exception:
            logging.error(
                "[Retriever] Fallback retrieval failed", exc_info=True)
            return []

    @staticmethod
    def _compact_chunks(chunks: Sequence[Dict[str, Any]]) -> str:
        """Trim and concatenate chunks into a single string obeying length limits."""
        content_parts: list[str] = []
        total_chars = 0
        for chunk in chunks:
            text = chunk.get("content") or chunk.get(
                "content_with_weight") or ""
            if not text:
                continue
            if len(text) > MAX_CHUNK_LENGTH:
                text = text[:MAX_CHUNK_LENGTH] + "..."
            if total_chars + len(text) > MAX_CONTENT_LENGTH:
                if content_parts:
                    break
                text = text[: MAX_CONTENT_LENGTH - 100] + "..."
            content_parts.append(text)
            total_chars += len(text)
            if total_chars >= MAX_CONTENT_LENGTH:
                break
        return "\n\n".join(content_parts)
