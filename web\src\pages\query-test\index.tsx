import { Form } from 'antd';
import { useState } from 'react';
import TestingControl from './testing-control';
import QueryTestResult from './testing-result';

import styles from './index.less';

const QueryTestPage: React.FC = () => {
  const [form] = Form.useForm();
  const [testQuestion, setTestQuestion] = useState('');
  const [isTestingInProgress, setIsTestingInProgress] = useState(false);

  const handleTesting = async () => {
    const values = await form.validateFields();
    const question = values.question?.trim();

    if (question) {
      // Immediately enter loading state to prevent duplicate clicks
      setIsTestingInProgress(true);
      setTestQuestion(question);
    }
  };

  const handleTestStart = () => {
    setIsTestingInProgress(true);
  };

  const handleTestComplete = () => {
    setIsTestingInProgress(false);
  };

  return (
    <div className={`${styles.testingContainer} main-content-container`}>
      <h2 className="main-page-title">提问测试</h2>

      <div className={styles.testingWrapper}>
        {/* 左侧控制面板 */}
        <div className={styles.leftPanel}>
          <TestingControl
            form={form}
            handleTesting={handleTesting}
            loading={isTestingInProgress}
          />
        </div>
        {/* 右侧结果区域 */}
        <div className={styles.rightPanel}>
          <QueryTestResult
            testQuestion={testQuestion}
            onTestStart={handleTestStart}
            onTestComplete={handleTestComplete}
          />
        </div>
      </div>
    </div>
  );
};

export default QueryTestPage;
