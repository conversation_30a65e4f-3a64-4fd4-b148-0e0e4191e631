import { FileUploadData } from '@/components/file-upload-dialog';
import { useSetModalState } from '@/hooks/common-hooks';
import { useGetFolderId, useUploadFile } from '@/hooks/use-drive-request';
import { useCallback } from 'react';

export const useHandleUploadFile = () => {
  const {
    visible: fileUploadVisible,
    hideModal: hideFileUploadModal,
    showModal: showFileUploadModal,
  } = useSetModalState();
  const { uploadFile, loading } = useUploadFile();
  const id = useGetFolderId();

  const onFileUploadOk = useCallback(
    async (data: FileUploadData): Promise<number | undefined> => {
      if (data.files.length > 0) {
        const ret: number = await uploadFile({
          fileList: data.files,
          parentId: id,
          tags: data.tags,
        });
        if (ret === 0) {
          hideFileUploadModal();
        }
        return ret;
      }
    },
    [uploadFile, hideFileUploadModal, id],
  );

  return {
    fileUploadLoading: loading,
    onFileUploadOk,
    fileUploadVisible,
    hideFileUploadModal,
    showFileUploadModal,
  };
};
