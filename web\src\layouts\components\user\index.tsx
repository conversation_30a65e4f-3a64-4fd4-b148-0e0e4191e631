import { useLogout } from '@/hooks/login-hooks';
import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import {
  ExclamationCircleOutlined,
  LogoutOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Avatar, Dropdown, Modal, Space } from 'antd';
import { ChevronDown, ChevronUp, UserRound } from 'lucide-react';
import React, { useState } from 'react';
import { useNavigate } from 'umi';

import styles from '../../index.less';

const { confirm } = Modal;

const App: React.FC = () => {
  const { data: userInfo } = useFetchUserInfo();
  const { logout } = useLogout();
  const navigate = useNavigate();
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const handleUserProfile = () => {
    navigate('/user-profile');
  };

  const handleLogout = () => {
    confirm({
      title: '退出登录',
      icon: <ExclamationCircleOutlined />,
      content: '您确定要退出登录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        logout();
      },
    });
  };

  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: '用户设置',
      icon: <UserOutlined />,
      onClick: handleUserProfile,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: handleLogout,
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      placement="bottomRight"
      trigger={['hover']}
      onOpenChange={setDropdownOpen}
    >
      <div className={styles.clickAvailable}>
        <Space size={4} align="center">
          <Avatar
            size={32}
            src={
              userInfo.avatar ?? (
                <UserRound
                  size={24}
                  color="#666"
                  style={{ marginTop: 3, marginLeft: 3 }}
                />
              )
            }
          />
          {dropdownOpen ? (
            <ChevronUp
              className="h-4 w-4"
              style={{
                color: '#666',
                transition: 'transform 0.2s ease, color 0.2s ease',
              }}
            />
          ) : (
            <ChevronDown
              className="h-4 w-4"
              style={{
                color: '#666',
                transition: 'transform 0.2s ease, color 0.2s ease',
              }}
            />
          )}
        </Space>
      </div>
    </Dropdown>
  );
};

export default App;
