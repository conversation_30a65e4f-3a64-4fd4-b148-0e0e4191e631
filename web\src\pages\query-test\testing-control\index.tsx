import { ModelVariableType } from '@/constants/knowledge';
import { useTranslate } from '@/hooks/common-hooks';
import {
  useChunkIsTesting,
  useFetchDefaultDocumentListForRetrieval,
} from '@/hooks/knowledge-hooks';
import { IDocumentInfo } from '@/interfaces/database/document';
import {
  EditOutlined,
  FileTextOutlined,
  SendOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Form,
  Input,
  List,
  Space,
  Spin,
  Typography,
} from 'antd';
import { FormInstance } from 'antd/lib';
import { useState } from 'react';

import styles from './index.less';

const { Text } = Typography;

type FieldType = {
  similarity_threshold?: number;
  vector_similarity_weight?: number;
  question: string;
  llm_id?: string;
  parameter?: ModelVariableType;
  temperature?: number;
  top_p?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
  temperatureEnabled?: boolean;
  topPEnabled?: boolean;
  presencePenaltyEnabled?: boolean;
  frequencyPenaltyEnabled?: boolean;
};

interface IProps {
  form: FormInstance;
  handleTesting: () => Promise<any>;
  /**
   * External loading state. If provided, it will override the internal
   * mutation loading derived from hooks so that parent components can fully
   * control the button disabled / loading status (e.g. query-test page).
   */
  loading?: boolean;
}

const TestingControl = ({
  form,
  handleTesting,
  loading: externalLoading,
}: IProps) => {
  const question = Form.useWatch('question', { form, preserve: true });
  // Fallback to knowledge-hooks loading when no external loading is provided
  const internalLoading = useChunkIsTesting();
  const loading =
    externalLoading !== undefined ? externalLoading : internalLoading;
  const { t } = useTranslate('knowledgeDetails');

  // 文档列表及选择
  const { documents, loading: documentsLoading } =
    useFetchDefaultDocumentListForRetrieval();

  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);

  const handleDocumentSelect = (docId: string, checked: boolean) => {
    if (checked) {
      setSelectedDocumentIds([...selectedDocumentIds, docId]);
    } else {
      setSelectedDocumentIds(selectedDocumentIds.filter((id) => id !== docId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDocumentIds(documents.map((doc) => doc.id));
    } else {
      setSelectedDocumentIds([]);
    }
  };

  const isAllSelected =
    documents.length > 0 && selectedDocumentIds.length === documents.length;
  const isIndeterminate =
    selectedDocumentIds.length > 0 &&
    selectedDocumentIds.length < documents.length;

  const buttonDisabled =
    loading ||
    !question ||
    (typeof question === 'string' && question.trim() === '');

  return (
    <section className={styles.testingControlWrapper}>
      <Form
        name="testing"
        layout="vertical"
        form={form}
        initialValues={{
          similarity_threshold: 0.7,
          vector_similarity_weight: 0.5,
          parameter: ModelVariableType.Precise,
          temperature: 0.1,
          top_p: 0.3,
          presence_penalty: 0.4,
          frequency_penalty: 0.7,
          temperatureEnabled: true,
          topPEnabled: true,
          presencePenaltyEnabled: true,
          frequencyPenaltyEnabled: true,
        }}
      >
        {/* 知识库文档面板 */}
        <Card
          size="small"
          className={styles.sectionCard}
          title={
            <div className={styles.sectionHeader}>
              <FileTextOutlined className={styles.sectionIcon} />
              <span>知识库文档</span>
            </div>
          }
        >
          <div className={styles.documentSection}>
            {documentsLoading ? (
              <div className={styles.loadingContainer}>
                <Spin size="small" />
                <Text type="secondary">加载文档列表中...</Text>
              </div>
            ) : documents.length > 0 ? (
              <div className={styles.documentList}>
                <div className={styles.selectAllContainer}>
                  <Checkbox
                    indeterminate={isIndeterminate}
                    checked={isAllSelected}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                  >
                    <Text strong>
                      全选 ({selectedDocumentIds.length}/{documents.length})
                    </Text>
                  </Checkbox>
                </div>
                <List
                  size="small"
                  className={styles.documentListContent}
                  dataSource={documents}
                  renderItem={(doc: IDocumentInfo) => (
                    <List.Item key={doc.id} className={styles.documentItem}>
                      <Checkbox
                        checked={selectedDocumentIds.includes(doc.id)}
                        onChange={(e) =>
                          handleDocumentSelect(doc.id, e.target.checked)
                        }
                      >
                        <Space direction="vertical" size={0}>
                          <Text
                            ellipsis={{ tooltip: doc.name }}
                            className={styles.documentName}
                          >
                            {doc.name}
                          </Text>
                          <Text
                            type="secondary"
                            className={styles.documentInfo}
                          >
                            Chunks: {doc.chunk_num || 0} | Status: {doc.run}
                          </Text>
                        </Space>
                      </Checkbox>
                    </List.Item>
                  )}
                />
              </div>
            ) : (
              <div className={styles.emptyDocuments}>
                <Text type="secondary">暂无可选择的文档</Text>
              </div>
            )}
          </div>
        </Card>

        {/* 测试文本面板 */}
        <Card
          size="small"
          className={styles.sectionCard}
          title={
            <div className={styles.sectionHeader}>
              <div className={styles.headerLeft}>
                <EditOutlined className={styles.sectionIcon} />
                <span>测试文本</span>
              </div>
            </div>
          }
        >
          <div className={styles.testingSection}>
            <Form.Item<FieldType>
              name="question"
              rules={[{ required: true, message: t('testTextPlaceholder') }]}
            >
              <Input.TextArea
                placeholder={t('testTextPlaceholder')}
                autoSize={{ minRows: 5, maxRows: 12 }}
                className={styles.testTextarea}
              />
            </Form.Item>

            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleTesting}
              disabled={buttonDisabled}
              loading={loading}
              className={styles.testButton}
              block
            >
              {t('testingLabel')}
            </Button>
          </div>
        </Card>
      </Form>
    </section>
  );
};

export default TestingControl;
