import ChunkMethodModal from '@/components/chunk-method-modal';
import SvgIcon from '@/components/svg-icon';
import {
  useFetchNextDocumentList,
  useSetNextDocumentStatus,
} from '@/hooks/document-hooks';
import { useSetSelectedRecord } from '@/hooks/logic-hooks';
import { useSelectParserList } from '@/hooks/user-setting-hooks';
import { getExtension } from '@/utils/document-util';
import { Divider, Flex, Switch, Table, Tooltip, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'react-i18next';
import CreateFileModal from './create-file-modal';
import DocumentToolbar from './document-toolbar';
import {
  useChangeDocumentParser,
  useCreateEmptyDocument,
  useGetRowSelection,
  useHandleUploadDocument,
  useHandleWebCrawl,
  useNavigateToOtherPage,
  useRenameDocument,
  useShowMetaModal,
} from './hooks';
import ParsingActionCell from './parsing-action-cell';
import RenameModal from './rename-modal';
import WebCrawlModal from './web-crawl-modal';

import FileUploadModal from '@/components/file-upload-modal';
import { DocumentParserType, RunningStatus } from '@/constants/knowledge';
import { IDocumentInfo } from '@/interfaces/database/document';
import { formatDate } from '@/utils/date';
import { CircleHelp } from 'lucide-react';
import styles from './index.less';
import ParsingStatusCell from './parsing-status-cell';
import { SetMetaModal } from './set-meta-modal';
const { Text } = Typography;

const KnowledgeFile = () => {
  const { searchString, documents, pagination, handleInputChange } =
    useFetchNextDocumentList();
  const parserList = useSelectParserList();
  const { setDocumentStatus } = useSetNextDocumentStatus();
  const { toChunk } = useNavigateToOtherPage();
  const { currentRecord, setRecord } = useSetSelectedRecord<IDocumentInfo>();
  const {
    renameLoading,
    onRenameOk,
    renameVisible,
    hideRenameModal,
    showRenameModal,
  } = useRenameDocument(currentRecord.id);
  const {
    createLoading,
    onCreateOk,
    createVisible,
    hideCreateModal,
    showCreateModal,
  } = useCreateEmptyDocument();
  const {
    changeParserLoading,
    onChangeParserOk,
    changeParserVisible,
    hideChangeParserModal,
    showChangeParserModal,
  } = useChangeDocumentParser(currentRecord.id);
  const {
    documentUploadVisible,
    hideDocumentUploadModal,
    showDocumentUploadModal,
    onDocumentUploadOk,
    documentUploadLoading,
    uploadFileList,
    setUploadFileList,
    uploadProgress,
    setUploadProgress,
  } = useHandleUploadDocument();
  const {
    webCrawlUploadVisible,
    hideWebCrawlUploadModal,
    showWebCrawlUploadModal,
    onWebCrawlUploadOk,
    webCrawlUploadLoading,
  } = useHandleWebCrawl();
  const { t } = useTranslation('translation', {
    keyPrefix: 'knowledgeDetails',
  });

  const {
    showSetMetaModal,
    hideSetMetaModal,
    setMetaVisible,
    setMetaLoading,
    onSetMetaModalOk,
  } = useShowMetaModal(currentRecord.id);

  const rowSelection = useGetRowSelection();

  const columns: ColumnsType<IDocumentInfo> = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      align: 'center',
      fixed: 'left',
      render: (_, __, index) => (
        <span className={styles.indexNumber}>
          {((pagination.current || 1) - 1) * (pagination.pageSize || 10) +
            index +
            1}
        </span>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text: any, { id, thumbnail, name }) => (
        <div className={styles.nameCell} onClick={() => toChunk(id)}>
          <Flex gap={12} align="center">
            {thumbnail ? (
              <img className={styles.fileIcon} src={thumbnail} alt="" />
            ) : (
              <SvgIcon
                name={`file-icon/${getExtension(name)}`}
                width={24}
                height={24}
                className={styles.fileIcon}
              />
            )}
            <Text ellipsis={{ tooltip: text }} className={styles.fileName}>
              {text}
            </Text>
          </Flex>
        </div>
      ),
    },
    {
      title: '解析方法',
      dataIndex: 'parser_id',
      key: 'parser_id',
      width: 120,
      align: 'center',
      responsive: ['md'],
      render: (text) => {
        const parser = parserList.find((x) => x.value === text);
        return (
          <span className={styles.parserTag}>{parser?.label || '通用'}</span>
        );
      },
    },
    {
      title: '分块数',
      dataIndex: 'chunk_num',
      key: 'chunk_num',
      width: 100,
      align: 'center',
      sorter: (a, b) => a.chunk_num - b.chunk_num,
      render: (value) => (
        <span className={styles.chunkCount}>
          {value >= 1000 ? `${(value / 1000).toFixed(1)}k` : value}
        </span>
      ),
    },
    {
      title: (
        <span className="flex items-center gap-2">
          {t('parsingStatus')}
          <Tooltip title={t('parsingStatusTip')}>
            <CircleHelp className="size-3" />
          </Tooltip>
        </span>
      ),
      dataIndex: 'run',
      key: 'run',
      width: 140,
      responsive: ['lg'],
      filters: Object.entries(RunningStatus).map(([key, value]) => ({
        text: t(`runningStatus${value}`),
        value: value,
      })),
      onFilter: (value, record: IDocumentInfo) => record.run === value,
      render: (text, record) => {
        return <ParsingStatusCell record={record}></ParsingStatusCell>;
      },
    },
    {
      title: '上传时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 150,
      responsive: ['lg'],
      sorter: (a, b) =>
        new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
      render(value) {
        return <span className={styles.uploadTime}>{formatDate(value)}</span>;
      },
    },
    {
      title: '状态',
      key: 'status',
      dataIndex: 'status',
      width: 80,
      align: 'center',
      filters: [
        { text: '可用', value: '1' },
        { text: '禁用', value: '0' },
      ],
      onFilter: (value, record) => record.status === value,
      render: (_, record) => (
        <div className={styles.statusCell}>
          <Switch
            checked={record.status === '1'}
            onChange={(e) => {
              setDocumentStatus({ status: e, documentId: record.id });
            }}
            size="small"
            className={styles.actionSwitch}
          />
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <div className={styles.actionCell}>
          <ParsingActionCell
            setCurrentRecord={setRecord}
            showRenameModal={showRenameModal}
            showChangeParserModal={showChangeParserModal}
            showSetMetaModal={showSetMetaModal}
            record={record}
          />
        </div>
      ),
    },
  ];

  const finalColumns = columns;

  // Convert parser_id string to DocumentParserType
  const getParserType = (parserId: string): DocumentParserType => {
    return parserId as DocumentParserType;
  };

  return (
    <div className={styles.datasetWrapper}>
      <h2 className={styles.datasetTitle}>{t('dataset')}</h2>
      <p className={styles.datasetDescription}>{t('datasetDescription')}</p>
      <Divider className={styles.divider}></Divider>
      <DocumentToolbar
        selectedRowKeys={rowSelection.selectedRowKeys as string[]}
        showCreateModal={showCreateModal}
        showWebCrawlModal={showWebCrawlUploadModal}
        showDocumentUploadModal={showDocumentUploadModal}
        searchString={searchString}
        handleInputChange={handleInputChange}
        documents={documents}
      ></DocumentToolbar>
      <Table
        rowKey="id"
        columns={finalColumns}
        dataSource={documents}
        pagination={{
          ...pagination,
          showQuickJumper: true,
          showSizeChanger: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} of ${total} items`,
          pageSizeOptions: ['10', '20', '50', '100'],
        }}
        rowSelection={rowSelection}
        className={styles.documentTable}
        size="middle"
        loading={false}
        scroll={{ x: 'max-content' }}
        locale={{
          emptyText: (
            <div className={styles.emptyState}>
              <div className={styles.emptyStateIcon}>📄</div>
              <h4>暂无文档</h4>
              <p>上传您的第一个文档开始使用</p>
            </div>
          ),
        }}
      />
      <CreateFileModal
        visible={createVisible}
        hideModal={hideCreateModal}
        loading={createLoading}
        onOk={onCreateOk}
      />
      <ChunkMethodModal
        documentId={currentRecord.id}
        parserId={getParserType(currentRecord.parser_id || 'naive')}
        parserConfig={currentRecord.parser_config}
        documentExtension={getExtension(currentRecord.name)}
        onOk={onChangeParserOk}
        visible={changeParserVisible}
        hideModal={hideChangeParserModal}
        loading={changeParserLoading}
      />
      <RenameModal
        visible={renameVisible}
        onOk={onRenameOk}
        loading={renameLoading}
        hideModal={hideRenameModal}
        initialName={currentRecord.name}
      ></RenameModal>
      <FileUploadModal
        visible={documentUploadVisible}
        hideModal={hideDocumentUploadModal}
        loading={documentUploadLoading}
        onOk={onDocumentUploadOk}
        uploadFileList={uploadFileList}
        setUploadFileList={setUploadFileList}
        uploadProgress={uploadProgress}
        setUploadProgress={setUploadProgress}
      ></FileUploadModal>
      <WebCrawlModal
        visible={webCrawlUploadVisible}
        hideModal={hideWebCrawlUploadModal}
        loading={webCrawlUploadLoading}
        onOk={onWebCrawlUploadOk}
      ></WebCrawlModal>
      {setMetaVisible && (
        <SetMetaModal
          visible={setMetaVisible}
          hideModal={hideSetMetaModal}
          loading={setMetaLoading}
          onOk={onSetMetaModalOk}
          initialMetaData={currentRecord.meta_fields}
        ></SetMetaModal>
      )}
    </div>
  );
};

export default KnowledgeFile;
