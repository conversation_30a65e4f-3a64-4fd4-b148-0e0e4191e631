@import '@/theme/high-tech-theme.less';

.datasetWrapper {
  width: 100%;
  padding: var(--spacing-lg);
  background-color: var(--background-primary);
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }

  @media (max-width: 480px) {
    padding: var(--spacing-sm);
  }
}

.datasetTitle {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.datasetDescription {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: var(--spacing-lg);
  max-width: 800px;
  line-height: 1.5;
}

.divider {
  margin: var(--spacing-lg) 0;
  border-color: var(--border-color);
}

.filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--spacing-md) 0;
  padding: var(--spacing-md) 0;
}

.indexNumber {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.nameCell {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 4px 0;

  &:hover {
    color: var(--primary-color);
  }
}

.fileIcon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
}

.fileName {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.parserTag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background-color: #f1f5f9;
  color: #64748b;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

.chunkCount {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
}

.recallCount {
  color: var(--text-secondary);
  font-size: 14px;
}

.uploadTime {
  color: var(--text-secondary);
  font-size: 13px;
}

.statusCell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.statusDot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.statusAvailable {
  background-color: #22c55e;
}

.statusDisabled {
  background-color: #ef4444;
}

.statusText {
  font-size: 13px;
  font-weight: 500;
}

.actionCell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.actionSwitch {
  flex-shrink: 0;
}

.documentTable {
  margin-top: var(--spacing-md);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  width: 100%;

  @media (max-width: 1200px) {
    overflow-x: auto;
  }

  :global {
    .ant-table {
      background: #ffffff;
      border-radius: 8px;
      border: none;
    }

    .ant-table-thead > tr > th {
      background: #f8fafc;
      color: #374151;
      font-weight: 500;
      font-size: 13px;
      border-bottom: 1px solid #e5e7eb;
      padding: 12px 16px;
      height: 48px;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f3f4f6;
      padding: 16px;
      background-color: transparent;
      font-size: 14px;
    }

    .ant-table-tbody > tr {
      transition: all 0.2s ease;

      &:hover > td {
        background-color: #f9fafb;
      }

      &:last-child > td {
        border-bottom: none;
      }
    }

    .ant-table-row-selected > td {
      background-color: #eff6ff !important;
    }

    .ant-pagination {
      margin-top: var(--spacing-lg);
      text-align: center;

      .ant-pagination-total-text {
        color: var(--text-secondary);
        font-size: 14px;
      }
    }

    .ant-switch {
      background-color: #d1d5db;

      &-checked {
        background-color: #3b82f6;
      }
    }

    .ant-table-filter-trigger {
      color: var(--text-secondary);
    }
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .emptyStateIcon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #9ca3af;

    @media (max-width: 768px) {
      font-size: 2rem;
      margin-bottom: 12px;
    }
  }

  h4 {
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 14px;

    @media (max-width: 768px) {
      font-size: 13px;
    }
  }
}

// 响应式工具栏
.filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: var(--spacing-md) 0;
  padding: var(--spacing-md) 0;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}
