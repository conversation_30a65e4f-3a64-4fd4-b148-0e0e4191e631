import { useTranslate } from '@/hooks/common-hooks';
import { useFetchAppConf } from '@/hooks/logic-hooks';
import { useNavigateWithFromState } from '@/hooks/route-hook';
import {
  ApiOutlined,
  AppstoreOutlined,
  CodeOutlined,
  ExperimentOutlined,
  FileSearchOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
  QuestionCircleOutlined,
  SearchOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { Button, Layout, Menu } from 'antd';
import { SquareLibrary } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useLocation } from 'umi';

import styles from './index.less';

const { Sider } = Layout;

interface IProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  visible?: boolean;
  className?: string;
}

const SideBar = ({
  collapsed,
  setCollapsed,
  visible = true,
  className = '',
}: IProps) => {
  const navigate = useNavigateWithFromState();
  const { pathname } = useLocation();
  const { t } = useTranslate('header');
  const appConf = useFetchAppConf();

  const menuItemsConfig = useMemo(
    () => [
      {
        key: '/workbench',
        label: '工作台',
        path: '/workbench',
        icon: <AppstoreOutlined />,
      },
      {
        key: '/ask',
        label: '提问',
        path: '/ask',
        icon: <QuestionCircleOutlined />,
      },
      {
        key: '/train',
        label: '训练',
        icon: <ExperimentOutlined />,
        children: [
          {
            key: '/resource',
            label: '资源',
            path: '/resource',
            icon: <SquareLibrary size={16} />,
          },
          {
            key: '/training-method',
            label: '训练设置',
            path: '/training-method',
            icon: <SettingOutlined />,
          },
          {
            key: '/data-training',
            label: '数据训练',
            path: '/data-training',
            icon: <CodeOutlined />,
          },
        ],
      },
      {
        key: '/inspect',
        label: '检验',
        icon: <FileSearchOutlined />,
        children: [
          {
            key: '/retrieval-test',
            label: '检索测试',
            path: '/retrieval-test',
            icon: <SearchOutlined />,
          },
          {
            key: '/query-test',
            label: '提问测试',
            path: '/query-test',
            icon: <MessageOutlined />,
          },
        ],
      },
      {
        key: '/api-management',
        label: '接口管理',
        path: '/api-management',
        icon: <ApiOutlined />,
      },
    ],
    [t],
  );

  const processMenuItems = (items: any[], parentPath = '') => {
    return items.map((item) => {
      const fullPath = item.path
        ? (parentPath + item.path).replace('//', '/')
        : parentPath;
      const newItem: any = {
        ...item,
      };
      if (item.children) {
        newItem.children = processMenuItems(item.children, fullPath);
      } else if (item.path) {
        newItem.onClick = () => navigate(item.path);
      }
      return newItem;
    });
  };

  const menuItems = useMemo(
    () => processMenuItems(menuItemsConfig),
    [menuItemsConfig, navigate],
  );

  const getOpenKeys = () => {
    for (const item of menuItemsConfig) {
      if (item.children) {
        for (const child of item.children) {
          if (pathname.startsWith(child.path)) {
            return [item.key];
          }
        }
      }
    }
    return [];
  };

  const [openKeys, setOpenKeys] = useState(getOpenKeys());

  useEffect(() => {
    if (!collapsed) {
      setOpenKeys(getOpenKeys());
    } else {
      setOpenKeys([]);
    }
  }, [pathname, collapsed, menuItemsConfig]);

  const selectedKeys = useMemo(() => {
    let bestMatch = '';
    let matchLength = 0;

    function findKey(items: any[], currentPath: string) {
      for (const item of items) {
        if (item.path && currentPath.startsWith(item.path)) {
          if (item.path.length > matchLength) {
            matchLength = item.path.length;
            bestMatch = item.key;
          }
        }
        if (item.children) {
          findKey(item.children, currentPath);
        }
      }
    }
    findKey(menuItemsConfig, pathname);
    return bestMatch ? [bestMatch] : [];
  }, [pathname, menuItemsConfig]);

  const onOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  const handleLogoClick = useCallback(() => {
    navigate('/');
  }, [navigate]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <div
      className={`${styles.highTechSidebar} ${className} ${collapsed ? styles.sidebarCollapsed : ''}`}
    >
      <div className={styles.sidebarContent}>
        <Menu
          mode="inline"
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          onOpenChange={onOpenChange}
          items={menuItems}
          className={styles.menu}
          inlineCollapsed={collapsed}
        />
      </div>

      <div className={styles.sidebarFooter}>
        {!collapsed && (
          <>
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => navigate('/system-setting')}
              className={styles.settingsButton}
            >
              系统设置
            </Button>
            <div className={styles.settingsDivider} />
          </>
        )}
        <Button
          type="text"
          icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={toggleCollapsed}
          className={styles.collapseButton}
          title={collapsed ? '展开菜单' : '收缩菜单'}
        />
      </div>
    </div>
  );
};

export default SideBar;
