@import '@/theme/high-tech-theme.less';

.pageWrapper {
  width: 100%;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }

  @media (max-width: 480px) {
    padding: var(--spacing-sm);
  }
}

.pageTitle {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.pageDescription {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: var(--spacing-lg);
  max-width: 800px;
  line-height: 1.5;
}

.divider {
  margin: var(--spacing-lg) 0;
  border-color: var(--border-color);
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md) 0;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}

.toolbarLeft {
  display: flex;
  align-items: center;
}

.toolbarRight {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;

  @media (max-width: 768px) {
    justify-content: space-between;
  }

  @media (max-width: 480px) {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

.selectedInfo {
  color: var(--text-secondary);
  font-size: 14px;
}

.searchInput {
  width: 280px;
  border-radius: 6px;

  @media (max-width: 768px) {
    width: 100%;
    max-width: 280px;
  }

  @media (max-width: 480px) {
    width: 100%;
  }
}

.addButton {
  border-radius: 6px;
  font-weight: 500;

  @media (max-width: 480px) {
    width: 100%;
  }
}

// 表格样式（复用knowledge-file的设计）
.indexNumber {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
}

.nameText {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.methodTag {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background-color: #f1f5f9;
  color: #64748b;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
}

// 格式标签容器
.formatsCell {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

// 格式标签样式
.formatTag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  background-color: #f1f5f9;
  color: #64748b;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #e2e8f0;
  white-space: nowrap;
}

.identifierCode {
  background-color: #f8fafc;
  color: #374151;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  border: 1px solid #e5e7eb;
}

.lengthValue {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 14px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: #6b7280;

  &:hover {
    background-color: #f3f4f6;
    color: #374151;
  }

  &:focus {
    background-color: #f3f4f6;
    color: #374151;
  }
}

.deleteButton {
  &:hover {
    background-color: #fef2f2;
    color: #ef4444;
  }
}

.trainingTable {
  margin-top: var(--spacing-md);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
  width: 100%;

  @media (max-width: 1200px) {
    overflow-x: auto;
  }

  :global {
    .ant-table {
      background: #ffffff;
      border-radius: 8px;
      border: none;
    }

    .ant-table-thead > tr > th {
      background: #f8fafc;
      color: #374151;
      font-weight: 500;
      font-size: 13px;
      border-bottom: 1px solid #e5e7eb;
      padding: 12px 16px;
      height: 48px;
    }

    .ant-table-tbody > tr > td {
      border-bottom: 1px solid #f3f4f6;
      padding: 16px;
      background-color: transparent;
      font-size: 14px;
    }

    .ant-table-tbody > tr {
      transition: all 0.2s ease;

      &:hover > td {
        background-color: #f9fafb;
      }

      &:last-child > td {
        border-bottom: none;
      }
    }

    .ant-table-row-selected > td {
      background-color: #eff6ff !important;
    }

    .ant-pagination {
      margin-top: var(--spacing-lg);
      text-align: center;

      .ant-pagination-total-text {
        color: var(--text-secondary);
        font-size: 14px;
      }
    }

    .ant-table-filter-trigger {
      color: var(--text-secondary);
    }
  }
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;

  .emptyStateIcon {
    font-size: 3rem;
    margin-bottom: 16px;
    color: #9ca3af;

    @media (max-width: 768px) {
      font-size: 2rem;
      margin-bottom: 12px;
    }
  }

  h4 {
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 14px;

    @media (max-width: 768px) {
      font-size: 13px;
    }
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .pageWrapper {
    background-color: var(--background-primary);
  }

  .methodTag {
    background-color: #374151;
    color: #9ca3af;
    border-color: #4b5563;
  }

  .identifierCode {
    background-color: #374151;
    color: #f3f4f6;
    border-color: #4b5563;
  }

  .actionButton {
    color: #9ca3af;

    &:hover,
    &:focus {
      background-color: rgba(75, 85, 99, 0.3);
      color: #f3f4f6;
    }
  }

  .deleteButton {
    &:hover {
      background-color: rgba(239, 68, 68, 0.1);
      color: #f87171;
    }
  }

  .trainingTable {
    border-color: #4b5563;

    :global {
      .ant-table {
        background-color: var(--neutral-800);
      }

      .ant-table-thead > tr > th {
        background-color: var(--neutral-700);
        color: var(--neutral-200);
        border-bottom-color: #4b5563;
      }

      .ant-table-tbody > tr > td {
        border-bottom-color: #4b5563;
        color: var(--neutral-300);
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #4b5563;
      }

      .ant-table-row-selected > td {
        background-color: rgba(59, 130, 246, 0.15) !important;
      }
    }
  }
}
