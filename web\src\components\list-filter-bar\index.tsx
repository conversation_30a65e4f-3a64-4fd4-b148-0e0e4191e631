import { cn } from '@/lib/utils';
import { SearchOutlined } from '@ant-design/icons';
import { Input } from 'antd';
import { ChevronDown } from 'lucide-react';
import React, {
  ChangeE<PERSON><PERSON><PERSON><PERSON>,
  PropsWithChildren,
  ReactNode,
  useMemo,
} from 'react';
import { useTranslation } from 'react-i18next';
import { Button, ButtonProps } from '../ui/button';
import { CheckboxFormMultipleProps, FilterPopover } from './filter-popover';
import './index.less';

interface IProps {
  title?: ReactNode;
  searchString?: string;
  onSearchChange?: ChangeEventHandler<HTMLInputElement>;
  showFilter?: boolean;
  leftPanel?: ReactNode;
}

const FilterButton = React.forwardRef<
  HTMLButtonElement,
  ButtonProps & { count?: number }
>(({ count = 0, ...props }, ref) => {
  return (
    <Button variant="secondary" {...props} ref={ref}>
      <span
        className={cn({
          'text-text-title': count > 0,
          'text-text-sub-title-invert': count === 0,
        })}
      >
        Filter
      </span>
      {count > 0 && (
        <span className="rounded-full bg-text-badge px-1 text-xs ">
          {count}
        </span>
      )}
      <ChevronDown />
    </Button>
  );
});

export default function ListFilterBar({
  title,
  children,
  searchString,
  onSearchChange,
  showFilter = true,
  leftPanel,
  value,
  onChange,
  filters,
  className,
  icon,
}: PropsWithChildren<IProps & Omit<CheckboxFormMultipleProps, 'setOpen'>> & {
  className?: string;
  icon?: ReactNode;
}) {
  const { t } = useTranslation('translation', {
    keyPrefix: 'knowledgeDetails',
  });

  const filterCount = useMemo(() => {
    return typeof value === 'object' && value !== null
      ? Object.values(value).reduce((pre, cur) => {
          return pre + cur.length;
        }, 0)
      : 0;
  }, [value]);

  return (
    <div
      className={cn(
        'flex justify-between mb-5 items-center list-filter-bar',
        className,
      )}
    >
      <div className="text-2xl font-semibold flex items-center gap-2.5">
        {/* {typeof icon === 'string' ? (
          <IconFont name={icon} className="size-6"></IconFont>
        ) : icon ? (
          icon
        ) : (
          <SquareLibrary className="h-6 w-6" />
        )} */}
        {leftPanel || title}
      </div>
      <div className="flex gap-5 items-center">
        {showFilter && (
          <FilterPopover value={value} onChange={onChange} filters={filters}>
            <FilterButton count={filterCount}></FilterButton>
          </FilterPopover>
        )}

        <div className="search-input-wrapper">
          <Input
            placeholder={t('searchFiles')}
            value={searchString}
            style={{
              width: 230,
              backgroundColor: '#f3f4f6',
              border: 'none',
              borderRadius: '8px',
            }}
            allowClear
            onChange={onSearchChange}
            prefix={<SearchOutlined />}
            styles={{
              affixWrapper: {
                backgroundColor: '#f3f4f6',
                border: 'none',
                borderRadius: '8px',
                boxShadow: 'none',
                transition: 'border-color 0.2s ease',
              },
              input: {
                backgroundColor: 'transparent',
                border: 'none',
                boxShadow: 'none',
              },
            }}
          />
        </div>
        {children}
      </div>
    </div>
  );
}
