@import '@/theme/high-tech-theme.less';

.configContainer {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.configTitle {
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
  background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  margin-bottom: var(--spacing-xs);
}

.configDescription {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  max-width: 800px;
}

.configCard {
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.formItem {
  margin-bottom: var(--spacing-md);

  :global {
    .ant-form-item-label > label {
      font-weight: 500;
      color: var(--text-primary);
    }
  }
}

.formInput,
.formTextarea {
  border-radius: var(--border-radius-md);
  border-color: var(--border-color);

  &:hover,
  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--glow-color);
  }
}

.permissionGroup {
  .permissionButton {
    border-radius: var(--border-radius-md);
    margin-right: var(--spacing-sm);
    font-weight: 500;

    &:first-child {
      border-top-right-radius: var(--border-radius-md);
      border-bottom-right-radius: var(--border-radius-md);
    }

    &:last-child {
      border-top-left-radius: var(--border-radius-md);
      border-bottom-left-radius: var(--border-radius-md);
    }
  }
}

.divider {
  margin: var(--spacing-lg) 0;
  border-color: var(--border-color);
}

.submitItem {
  margin-top: var(--spacing-lg);
  margin-bottom: 0;
}

.buttonWrapper {
  display: flex;
  justify-content: flex-end;
}

.cancelButton {
  border-radius: var(--border-radius-md);
  font-weight: 500;

  &:hover {
    color: var(--primary-color);
    border-color: var(--primary-color);
  }
}

.saveButton {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.15);
  transition: all var(--transition-normal);

  &:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .configCard {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
  }

  .formInput,
  .formTextarea {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
    color: var(--text-primary);

    &::placeholder {
      color: var(--neutral-500);
    }
  }
}

.buttonWrapper {
  text-align: right;
}

.tagTabsWrapper {
  :global(.ant-tabs-tab) {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 6px 6px 0 0;
    background-color: transparent;

    &:not(:first-child) {
      margin-left: 6px;
    }
  }

  :global(.ant-tabs-tab-active) {
    background-color: white;
  }
}

.tabContentWrapper {
  display: flex;
  margin-top: -1px;
  padding: 20px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.tags {
  margin-bottom: 24px;
}

.preset {
  display: flex;
  height: 80px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  padding: 5px;
  margin-bottom: 24px;

  .left {
    flex: 1;
  }

  .right {
    width: 100px;
    border-left: 1px solid rgba(0, 0, 0, 0.4);
    margin: 10px 0px;
    padding: 5px;
  }
}

.configurationWrapper {
  padding: 0 52px;

  .buttonWrapper {
    text-align: right;
  }

  .variableSlider {
    width: 100%;
  }
}

.categoryPanelWrapper {
  .topTitle {
    margin-top: 0;
  }

  .imageRow {
    margin-top: 16px;
  }

  .image {
    width: 100%;
  }
}
