import { CloseOutlined, FullscreenOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON><PERSON>, Spin, message } from 'antd';
import mammoth from 'mammoth';
import React, { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';
import styles from './FilePreview.less';

export interface FilePreviewProps {
  visible: boolean;
  onClose: () => void;
  file: {
    name: string;
    type: string;
    location: string;
    size: number;
  } | null;
}

const FilePreview: React.FC<FilePreviewProps> = ({
  visible,
  onClose,
  file,
}) => {
  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<
    'video' | 'pdf' | 'document' | 'excel' | 'image' | 'text' | 'unsupported'
  >('unsupported');

  // 获取文件类型
  const getFileType = (fileName: string, type: string) => {
    const lowerName = fileName.toLowerCase();
    const lowerType = type.toLowerCase();

    if (
      lowerType === 'visual' ||
      lowerName.endsWith('.mp4') ||
      lowerName.endsWith('.avi') ||
      lowerName.endsWith('.mov') ||
      lowerName.endsWith('.webm')
    ) {
      return 'video';
    }
    if (lowerType === 'pdf' || lowerName.endsWith('.pdf')) {
      return 'pdf';
    }
    if (
      lowerType === 'document' ||
      lowerName.endsWith('.doc') ||
      lowerName.endsWith('.docx')
    ) {
      return 'document';
    }
    if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) {
      return 'excel';
    }
    if (
      lowerName.endsWith('.jpg') ||
      lowerName.endsWith('.jpeg') ||
      lowerName.endsWith('.png') ||
      lowerName.endsWith('.gif') ||
      lowerName.endsWith('.webp')
    ) {
      return 'image';
    }
    if (lowerName.endsWith('.txt') || lowerName.endsWith('.md')) {
      return 'text';
    }
    return 'unsupported';
  };

  // 获取文件URL（这里需要根据实际情况调整）
  const getFileUrl = (location: string) => {
    // 如果是完整URL，直接返回
    if (location.startsWith('http://') || location.startsWith('https://')) {
      return location;
    }
    // 否则拼接基础URL（根据实际API调整）
    return `/api/files/${encodeURIComponent(location)}`;
  };

  // 处理Word文档预览
  const previewWordDocument = async (url: string) => {
    try {
      setLoading(true);
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      setContent(result.value);
    } catch (error) {
      console.error('Error previewing Word document:', error);
      message.error('无法预览Word文档');
      setContent('<p>无法预览此文档</p>');
    } finally {
      setLoading(false);
    }
  };

  // 处理Excel文件预览
  const previewExcelFile = async (url: string) => {
    try {
      setLoading(true);
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });

      // 取第一个工作表
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const htmlContent = XLSX.utils.sheet_to_html(worksheet);

      setContent(htmlContent);
    } catch (error) {
      console.error('Error previewing Excel file:', error);
      message.error('无法预览Excel文件');
      setContent('<p>无法预览此文件</p>');
    } finally {
      setLoading(false);
    }
  };

  // 处理文本文件预览
  const previewTextFile = async (url: string) => {
    try {
      setLoading(true);
      const response = await fetch(url);
      const text = await response.text();
      setContent(
        `<pre style="white-space: pre-wrap; font-family: monospace;">${text}</pre>`,
      );
    } catch (error) {
      console.error('Error previewing text file:', error);
      message.error('无法预览文本文件');
      setContent('<p>无法预览此文件</p>');
    } finally {
      setLoading(false);
    }
  };

  // 重置状态
  const resetState = () => {
    setContent(null);
    setPreviewType('unsupported');
    setLoading(false);
  };

  // 处理文件预览
  useEffect(() => {
    if (!visible || !file) {
      resetState();
      return;
    }

    const fileType = getFileType(file.name, file.type);
    setPreviewType(fileType);

    const fileUrl = getFileUrl(file.location);

    switch (fileType) {
      case 'video':
      case 'pdf':
      case 'image':
        // 这些类型直接使用URL，不需要预处理
        setContent(fileUrl);
        break;
      case 'document':
        previewWordDocument(fileUrl);
        break;
      case 'excel':
        previewExcelFile(fileUrl);
        break;
      case 'text':
        previewTextFile(fileUrl);
        break;
      default:
        setContent(null);
        message.warning('暂不支持此文件格式的预览');
    }
  }, [visible, file]);

  // 渲染预览内容
  const renderPreviewContent = () => {
    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <Spin size="large" />
          <p>正在加载文件...</p>
        </div>
      );
    }

    if (!content) {
      return (
        <div className={styles.errorContainer}>
          <p>无法预览此文件</p>
        </div>
      );
    }

    switch (previewType) {
      case 'video':
        return (
          <video
            controls
            className={styles.videoPlayer}
            src={content}
            style={{ width: '100%', height: '100%', maxHeight: '70vh' }}
          >
            您的浏览器不支持视频播放
          </video>
        );

      case 'pdf':
        return (
          <iframe
            src={content}
            className={styles.pdfViewer}
            style={{ width: '100%', height: '70vh', border: 'none' }}
            title="PDF预览"
          />
        );

      case 'image':
        return (
          <img
            src={content}
            alt={file?.name}
            className={styles.imagePreview}
            style={{
              maxWidth: '100%',
              maxHeight: '70vh',
              objectFit: 'contain',
            }}
          />
        );

      case 'document':
      case 'excel':
      case 'text':
        return (
          <div
            className={styles.documentViewer}
            dangerouslySetInnerHTML={{ __html: content }}
            style={{ height: '70vh', overflow: 'auto', padding: '20px' }}
          />
        );

      default:
        return (
          <div className={styles.errorContainer}>
            <p>暂不支持此文件格式的预览</p>
          </div>
        );
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width="80vw"
      style={{ top: '10vh' }}
      className={styles.filePreviewModal}
      destroyOnClose
      maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
      closeIcon={
        <Button
          type="text"
          icon={<CloseOutlined />}
          className={styles.closeButton}
        />
      }
      title={
        <div className={styles.modalHeader}>
          <span className={styles.fileName}>{file?.name}</span>
          <div className={styles.headerActions}>
            <Button
              type="text"
              icon={<FullscreenOutlined />}
              className={styles.fullscreenButton}
              onClick={() => {
                // 这里可以添加全屏功能
                message.info('全屏功能开发中');
              }}
            />
          </div>
        </div>
      }
    >
      <div className={styles.previewContainer}>{renderPreviewContent()}</div>
    </Modal>
  );
};

export default FilePreview;
