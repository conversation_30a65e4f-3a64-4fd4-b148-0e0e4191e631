from __future__ import annotations

from typing import List

from api.celery_app import celery_app  # noqa: E402
from api.db.services.document_service import DocumentService  # noqa: E402
from api.db.services.knowledgebase_service import KnowledgebaseService  # noqa: E402
from api.db.services.lesson_plan_service import LessonPlanService  # noqa: E402
import logging


@celery_app.task(name="lesson_plan.cleanup_kb_doc")
def cleanup_kb_doc(session_id: str, kb_id: str, doc_ids: List[str]):
    """Delete temporary documents and KB created for LessonPlan V2."""
    logging.info("[cleanup_kb_doc] triggered for session %s", session_id)

    # Delete documents
    for doc_id in doc_ids:
        try:
            DocumentService.clear_chunk_num(doc_id)
            DocumentService.remove_document(doc_id, tenant_id="")
            logging.info("Removed document %s", doc_id)
        except Exception:
            logging.exception("Failed to remove document %s", doc_id)

    # If K<PERSON> has no documents left, delete it
    try:
        cnt = DocumentService.get_kb_doc_count(kb_id)
        if cnt == 0:
            KnowledgebaseService.delete(kb_id)
            logging.info("Removed empty temporary KB %s", kb_id)
    except Exception:
        logging.exception("Failed to evaluate or delete KB %s", kb_id)

    # mark session status
    try:
        LessonPlanService.update_session(session_id, status="cleaned")
    except Exception:
        logging.exception("Failed to mark session cleaned")
