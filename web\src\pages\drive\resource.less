@import '@/theme/high-tech-theme.less';

// 强制浅色主题样式
.resource-page {
  background: var(--background-primary);
  min-height: calc(100vh - var(--header-height) + 4px);

  // 强制浅色主题，覆盖任何深色主题设置
  &.light-theme-override {
    background: #f9fafb !important;
    color: #111827 !important;

    // 强制所有子元素使用浅色主题
    &,
    & * {
      --background-primary: #f9fafb !important;
      --background-secondary: #f6f6f6 !important;
      --background-tertiary: #e5e7eb !important;
      --card-background: white !important;
      --text-primary: #111827 !important;
      --text-secondary: #4b5563 !important;
      --text-tertiary: #6b7280 !important;
      --border-color: #0000000f !important;
      --border-color-dark: #d1d5db !important;
    }
  }

  // 搜索输入框样式 - Drive 页面特定覆盖
  &.resource-filter-bar,
  .resource-filter-bar {
    .search-input-wrapper {
      // 页面级别的最高优先级样式覆盖
      :global {
        span.ant-input-affix-wrapper,
        .ant-input-affix-wrapper,
        span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq {
          background: #f3f4f6 !important;
          background-color: #f3f4f6 !important;
          border: 1px solid rgba(0, 0, 0, 0) !important;
          border-width: 1px !important;
          border-color: rgba(0, 0, 0, 0) !important;
          box-shadow: none !important;
          border-radius: 8px !important;
          transition: border-color 0.2s ease !important;

          &:hover {
            background: #f3f4f6 !important;
            background-color: #f3f4f6 !important;
            border: 1px solid rgba(0, 0, 0, 0) !important;
            border-width: 1px !important;
            border-color: rgba(0, 0, 0, 0) !important;
            box-shadow: none !important;
            outline: none !important;
          }

          &:focus,
          &:focus-within,
          &:active,
          &.ant-input-affix-wrapper-focused,
          &.ant-input-affix-wrapper-borderless {
            background: #f3f4f6 !important;
            background-color: #f3f4f6 !important;
            border: 1px solid #3b82f6 !important;
            border-width: 1px !important;
            border-color: #3b82f6 !important;
            box-shadow: none !important;
            outline: none !important;
          }

          input.ant-input,
          .ant-input {
            background: transparent !important;
            background-color: transparent !important;
            border: none !important;
            border-width: 0 !important;
            box-shadow: none !important;
            outline: none !important;

            &:hover,
            &:focus,
            &:active {
              background: transparent !important;
              background-color: transparent !important;
              border: none !important;
              border-width: 0 !important;
              box-shadow: none !important;
              outline: none !important;
            }
          }
        }
      }
    }
  }

  .resource-header {
    .resource-title {
      font-size: var(--font-size-2xl);
      font-weight: 600;
      .gradient-text();
      margin-bottom: var(--spacing-xs);
    }

    .resource-subtitle {
      color: var(--neutral-500);
      font-size: var(--font-size-sm);
      font-weight: 400;
      /* Ensure subtitle content (e.g. BulkOperateBar) is vertically centered */
      display: flex;
      align-items: center;

      // 当操作栏显示在subtitle内时的样式（紧凑模式）
      > div {
        // 紧凑模式的操作栏样式
        &.flex {
          color: var(--text-primary);
          font-size: var(--font-size-sm);

          section {
            color: var(--primary-color);
            font-weight: 500;

            span {
              font-size: var(--font-size-sm);
            }
          }

          // 操作按钮容器
          > div:last-child {
            display: flex;
            gap: var(--spacing-xs);

            > div {
              :global(.btn) {
                padding: 4px 8px;
                font-size: var(--font-size-xs);
                height: auto;
                min-height: 28px;
                border: 1px solid var(--border-color);
                background: var(--card-background);
                color: var(--text-primary);

                &:hover {
                  background: var(--background-secondary);
                  border-color: var(--primary-color);
                  color: var(--primary-color);
                }

                // 删除按钮特殊样式
                &.text-text-delete-red {
                  color: var(--error-color);

                  &:hover {
                    background: var(--error-ultralight);
                    border-color: var(--error-color);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 面包屑导航样式
  .resource-breadcrumb {
    margin-bottom: var(--spacing-sm);

    :global(.breadcrumb-list) {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      flex-wrap: wrap;
    }

    :global(.breadcrumb-item) {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      transition: color var(--transition-fast);

      &:hover {
        color: var(--primary-color);
      }

      &.cursor-pointer {
        cursor: pointer;
      }
    }

    :global(.breadcrumb-separator) {
      color: var(--text-tertiary);
      margin: 0 var(--spacing-xs);
    }

    :global(.breadcrumb-page) {
      color: var(--primary-color);
      font-weight: 500;
    }
  }

  .resource-toolbar {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    .view-toggle {
      display: flex;
      gap: var(--spacing-xs);
      padding: 2px;
      background: var(--background-secondary);
      border-radius: var(--border-radius-md);
      border: var(--border-width) solid var(--border-color);

      .view-toggle-btn {
        min-width: auto;
        padding: var(--spacing-xs);
        transition: all var(--transition-fast);

        &:hover {
          background: var(--primary-ultralight);
        }
      }
    }

    .add-resource-btn {
      .glow-effect();
      background: linear-gradient(
        135deg,
        var(--primary-color),
        var(--accent-color)
      );
      border: none;
      color: white;
      font-weight: 500;
      transition: all var(--transition-normal);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 0 20px var(--glow-color);
      }
    }
  }

  .resource-content {
    margin-top: var(--spacing-lg);
  }
}

// Grid View Styles
.resource-grid-container {
  .resource-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
  }

  .resource-grid-loading {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-lg);

    .resource-card-skeleton {
      .high-tech-card();
      padding: var(--spacing-md);
      height: 320px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-md);

      .skeleton-icon {
        width: 64px;
        height: 64px;
        background: linear-gradient(
          90deg,
          var(--neutral-200) 25%,
          var(--neutral-100) 50%,
          var(--neutral-200) 75%
        );
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: var(--border-radius-md);
      }

      .skeleton-content {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);

        .skeleton-title,
        .skeleton-subtitle,
        .skeleton-meta {
          height: 16px;
          background: linear-gradient(
            90deg,
            var(--neutral-200) 25%,
            var(--neutral-100) 50%,
            var(--neutral-200) 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: var(--border-radius-sm);
        }

        .skeleton-title {
          width: 80%;
          height: 20px;
        }

        .skeleton-subtitle {
          width: 60%;
        }

        .skeleton-meta {
          width: 40%;
        }
      }
    }
  }

  .resource-empty-state {
    text-align: center;
    padding: var(--spacing-3xl);

    .empty-icon {
      margin-bottom: var(--spacing-lg);
      color: var(--text-tertiary);
    }

    .empty-title {
      font-size: var(--font-size-xl);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .empty-description {
      color: var(--text-secondary);
      max-width: 400px;
      margin: 0 auto;
    }
  }

  /* 更强的全局选择器确保 Checkbox 主题色 */
  :global(button[role='checkbox']) {
    border: 1px solid #d1d5db !important;

    &[data-state='checked'] {
      background-color: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      color: white !important;
    }

    &:hover {
      border-color: var(--primary-color) !important;
    }

    &:focus-visible {
      box-shadow: 0 0 0 2px var(--primary-ultralight) !important;
      outline: none !important;
    }
  }

  /* 备用选择器 */
  :global(.peer) {
    border: 1px solid #d1d5db !important;

    &[data-state='checked'] {
      background-color: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      color: white !important;
    }

    &:hover {
      border-color: var(--primary-color) !important;
    }
  }
}

// Resource Card Styles
.resource-card {
  background: var(--card-background);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-sm);
  height: 240px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--accent-color)
    );
    opacity: 0;
    transition: opacity var(--transition-normal);
  }

  &:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);

    &::before {
      opacity: 1;
    }

    .resource-card-icon {
      transform: scale(1.05);
    }
  }

  &.resource-card-selected {
    border-color: var(--primary-color);
    background: var(--primary-ultralight);

    /* 移除顶部渐变条 */
    &::before {
      display: none;
    }
  }

  /* 选中的文件夹卡片与文件保持同样背景 */
  &.resource-card-folder.resource-card-selected {
    background: var(--primary-ultralight);
  }

  &.resource-card-folder {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.05) 0%,
      rgba(16, 185, 129, 0.05) 100%
    );

    /* 文件夹行卡片布局 */
    flex-direction: row;
    align-items: center;
    height: 56px;
    padding: 0 var(--spacing-md);

    .resource-card-header {
      order: 3;
      margin-left: auto;

      .resource-card-checkbox {
        display: none;
      }
    }

    .resource-card-icon {
      order: 1;
      height: auto;
      margin: 0 var(--spacing-sm) 0 0;

      svg {
        width: 24px;
        height: 24px;
      }
    }

    .resource-card-content {
      order: 2;
      flex: 1;
      min-width: 0;

      .resource-card-title {
        flex: 1 1 auto;
        max-width: 100%;
        text-align: left !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .resource-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    min-height: 24px;

    .resource-card-checkbox {
      opacity: 0;
      transition: opacity var(--transition-fast);

      /* Checkbox 主题色样式 - 更强的选择器覆盖 */
      :global(button) {
        border-color: rgba(156, 163, 175, 1) !important;

        &[data-state='checked'] {
          background-color: var(--primary-color) !important;
          border-color: var(--primary-color) !important;
          color: white !important;
        }

        &:hover {
          border-color: var(--primary-color) !important;
        }

        &:focus-visible {
          box-shadow: 0 0 0 2px var(--primary-ultralight) !important;
          outline: none !important;
        }
      }

      /* 更具体的 Radix UI 选择器 */
      :global([data-radix-collection-item]) {
        &[data-state='checked'] {
          background-color: var(--primary-color) !important;
          border-color: var(--primary-color) !important;
        }
      }
    }

    .resource-card-menu {
      opacity: 0;
      transition: opacity var(--transition-fast);
      padding: 2px;
      height: 20px;
      width: 20px;
    }
  }

  &:hover .resource-card-header {
    .resource-card-checkbox,
    .resource-card-menu {
      opacity: 1;
    }
  }

  &.resource-card-selected .resource-card-header {
    .resource-card-checkbox {
      opacity: 1;
    }
  }

  .resource-card-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-md);
    transition: transform var(--transition-normal);
    height: 80px;

    // 文件图标样式
    .file-icon {
      width: 64px;
      height: 80px;
      position: relative;
      border-radius: var(--border-radius-sm);
      display: flex;
      align-items: flex-end;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .file-icon-bg {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: 600;
        color: white;
        border-radius: 0 0 var(--border-radius-sm) var(--border-radius-sm);
      }

      &.pdf-icon {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a5a 100%);
        .file-icon-bg {
          background: rgba(238, 90, 90, 0.9);
        }
      }

      &.doc-icon {
        background: linear-gradient(135deg, #4dabf7 0%, #339af0 100%);
        .file-icon-bg {
          background: rgba(51, 154, 240, 0.9);
        }
      }

      &.txt-icon {
        background: linear-gradient(135deg, #69db7c 0%, #51cf66 100%);
        .file-icon-bg {
          background: rgba(81, 207, 102, 0.9);
        }
      }

      &.img-icon {
        background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
        .file-icon-bg {
          background: rgba(255, 152, 0, 0.9);
        }
      }

      &.video-icon {
        background: linear-gradient(135deg, #9c88ff 0%, #8b5cf6 100%);
        .file-icon-bg {
          background: rgba(139, 92, 246, 0.9);
        }
      }

      &.csv-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        .file-icon-bg {
          background: rgba(5, 150, 105, 0.9);
        }
      }

      &.default-icon {
        background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
        .file-icon-bg {
          background: rgba(100, 116, 139, 0.9);
        }
      }
    }
  }

  .resource-card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);

    /* 文件卡片（非文件夹）标题居中 */
    :not(.resource-card-folder) & {
      .resource-card-title {
        text-align: center;
        margin-left: auto;
        margin-right: auto;
      }
    }

    .resource-card-title {
      font-weight: 500;
      color: var(--text-primary);
      font-size: var(--font-size-sm);
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: var(--spacing-xs);
    }

    .resource-card-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 2px;
      margin-top: auto;

      span {
        font-size: 10px;
        line-height: 1.2;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 60px;
      }
    }
  }

  /* 新增明确的文件夹/文件标题样式 */
  .folder-title {
    text-align: left;
    margin-left: 0;
    margin-right: 0;
  }

  .file-title {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }
}

// List View Styles
.resource-list-container {
  padding: 0;
  overflow: hidden;

  .resource-table {
    margin-top: var(--spacing-md);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
    width: 100%;

    @media (max-width: 1200px) {
      overflow-x: auto;
    }

    :global {
      .ant-table {
        background: #ffffff;
        border-radius: 8px;
        border: none;
      }

      .ant-table-thead > tr > th {
        background: #f8fafc;
        color: #374151;
        font-weight: 500;
        font-size: 13px;
        border-bottom: 1px solid #e5e7eb;
        padding: 12px 16px;
        height: 48px;
      }

      .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f3f4f6;
        padding: 16px;
        background-color: transparent;
        font-size: 14px;
      }

      .ant-table-tbody > tr {
        transition: all 0.2s ease;

        &:hover > td {
          background-color: #f9fafb;
        }

        &:last-child > td {
          border-bottom: none;
        }
      }

      .ant-table-row-selected > td {
        background-color: #eff6ff !important;
      }

      .ant-pagination {
        margin-top: var(--spacing-lg);
        text-align: center;

        .ant-pagination-total-text {
          color: var(--text-secondary);
          font-size: 14px;
        }
      }

      .ant-switch {
        background-color: #d1d5db;

        &-checked {
          background-color: #3b82f6;
        }
      }

      .ant-table-filter-trigger {
        color: var(--text-secondary);
      }
    }
  }

  .file-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
    flex-shrink: 0;
  }

  .file-name {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;

    &.clickable {
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        color: var(--primary-color);
      }
    }
  }

  .upload-time {
    color: var(--text-secondary);
    font-size: 13px;
  }

  .file-size {
    color: var(--text-primary);
    font-size: 14px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;

    .empty-state-icon {
      font-size: 32px;
      margin-bottom: 16px;
      color: #9ca3af;

      @media (max-width: 768px) {
        font-size: 2rem;
        margin-bottom: 12px;
      }
    }

    h4 {
      margin-bottom: 8px;
      color: var(--text-primary);
      font-weight: 500;

      @media (max-width: 768px) {
        font-size: 16px;
      }
    }

    p {
      color: var(--text-secondary);
      margin: 0;
      font-size: 14px;

      @media (max-width: 768px) {
        font-size: 13px;
      }
    }
  }
}

// Shimmer Animation
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// Light Mode Overrides (浅色主题覆盖)
.resource-page {
  // 确保浅色主题下的正确背景色
  background: var(--background-primary) !important;

  // 全局样式覆盖 - 更加具体的选择器
  :global {
    // 分页组件样式修复
    .pagination {
      .pagination-content {
        .pagination-item {
          background: var(--card-background) !important;
          border: 1px solid var(--border-color) !important;
          color: var(--text-primary) !important;

          &:hover {
            background: var(--background-secondary) !important;
            border-color: var(--primary-color) !important;
          }

          // 当前页和选中状态
          &.bg-background-header-bar {
            background: var(--primary-color) !important;
            color: white !important;
          }
        }

        .pagination-link {
          color: var(--text-primary) !important;

          &:hover {
            color: var(--primary-color) !important;
          }
        }

        .pagination-previous,
        .pagination-next {
          color: var(--text-primary) !important;

          &:hover {
            color: var(--primary-color) !important;
          }
        }
      }
    }

    // 页面大小选择器修复
    .ragflow-select {
      .select-trigger {
        background: var(--card-background) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;

        &.bg-background-header-bar {
          background: var(--card-background) !important;
        }
      }

      .select-content {
        background: var(--card-background) !important;
        border: 1px solid var(--border-color) !important;
        color: var(--text-primary) !important;
      }

      .select-item {
        color: var(--text-primary) !important;

        &:hover {
          background: var(--background-secondary) !important;
        }
      }
    }

    // 通用深色背景修复
    .bg-background-header-bar {
      background: var(--card-background) !important;
      color: var(--text-primary) !important;
    }

    .text-text-title {
      color: var(--text-primary) !important;
    }

    .text-text-sub-title-invert {
      color: var(--text-secondary) !important;
    }

    // 修复所有可能的深色主题类
    .bg-gray-800,
    .bg-slate-800,
    .bg-neutral-800,
    .bg-zinc-800 {
      background: var(--card-background) !important;
      color: var(--text-primary) !important;
    }

    .text-white {
      color: var(--text-primary) !important;
    }

    .text-gray-400,
    .text-slate-400 {
      color: var(--text-secondary) !important;
    }

    // 修复按钮样式
    .btn,
    button {
      &:not(.add-resource-btn) {
        &.btn-outline,
        &[data-variant='outline'] {
          background: transparent !important;
          color: var(--text-secondary) !important;
          border: 1px solid var(--border-color) !important;

          &:hover {
            background: var(--background-secondary) !important;
            color: var(--text-primary) !important;
            border-color: var(--border-color-dark) !important;
          }
        }

        &:not(.btn-outline):not([data-variant='outline']) {
          background: var(--primary-color) !important;
          color: white !important;
          border-color: var(--primary-color) !important;

          &:hover {
            background: var(--primary-hover) !important;
          }
        }
      }
    }

    // 修复输入框样式
    input,
    .input {
      background: var(--card-background) !important;
      border: 1px solid var(--border-color) !important;
      color: var(--text-primary) !important;

      &:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 2px var(--primary-ultralight) !important;
      }

      &::placeholder {
        color: var(--text-tertiary) !important;
      }
    }

    // 修复卡片样式
    .card {
      background: var(--card-background) !important;
      border: 1px solid var(--border-color) !important;
      color: var(--text-primary) !important;
    }
  }

  // 修复列表/网格切换按钮
  .resource-toolbar {
    .view-toggle {
      background: var(--card-background) !important;
      border: 1px solid var(--border-color) !important;

      :global(.view-toggle-btn) {
        background: transparent !important;
        color: var(--text-secondary) !important;
        border: none !important;

        &:hover {
          background: var(--background-secondary) !important;
          color: var(--text-primary) !important;
        }

        // 选中状态 - 默认variant按钮
        &:not(.btn-outline) {
          background: var(--primary-color) !important;
          color: white !important;
          border-color: var(--primary-color) !important;
        }

        // outline variant按钮
        &.btn-outline {
          background: transparent !important;
          color: var(--text-secondary) !important;
          border: 1px solid var(--border-color) !important;

          &:hover {
            background: var(--background-secondary) !important;
            color: var(--text-primary) !important;
            border-color: var(--border-color-dark) !important;
          }
        }
      }
    }

    // 添加文件按钮保持高科技风格
    :global(.add-resource-btn) {
      background: linear-gradient(
        135deg,
        var(--primary-color),
        var(--accent-color)
      ) !important;
      color: white !important;
      border: none !important;
      box-shadow: 0 0 15px var(--glow-color) !important;

      &:hover {
        transform: translateY(-1px) !important;
        box-shadow: 0 0 20px var(--glow-color) !important;
      }
    }
  }

  // 修复批量操作栏
  :global(.bulk-operate-bar) {
    background: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
  }

  // 修复下拉菜单
  :global(.dropdown-menu-content) {
    background: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    box-shadow: var(--dropdown-shadow) !important;

    :global(.dropdown-menu-item) {
      color: var(--text-primary) !important;

      &:hover {
        background: var(--background-secondary) !important;
      }
    }
  }

  // 修复搜索框
  :global(.search-input) {
    background: var(--card-background) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px var(--primary-ultralight) !important;
    }
  }
}

// Dark Mode Adjustments
.dark {
  .resource-card {
    &.resource-card-folder {
      background: linear-gradient(
        135deg,
        rgba(59, 130, 246, 0.1) 0%,
        rgba(16, 185, 129, 0.1) 100%
      );
    }

    &.resource-card-selected {
      background: rgba(59, 130, 246, 0.15);
    }
  }

  .resource-card-skeleton {
    .skeleton-icon,
    .skeleton-title,
    .skeleton-subtitle,
    .skeleton-meta {
      background: linear-gradient(
        90deg,
        var(--neutral-700) 25%,
        var(--neutral-600) 50%,
        var(--neutral-700) 75%
      );
      background-size: 200% 100%;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .resource-page {
    padding: var(--spacing-md);

    .resource-toolbar {
      flex-direction: column;
      align-items: stretch;
      gap: var(--spacing-sm);

      .view-toggle {
        justify-content: center;
      }
    }
  }

  .resource-grid-container {
    .resource-grid,
    .resource-grid-loading {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: var(--spacing-sm);
    }
  }

  .resource-card {
    height: 200px;
    padding: var(--spacing-xs);
  }
}

/* 强制 Checkbox 主题色 - 最高优先级覆盖 */
.resource-page {
  [role='checkbox'] {
    border: 1px solid #d1d5db !important;

    &[data-state='checked'] {
      background-color: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      color: white !important;
    }

    &:hover {
      border-color: var(--primary-color) !important;
    }

    &:focus-visible {
      box-shadow: 0 0 0 2px var(--primary-ultralight) !important;
      outline: none !important;
    }
  }

  .peer {
    border: 1px solid #d1d5db !important;

    &[data-state='checked'] {
      background-color: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      color: white !important;
    }

    &:hover {
      border-color: var(--primary-color) !important;
    }
  }
}

// 菜单项样式 (与document-toolbar保持一致)
.menuItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 0;

  // &:hover {
  //   background-color: #f9fafb;
  // }

  // &:active {
  //   background-color: #f3f4f6;
  // }
}

.menuIcon {
  font-size: 14px;
  color: #6b7280;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menuText {
  font-size: 14px;
  color: #374151;
  font-weight: 400;
  line-height: 1.5;
}

// 下拉菜单容器样式
.dropdownOverlay {
  :global {
    .ant-dropdown {
      border-radius: 8px;
      box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
      border: 1px solid #e5e7eb;
      padding: 4px 0;
      min-width: 140px;
    }

    .ant-dropdown-menu {
      border-radius: 8px;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    .ant-dropdown-menu-item {
      padding: 0;
      margin: 0;
      border-radius: 0;

      &:hover {
        background-color: transparent;
      }
    }

    .ant-dropdown-menu-item-divider {
      margin: 4px 12px;
      border-top-color: #e5e7eb;
    }
  }
}
