# 系统设置模块实现说明

## 功能概述

本次实现了一个完整的「系统设置」模块，包括路由配置、页面布局、子页面和国际化支持。

## 实现的功能

### 1. 路由配置

- 在 `web/src/routes.ts` 中添加了系统设置路由：
  - 主路由：`/system-setting`
  - 子路由：
    - `/system-setting/group-member` (团队成员)
    - `/system-setting/model-setting` (模型设置)
  - 默认重定向到团队成员页面

### 2. 页面结构

#### 主页面 (`web/src/pages/system-setting/index.tsx`)

- 使用与用户设置相同的布局结构
- 包含侧边栏和内容区域
- 支持路由嵌套

#### 侧边栏 (`web/src/pages/system-setting/sidebar/`)

- 显示两个导航项：团队成员、模型设置
- 支持当前页面高亮
- 响应式设计

#### 团队成员页面 (`web/src/pages/system-setting/group-member/`)

- 显示系统管理团队信息
- 邀请成员功能界面
- 团队成员列表（待实现）
- 管理权限设置（待实现）

#### 模型设置页面 (`web/src/pages/system-setting/model-setting/`)

- 模型搜索功能
- 添加模型按钮
- 模型提供商卡片展示
- 按照图片要求的布局实现
- 包含 8 个示例模型：deepseek、4.0-8k、qwen-plus、llama3:8b、qwen:0.5、general、GLM-4、openai-kimi

### 3. 样式设计

- 现代化的卡片布局
- 响应式设计支持
- 悬停动画效果
- 统一的设计语言

### 4. 国际化支持

- 中文翻译：`web/src/locales/zh.ts`
- 英文翻译：`web/src/locales/en.ts`
- 支持的键值：
  - `group-member`: 团队成员 / Group Member
  - `model-setting`: 模型设置 / Model Setting

### 5. 导航集成

- 左侧主导航底部的「系统设置」按钮已配置正确
- 点击后导航到 `/system-setting` 路径
- 默认显示团队成员页面

## 文件结构

```
web/src/pages/system-setting/
├── index.tsx                    # 主页面
├── index.less                   # 主页面样式
├── constants.tsx                # 常量配置
├── sidebar/
│   ├── index.tsx               # 侧边栏组件
│   └── index.less              # 侧边栏样式
├── group-member/
│   ├── index.tsx               # 团队成员页面
│   └── index.less              # 团队成员样式
└── model-setting/
    ├── index.tsx               # 模型设置页面
    └── index.less              # 模型设置样式
```

## 待完善功能

### 团队成员页面

1. 团队成员列表数据获取和显示
2. 邀请成员功能实现
3. 权限管理功能
4. 成员角色管理

### 模型设置页面

1. 真实模型数据接口集成
2. 模型搜索功能实现
3. 添加新模型功能
4. 模型配置和管理

## 技术特点

1. **模块化设计**：每个页面都是独立的模块，便于维护和扩展
2. **响应式布局**：支持不同屏幕尺寸的设备
3. **国际化支持**：多语言界面支持
4. **代码复用**：参考现有用户设置页面的成熟模式
5. **类型安全**：使用 TypeScript 确保类型安全

## 如何使用

1. 启动开发服务器：`npm run dev`
2. 访问页面：点击左侧导航底部的「系统设置」按钮
3. 或直接访问：`http://localhost:8000/system-setting`

## 构建验证

所有代码已通过构建验证，生成的文件包括：

- `p__system-setting__index.9b237bd2.async.js` (6.72 kB)
- `p__system-setting__group-member__index.90f4b6df.async.js` (1.84 kB)
- `p__system-setting__model-setting__index.6cc73881.async.js` (1.25 kB)
- 相应的 CSS 文件

系统设置模块已成功集成到 RAGFlow 项目中，提供了完整的用户界面和良好的用户体验。
