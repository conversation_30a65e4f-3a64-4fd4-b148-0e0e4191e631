import { useRegisterAndAddTenantUser } from '@/hooks/system-setting-hooks';
import { rsaPsw } from '@/utils';
import { LockOutlined, MailOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Form, Input, Modal, message } from 'antd';
import { useTranslation } from 'react-i18next';

interface AddMemberModalProps {
  visible: boolean;
  onCancel: () => void;
  tenantId?: string;
  tenantName?: string;
}

interface FormValues {
  nickname: string;
  email: string;
  password: string;
  confirmPassword: string;
}

const AddMemberModal: React.FC<AddMemberModalProps> = ({
  visible,
  onCancel,
  tenantId,
  tenantName,
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm<FormValues>();
  const { registerAndAddUser, loading } = useRegisterAndAddTenantUser();

  const handleSubmit = async (values: FormValues) => {
    if (!tenantId) {
      message.error('请先选择租户');
      return;
    }

    try {
      const encryptedPassword = rsaPsw(values.password);
      if (!encryptedPassword) {
        message.error('密码加密失败，请重试');
        return;
      }

      const userData = {
        nickname: values.nickname,
        email: values.email,
        password: encryptedPassword,
      };

      const result = await registerAndAddUser({ tenantId, userData });

      if (result.code === 0) {
        form.resetFields();
        onCancel();
      } else {
        message.error(result.message || '添加成员失败');
      }
    } catch (error) {
      console.error('Error adding member:', error);
      message.error('添加成员失败，请重试');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`添加成员到 ${tenantName || '租户'}`}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={480}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        autoComplete="off"
        style={{ marginTop: 24 }}
      >
        <Form.Item
          name="nickname"
          label="用户名"
          rules={[
            { required: true, message: '请输入用户名' },
            { min: 3, message: '用户名至少3个字符' },
            { max: 20, message: '用户名最多20个字符' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="请输入用户名"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: '请输入邮箱' },
            { type: 'email', message: '请输入有效的邮箱地址' },
          ]}
        >
          <Input
            prefix={<MailOutlined />}
            placeholder="请输入邮箱地址"
            size="large"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label="密码"
          rules={[
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码至少6个字符' },
            { max: 20, message: '密码最多20个字符' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入密码"
            size="large"
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, marginTop: 32 }}>
          <div style={{ display: 'flex', gap: 12, justifyContent: 'flex-end' }}>
            <Button onClick={handleCancel} size="large">
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              size="large"
            >
              添加成员
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddMemberModal;
