import { useTranslate } from '@/hooks/common-hooks';
import { IModalProps } from '@/interfaces/common';
import {
  Checkbox,
  Flex,
  Modal,
  Progress,
  Radio,
  Select,
  Upload,
  UploadFile,
  UploadProps,
} from 'antd';
import { Upload as LucideUpload } from 'lucide-react';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';

import { useSelectParserList } from '@/hooks/user-setting-hooks';
import styles from './index.less';

const { Dragger } = Upload;

const FileUpload = ({
  directory,
  fileList,
  setFileList,
  uploadProgress,
}: {
  directory: boolean;
  fileList: UploadFile[];
  setFileList: Dispatch<SetStateAction<UploadFile[]>>;
  uploadProgress?: number;
}) => {
  const { t } = useTranslate('fileManager');
  const props: UploadProps = {
    multiple: true,
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file: UploadFile) => {
      setFileList((pre) => {
        return [...pre, file];
      });

      return false;
    },
    directory,
    fileList,
    progress: {
      strokeWidth: 2,
    },
  };

  return (
    <>
      <Progress percent={uploadProgress} showInfo={false} strokeWidth={4} />
      <Dragger {...props} className={styles.uploader}>
        <p className="ant-upload-drag-icon">
          <LucideUpload
            size={48}
            color="rgb(107, 114, 128)"
            style={{ display: 'block', margin: '0 auto' }}
          />
        </p>
        <p className="ant-upload-text">{t('uploadTitle')}</p>
        <p className="ant-upload-hint">{t('uploadDescription')}</p>
        {false && <p className={styles.uploadLimit}>{t('uploadLimit')}</p>}
      </Dragger>
    </>
  );
};

interface IFileUploadModalProps
  extends IModalProps<
    { parseOnCreation: boolean; directoryFileList: UploadFile[] } | UploadFile[]
  > {
  uploadFileList?: UploadFile[];
  setUploadFileList?: Dispatch<SetStateAction<UploadFile[]>>;
  uploadProgress?: number;
  setUploadProgress?: Dispatch<SetStateAction<number>>;
}

const FileUploadModal = ({
  visible,
  hideModal,
  loading,
  onOk: onFileUploadOk,
  uploadFileList: fileList,
  setUploadFileList: setFileList,
  uploadProgress,
  setUploadProgress,
}: IFileUploadModalProps) => {
  const { t } = useTranslate('fileManager');
  const parserList = useSelectParserList();
  const [parseOnCreation, setParseOnCreation] = useState(true);

  const defaultParserValue = useMemo(() => {
    const generalItem = parserList.find(
      (p) => p.label.toLowerCase() === 'general' || p.label === '通用',
    );
    return generalItem?.value || parserList[0]?.value;
  }, [parserList]);

  const [selectedParser, setSelectedParser] = useState<string | undefined>(
    defaultParserValue,
  );

  useEffect(() => {
    if (parserList.length === 0) return;
    setSelectedParser((prev) => prev ?? defaultParserValue);
  }, [parserList, defaultParserValue]);

  const [currentFileList, setCurrentFileList] = useState<UploadFile[]>([]);
  const [directoryFileList, setDirectoryFileList] = useState<UploadFile[]>([]);
  const [uploadType, setUploadType] = useState<'file' | 'directory'>('file');

  const clearFileList = () => {
    if (setFileList) {
      setFileList([]);
      setUploadProgress?.(0);
    } else {
      setCurrentFileList([]);
    }
    setDirectoryFileList([]);
  };

  const onOk = async () => {
    if (uploadProgress === 100) {
      hideModal?.();
      return;
    }

    const ret = await onFileUploadOk?.(
      fileList
        ? { parseOnCreation, directoryFileList }
        : [...currentFileList, ...directoryFileList],
    );
    return ret;
  };

  const afterClose = () => {
    clearFileList();
  };

  return (
    <>
      <Modal
        wrapClassName={styles.uploadModal}
        title={t('uploadFile')}
        open={visible}
        onOk={onOk}
        onCancel={hideModal}
        confirmLoading={loading}
        afterClose={afterClose}
      >
        <Flex gap={'large'} vertical>
          <Flex className={styles.formRow}>
            <span className={styles.label}>上传类型</span>
            <Radio.Group
              value={uploadType}
              onChange={(e) => setUploadType(e.target.value)}
            >
              <Radio value="file">{t('file')}</Radio>
              <Radio value="directory">{t('directory')}</Radio>
            </Radio.Group>
          </Flex>
          <Flex className={styles.formRow} style={{ marginTop: 4 }}>
            <span className={styles.label}>解析方法</span>
            <Select
              style={{ minWidth: 140 }}
              value={selectedParser}
              onChange={setSelectedParser}
              options={parserList}
            />
            <Checkbox
              checked={parseOnCreation}
              onChange={(e) => setParseOnCreation(e.target.checked)}
            >
              {t('parseOnCreation')}
            </Checkbox>
          </Flex>
          {uploadType === 'file' ? (
            <FileUpload
              directory={false}
              fileList={fileList ? fileList : currentFileList}
              setFileList={setFileList ? setFileList : setCurrentFileList}
              uploadProgress={uploadProgress}
            />
          ) : (
            <FileUpload
              directory
              fileList={directoryFileList}
              setFileList={setDirectoryFileList}
              uploadProgress={uploadProgress}
            />
          )}
        </Flex>
      </Modal>
    </>
  );
};

export default FileUploadModal;
