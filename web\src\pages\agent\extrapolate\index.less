@import '@/theme/high-tech-theme.less';

.extrapolateContainer {
  width: 100%;
}

.pageHeader {
  margin-bottom: var(--spacing-lg);

  .backButton {
    margin-bottom: var(--spacing-sm);
    padding: 0;
    display: flex;
    align-items: center;
    color: var(--text-secondary);

    &:hover {
      color: var(--primary-color);
    }
  }

  .pageTitle {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);

    .titleIcon {
      font-size: 28px;
      margin-right: var(--spacing-sm);
      color: var(--primary-color);
    }

    h3 {
      margin: 0;
      font-weight: 600;
      background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
    }
  }

  .pageDescription {
    color: var(--text-secondary);
    font-size: 1rem;
    max-width: 600px;
  }
}

.formCard {
  height: 100%;
  margin-bottom: var(--spacing-md);
  .high-tech-card();

  :global(.ant-card-head) {
    border-bottom: 1px solid var(--border-color-light);
    padding: var(--spacing-md) var(--spacing-lg);
  }

  :global(.ant-card-body) {
    padding: var(--spacing-lg);
  }
}

.formActions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);

  :global(.ant-btn-primary:not(:disabled):not(.ant-btn-disabled):hover) {
    transform: translateY(-3px) scale(1.02);
    background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #faad14 100%);
    box-shadow:
      0 8px 32px 0 rgba(var(--primary-color-rgb), 0.6),
      0 0 20px 0 rgba(var(--primary-color-rgb), 0.4),
      0 0 40px 0 rgba(var(--accent-color-rgb), 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);

    // 启用发光边框
    &::before {
      opacity: 1;
      animation: borderGlow 2s ease-in-out infinite alternate;
    }

    // 添加发光文字效果
    color: #ffffff;
    text-shadow:
      0 0 5px rgba(255, 255, 255, 0.8),
      0 0 10px rgba(var(--primary-color-rgb), 0.6),
      0 0 15px rgba(var(--accent-color-rgb), 0.4);
  }

  .generateButton {
    min-width: 180px;
    height: 48px;
    font-size: 1rem;
    font-weight: 500;
    border-radius: var(--border-radius-md);
    background: linear-gradient(
      90deg,
      var(--primary-color) 0%,
      var(--accent-color) 100%
    );
    border: none;
    box-shadow:
      0 4px 14px 0 rgba(var(--primary-color-rgb), 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    // 添加伪元素用于高科技发光边框效果
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: var(--border-radius-md);
      padding: 1px;
      background: linear-gradient(
        45deg,
        transparent,
        rgba(var(--primary-color-rgb), 0.3),
        transparent,
        rgba(var(--accent-color-rgb), 0.3),
        transparent
      );
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: exclude;
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask-composite: exclude;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-3px) scale(1.02);
      background: linear-gradient(90deg, #1890ff 0%, #52c41a 50%, #faad14 100%);
      box-shadow:
        0 8px 32px 0 rgba(var(--primary-color-rgb), 0.6),
        0 0 20px 0 rgba(var(--primary-color-rgb), 0.4),
        0 0 40px 0 rgba(var(--accent-color-rgb), 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1);

      // 启用发光边框
      &::before {
        opacity: 1;
        animation: borderGlow 2s ease-in-out infinite alternate;
      }

      // 添加发光文字效果
      color: #ffffff;
      text-shadow:
        0 0 5px rgba(255, 255, 255, 0.8),
        0 0 10px rgba(var(--primary-color-rgb), 0.6),
        0 0 15px rgba(var(--accent-color-rgb), 0.4);
    }

    &:active {
      transform: translateY(-1px) scale(1.01);
      box-shadow:
        0 4px 16px 0 rgba(var(--primary-color-rgb), 0.7),
        0 0 10px 0 rgba(var(--primary-color-rgb), 0.5),
        inset 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    // 添加脉冲动画关键帧
    @keyframes borderGlow {
      0% {
        background: linear-gradient(
          45deg,
          transparent,
          rgba(var(--primary-color-rgb), 0.3),
          transparent,
          rgba(var(--accent-color-rgb), 0.3),
          transparent
        );
      }
      50% {
        background: linear-gradient(
          45deg,
          rgba(var(--accent-color-rgb), 0.2),
          rgba(var(--primary-color-rgb), 0.5),
          rgba(var(--accent-color-rgb), 0.2),
          rgba(var(--primary-color-rgb), 0.5),
          rgba(var(--accent-color-rgb), 0.2)
        );
      }
      100% {
        background: linear-gradient(
          45deg,
          transparent,
          rgba(var(--primary-color-rgb), 0.4),
          transparent,
          rgba(var(--accent-color-rgb), 0.4),
          transparent
        );
      }
    }
  }
}

.resultCard {
  height: 100%;
  .high-tech-card();

  :global(.ant-card-head) {
    border-bottom: 1px solid var(--border-color-light);
    padding: var(--spacing-md) var(--spacing-lg);
  }

  :global(.ant-card-body) {
    padding: var(--spacing-lg);
    min-height: 400px;
    max-height: 600px;
    overflow-y: auto;
  }

  // 转发到3.0系统按钮的特殊样式
  .forwardButton {
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%) !important;
    border: none !important;
    color: white !important;
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3) !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;

    &:hover {
      background: linear-gradient(90deg, #389e0d 0%, #52c41a 100%) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 16px rgba(82, 196, 26, 0.4) !important;
      color: white !important;
    }

    &:focus {
      background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%) !important;
      color: white !important;
    }

    &:active {
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(82, 196, 26, 0.5) !important;
    }

    :global(.anticon) {
      color: white !important;
    }
  }
}

.resultContent {
  line-height: 1.66;
  color: var(--text-primary);
  width: 100%;
  height: 100%;

  // Styling for markdown content
  :global {
    .text {
      width: 100%;
      height: 100%;
      padding: var(--spacing-sm);
    }

    .text h1,
    .text h2,
    .text h3,
    .text h4,
    .text h5,
    .text h6 {
      font-weight: 600;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
    }

    .text h1 {
      font-size: 1.8em;
    }

    .text h2 {
      font-size: 1.5em;
    }

    .text h3 {
      font-size: 1.3em;
    }

    .text p {
      margin-bottom: 1em;
    }

    .text ul,
    .text ol {
      padding-left: 1.5em;
      margin-bottom: 1em;
    }

    .text code {
      background-color: rgba(var(--primary-color-rgb), 0.1);
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo,
        monospace;
      font-size: 0.9em;
    }

    .text pre {
      margin: 1em 0;
      padding: 1em;
      background-color: var(--code-bg);
      border-radius: var(--border-radius-sm);
      overflow-x: auto;
    }
  }
}

.emptyResult {
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  .placeholder {
    text-align: center;
    color: var(--text-tertiary);

    .placeholderIcon {
      font-size: 48px;
      margin-bottom: var(--spacing-md);
      display: block;
      opacity: 0.5;
    }

    div {
      font-size: 1rem;
      line-height: 1.5;
    }
  }
}

.loadingResult {
  height: 100%;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  .loadingContainer {
    text-align: center;
    color: var(--text-secondary);

    p {
      font-size: 1rem;
      margin: 0;
      color: #666;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .extrapolateContainer {
  }

  .pageHeader {
    .pageTitle {
      .titleIcon {
        font-size: 24px;
      }

      h3 {
        font-size: 1.5rem;
      }
    }

    .pageDescription {
      font-size: 0.9rem;
    }
  }

  .formCard,
  .resultCard {
    :global(.ant-card-body) {
      padding: var(--spacing-md);
    }
  }

  .resultCard {
    :global(.ant-card-body) {
      max-height: 400px;
    }
  }
}
