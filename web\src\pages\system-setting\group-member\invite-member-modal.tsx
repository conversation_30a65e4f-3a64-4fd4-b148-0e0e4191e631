import {
  ISearchUser,
  useInviteTenantUser,
  useSearchUsers,
} from '@/hooks/system-setting-hooks';
import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import { ITenantUser } from '@/interfaces/database/user-setting';
import { MailOutlined, UserOutlined } from '@ant-design/icons';
import { useDebounceFn } from 'ahooks';
import {
  Avatar,
  Button,
  Empty,
  Input,
  List,
  Modal,
  Spin,
  Typography,
} from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

const { Search } = Input;
const { Text } = Typography;

interface InviteMemberModalProps {
  visible: boolean;
  onCancel: () => void;
  tenantId?: string;
  tenantName?: string;
  tenantUsers?: ITenantUser[]; // 当前租户的用户列表
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({
  visible,
  onCancel,
  tenantId,
  tenantName,
  tenantUsers = [],
}) => {
  const { t } = useTranslation();
  const [searchKeyword, setSearchKeyword] = useState('');
  const [debouncedKeyword, setDebouncedKeyword] = useState('');

  const { data: searchResult, loading: searchLoading } =
    useSearchUsers(debouncedKeyword);
  const { inviteUser, loading: inviteLoading } = useInviteTenantUser();
  const { data: currentUser } = useFetchUserInfo(); // 获取当前用户信息

  // 创建不可邀请用户邮箱的映射（包括租户用户和当前用户）
  const excludedUserEmails = useMemo(() => {
    const emails = new Set(
      tenantUsers ? tenantUsers?.map((user) => user.email) : [],
    );
    // 添加当前用户邮箱到排除列表
    if (currentUser?.email) {
      emails.add(currentUser.email);
    }
    return emails;
  }, [tenantUsers, currentUser?.email]);

  // 防抖搜索
  const { run: debouncedSearch } = useDebounceFn(
    (keyword: string) => {
      setDebouncedKeyword(keyword);
    },
    { wait: 300 },
  );

  useEffect(() => {
    if (searchKeyword.length >= 2) {
      debouncedSearch(searchKeyword);
    } else {
      setDebouncedKeyword('');
    }
  }, [searchKeyword, debouncedSearch]);

  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  const handleInviteUser = async (user: ISearchUser) => {
    if (!tenantId) {
      return;
    }

    try {
      await inviteUser({ tenantId, email: user.email });
    } catch (error) {
      console.error('Error inviting user:', error);
    }
  };

  const handleCancel = () => {
    setSearchKeyword('');
    setDebouncedKeyword('');
    onCancel();
  };

  // 判断用户是否不可邀请（已在租户中或是当前用户）
  const isUserExcluded = (userEmail: string) => {
    return excludedUserEmails.has(userEmail);
  };

  // 获取不可邀请的原因
  const getExcludeReason = (userEmail: string) => {
    if (currentUser?.email === userEmail) {
      return '当前用户';
    }
    if (tenantUsers.some((user) => user.email === userEmail)) {
      return '已在租户中';
    }
    return '';
  };

  return (
    <Modal
      title={`邀请成员加入 ${tenantName || '租户'}`}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <div style={{ marginTop: 16 }}>
        <Search
          placeholder="搜索用户名或邮箱..."
          value={searchKeyword}
          onChange={(e) => handleSearch(e.target.value)}
          size="middle"
          prefix={<UserOutlined />}
          allowClear
        />

        <div
          style={{
            marginTop: 16,
            minHeight: 300,
            maxHeight: 400,
            overflow: 'auto',
          }}
        >
          {searchLoading ? (
            <div style={{ textAlign: 'center', padding: 40 }}>
              <Spin size="large" />
            </div>
          ) : debouncedKeyword && searchResult.users.length === 0 ? (
            <Empty
              description="未找到匹配的用户"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ) : debouncedKeyword ? (
            <List
              dataSource={searchResult.users}
              renderItem={(user) => {
                const userExcluded = isUserExcluded(user.email);
                const excludeReason = getExcludeReason(user.email);
                return (
                  <List.Item
                    actions={[
                      <Button
                        key="invite"
                        type="primary"
                        size="small"
                        loading={inviteLoading}
                        disabled={userExcluded}
                        onClick={() => handleInviteUser(user)}
                        icon={<MailOutlined />}
                      >
                        {userExcluded ? '不可邀请' : '邀请'}
                      </Button>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          src={user.avatar}
                          style={{
                            backgroundColor: user.avatar
                              ? undefined
                              : '#1890ff',
                          }}
                        >
                          {!user.avatar &&
                            user.nickname?.charAt(0).toUpperCase()}
                        </Avatar>
                      }
                      title={
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 8,
                          }}
                        >
                          <span>{user.nickname}</span>
                          {userExcluded && (
                            <span
                              style={{
                                fontSize: 12,
                                color: '#999',
                                background: '#f0f0f0',
                                padding: '2px 6px',
                                borderRadius: 4,
                                fontWeight: 'normal',
                              }}
                            >
                              {excludeReason}
                            </span>
                          )}
                        </div>
                      }
                      description={
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {user.email}
                        </Text>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: 40, color: '#999' }}>
              <UserOutlined style={{ fontSize: 48, marginBottom: 16 }} />
              <div>请输入用户名或邮箱进行搜索</div>
              <div style={{ fontSize: 12, marginTop: 8 }}>
                至少输入2个字符开始搜索
              </div>
            </div>
          )}
        </div>

        {debouncedKeyword && searchResult.total > 0 && (
          <div style={{ textAlign: 'center', marginTop: 16, color: '#666' }}>
            找到 {searchResult.total} 个用户
          </div>
        )}
      </div>
    </Modal>
  );
};

export default InviteMemberModal;
