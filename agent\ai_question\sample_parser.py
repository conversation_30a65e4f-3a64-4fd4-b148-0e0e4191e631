from __future__ import annotations

"""Sample file parsing utilities for AI Question.

In the initial refactor we provide a minimal implementation (TXT & CSV). More
formats can be added later by reusing Extrapolate parsing strategies.
"""

import csv
import logging
from io import BytesIO, String<PERSON>
from typing import Tuple, List

MAX_CONTENT_LENGTH = 10_240  # chars


def _clean_text(text: str) -> str:
    return "\n".join(line.strip() for line in text.splitlines() if line.strip())


def parse_txt(content_bytes: bytes) -> str:
    try:
        text = content_bytes.decode("utf-8", errors="ignore")
    except UnicodeDecodeError:
        text = content_bytes.decode("gbk", errors="ignore")
    return _clean_text(text)


def parse_csv(content_bytes: bytes) -> Tuple[str, List[str]]:
    try:
        text = content_bytes.decode("utf-8", errors="ignore")
    except UnicodeDecodeError:
        text = content_bytes.decode("gbk", errors="ignore")
    reader = csv.reader(StringIO(text))
    rows = list(reader)
    if not rows:
        return "", []
    headers = rows[0]
    body_lines = [", ".join(row) for row in rows[1:]]
    return _clean_text("\n".join(body_lines)), headers


class SampleFileParser:
    """Lightweight callable used as Canvas component."""

    SUPPORTED = {"txt": parse_txt, "csv": parse_csv}

    def __call__(self, filename: str, file_bytes: bytes) -> Tuple[str, List[str]]:
        suffix = filename.lower().rsplit(
            ".", 1)[-1] if "." in filename else "txt"
        parser = self.SUPPORTED.get(suffix)
        if not parser:
            logging.warning(
                "[SampleFileParser] Unsupported format: %s", suffix)
            return "", []
        content, *rest = parser(file_bytes)
        content = content[:MAX_CONTENT_LENGTH] + \
            "..." if len(content) > MAX_CONTENT_LENGTH else content
        headers = rest[0] if rest else []
        return content, headers
