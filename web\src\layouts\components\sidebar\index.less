@import '@/theme/high-tech-theme.less';

.highTechSidebar {
  width: var(--sidebar-width);
  height: calc(100vh - var(--header-height) + 4px);
  background-color: var(--card-background);
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--border-color);
  transition: all var(--transition-normal);
  overflow: hidden;
  z-index: 990;

  &.sidebarCollapsed {
    width: var(--sidebar-collapsed-width);

    .menu {
      padding: 0 !important;
    }
  }
}

.sidebarContent {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-top: var(--spacing-md);
}

/* Collapsed sidebar content adjustments */
.sidebarCollapsed {
  .sidebarContent {
    padding-top: var(--spacing-md);
    padding-left: 0 !important;
    padding-right: 0 !important;

    :global {
      .ant-menu {
        .ant-menu-item {
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-items: center !important;
          justify-content: center !important;
          border-radius: var(--border-radius-md) !important;
          width: var(--sidebar-item-size) !important;
          height: var(--sidebar-item-size) !important;
          line-height: var(--sidebar-item-size) !important;

          .ant-menu-title-content {
            font-size: 14px !important;
            line-height: 2 !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            opacity: 1 !important;
            text-align: center !important;
          }

          &.ant-menu-item-selected {
            .ant-menu-title-content {
              font-weight: 400;
            }
          }
        }
      }

      .ant-menu-submenu {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;

        .ant-menu-submenu-title {
          display: flex !important;
          flex-direction: column !important;
          align-items: center !important;
          justify-content: center !important;
          border-radius: var(--border-radius-md) !important;
          margin: 0 !important;
          padding: 0 !important;
          width: var(--sidebar-item-size) !important;
          height: var(--sidebar-item-size) !important;
          line-height: var(--sidebar-item-size) !important;

          .ant-menu-title-content {
            font-size: 14px !important;
            line-height: 2 !important;
            height: auto !important;
            margin: 0 !important;
            padding: 0 !important;
            opacity: 1 !important;
            text-align: center !important;
          }

          .ant-menu-submenu-arrow {
            display: none !important;
          }
        }

        &.ant-menu-submenu-selected {
          .ant-menu-title-content {
            font-weight: 400;
          }
        }
      }
    }
  }

  .menu {
    padding: 0 !important;
    width: var(--sidebar-collapsed-width) !important;
    margin: 0 !important;

    /* 确保菜单容器完全重置 */
    :global {
      .ant-menu-root {
        padding: 0 !important;
        margin: 0 !important;
      }
    }
  }
}

.menu {
  border-right: none !important;

  /* Customizing ant design menu to match flat high-tech style */
  :global {
    .ant-menu-title-content {
      font-weight: 400;
      font-family: var(--font-family);
    }

    .ant-menu-item {
      border-radius: var(--border-radius-md) !important;

      .ant-menu-title-content {
        font-size: 14px;
        height: 42px;
        line-height: 42px;
      }

      &::after {
        display: none !important;
      }
    }

    .ant-menu-submenu-title {
      border-radius: var(--border-radius-md) !important;

      .ant-menu-title-content {
        font-size: 14px;
        height: 42px;
        line-height: 42px;
      }

      .ant-menu-submenu-arrow {
        color: #afafaf;
      }
    }

    .ant-menu-sub.ant-menu-inline {
      background: transparent !important;
    }

    .ant-menu-item-selected,
    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      background-color: var(--background-menu-selected) !important;
      color: var(--primary-color) !important;
      font-weight: 400;
    }

    .ant-menu-item:hover,
    .ant-menu-submenu-title:hover {
      color: var(--primary-color) !important;
      background-color: var(--neutral-100) !important;
    }

    /* Icon styling */
    .ant-menu-item .anticon,
    .ant-menu-submenu-title .anticon {
      font-size: 16px;
    }

    .ant-menu-item-icon {
      color: var(--text-secondary);
    }

    .ant-menu-item-selected .ant-menu-item-icon {
      color: var(--primary-color);
    }

    /* Collapsed state styles */
    .ant-menu-inline-collapsed {
      width: var(--sidebar-collapsed-width) !important;
      margin: 0 !important;
      padding: 0 !important;

      /* 重置所有Antd默认间距和定位 */
      .ant-menu-item,
      .ant-menu-submenu > .ant-menu-submenu-title {
        padding: 0 !important;
        margin: 4px 0 !important;
        width: var(--sidebar-collapsed-width) !important;
        height: 64px !important;
        line-height: 1 !important;
        flex-direction: column !important;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: var(--border-radius-md) !important;

        /* 完全居中的关键设置 */
        position: relative !important;
        left: 0 !important;
        right: 0 !important;
        transform: none !important;

        /* 显示文本并保持居中 */
        .ant-menu-title-content {
          display: block !important;
          font-size: 12px !important;
          line-height: 20px !important;
          margin-top: 4px !important;
          width: 100%;
          text-align: center !important;
        }

        /* 图标完全居中设置 */
        .ant-menu-item-icon,
        .anticon {
          margin: 0 !important;
          padding: 0 !important;
          font-size: 18px !important;
          display: block !important;
          line-height: 1 !important;
          position: static !important;
          width: auto !important;
          height: auto !important;
        }

        /* Normal state colors */
        .ant-menu-item-icon,
        .anticon {
          color: var(--text-secondary) !important;
        }

        /* Hover effect for collapsed state - 圆角矩形高宽一致 */
        &:hover {
          background-color: var(--primary-ultralight) !important;
          color: var(--primary-color) !important;

          .ant-menu-item-icon,
          .anticon {
            color: var(--primary-color) !important;
          }
        }

        /* Selected state */
        &.ant-menu-item-selected {
          background-color: var(--background-menu-selected) !important;
          color: var(--primary-color) !important;
          font-weight: 400;

          .ant-menu-item-icon,
          .anticon {
            color: var(--primary-color) !important;
          }
        }
      }

      /* 确保父级容器不干扰 */
      > .ant-menu-item,
      > .ant-menu-submenu {
        width: var(--sidebar-collapsed-width) !important;
        padding: 0 !important;
        margin: 4px 0 !important;
      }

      .ant-menu-submenu {
        .ant-menu-submenu-arrow {
          display: none !important;
        }
      }

      /* Hide submenus in collapsed state */
      .ant-menu-submenu-inline {
        display: none !important;
      }
    }
  }
}

.sidebarFooter {
  padding: 0;
  display: flex;
  flex-direction: column;
  border-top: 1px solid var(--border-color);
  position: relative;
  min-height: 132px; /* 进一步增加高度确保有足够空间 */
}

.settingsButton {
  padding: 8px 16px;
  border-radius: var(--border-radius-md);
  font-weight: 400;
  width: calc(100% - 2 * var(--spacing-md));
  height: 40px !important;
  text-align: left;
  transition: all var(--transition-normal);
  margin: var(--spacing-md) var(--spacing-md) 0 var(--spacing-md);
  display: flex;
  align-items: center;

  &:hover {
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
  }
}

.settingsDivider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-md) var(--spacing-md) 0 var(--spacing-md);
  opacity: 1;
  transition: opacity var(--transition-normal);
}

.collapseButton {
  padding: 8px;
  border-radius: var(--border-radius-md);
  font-weight: 400;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-normal);
  color: var(--text-secondary);
  position: absolute;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  right: var(--spacing-md);
  width: calc(100% - 2 * var(--spacing-md));

  &:hover {
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
  }

  .anticon {
    font-size: 16px;
  }
}

/* Collapsed sidebar footer styles */
.sidebarCollapsed {
  .sidebarFooter {
    padding: 0;
    align-items: center;
    min-height: 80px; /* 折叠状态下减少高度 */
    display: flex;
    justify-content: center;
  }

  .settingsDivider {
    opacity: 0; /* 折叠状态下隐藏分隔线 */
    height: 0; /* 完全隐藏 */
    margin: 0; /* 移除边距 */
  }

  .collapseButton {
    position: absolute;
    bottom: var(--spacing-md);
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    min-width: 40px;
    max-width: 40px;
    padding: 8px;
    right: auto; /* 重置right属性 */
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .highTechSidebar {
    background-color: var(--neutral-800);
    border-right-color: var(--neutral-700);
  }

  .sidebarFooter {
    border-top-color: var(--neutral-700);
  }

  .menu {
    :global {
      .ant-menu-item-selected {
        background-color: rgba(59, 130, 246, 0.2) !important;
      }

      .ant-menu-item:hover,
      .ant-menu-submenu-title:hover {
        background-color: var(--neutral-700) !important;
      }
    }
  }
}

@media (max-width: 1080px) {
  .highTechSidebar {
    .collapseButton {
      display: none;
    }
  }
}
