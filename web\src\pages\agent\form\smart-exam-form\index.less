@import '@/theme/high-tech-theme.less';

.fullWidthSelect {
  width: 100%;
}

.numberInput {
  width: 100%;
}

// 出题方式选择器样式
.questionMethodSelector {
  margin-top: var(--spacing-xs);

  .radioGroup {
    display: flex;
    gap: var(--spacing-lg);

    .radioOption {
      font-size: 14px;
      color: var(--text-primary);

      .ant-radio {
        .ant-radio-inner {
          border-color: var(--border-color);
          background-color: white;

          &::after {
            background-color: var(--primary-color);
          }
        }

        &.ant-radio-checked {
          .ant-radio-inner {
            border-color: var(--primary-color);
            background-color: white;
          }
        }
      }

      &:hover {
        .ant-radio {
          .ant-radio-inner {
            border-color: var(--primary-color);
          }
        }
      }
    }
  }
}

.questionTypeSection {
  margin-top: var(--spacing-md);
}

.questionTypeCard {
  .high-tech-card();

  :global(.ant-card-body) {
    padding: var(--spacing-md);
  }
}

// File upload section styling (same as extrapolate-form)
.fileUploadSection {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);

  .uploadBox {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    min-height: 120px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-subtle);
    transition: all var(--transition-normal);
    cursor: pointer;
    box-sizing: border-box;
    position: relative;

    &:hover {
      border-color: var(--primary-color);
      background-color: rgba(var(--primary-color-rgb), 0.02);
    }

    .uploadPlaceholder {
      text-align: center;

      .uploadIcon {
        width: 48px;
        height: 48px;
        margin: 0 auto var(--spacing-md);
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='none' stroke='%23a0aec0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4'%3E%3C/path%3E%3Cpolyline points='17 8 12 3 7 8'%3E%3C/polyline%3E%3Cline x1='12' y1='3' x2='12' y2='15'%3E%3C/line%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: center;
      }

      .dragText {
        font-size: 1rem;
        color: var(--text-secondary);
        margin-bottom: var(--spacing-xs);

        .browseText {
          color: var(--primary-color);
          cursor: pointer;
          font-weight: 500;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .supportedFormats {
        font-size: 0.8rem;
        color: var(--text-tertiary);
      }
    }

    .ant-upload-drag-container {
      width: 100%;
      height: 100%;
    }
  }

  // Upload.Dragger overrides to use our custom styling
  :global(.ant-upload-wrapper) {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    width: 100% !important;

    // Remove all default antd upload spacing
    &,
    & * {
      box-sizing: border-box !important;
    }

    &:hover {
      border: none !important;
      background: transparent !important;
    }

    :global(.ant-upload-drag-container) {
      padding: 0 !important;
      margin: 0 !important;
      width: 100% !important;
      height: 100% !important;
    }

    // Ensure the uploadBox fills the entire dragger area
    :global(.ant-upload-btn) {
      padding: 0 !important;
      width: 100% !important;
      height: 100% !important;
    }

    &.ant-upload-drag-hover {
      .uploadBox {
        border-color: var(--primary-color) !important;
        background-color: rgba(var(--primary-color-rgb), 0.02) !important;
      }
    }

    // Force the upload content to match our custom styling
    & > .ant-upload-btn {
      display: block !important;
      width: 100% !important;
      height: auto !important;
      padding: 0 !important;
      border: none !important;
      background: none !important;

      & > div {
        width: 100% !important;
      }
    }
  }
}

/* The high-tech styles for the form elements */
:global {
  .ant-input,
  .ant-input-number,
  .ant-select-selector,
  .ant-input-textarea {
    border-radius: var(--border-radius-md) !important;
    border: 1px solid var(--border-color) !important;
    background-color: white !important;
    transition: all var(--transition-normal);

    &:hover,
    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1) !important;
    }
  }

  textarea.text-base,
  input.text-base {
    background-color: white !important;
  }

  .ant-card-body {
    padding: var(--spacing-md);
  }

  .ant-form-item-label > label {
    font-weight: 500;
    color: var(--text-primary);
  }
}
