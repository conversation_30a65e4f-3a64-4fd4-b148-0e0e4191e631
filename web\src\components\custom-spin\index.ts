// 自定义Spin组件库主入口文件

// 导出所有Spin组件
export { default as CircleSpin } from './CircleSpin';
export { default as LeafSpin } from './LeafSpin';

// 导出类型定义
export type {
  CircleSpinProps,
  ExtendedSpinProps,
  LeafSpinProps,
  SpinProps,
  SpinType,
} from './types';

// 为了向后兼容，导出CircleSpin作为默认的CustomSpin
export { default as CustomSpin } from './CircleSpin';

// 默认导出CircleSpin
export { default } from './CircleSpin';

// 导出组件工厂函数（可选）
export const createSpinComponent = (type: 'circle' | 'leaf' = 'circle') => {
  switch (type) {
    case 'circle':
      return require('./CircleSpin').default;
    case 'leaf':
      return require('./LeafSpin').default;
    default:
      return require('./CircleSpin').default;
  }
};
