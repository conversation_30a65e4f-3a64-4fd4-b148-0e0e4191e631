import { MessageType } from '@/constants/chat';
import { useSetModalState } from '@/hooks/common-hooks';
import { IReference, IReferenceChunk } from '@/interfaces/database/chat';
import classNames from 'classnames';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';

import assistantAvatar from '@/assets/img/railway-assistant.png';
import { CircleSpin } from '@/components/custom-spin';
import {
  useFetchDocumentInfosByIds,
  useFetchDocumentThumbnailsByIds,
} from '@/hooks/document-hooks';
import { IRegenerateMessage, IRemoveMessageById } from '@/hooks/logic-hooks';
import { IMessage } from '@/pages/chat/interface';
import MarkdownContent from '@/pages/chat/markdown-content';
import { getExtension, isImage } from '@/utils/document-util';
import { Avatar, Button, Flex, List, Typography } from 'antd';
import FileIcon from '../file-icon';
import IndentedTreeModal from '../indented-tree/modal';
import NewDocumentLink from '../new-document-link';
import { SourceCardProps } from '../source-card/source-card';
import { useTheme } from '../theme-provider';
import { AssistantGroupButton, UserGroupButton } from './group-button';
import styles from './index.less';

const { Text } = Typography;

interface IProps extends Partial<IRemoveMessageById>, IRegenerateMessage {
  item: IMessage;
  reference: IReference;
  loading?: boolean;
  sendLoading?: boolean;
  visibleAvatar?: boolean;
  nickname?: string;
  avatar?: string;
  avatarDialog?: string | null;
  clickDocumentButton?: (documentId: string, chunk: IReferenceChunk) => void;
  index: number;
  showLikeButton?: boolean;
  showLoudspeaker?: boolean;
  sources?: SourceCardProps[];
}

const MessageItem = ({
  item,
  reference,
  loading = false,
  avatar,
  avatarDialog,
  sendLoading = false,
  clickDocumentButton,
  index,
  removeMessageById,
  regenerateMessage,
  showLikeButton = true,
  showLoudspeaker = true,
  visibleAvatar = true,
  sources = [],
}: IProps) => {
  const { theme } = useTheme();
  const isAssistant = item.role === MessageType.Assistant;
  const isUser = item.role === MessageType.User;
  const { data: documentList, setDocumentIds } = useFetchDocumentInfosByIds();
  const { data: documentThumbnails, setDocumentIds: setIds } =
    useFetchDocumentThumbnailsByIds();
  const { visible, hideModal, showModal } = useSetModalState();
  const [clickedDocumentId, setClickedDocumentId] = useState('');

  const referenceDocumentList = useMemo(() => {
    return reference?.doc_aggs ?? [];
  }, [reference?.doc_aggs]);

  // Convert reference docs to source cards when no explicit sources are provided
  const derivedSources = useMemo(() => {
    if (sources && sources.length > 0) {
      return sources;
    }

    return referenceDocumentList.map((doc) => ({
      title: doc.doc_name,
      source: 'Document',
      sourceUrl: doc.url || '',
      type: 'document' as const,
      description: `Document: ${doc.doc_name}`, // Use document name instead of non-existent snippet
    }));
  }, [referenceDocumentList, sources]);

  const handleUserDocumentClick = useCallback(
    (id: string) => () => {
      setClickedDocumentId(id);
      showModal();
    },
    [showModal],
  );

  const handleSourceClick = useCallback((source: SourceCardProps) => {
    // Handle source click based on type
    if (source.sourceUrl) {
      window.open(source.sourceUrl, '_blank');
    }
  }, []);

  const handleRegenerateMessage = useCallback(() => {
    regenerateMessage?.(item);
  }, [regenerateMessage, item]);

  useEffect(() => {
    const ids = item?.doc_ids ?? [];
    if (ids.length) {
      setDocumentIds(ids);
      const documentIds = ids.filter((x) => !(x in documentThumbnails));
      if (documentIds.length) {
        setIds(documentIds);
      }
    }
  }, [item.doc_ids, setDocumentIds, setIds, documentThumbnails]);

  // Render action buttons based on message type
  const renderActionButtons = () => {
    if (isAssistant) {
      return (
        index !== 0 && (
          <AssistantGroupButton
            messageId={item.id}
            content={item.content}
            prompt={item.prompt}
            showLikeButton={showLikeButton}
            audioBinary={item.audio_binary}
            showLoudspeaker={showLoudspeaker}
          />
        )
      );
    } else {
      return (
        <UserGroupButton
          content={item.content}
          messageId={item.id}
          removeMessageById={removeMessageById}
          regenerateMessage={regenerateMessage && handleRegenerateMessage}
          sendLoading={sendLoading}
        />
      );
    }
  };

  return (
    <div
      className={classNames(styles.messageItem, {
        [styles.messageItemLeft]: item.role === MessageType.Assistant,
        [styles.messageItemRight]: item.role === MessageType.User,
      })}
    >
      <section
        className={classNames(styles.messageItemSection, {
          [styles.messageItemSectionLeft]: item.role === MessageType.Assistant,
          [styles.messageItemSectionRight]: item.role === MessageType.User,
        })}
      >
        {/* 隐藏assistant header以保持简洁 */}

        <div
          className={classNames(styles.messageItemContent, {
            [styles.messageItemContentReverse]: item.role === MessageType.User,
          })}
        >
          {isAssistant && (
            <CircleSpin spinning={loading} size="small">
              <Avatar
                src={assistantAvatar}
                size={40}
                style={{ marginTop: index !== 0 ? 12 : -10 }}
              />
            </CircleSpin>
          )}

          <Flex
            vertical
            gap={8}
            flex={1}
            style={isUser ? { alignItems: 'flex-end' } : {}}
          >
            <div
              className={
                isAssistant
                  ? theme === 'dark'
                    ? styles.messageTextDark
                    : styles.messageText
                  : styles.messageUserText
              }
            >
              <MarkdownContent
                loading={loading}
                content={item.content}
                reference={reference}
                clickDocumentButton={clickDocumentButton}
              ></MarkdownContent>
            </div>

            {/* If assistant message, show action buttons below the message */}
            {isAssistant && (
              <div className={styles.assistantActionButtons}>
                {renderActionButtons()}
              </div>
            )}

            {/* Source cards section for assistant messages */}
            {/* {isAssistant && derivedSources.length > 0 && (
              <SourceSection
                sources={derivedSources}
                onSourceClick={handleSourceClick}
              />
            )} */}

            {/* Legacy document list - can be hidden if using source cards instead */}
            {isAssistant &&
              referenceDocumentList.length > 0 &&
              sources.length === 0 && (
                <List
                  className={styles.legacyDocList}
                  bordered
                  dataSource={referenceDocumentList}
                  renderItem={(item) => {
                    return (
                      <List.Item>
                        <Flex gap={'small'} align="center">
                          <FileIcon
                            id={item.doc_id}
                            name={item.doc_name}
                          ></FileIcon>

                          <NewDocumentLink
                            documentId={item.doc_id}
                            documentName={item.doc_name}
                            prefix="document"
                            link={item.url}
                          >
                            {item.doc_name}
                          </NewDocumentLink>
                        </Flex>
                      </List.Item>
                    );
                  }}
                />
              )}

            {isUser && documentList.length > 0 && (
              <List
                bordered
                style={{ textAlign: 'left', width: '100%' }}
                dataSource={documentList}
                renderItem={(item) => {
                  // TODO:
                  // const fileThumbnail =
                  //   documentThumbnails[item.id] || documentThumbnails[item.id];
                  const fileExtension = getExtension(item.name);
                  return (
                    <List.Item>
                      <Flex gap={'small'} align="center">
                        <FileIcon id={item.id} name={item.name}></FileIcon>

                        {isImage(fileExtension) ? (
                          <NewDocumentLink
                            documentId={item.id}
                            documentName={item.name}
                            prefix="document"
                          >
                            {item.name}
                          </NewDocumentLink>
                        ) : (
                          <Button
                            type={'text'}
                            onClick={handleUserDocumentClick(item.id)}
                          >
                            <Text
                              style={{ maxWidth: '40vw' }}
                              ellipsis={{ tooltip: item.name }}
                            >
                              {item.name}
                            </Text>
                          </Button>
                        )}
                      </Flex>
                    </List.Item>
                  );
                }}
              />
            )}
          </Flex>
        </div>

        {/* If user message, show action buttons on the left side */}
        {isUser && (
          <div className={styles.userActionButtons}>
            {renderActionButtons()}
          </div>
        )}
      </section>

      {visible && (
        <IndentedTreeModal
          visible={visible}
          hideModal={hideModal}
          documentId={clickedDocumentId}
        ></IndentedTreeModal>
      )}
    </div>
  );
};

export default memo(MessageItem);
