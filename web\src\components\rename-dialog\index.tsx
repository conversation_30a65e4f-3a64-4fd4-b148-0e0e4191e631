import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { IModalProps } from '@/interfaces/common';
import { TagRenameId } from '@/pages/add-knowledge/constant';
import '@/pages/drive/modal-styles.less';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { ButtonLoading } from '../ui/button';
import { RenameForm } from './rename-form';

export function RenameDialog({
  hideModal,
  initialName,
  onOk,
  loading,
  title,
}: IModalProps<any> & { initialName?: string; title?: ReactNode }) {
  const { t } = useTranslation();

  return (
    <Dialog open onOpenChange={hideModal}>
      <DialogContent className="ht-modal-content">
        <DialogHeader className="ht-modal-header">
          <DialogTitle className="ht-modal-title">
            {title || t('common.rename')}
          </DialogTitle>
        </DialogHeader>
        <div className="ht-modal-body">
          <RenameForm
            initialName={initialName}
            hideModal={hideModal}
            onOk={onOk}
          ></RenameForm>
        </div>
        <DialogFooter className="ht-modal-footer">
          <ButtonLoading
            type="submit"
            form={TagRenameId}
            loading={loading}
            className="ht-modal-btn primary"
          >
            {t('common.save')}
          </ButtonLoading>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
