.groupMemberWrapper {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .headerLeft {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .headerRight {
      display: flex;
      align-items: center;
    }
  }

  .mainContent {
    display: flex;
    gap: 24px;
    height: calc(100vh - 200px);

    .leftPanel {
      width: 280px;
      flex-shrink: 0;

      .addDepartmentCard {
        margin-bottom: 16px;

        :global(.ant-card-head) {
          padding: 12px 16px;
          min-height: auto;
        }

        :global(.ant-card-body) {
          padding: 12px;
        }
      }

      .treeContainer {
        background: #fff;
        border-radius: 8px;
        padding: 16px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        height: calc(100% - 0px);
        overflow-y: auto;

        .departmentTree {
          :global {
            .ant-tree-treenode {
              padding: 4px 0;
            }

            .ant-tree-node-content-wrapper {
              padding: 4px 8px;
              border-radius: 4px;
              transition: all 0.2s;

              &:hover {
                background-color: #f5f5f5;
              }
            }

            .ant-tree-node-content-wrapper-selected {
              background-color: #e6f7ff !important;
              color: #1890ff;
            }

            .ant-tree-iconEle {
              margin-right: 8px;
            }

            .ant-tree-title {
              font-size: 14px;
            }
          }
        }
      }
    }

    .rightPanel {
      flex: 1;
      display: flex;
      flex-direction: column;

      :global(.ant-card) {
        height: 100%;
        display: flex;
        flex-direction: column;

        .ant-card-body {
          flex: 1;
          display: flex;
          flex-direction: column;
          padding: 24px;
        }
      }

      .tableHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;

        .tableFilters {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .tableActions {
          display: flex;
          align-items: center;
        }
      }

      .memberTable {
        flex: 1;

        :global {
          .ant-table-wrapper {
            height: 100%;
            display: flex;
            flex-direction: column;
          }

          .ant-table {
            flex: 1;
          }

          .ant-table-tbody > tr > td {
            padding: 12px 16px;
          }

          .ant-table-thead > tr > th {
            background: #fafafa;
            font-weight: 600;
            color: #262626;
          }

          .ant-avatar {
            margin-right: 8px;
          }
        }
      }

      .pagination {
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .groupMemberWrapper {
    .mainContent {
      flex-direction: column;
      height: auto;

      .leftPanel {
        width: 100%;
        margin-bottom: 24px;

        .treeContainer {
          height: 300px;
        }
      }

      .rightPanel {
        :global(.ant-card) {
          height: auto;
        }
      }
    }
  }
}
