@import '../../theme/vars';

.loginPage {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #53b1fd 0%, #1570ef 100%);
  font-family: 'SF Pro SC', 'SF Pro Text', 'SF Pro Icons', 'PingFang SC',
    'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  background-size: cover;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('@/assets/img/login-background.jpg');
    background-size: cover;
    background-position: center;
    opacity: 1;
    z-index: 0;
  }

  .loginContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    z-index: 1;
    margin-top: -8%;
  }

  .logoContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
  }

  .logo {
    width: 96px;
    margin-bottom: 48px;
  }

  .platformTitle {
    color: white;
    font-size: 56px;
    font-weight: bold;
    text-align: center;
    margin: 0;
  }

  .formContainer {
    background: rgba(255, 255, 255, 0.85);
    border-radius: 0;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    padding: 32px;
    width: 900px;
    background-image: url('@/assets/img/login-background.jpg');
    background-size: auto;
    background-position: 46.5% 58.5%;
    background-repeat: no-repeat;
    margin-top: 8px;

    form {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 0;
      height: 48px;
      align-items: center;
      flex-wrap: nowrap;
      gap: 16px;

      > :global(.ant-form-item) {
        flex: 1;
        min-width: 180px;
        margin-right: 0 !important;
        margin-bottom: 0 !important;

        &:nth-child(1),
        &:nth-child(2) {
          flex: 2;
          min-width: 220px;
        }

        &:last-child {
          flex: 1;
          min-width: 120px;
          max-width: 150px;
        }
      }
    }

    .loginFormItem {
      border-radius: 0;
      :global(.ant-input-affix-wrapper-lg) {
        border-radius: 0;
        line-height: 32px;
        margin-bottom: 0;
      }
    }

    .loginButtonItem {
      margin-bottom: 0 !important;
    }
  }

  .inputIcon {
    color: #999;
    margin-right: 8px;
  }

  .loginOptions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;

    .rememberMe {
      margin-bottom: 0;
    }

    .forgotPassword {
      color: #fff;
      cursor: pointer;
      width: 40px;
      font-size: 16px;
      line-height: 1.33;
      text-decoration: underline;
    }
  }

  .loginButton {
    height: 48px;
    font-size: 20px;
    background: #11b2e8;
    border-color: #22c3f9;
    box-shadow: 0 0 0 2px rgba(200, 200, 200, 0.1);
    border-radius: 0;
    width: 100%;
    min-width: 120px;
  }

  .switchMode {
    text-align: center;
    color: #666;

    button {
      padding: 0 4px;
    }
  }

  /* Large screens */
  @media screen and (min-width: 1200px) {
    .formContainer {
      width: 1000px;

      form {
        > :global(.ant-form-item) {
          &:nth-child(1),
          &:nth-child(2) {
            min-width: 250px;
          }

          &:last-child {
            min-width: 140px;
            max-width: 180px;
          }
        }
      }
    }
  }

  /* Responsive adjustments */
  @media screen and (max-width: 840px) {
    .loginContainer {
      margin-top: 0;
      padding: 20px;
    }

    .logoContainer {
      margin-bottom: 30px;
    }

    .logo {
      width: 64px;
      margin-bottom: 24px;
    }

    .platformTitle {
      font-size: 24px;
      line-height: 1.2;
    }

    .formContainer {
      width: 100%;
      max-width: 400px;
      padding: 16px;
      background-position: center;
      margin-top: 0;

      form {
        flex-direction: column;
        height: auto;
        gap: 16px;

        > :global(.ant-form-item) {
          min-width: unset;
          width: 100%;
          margin-right: 0 !important;
          margin-bottom: 0;
        }
      }
    }

    .loginFormItem {
      margin-bottom: 16px !important;
    }

    .loginButtonItem {
      margin-bottom: 0 !important;
      margin-top: 8px;
    }

    .loginButton {
      width: 100%;
      margin-right: 0;
    }

    .loginOptions {
      margin-top: 16px;
      justify-content: center;

      .forgotPassword {
        width: auto;
      }
    }
  }

  /* Extra small screens */
  @media screen and (max-width: 480px) {
    .loginContainer {
      padding: 16px;
    }

    .logoContainer {
      margin-bottom: 20px;
    }

    .logo {
      width: 48px;
      margin-bottom: 16px;
    }

    .platformTitle {
      font-size: 20px;
      padding: 0 10px;
    }

    .formContainer {
      padding: 20px 16px;
      max-width: 320px;
    }

    .loginOptions {
      margin-top: 12px;
    }
  }
}
