from __future__ import annotations

"""AI Question business logic service (v2).

This first draft focuses on the overall control-flow. File-upload and KB
embedding reuse SmartExam patterns but some implementation details are kept
minimal – they can be iterated once other building blocks are ready.
"""

import json
import logging
import time
from typing import Dict, Generator, Optional, Tuple

from flask import Request, Response

from api.utils.api_utils import (
    get_data_error_result,
    get_data_from_request,
    get_json_result,
    server_error_response,
)
from api.utils.tenant_utils import get_tenant_with_fallback
from agent.ai_question import AIQuestionParam, AIQuestionContext
from agent.ai_question.sample_parser import SampleFileParser
from agent.ai_question.generator import AIQuestionGenerator
from agent.common_exam.retriever import Retriever
from agent.common_exam.streamer import Streamer
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.file_service import FileService
from api.db.services.document_service import DocumentService, _get_or_create_kb_for_user
from api.db.services.file2document_service import File2DocumentService
from api.db.services.llm_service import LLMBundle
from api.db import LLMType
from agent.canvas import Canvas
from api.services.lesson_plan_v2_service import SSEStreamBuffer, MinimalCanvas
from api import settings
from agent.common_exam import QuestionMethod, QuestionDistribution
from agent.common_exam import Difficulty


DEFAULT_EXAM_KB_NAME = "tenant-exam-question"


class _UploadContext:
    """Information about an uploaded sample file and its KB/doc IDs."""

    def __init__(self, kb_id: str, doc_id: str, cleanup_job_id: Optional[str]):
        self.kb_id = kb_id
        self.doc_id = doc_id
        self.cleanup_job_id = cleanup_job_id


# --------------------------------------------------------------------------------------
# Core Service
# --------------------------------------------------------------------------------------


class AIQuestionService:
    """Business logic encapsulation for /ai-question API endpoints."""

    # ------------------------------------------------------------------
    # Public entry – start generation
    # ------------------------------------------------------------------

    @staticmethod
    def build_start_response(req: Request, user) -> Response:
        """Handle POST /ai-question/start and return SSE streaming Response."""
        try:
            data, files = AIQuestionService._extract_request_data(req)
            logging.info(
                "[AIQuestion] Received data: %s, files: %s",
                list(data.keys()),
                list(files.keys()) if files else [],
            )

            # Pydantic validation
            try:
                param = AIQuestionParam(**data)
            except Exception as ve:
                return get_json_result(data={}, code=400, message=str(ve))

            # File upload – optional but recommended
            sample_file = files.get("sample_file") if files else None
            sample_content = ""
            upload_ctx: Optional[_UploadContext] = None
            if sample_file and sample_file.filename:
                ok, sample_content, upload_ctx = AIQuestionService._handle_file_upload(
                    user.id, sample_file
                )
                if not ok:
                    return get_json_result(data={}, code=500, message="文件上传解析失败")
            else:
                # manual content fallback (前端可传 sample_content)
                sample_content = data.get("sample_content", "")

            # Tenant & Canvas
            success, tenant = get_tenant_with_fallback(user.id)
            if not success or not tenant:
                return get_data_error_result(message="Unable to determine tenant for user")
            tenant_id = tenant.id
            canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user.id)

            # Build streaming generator
            stream_gen = AIQuestionService._generate_ai_question_stream(
                canvas,
                param,
                sample_content,
                upload_ctx,
                req.headers.get("Last-Event-ID", ""),
            )
            resp = Response(stream_gen, mimetype="text/event-stream")
            resp.headers["Cache-Control"] = "no-cache"
            resp.headers["X-Accel-Buffering"] = "no"
            return resp
        except Exception as e:
            logging.error("[AIQuestion] Fatal error: %s", e, exc_info=True)
            return server_error_response(e)

    # ------------------------------------------------------------------
    # Internals
    # ------------------------------------------------------------------

    @staticmethod
    def _extract_request_data(req: Request) -> Tuple[Dict, Dict]:
        if req.content_type and "application/json" in (req.content_type or ""):
            data = get_data_from_request(req)
            files = {}
        else:
            data = req.form.to_dict()
            files = req.files
        return data, files

    # ----------------------------------------------
    # File upload & async embedding (same pattern as smart-exam)
    # ----------------------------------------------

    @staticmethod
    def _handle_file_upload(user_id: str, sample_file) -> Tuple[bool, str, Optional[_UploadContext]]:
        try:
            logging.info(
                "[AIQuestion] Upload file for user %s, filename: %s", user_id, sample_file.filename)
            kb_id = _get_or_create_kb_for_user(user_id)
            ok, kb_obj = KnowledgebaseService.get_by_id(kb_id)
            if not ok or not kb_obj:
                raise LookupError("Knowledge base not found for upload")

            err_list, files = FileService.upload_document(
                kb_obj, [sample_file], user_id)
            if err_list:
                raise Exception("; ".join(err_list))
            if not files:
                raise Exception("no file uploaded")
            doc_id = files[0][0]["id"]

            # read content for prompt
            sample_file.seek(0)
            try:
                file_content = sample_file.read().decode("utf-8", errors="ignore")
            except Exception:
                sample_file.seek(0)
                file_content = sample_file.read().decode("gbk", errors="ignore")
            logging.info(
                "[AIQuestion] Uploaded sample file content length %d", len(file_content))

            # async embedding – fire & forget (same as SmartExam)
            try:
                info = {"run": 1, "progress": 0, "progress_msg": "",
                        "chunk_num": 0, "token_num": 0}
                DocumentService.update_by_id(doc_id, info)
                bucket, name = File2DocumentService.get_storage_address(
                    doc_id=doc_id)
                from api.db.services.task_service import queue_tasks
                queue_tasks(files[0][0], bucket, name, 0)
            except Exception as e:
                logging.warning(
                    "[AIQuestion] start async embedding failed: %s", e)

            # schedule cleanup (2h)
            cleanup_job_id = None
            try:
                from api.tasks.lesson_plan_tasks import cleanup_kb_doc
                cleanup_async = cleanup_kb_doc.apply_async(
                    args=[None, kb_id, [doc_id]], countdown=7200)
                cleanup_job_id = cleanup_async.id
            except Exception as e:
                logging.warning("[AIQuestion] schedule cleanup failed: %s", e)

            return True, file_content, _UploadContext(kb_id, doc_id, cleanup_job_id)
        except Exception as e:
            logging.error("[AIQuestion] file upload failed: %s",
                          e, exc_info=True)
            return False, "", None

    # ----------------------------------------------
    # Streaming generator
    # ----------------------------------------------

    @staticmethod
    def _generate_ai_question_stream(
        canvas: Canvas,
        param: AIQuestionParam,
        sample_content: str,
        upload_ctx: Optional[_UploadContext],
        last_event_header: str,
    ) -> Generator[str, None, None]:
        from api.db.services.llm_service import LLMBundle
        from api.db import LLMType

        # parse last event id for resume
        try:
            last_event_id = int(last_event_header) if last_event_header else -1
        except ValueError:
            last_event_id = -1

        tenant_id = canvas.get_tenant_id()

        # Normalize enum fields (Pydantic uses use_enum_values=True -> str)
        try:
            # convert str -> enum if necessary
            qmethod = QuestionMethod(param.question_method)
        except Exception:
            qmethod = QuestionMethod.RANDOM

        try:
            diff_enum = Difficulty(
                param.difficulty) if param.difficulty else Difficulty.MEDIUM
        except Exception:
            diff_enum = Difficulty.MEDIUM

        # Build question distribution based on generation method
        if qmethod == QuestionMethod.RANDOM:
            dist = QuestionDistribution()  # generator decides counts
        else:
            dist = QuestionDistribution(
                single_choice=param.single_choice_count,
                multiple_choice=param.multiple_choice_count,
                fill_blank=param.fill_blank_count,
                true_false=param.true_false_count,
                short_answer=param.short_answer_count,
                ordering=param.ordering_count,
            )

        ctx = AIQuestionContext(
            knowledge_points=param.knowledge_points,
            objective=param.objective,
            exam_scene=param.exam_scene,
            difficulty=diff_enum,
            question_method=qmethod,
            distribution=dist,
        )
        ctx.sample_content = sample_content
        ctx.tenant_id = tenant_id
        ctx.user_id = canvas.get_user_id()

        # TODO: template retrieval & KB retrieval – omitted in first draft

        # LLM bundle
        llm_bundle = LLMBundle(tenant_id, LLMType.CHAT, param.llm_id)
        generator = AIQuestionGenerator(llm_bundle, stream=True)
        streamer = SSEStreamBuffer(send_interval=1.0)

        def _stream():
            yield "event:init\ndata:{}\n\n"
            for chunk in generator.stream_generate(ctx):
                ev = streamer.add_chunk(chunk, last_event_id)
                if ev:
                    yield f"id:{ev['id']}\nevent:{ev['event']}\ndata:{ev['data']}\n\n"
            ev = streamer.flush()
            if ev:
                yield f"id:{ev['id']}\nevent:{ev['event']}\ndata:{ev['data']}\n\n"
            yield "id:-2\nevent:done\ndata:{}\n\n"
        return _stream()
