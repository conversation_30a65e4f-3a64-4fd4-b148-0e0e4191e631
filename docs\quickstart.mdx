---
sidebar_position: 0
slug: /
---

# Get started
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';
import APITable from '@site/src/components/APITable';

RAGFlow is an open-source RAG (Retrieval-Augmented Generation) engine based on deep document understanding. When integrated with LLMs, it is capable of providing truthful question-answering capabilities, backed by well-founded citations from various complex formatted data.

This quick start guide describes a general process from:

- Starting up a local RAGFlow server, 
- Creating a knowledge base, 
- Intervening with file parsing, to 
- Establishing an AI chat based on your datasets.

:::danger IMPORTANT
We officially support x86 CPU and Nvidia GPU, and this document offers instructions on deploying RAGFlow using Docker on x86 platforms. While we also test RAGFlow on ARM64 platforms, we do not maintain RAGFlow Docker images for ARM.

If you are on an ARM platform, follow [this guide](./develop/build_docker_image.mdx) to build a RAGFlow Docker image.
:::

## Prerequisites

- CPU &ge; 4 cores (x86);
- RAM &ge; 16 GB;
- Disk &ge; 50 GB;
- Docker &ge; 24.0.0 & Docker Compose &ge; v2.26.1.

## Start up the server

This section provides instructions on setting up the RAGFlow server on Linux. If you are on a different operating system, no worries. Most steps are alike.

<details>
  <summary>1. Ensure <code>vm.max_map_count</code> &ge; 262144:</summary>

   `vm.max_map_count`. This value sets the maximum number of memory map areas a process may have. Its default value is 65530. While most applications require fewer than a thousand maps, reducing this value can result in abnormal behaviors, and the system will throw out-of-memory errors when a process reaches the limitation.

   RAGFlow v0.18.0 uses Elasticsearch or [Infinity](https://github.com/infiniflow/infinity) for multiple recall. Setting the value of `vm.max_map_count` correctly is crucial to the proper functioning of the Elasticsearch component.

<Tabs
  defaultValue="linux"
  values={[
    {label: 'Linux', value: 'linux'},
    {label: 'macOS', value: 'macos'},
    {label: 'Windows', value: 'windows'},
  ]}>
  <TabItem value="linux">
   1.1. Check the value of `vm.max_map_count`:

   ```bash
   $ sysctl vm.max_map_count
   ```

   1.2. Reset `vm.max_map_count` to a value at least 262144 if it is not.

   ```bash
   $ sudo sysctl -w vm.max_map_count=262144
   ```

   :::caution WARNING
   This change will be reset after a system reboot. If you forget to update the value the next time you start up the server, you may get a `Can't connect to ES cluster` exception.
   :::
   
   1.3. To ensure your change remains permanent, add or update the `vm.max_map_count` value in **/etc/sysctl.conf** accordingly:

   ```bash
   vm.max_map_count=262144
   ```
  </TabItem>
  <TabItem value="macos">
   If you are on macOS with Docker Desktop, run the following command to update `vm.max_map_count`:

   ```bash
   docker run --rm --privileged --pid=host alpine sysctl -w vm.max_map_count=262144
   ```

   :::caution WARNING
   This change will be reset after a system reboot. If you forget to update the value the next time you start up the server, you may get a `Can't connect to ES cluster` exception.
   :::

   To make your change persistent, create a file with proper settings:

   1.1. Create a file:

   ```shell
   sudo nano /Library/LaunchDaemons/com.user.vmmaxmap.plist
   ```

   1.2. Open the file:

   ```shell
   sudo launchctl load /Library/LaunchDaemons/com.user.vmmaxmap.plist
   ```

   1.3. Add settings:

   ```xml
   <?xml version="1.0" encoding="UTF-8"?>
   <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
   <plist version="1.0">
   <dict>
       <key>Label</key>
       <string>com.user.vmmaxmap</string>
       <key>ProgramArguments</key>
       <array>
           <string>/usr/sbin/sysctl</string>
           <string>-w</string>
           <string>vm.max_map_count=262144</string>
       </array>
       <key>RunAtLoad</key>
       <true/>
   </dict>
   </plist>
   ```

   1.4. After saving the file, load the new daemon:

   ```shell
   sudo launchctl load /Library/LaunchDaemons/com.user.vmmaxmap.plist
   ```

   :::note
   If the above steps do not work, consider using [this workaround](https://github.com/docker/for-mac/issues/7047#issuecomment-1791912053), which employs a container and does not require manual editing of the macOS settings.
   :::

  </TabItem>
  <TabItem value="windows">

   #### If you are on Windows with Docker Desktop, then you *must* use docker-machine to set `vm.max_map_count`:

   ```bash
   $ docker-machine ssh
   $ sudo sysctl -w vm.max_map_count=262144
   ```
   #### If you are on Windows with Docker Desktop WSL 2 backend, then use docker-desktop to set `vm.max_map_count`:

   1.1. Run the following in WSL: 
   ```bash
   $ wsl -d docker-desktop -u root
   $ sysctl -w vm.max_map_count=262144
   ```

   :::caution WARNING
   This change will be reset after you restart Docker. If you forget to update the value the next time you start up the server, you may get a `Can't connect to ES cluster` exception.
   :::

   1.2. If you do not wish to have to run those commands each time you restart Docker, you can update your `%USERPROFILE%.wslconfig` as follows to keep your change permanent and globally for all WSL distributions:

   ```bash
   [wsl2]
   kernelCommandLine = "sysctl.vm.max_map_count=262144"
   ```
   *This causes all WSL2 virtual machines to have that setting assigned when they start.*

   :::note
   If you are on Windows 11 or Windows 10 version 22H2, and have installed the Microsoft Store version of WSL, you can also update the **/etc/sysctl.conf** within the docker-desktop WSL distribution to keep your change permanent:

   ```bash
   $ wsl -d docker-desktop -u root
   $ vi /etc/sysctl.conf
   ```

   ```bash
   # Append a line, which reads: 
   vm.max_map_count = 262144
   ```
   :::
  </TabItem>
</Tabs>

</details>

2. Clone the repo:

   ```bash
   $ git clone https://github.com/infiniflow/ragflow.git
   $ cd ragflow/docker
   $ git checkout -f v0.18.0
   ```

3. Use the pre-built Docker images and start up the server:

   :::tip NOTE
   The command below downloads the `v0.18.0-slim` edition of the RAGFlow Docker image. Refer to the following table for descriptions of different RAGFlow editions. To download a RAGFlow edition different from `v0.18.0-slim`, update the `RAGFLOW_IMAGE` variable accordingly in **docker/.env** before using `docker compose` to start the server. For example: set `RAGFLOW_IMAGE=infiniflow/ragflow:v0.18.0` for the full edition `v0.18.0`.
   :::

   ```bash
   # Use CPU for embedding and DeepDoc tasks:
   $ docker compose -f docker-compose.yml up -d

   # To use GPU to accelerate embedding and DeepDoc tasks:
   # docker compose -f docker-compose-gpu.yml up -d
   ```

```mdx-code-block
<APITable>
```

| RAGFlow image tag   | Image size (GB) | Has embedding models and Python packages? | Stable?                  |
| ------------------- | --------------- | ----------------------------------------- | ------------------------ |
| `v0.18.0`           | &approx;9       | :heavy_check_mark:                        | Stable release           |
| `v0.18.0-slim`      | &approx;2       | ❌                                        | Stable release           |
| `nightly`           | &approx;9       | :heavy_check_mark:                        | *Unstable* nightly build |
| `nightly-slim`      | &approx;2       | ❌                                        | *Unstable* nightly build |

```mdx-code-block
</APITable>
```

4. Check the server status after having the server up and running:

   ```bash
   $ docker logs -f ragflow-server
   ```

   _The following output confirms a successful launch of the system:_

   ```bash
        ____   ___    ______ ______ __
       / __ \ /   |  / ____// ____// /____  _      __
      / /_/ // /| | / / __ / /_   / // __ \| | /| / /
     / _, _// ___ |/ /_/ // __/  / // /_/ /| |/ |/ /
    /_/ |_|/_/  |_|\____//_/    /_/ \____/ |__/|__/
   
    * Running on all addresses (0.0.0.0)
   ```

:::danger IMPORTANT
If you skip this confirmation step and directly log in to RAGFlow, your browser may prompt a `network anomaly` error because, at that moment, your RAGFlow may not be fully initialized.
:::  

5. In your web browser, enter the IP address of your server and log in to RAGFlow.

:::caution WARNING
With the default settings, you only need to enter `http://IP_OF_YOUR_MACHINE` (**sans** port number) as the default HTTP serving port `80` can be omitted when using the default configurations.
:::

## Configure LLMs

RAGFlow is a RAG engine and needs to work with an LLM to offer grounded, hallucination-free question-answering capabilities. RAGFlow supports most mainstream LLMs. For a complete list of supported models, please refer to [Supported Models](./references/supported_models.mdx).

:::note 
RAGFlow also supports deploying LLMs locally using Ollama, Xinference, or LocalAI, but this part is not covered in this quick start guide. 
:::

To add and configure an LLM: 

1. Click on your logo on the top right of the page **>** **Model providers**:

   ![add llm](https://github.com/infiniflow/ragflow/assets/********/********-028b-4b3d-add9-5c5a6e626814)

   > Each RAGFlow account is able to use **text-embedding-v2** for free, an embedding model of Tongyi-Qianwen. This is why you can see Tongyi-Qianwen in the **Added models** list. And you may need to update your Tongyi-Qianwen API key at a later point.

2. Click on the desired LLM and update the API key accordingly (DeepSeek-V2 in this case):

   ![update api key](https://github.com/infiniflow/ragflow/assets/********/4e5e13ef-a98d-42e6-bcb1-0c6045fc1666)

   *Your added models appear as follows:* 

   ![added available models](https://github.com/infiniflow/ragflow/assets/********/d08b80e4-f921-480a-b41d-11832489c916)

3. Click **System Model Settings** to select the default models: 

   - Chat model, 
   - Embedding model, 
   - Image-to-text model. 

   ![system model settings](https://github.com/infiniflow/ragflow/assets/********/cdcc1da5-4494-44cd-ad5b-1222ed6acc3f)

> Some models, such as the image-to-text model **qwen-vl-max**, are subsidiary to a specific LLM. And you may need to update your API key to access these models. 

## Create your first knowledge base

You are allowed to upload files to a knowledge base in RAGFlow and parse them into datasets. A knowledge base is virtually a collection of datasets. Question answering in RAGFlow can be based on a particular knowledge base or multiple knowledge bases. File formats that RAGFlow supports include documents (PDF, DOC, DOCX, TXT, MD), tables (CSV, XLSX, XLS), pictures (JPEG, JPG, PNG, TIF, GIF), and slides (PPT, PPTX).

To create your first knowledge base:

1. Click the **Knowledge Base** tab in the top middle of the page **>** **Create knowledge base**.

2. Input the name of your knowledge base and click **OK** to confirm your changes.

   _You are taken to the **Configuration** page of your knowledge base._

   ![knowledge base configuration](https://github.com/infiniflow/ragflow/assets/********/384c671a-8b9c-468c-b1c9-1401128a9b65)

3. RAGFlow offers multiple chunk templates that cater to different document layouts and file formats. Select the embedding model and chunking method (template) for your knowledge base. 

:::danger IMPORTANT 
Once you have selected an embedding model and used it to parse a file, you are no longer allowed to change it. The obvious reason is that we must ensure that all files in a specific knowledge base are parsed using the *same* embedding model (ensure that they are being compared in the same embedding space). 
:::

   _You are taken to the **Dataset** page of your knowledge base._

4. Click **+ Add file** **>** **Local files** to start uploading a particular file to the knowledge base. 

5. In the uploaded file entry, click the play button to start file parsing:

   ![file parsing](https://github.com/infiniflow/ragflow/assets/********/19f273fa-0ab0-435e-bdf4-a47fb080a078)

   _When the file parsing completes, its parsing status changes to **SUCCESS**._

:::caution NOTE 
- If your file parsing gets stuck at below 1%, see [this FAQ](./faq.mdx#why-does-my-document-parsing-stall-at-under-one-percent).
- If your file parsing gets stuck at near completion, see [this FAQ](./faq.mdx#why-does-my-pdf-parsing-stall-near-completion-while-the-log-does-not-show-any-error)
:::

## Intervene with file parsing

RAGFlow features visibility and explainability, allowing you to view the chunking results and intervene where necessary. To do so:

1. Click on the file that completes file parsing to view the chunking results:

   _You are taken to the **Chunk** page:_

   ![chunks](https://github.com/infiniflow/ragflow/assets/********/0547fd0e-e71b-41f8-8e0e-31649c85fd3d)

2. Hover over each snapshot for a quick view of each chunk.

3. Double click the chunked texts to add keywords or make *manual* changes where necessary:

   ![update chunk](https://github.com/infiniflow/ragflow/assets/********/1d84b408-4e9f-46fd-9413-8c1059bf9c76)

:::caution NOTE
You can add keywords to a file chunk to improve its ranking for queries containing those keywords. This action increases its keyword weight and can improve its position in search list.
:::

4. In Retrieval testing, ask a quick question in **Test text** to double check if your configurations work:

   _As you can tell from the following, RAGFlow responds with truthful citations._

   ![retrieval test](https://github.com/infiniflow/ragflow/assets/********/c03f06f6-f41f-4b20-a97e-ae405d3a950c)

## Set up an AI chat

Conversations in RAGFlow are based on a particular knowledge base or multiple knowledge bases. Once you have created your knowledge base and finished file parsing, you can go ahead and start an AI conversation.

1. Click the **Chat** tab in the middle top of the mage **>** **Create an assistant** to show the **Chat Configuration** dialogue *of your next dialogue*.
   > RAGFlow offer the flexibility of choosing a different chat model for each dialogue, while allowing you to set the default models in **System Model Settings**.

2. Update **Assistant settings**:

   - Name your assistant and specify your knowledge bases.
   - **Empty response**:
     - If you wish to *confine* RAGFlow's answers to your knowledge bases, leave a response here. Then when it doesn't retrieve an answer, it *uniformly* responds with what you set here.
     - If you wish RAGFlow to *improvise* when it doesn't retrieve an answer from your knowledge bases, leave it blank, which may give rise to hallucinations.

3. Update **Prompt engine** or leave it as is for the beginning.

4. Update **Model settings**.

5. Now, let's start the show:

   ![question1](https://github.com/infiniflow/ragflow/assets/********/bb72dd67-b35e-4b2a-87e9-4e4edbd6e677)

   ![question2](https://github.com/infiniflow/ragflow/assets/********/7cc585ae-88d0-4aa2-817d-0370b2ad7230)


:::tip NOTE
RAGFlow also offers HTTP and Python APIs for you to integrate RAGFlow's capabilities into your applications. Read the following documents for more information:

- [Acquire a RAGFlow API key](./develop/acquire_ragflow_api_key.md)
- [HTTP API reference](./references/http_api_reference.md)
- [Python API reference](./references/python_api_reference.md)
:::
