import { GenerateCube } from '@/components/generate-cube';
import HightLightMarkdown from '@/components/highlight-markdown';
import { useFetchLlmList } from '@/hooks/llm-hooks';
import { getV3SystemConfig } from '@/services/agent_service';
import { exportMarkdownToDocx } from '@/utils/markdown-export';
import { markdownToExcelV2 } from '@/utils/markdown-to-excel-utils';
import { convertAndSendQuestions } from '@/utils/question-util';
import {
  ArrowLeftOutlined,
  CloudUploadOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FormatPainterOutlined,
} from '@ant-design/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Col, Row, Typography, message } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { flushSync } from 'react-dom';
import { useForm } from 'react-hook-form';
import { history } from 'umi';
import { z } from 'zod';
import AIQuestionForm from '../form/ai-question-form';
import { INextOperatorForm } from '../interface';
import styles from './index.less';

const { Title, Paragraph } = Typography;

// Define the form schema
const formSchema = z.object({
  exam_scene: z.string().min(1, { message: '场景不能为空' }),
  knowledge_points: z.string().min(1, { message: '内容及知识点不能为空' }),
  objective: z.string().min(1, { message: '出题目标不能为空' }),
  question_generation_type: z.enum(['random', 'byType']).default('random'),
  random_questions_enabled: z.boolean().default(true),
  question_count: z.number().min(1).max(100).default(10),
  by_question_type_enabled: z.boolean().default(false),
  single_choice_count: z.number().min(0).max(50).default(0),
  multiple_choice_count: z.number().min(0).max(50).default(0),
  fill_blank_count: z.number().min(0).max(50).default(0),
  true_false_count: z.number().min(0).max(50).default(0),
  short_answer_count: z.number().min(0).max(50).default(0),
  ordering_count: z.number().min(0).max(50).default(0),
  other_requirements: z.string().optional(),
  llm_id: z.string().min(1, { message: '请选择一个模型' }),
  exam_kb_id: z.string().optional(),
  kb_ids: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

const AIQuestionPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [generatedQuestions, setGeneratedQuestions] = useState<string | null>(
    null,
  );
  // Only need to cache the markdown data now since backend returns markdown directly
  const [markdownData, setMarkdownData] = useState<string | null>(null);
  const [exportingToV3System, setExportingToV3System] = useState(false);
  const { list: llmList } = useFetchLlmList();
  const [defaultLlmId, setDefaultLlmId] = useState<string>(
    'deepseek-r1:32b@Ollama',
  );

  // ---- Streaming helpers ----
  const thinkModeRef = useRef(false);

  // Get the first available CHAT model as default
  useEffect(() => {
    if (llmList && llmList.length > 0) {
      const chatModels = llmList.filter((llm: any) => llm.llm_type === 'CHAT');
      if (chatModels.length > 0) {
        setDefaultLlmId(chatModels[0].id.toString());
      }
    }
  }, [llmList]);

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      exam_scene: '',
      knowledge_points: '',
      objective: '',
      question_generation_type: 'random',
      random_questions_enabled: true,
      question_count: 10,
      by_question_type_enabled: false,
      single_choice_count: 0,
      multiple_choice_count: 0,
      fill_blank_count: 0,
      true_false_count: 0,
      short_answer_count: 0,
      ordering_count: 0,
      other_requirements: '',
      llm_id: defaultLlmId,
      exam_kb_id: undefined,
      kb_ids: [],
    },
  });

  // Update llm_id when defaultLlmId changes
  useEffect(() => {
    if (defaultLlmId) {
      form.setValue('llm_id', defaultLlmId);
    }
  }, [defaultLlmId, form]);

  // Monitor generatedQuestions for changes
  useEffect(() => {
    if (generatedQuestions) {
      console.log('generatedQuestions updated:', {
        length: generatedQuestions.length,
        preview: generatedQuestions.slice(0, 100) + '...',
      });
    }
  }, [generatedQuestions]);

  const handleGoBack = () => {
    history.push('/workbench');
  };

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    setMarkdownData('');

    try {
      const formData = new FormData();
      formData.append('exam_scene', data.exam_scene);
      formData.append('knowledge_points', data.knowledge_points);
      formData.append('objective', data.objective);
      formData.append('llm_id', data.llm_id || 'deepseek-r1:32b@Ollama');

      const isRandom = data.question_generation_type === 'random';
      formData.append('question_method', isRandom ? 'random' : 'by_type');

      if (isRandom) {
        formData.append('question_count', data.question_count.toString());
      } else {
        formData.append(
          'single_choice_count',
          data.single_choice_count.toString(),
        );
        formData.append(
          'multiple_choice_count',
          data.multiple_choice_count.toString(),
        );
        formData.append('fill_blank_count', data.fill_blank_count.toString());
        formData.append('true_false_count', data.true_false_count.toString());
        formData.append(
          'short_answer_count',
          data.short_answer_count.toString(),
        );
        formData.append('ordering_count', data.ordering_count.toString());
      }

      // Attach auth header dynamically to avoid circular deps
      const { getAuthorization } = await import('@/utils/authorization-util');

      const resp = await fetch('/v1/agent/ai-question/start', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          Authorization: getAuthorization(),
        },
      });

      if (!resp.ok) {
        throw new Error(`HTTP ${resp.status}`);
      }

      const reader = resp.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let accumContent = '';

      const processBuffer = () => {
        let idx;
        while ((idx = buffer.indexOf('\n\n')) >= 0) {
          const rawEvent = buffer.slice(0, idx + 2);
          buffer = buffer.slice(idx + 2);

          let eventName = '';
          let dataStr = '';

          rawEvent
            .trim()
            .split('\n')
            .forEach((line) => {
              if (line.startsWith('event:')) {
                eventName = line.slice(6).trim();
              } else if (line.startsWith('data:')) {
                dataStr += line.slice(5).trim();
              }
            });

          if (!eventName || !dataStr) return;

          try {
            const evtData = JSON.parse(dataStr);

            switch (eventName) {
              case 'stream': {
                let chunkText = evtData.chunk || '';

                // Filter <think> blocks
                if (thinkModeRef.current) {
                  const closeIdx = chunkText.indexOf('</think>');
                  if (closeIdx !== -1) {
                    chunkText = chunkText.slice(closeIdx + 8);
                    thinkModeRef.current = false;
                  } else {
                    chunkText = '';
                  }
                }

                const startIdx = chunkText.indexOf('<think>');
                if (startIdx !== -1) {
                  const beforeThink = chunkText.slice(0, startIdx);
                  const afterStart = chunkText.slice(startIdx + 7);
                  const endIdx = afterStart.indexOf('</think>');
                  if (endIdx !== -1) {
                    const afterThink = afterStart.slice(endIdx + 8);
                    chunkText = beforeThink + afterThink;
                  } else {
                    thinkModeRef.current = true;
                    chunkText = beforeThink;
                  }
                }

                accumContent += chunkText;
                flushSync(() => {
                  setMarkdownData(accumContent);
                });
                // display as soon as first chunk arrives
                setLoading(false);
                break;
              }
              case 'done':
                setLoading(false);
                flushSync(() => {
                  setGeneratedQuestions(accumContent);
                });
                message.success('题目生成成功！');
                break;
              case 'error':
                message.error(evtData.detail || '服务端错误');
                setLoading(false);
                break;
              default:
                break;
            }
          } catch (err) {
            console.error('Failed to parse SSE event', err, dataStr);
          }
        }
      };

      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        processBuffer();
      }
    } catch (err: any) {
      console.error('AIQuestion generation failed', err);
      message.error(err.message || '生成试题失败');
      setLoading(false);
    }
  };

  // 添加新的表单提交处理函数，确保按钮点击后立即禁用
  const handleFormSubmit = () => {
    setLoading(true);
    form.handleSubmit(onSubmit)();
  };

  // 导出到Word可打开的HTML格式
  const handleExportToDocx = () => {
    if (markdownData) {
      try {
        // 生成基于考试场景的文件名
        const defaultFileName = '考题测试_' + new Date().getTime();
        const fileName = `${defaultFileName}.docx`;

        // 导出为DOCX格式
        exportMarkdownToDocx(markdownData, fileName);
      } catch (error) {
        console.error('导出文件错误:', error);
        message.error('导出文件失败');
      }
    }
  };

  const handleExportToExcel = () => {
    if (markdownData) {
      try {
        // 生成基于考试场景的文件名
        const defaultFileName = '考题测试_' + new Date().getTime();

        // 使用新的markdown到Excel转换函数
        markdownToExcelV2(markdownData, defaultFileName);
      } catch (error) {
        console.error('导出文件错误:', error);
        message.error('导出文件失败');
      }
    }
  };

  // 导出到3.0系统
  const handleExportToV3System = async () => {
    if (!markdownData) {
      message.error('没有可导出的题目数据');
      return;
    }

    setExportingToV3System(true);

    try {
      // 动态获取V3系统配置
      const configResponse = await getV3SystemConfig();

      if (configResponse.code !== 0 || !configResponse.data) {
        message.error('获取V3系统配置失败');
        return;
      }

      const { endpoint, token } = configResponse.data;

      const success = await convertAndSendQuestions(
        markdownData,
        token,
        endpoint,
      );

      if (success) {
        message.success('题目已成功导出到3.0系统');
      }
    } catch (error) {
      console.error('导出到3.0系统失败:', error);
      message.error('导出到3.0系统失败，请重试');
    } finally {
      setExportingToV3System(false);
    }
  };

  // Cast the form to match INextOperatorForm interface expected by AIQuestionForm
  const aiQuestionForm = form as unknown as INextOperatorForm['form'];

  return (
    <div className={`${styles.aiQuestionContainer} main-content-container`}>
      <div className={styles.pageHeader}>
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          className={styles.backButton}
        >
          返回
        </Button>
        <div className={styles.pageTitle}>
          <h2 className="main-page-title">AI出题</h2>
        </div>
        <Paragraph className={styles.pageDescription}>
          基于知识库，智能生成考试题目
        </Paragraph>
      </div>

      <Row gutter={24}>
        <Col span={10}>
          <Card className={styles.formCard} title="出题配置">
            <AIQuestionForm form={aiQuestionForm} />

            <div className={styles.formActions}>
              <Button
                type="primary"
                icon={<FormatPainterOutlined />}
                size="large"
                loading={loading}
                onClick={handleFormSubmit}
                className={styles.generateButton}
              >
                一键生成
              </Button>

              {/* <Button
                type="default"
                icon={<FormatPainterOutlined />}
                size="large"
                loading={loading}
                onClick={handleTest}
              >
                测试
              </Button> */}
            </div>
          </Card>
        </Col>

        <Col span={14}>
          <Card
            className={styles.resultCard}
            title="生成结果"
            extra={
              markdownData && (
                <>
                  <Button
                    type="default"
                    icon={<FileWordOutlined size={14} />}
                    onClick={handleExportToDocx}
                    disabled={!markdownData}
                    style={{ marginRight: 16 }}
                  >
                    导出Word
                  </Button>
                  <Button
                    type="default"
                    icon={<FileExcelOutlined size={14} />}
                    onClick={handleExportToExcel}
                    disabled={!markdownData}
                    style={{ marginRight: 16 }}
                  >
                    导出Excel
                  </Button>
                  <Button
                    type="primary"
                    icon={<CloudUploadOutlined size={14} />}
                    onClick={handleExportToV3System}
                    disabled={!markdownData}
                    loading={exportingToV3System}
                  >
                    转发到3.0系统
                  </Button>
                </>
              )
            }
          >
            {loading ? (
              <div className={styles.loadingResult}>
                <div className={styles.loadingContainer}>
                  <GenerateCube color="#1890ff" size={12} />
                  <p
                    style={{
                      marginTop: '4px',
                      textAlign: 'center',
                      color: '#666',
                    }}
                  >
                    正在生成试题，请稍候...
                  </p>
                </div>
              </div>
            ) : markdownData ? (
              <div className={styles.resultContent}>
                <HightLightMarkdown>{markdownData}</HightLightMarkdown>
              </div>
            ) : (
              <div className={styles.emptyResult}>
                <div className={styles.placeholder}>
                  <FormatPainterOutlined className={styles.placeholderIcon} />
                  <p>完成左侧表单配置后，点击「一键生成」按钮生成试题</p>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AIQuestionPage;
