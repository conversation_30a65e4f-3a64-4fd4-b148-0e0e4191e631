#!/usr/bin/env python3
"""
测试Extrapolate API的脚本
"""

import requests
import os

# 配置
HOST_ADDRESS = "http://*************:9380"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = """ctAseGvejiaSWWZ88T/m4FQVOpQyUvP+x7sXtdv3feqZACiQleuewkUi35E16wSd5C5QcnkkcV9cYc8TKPTRZlxappDuirxghxoOvFcJxFU4ixLsDfN33jCHRoDUW81IH9zjij/vaw8IbVyb6vuwg6MX6inOEBRRzVbRYxXOu1wkWY6SsI8X70oF9aeLFp/PzQpjoe/YbSqpTq8qqrmHzn9vO+yvyYyvmDsphXeX8f7fp9c7vUsfOCkM+gHY3PadG+QHa7KI7mzTKgUTZImK6BZtfRBATDTthEUbbaTewY4H0MnWiCeeDhcbeQao6cFy1To8pE3RpmxnGnS8BsBn8w=="""

# 创建session来保持登录状态
session = requests.Session()

def login_user():
    """登录用户"""
    url = f"{HOST_ADDRESS}/v1/user/login"
    data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    response = session.post(url, json=data)
    result = response.json()
    if result.get("code") == 0:
        print(f"Login successful!")
        return True
    else:
        print(f"Login failed: {result}")
        return False

def test_extrapolate_api():
    """测试extrapolate API"""
    url = f"{HOST_ADDRESS}/v1/agent_app/extrapolate/generate"
    
    # 创建测试文件内容
    sample_content = """数学试题示例：

1. 计算题：求函数 f(x) = x² + 2x - 3 的零点。
   解：设 f(x) = 0，即 x² + 2x - 3 = 0
   因式分解：(x + 3)(x - 1) = 0
   所以 x = -3 或 x = 1

2. 选择题：下列哪个函数是奇函数？
   A. f(x) = x²
   B. f(x) = x³
   C. f(x) = |x|
   D. f(x) = x² + 1
   答案：B

3. 应用题：某商品原价100元，先涨价20%，后降价20%，现价是多少？
   解：涨价后：100 × (1 + 20%) = 120元
   降价后：120 × (1 - 20%) = 96元
   现价是96元。
"""
    
    # 准备FormData和文件
    files = {
        'sample_file': ('sample_questions.txt', sample_content.encode('utf-8'), 'text/plain')
    }
    
    data = {
        'llm_id': 'glm-4-flash@ZHIPU-AI',
        'question_type': 'same',
        'structure': 'same',
        'knowledge_points': 'same_context',
        'difficulty': 'same',
        'quantity': '3'
    }
    
    try:
        response = session.post(url, files=files, data=data)
        result = response.json()
        print(f"Extrapolate API response: {result}")
        
        if result.get("code") == 0:
            print("✅ Extrapolate API test passed!")
            generated_questions = result.get("data", {}).get("generated_questions", "")
            print(f"Generated questions preview: {generated_questions[:500]}...")
        else:
            print(f"❌ Extrapolate API test failed: {result.get('message')}")
            
    except Exception as e:
        print(f"❌ Exception during API test: {e}")

def main():
    print("Starting Extrapolate API test...")
    
    # 登录获取session
    if not login_user():
        print("❌ Failed to login")
        return
        
    # 测试extrapolate API
    print("\n=== Testing Extrapolate API ===")
    test_extrapolate_api()

if __name__ == "__main__":
    main() 