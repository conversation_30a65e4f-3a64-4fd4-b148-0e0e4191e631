#!/usr/bin/env python3
"""
测试Llama.cpp完整集成
"""

import sys
import os
import json
import requests

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_llamacpp_config():
    """测试Llama.cpp配置是否正确添加"""
    
    print("=" * 60)
    print("测试Llama.cpp配置集成")
    print("=" * 60)
    
    # 1. 检查配置文件
    print("\n1. 检查配置文件...")
    try:
        with open('conf/llm_factories.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 查找Llama.cpp工厂
        llamacpp_factory = None
        for factory in config['factory_llm_infos']:
            if factory['name'] == 'Llama.cpp':
                llamacpp_factory = factory
                break
        
        if llamacpp_factory:
            print("✓ Llama.cpp工厂配置已添加")
            print(f"  - 名称: {llamacpp_factory['name']}")
            print(f"  - 标签: {llamacpp_factory['tags']}")
            print(f"  - 状态: {llamacpp_factory['status']}")
            print(f"  - 模型数量: {len(llamacpp_factory['llm'])}")
            
            for model in llamacpp_factory['llm']:
                print(f"    * {model['llm_name']} ({model['model_type']})")
        else:
            print("✗ Llama.cpp工厂配置未找到")
            return False
            
    except Exception as e:
        print(f"✗ 配置文件检查失败: {e}")
        return False
    
    # 2. 检查后端代码修改
    print("\n2. 检查后端代码修改...")
    try:
        # 检查embedding_model.py
        with open('rag/llm/embedding_model.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class LlamaCppEmbed' in content:
                print("✓ LlamaCppEmbed类已添加")
            else:
                print("✗ LlamaCppEmbed类未找到")
                return False
        
        # 检查__init__.py
        with open('rag/llm/__init__.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if '"Llama.cpp": LlamaCppEmbed' in content:
                print("✓ EmbeddingModel字典已更新")
            else:
                print("✗ EmbeddingModel字典未更新")
                return False
                
        # 检查llm_app.py
        with open('api/apps/llm_app.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'llm_name += "___LlamaCpp"' in content:
                print("✓ 后端处理逻辑已添加")
            else:
                print("✗ 后端处理逻辑未添加")
                return False
                
    except Exception as e:
        print(f"✗ 后端代码检查失败: {e}")
        return False
    
    # 3. 检查前端代码修改
    print("\n3. 检查前端代码修改...")
    try:
        # 检查常量定义
        with open('web/src/constants/llm.ts', 'r', encoding='utf-8') as f:
            content = f.read()
            if "LlamaCpp = 'Llama.cpp'" in content:
                print("✓ LLMFactory枚举已更新")
            else:
                print("✗ LLMFactory枚举未更新")
                return False
            
            if "'llama-cpp'" in content:
                print("✓ IconMap已更新")
            else:
                print("✗ IconMap未更新")
                return False
        
        # 检查本地工厂列表
        with open('web/src/pages/user-setting/constants.tsx', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'LLMFactory.LlamaCpp' in content:
                print("✓ LocalLlmFactories已更新")
            else:
                print("✗ LocalLlmFactories未更新")
                return False
        
        # 检查图标文件
        if os.path.exists('web/src/assets/svg/llm/llama-cpp.svg'):
            print("✓ 图标文件存在")
        else:
            print("✗ 图标文件不存在")
            return False
            
    except Exception as e:
        print(f"✗ 前端代码检查失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("✓ 所有配置检查通过！Llama.cpp已成功集成到RAGFlow")
    print("=" * 60)
    
    return True

def test_llamacpp_api():
    """测试Llama.cpp API连接（可选）"""
    
    print("\n4. 测试API连接（可选）...")
    try:
        # 尝试连接到Llama.cpp服务
        response = requests.post(
            "http://192.168.3.124:8080/embedding",
            json={"content": "测试连接"},
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✓ API连接成功")
            print(f"  - 响应格式: {type(result)}")
            if isinstance(result, list) and len(result) > 0:
                print(f"  - Embedding维度: {len(result[0].get('embedding', [[]])[0])}")
            return True
        else:
            print(f"✗ API连接失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"⚠ API连接测试跳过（网络不可达）: {e}")
        return True  # 网络测试失败不影响配置验证

def print_usage_guide():
    """打印使用指南"""
    
    print("\n" + "=" * 60)
    print("使用指南")
    print("=" * 60)
    print("""
1. 启动RAGFlow服务
2. 登录管理界面，进入 设置 -> 模型管理
3. 在"待添加模型"列表中找到"Llama.cpp"
4. 点击"添加模型"按钮
5. 填写配置信息：
   - 模型类型: embedding 或 chat
   - 模型名称: llama-cpp-embedding 或 llama-cpp-chat
   - API Base: http://192.168.3.124:8080
   - API Key: 任意值（如"dummy"）
6. 保存配置
7. 在知识库设置中选择该embedding模型

注意事项：
- 确保192.168.3.124:8080服务正常运行
- 确保网络连通性
- 模型名称可以自定义，但建议使用有意义的名称
""")

if __name__ == "__main__":
    success = test_llamacpp_config()
    
    if success:
        test_llamacpp_api()
        print_usage_guide()
    
    sys.exit(0 if success else 1)
