@import '@/theme/high-tech-theme.less';

.relatedResources {
  width: 280px;
  min-width: 280px;
  max-width: 280px;
  padding: 16px 8px;
  background-color: var(--card-background);
  // border-left: 1px solid var(--border-color);
  position: relative;
  flex-shrink: 0;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 8px;
  }

  .sectionTitle {
    font-size: var(--font-size-lg);
    font-weight: 600;
    background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
  }

  .refreshButton {
    width: 24px;
    height: 24px;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-tertiary);
    transition: all var(--transition-fast);

    &:hover:not(:disabled) {
      color: var(--primary-color);
      background-color: var(--primary-ultralight);
    }

    &:disabled {
      opacity: 0.5;
    }
  }

  /* 当前问答上下文信息 */
  .currentContext {
    margin: 0 8px 16px 8px;
    padding: 12px;
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);

    .contextHeader {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    .contextIcon {
      font-size: 12px;
      color: var(--primary-color);
      margin-right: 6px;
    }

    .contextLabel {
      font-size: 12px;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .contextText {
      display: block;
      font-size: 12px;
      line-height: 1.4;
      color: var(--text-primary);
      margin-bottom: 8px;
      cursor: help;
    }

    .resourceStats {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .statItem {
        font-size: 11px;
        color: var(--text-tertiary);
        background-color: var(--neutral-100);
        padding: 2px 6px;
        border-radius: 4px;
        line-height: 1.2;
      }
    }
  }

  .resourceList {
    flex: 1;
    overflow-y: auto;
    padding: 0 8px;
  }

  .resourceCard {
    margin-bottom: 8px;
    cursor: pointer;
    transition: all var(--transition-fast);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    background: var(--card-background);

    :global(.ant-card-body) {
      padding: 12px;
      min-height: auto;
    }

    &:hover {
      border-color: var(--primary-color);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
      transform: translateY(-1px);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .contentSection {
    flex: 1;
    min-width: 0;
  }

  .fileName {
    display: block;
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin-bottom: 4px;
  }

  .keyword {
    display: block;
    font-size: 12px;
    line-height: 1.3;
    margin-bottom: 2px;
  }

  .sourceTag {
    display: inline-block;
    font-size: 11px;
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
    padding: 1px 4px;
    border-radius: 2px;
    line-height: 1.2;
  }

  .details {
    font-size: 11px;
    color: var(--text-tertiary);
    margin: 0;
    line-height: 1.3;
  }

  .fileIcon {
    font-size: 20px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .errorState {
    text-align: center;
    padding: 16px;

    .ant-btn-link {
      padding: 0;
      height: auto;
      margin-top: 8px;
    }
  }

  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    color: var(--text-tertiary);

    :global(.ant-empty-image) {
      height: 48px;
    }

    :global(.ant-empty-description) {
      font-size: 12px;
      color: var(--text-tertiary);
    }
  }
}

/* Dark theme adjustments */
.dark {
  .relatedResources {
    .currentContext {
      background-color: var(--neutral-800);
      // border-color: var(--neutral-700);

      .resourceStats .statItem {
        background-color: var(--neutral-700);
        color: var(--text-secondary);
      }
    }

    .resourceCard {
      &:hover {
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
      }
    }
  }
}
