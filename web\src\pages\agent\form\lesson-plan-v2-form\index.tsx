import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { InputNumber as AntdInputNumber, Radio, Upload } from 'antd';
import { useState } from 'react';
import { INextOperatorForm } from '../../interface';
import styles from './index.less';

// Custom InputNumber component that is compatible with the form
const InputNumber = ({ value, onChange, ...props }: any) => {
  return <AntdInputNumber value={value} onChange={onChange} {...props} />;
};

interface LessonPlanV2FormProps extends INextOperatorForm {
  uploadProps: any;
}

const LessonPlanV2Form = ({ form, uploadProps }: LessonPlanV2FormProps) => {
  const [contentInputMethod, setContentInputMethod] = useState<'text' | 'file'>(
    'text',
  );

  // Handle switching between text and file input
  const handleContentMethodChange = (e: any) => {
    const method = e.target.value as 'text' | 'file';
    setContentInputMethod(method);

    if (method === 'text') {
      // Clear file when switching to text
      form.setValue('course_file', undefined);
      if (uploadProps.onRemove) {
        uploadProps.onRemove();
      }
    } else {
      // Clear text when switching to file
      form.setValue('course_content', '');
    }
  };

  return (
    <Form {...form}>
      <form
        className="space-y-6"
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        {/* Course name */}
        <FormField
          control={form.control}
          name="course_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>课程名称</FormLabel>
              <FormControl>
                <Input {...field} placeholder="如：铁路信号工安全操作规程" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Class type */}
        <FormField
          control={form.control}
          name="class_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel>课型</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  placeholder="如：新授课、复习课、习题课、实验课"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Class hours */}
        <FormField
          control={form.control}
          name="class_hours"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                课时 <span style={{ fontSize: 12 }}>(分钟)</span>
              </FormLabel>
              <FormControl>
                <InputNumber
                  value={field.value}
                  onChange={field.onChange}
                  placeholder="请输入课时"
                  min={10}
                  max={1000}
                  className={styles.fullWidthSelect}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Target audience */}
        <FormField
          control={form.control}
          name="target_audience"
          render={({ field }) => (
            <FormItem>
              <FormLabel>授课对象</FormLabel>
              <FormControl>
                <Input {...field} placeholder="如：铁路信号工" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Input method selector */}
        <FormItem>
          <FormLabel>课程内容</FormLabel>
          <div className={styles.contentMethodSelector}>
            <Radio.Group
              value={contentInputMethod}
              onChange={handleContentMethodChange}
              className={styles.radioGroup}
            >
              <Radio value="text" className={styles.radioOption}>
                手工输入
              </Radio>
              <Radio value="file" className={styles.radioOption}>
                上传文件
              </Radio>
            </Radio.Group>
          </div>
        </FormItem>

        {/* Course content textarea */}
        {contentInputMethod === 'text' && (
          <FormField
            control={form.control}
            name="course_content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>课程内容</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="请输入课程内容或者简介"
                    rows={6}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        {/* File upload */}
        {contentInputMethod === 'file' && (
          <FormField
            control={form.control}
            name="course_file"
            render={({ field }) => (
              <FormItem>
                <FormLabel>课程材料</FormLabel>
                <FormControl>
                  <div className={styles.uploadSection}>
                    <Upload.Dragger
                      {...uploadProps}
                      accept=".doc,.docx,.txt"
                      maxCount={1}
                      className="upload-dragger-custom"
                    >
                      <div className={styles.uploadBox}>
                        <div className={styles.uploadPlaceholder}>
                          <div className={styles.uploadIcon}></div>
                          <p className={styles.dragText}>
                            将文件拖拽到此处，或
                            <span className={styles.browseText}>点击上传</span>
                          </p>
                          <p className={styles.supportedFormats}>
                            支持 .doc .docx .txt 格式，单文件不超过 2MB
                          </p>
                        </div>
                      </div>
                    </Upload.Dragger>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </form>
    </Form>
  );
};

export default LessonPlanV2Form;
