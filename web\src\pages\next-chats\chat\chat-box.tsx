import { ChatInput } from '@/components/chat-input';

/**
 * Middle display area of the chat page.
 * 1. Adds a fixed title bar at the top to show the current session title.
 * 2. Shifts the message list downward by placing it after the title bar.
 */
export function ChatBox() {
  // TODO: Replace the hard-coded title with the real session title when data is available.
  const sessionTitle = '当前会话标题';

  return (
    <section className="border-x flex-1 flex flex-col">
      {/* Title bar */}
      <div className="shrink-0 sticky top-0 bg-colors-background-neutral-strong z-10 border-b border-colors-outline-neutral-standard">
        <h2 className="text-[16px] font-medium leading-[48px] px-4">
          {sessionTitle}
        </h2>
      </div>

      {/* Message list placeholder  */}
      <div className="flex-1 overflow-auto px-6 py-4 space-y-4">
        {/* Messages will be rendered here */}
      </div>

      {/* Chat input */}
      <ChatInput />
    </section>
  );
}
