from __future__ import annotations

"""Smart Exam business logic service.

This module encapsulates the smart exam generation processing following
the LessonPlanV2Service architecture. It handles file upload, embedding,
and streaming response generation.

Single-responsibility is kept by:
* _request_to_param – validation & DTO construction only.
* _handle_file_upload – file IO & async embedding only.
* build_start_response – end-to-end start flow with SSE streaming.
* cancel_cleanup – revoke scheduled cleanup job.
"""

import json
import logging
import time
import traceback
from typing import Dict, Generator, Optional, Tuple

from flask import Request, Response

from api.utils.api_utils import (
    get_data_error_result,
    get_data_from_request,
    get_json_result,
    server_error_response,
)
from api.utils.tenant_utils import get_tenant_with_fallback
from agent.smart_exam import SmartExamParam, SmartExamParamLegacyAdapter
from agent.component.template_retrieval import TemplateRetrieval, TemplateRetrievalParam
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.file_service import FileService
from api.db.services.document_service import DocumentService, _get_or_create_kb_for_user
from api.db.services.file2document_service import File2DocumentService
from api.db.services.llm_service import TenantLLMService
from api.db.services.user_service import TenantService
from api.db import LLMType
from agent.canvas import Canvas

# Use the same SSE and Canvas classes from lesson plan v2 service
from api.services.lesson_plan_v2_service import SSEStreamBuffer, MinimalCanvas

DEFAULT_PRESET_KB_NAME = "tenant-exam-question"

# --------------------------------------------------------------------------------------
# Helper data classes
# --------------------------------------------------------------------------------------


class _UploadContext:
    """Holds context about the user-uploaded question bank file.

    Separated to top level so that runtime type checkers (e.g. *beartype*) can import
    it directly via ``api.services.smart_exam_service._UploadContext``.  Nested class
    definitions cannot be resolved as forward references by such libraries.
    """

    def __init__(self, kb_id: str, doc_id: str, cleanup_job_id: Optional[str]):
        self.kb_id = kb_id
        self.doc_id = doc_id
        self.cleanup_job_id = cleanup_job_id

# --------------------------------------------------------------------------------------
# Core service
# --------------------------------------------------------------------------------------


class SmartExamService:
    """Business logic of smart-exam API endpoints extracted from agent_app."""

    # ------------------------------------------------------------------
    # Public API (called from Flask routes)
    # ------------------------------------------------------------------

    @staticmethod
    def build_start_response(req: Request, user) -> Response:
        """Handle POST /smart-exam/start and build an SSE streaming response."""
        try:
            data, files = SmartExamService._extract_request_data(req)
            logging.info(
                "[SmartExam] Received data: %s, files: %s",
                list(data.keys()),
                list(files.keys()) if files else [],
            )

            # Validate minimal required fields early
            required_fields = ["exam_name", "knowledge_points", "difficulty"]
            for f in required_fields:
                if not data.get(f):
                    return get_json_result(data={}, code=400, message=f"Missing field: {f}")

            # Build SmartExamParam via legacy adapter (Pydantic validation)
            try:
                param = SmartExamParamLegacyAdapter.from_request_data(data)
            except Exception as ve:
                return get_json_result(data={}, code=400, message=str(ve))

            # Handle file upload
            question_bank_file = files.get(
                "question_bank_file") if files else None
            upload_ctx = None

            if question_bank_file and question_bank_file.filename:
                ok, param.question_bank_file_content, upload_ctx = SmartExamService._handle_file_upload(
                    user.id, question_bank_file
                )
                if not ok:
                    return get_json_result(data={}, code=500, message="文件上传解析失败")
            else:
                return get_json_result(data={}, code=400, message="试题文件是必需的")

            # Tenant & Canvas initialisation
            success, tenant = get_tenant_with_fallback(user.id)
            if not success or not tenant:
                return get_data_error_result(message="Unable to determine tenant for user")
            tenant_id = tenant.id
            canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user.id)

            # Set KB IDs for retrieval
            if upload_ctx:
                param.user_kb_id = upload_ctx.kb_id
                logging.info(f"[SmartExam] Set user KB ID: {param.user_kb_id}")

                # Check if the user KB is ready for retrieval
                try:
                    done, status_msg = KnowledgebaseService.is_parsed_done(
                        param.user_kb_id)
                    logging.info(
                        f"[SmartExam] User KB parsing status: done={done}, msg='{status_msg}'")
                    if not done:
                        logging.warning(
                            f"[SmartExam] User KB {param.user_kb_id} is not ready for retrieval yet")
                except Exception as e:
                    logging.error(
                        f"[SmartExam] Error checking user KB status: {e}")
            else:
                logging.warning(f"[SmartExam] No upload context provided")

            # Find preset question bank KB
            try:
                logging.info(
                    f"[SmartExam] Searching for preset KB '{DEFAULT_PRESET_KB_NAME}' in tenant {tenant_id}")
                exists, preset_kb = KnowledgebaseService.get_by_name(
                    DEFAULT_PRESET_KB_NAME, tenant_id)
                if exists and preset_kb:
                    param.preset_kb_id = preset_kb.id
                    logging.info(
                        f"[SmartExam] Found preset KB: {param.preset_kb_id}, name: {preset_kb.name}, tenant_id: {preset_kb.tenant_id}")

                    # Check preset KB status
                    try:
                        done, status_msg = KnowledgebaseService.is_parsed_done(
                            param.preset_kb_id)
                        logging.info(
                            f"[SmartExam] Preset KB parsing status: done={done}, msg='{status_msg}'")
                    except Exception as e:
                        logging.error(
                            f"[SmartExam] Error checking preset KB status: {e}")

                else:
                    logging.warning(
                        f"[SmartExam] Preset KB '{DEFAULT_PRESET_KB_NAME}' not found for tenant {tenant_id}")
                    # Try to find any knowledge base with similar name across all tenants for debugging
                    try:
                        all_kbs = KnowledgebaseService.query(
                            name=DEFAULT_PRESET_KB_NAME, status="1")
                        if all_kbs:
                            logging.info(
                                f"[SmartExam] Found {len(all_kbs)} KB(s) named '{DEFAULT_PRESET_KB_NAME}' in other tenants: {[(kb.id, kb.tenant_id) for kb in all_kbs]}")
                        else:
                            logging.warning(
                                f"[SmartExam] No KB named '{DEFAULT_PRESET_KB_NAME}' found in any tenant")
                    except Exception as debug_e:
                        logging.error(
                            f"[SmartExam] Error in debug search: {debug_e}")
                    param.preset_kb_id = None
            except Exception as e:
                logging.error(
                    f"[SmartExam] Error searching for preset KB: {e}", exc_info=True)
                param.preset_kb_id = None

            # Template retrieval (optional but recommended for better generation)
            tpl_text = SmartExamService._retrieve_template_text(
                canvas, param.llm_id, user.id)
            param.template_text = tpl_text

            # Build streaming generator
            stream_gen = SmartExamService._generate_smart_exam_stream(
                canvas,
                param,
                upload_ctx,
                req.headers.get("Last-Event-ID", ""),
            )

            resp = Response(stream_gen, mimetype="text/event-stream")
            resp.headers["Cache-Control"] = "no-cache"
            resp.headers["X-Accel-Buffering"] = "no"
            return resp

        except Exception as e:
            logging.error("[SmartExam] Fatal error: %s", e, exc_info=True)
            return server_error_response(e)

    @staticmethod
    def cancel_cleanup(session_id: str):
        """Handle POST /smart-exam/<session_id>/cancel_cleanup."""
        # For smart exam, we don't need session persistence like lesson plan
        # Just revoke the cleanup job if provided
        try:
            from api.celery_app import celery_app as _celery_app
            # Since we don't have session storage for smart exam,
            # we'll just return success
            return get_json_result(data=True)
        except Exception as e:
            logging.error("cancel cleanup error: %s", e, exc_info=True)
            return server_error_response(e)

    # ------------------------------------------------------------------
    # Internals helpers
    # ------------------------------------------------------------------

    @staticmethod
    def _extract_request_data(req: Request) -> Tuple[Dict, Dict]:
        if req.content_type and "application/json" in (req.content_type or ""):
            data = get_data_from_request(req)
            files = {}
        else:
            data = req.form.to_dict()
            files = req.files
        return data, files

    @staticmethod
    def _handle_file_upload(user_id: str, question_bank_file) -> Tuple[bool, str, Optional[_UploadContext]]:
        """Upload question bank file to KB & kick off async embedding. Returns (ok, file_content, ctx)"""
        try:
            logging.info(
                f"[SmartExam] Starting file upload for user {user_id}, filename: {question_bank_file.filename}")

            # Create or get KB for user
            kb_id = _get_or_create_kb_for_user(user_id)
            logging.info(f"[SmartExam] Got/created user KB: {kb_id}")

            e, kb = KnowledgebaseService.get_by_id(kb_id)
            if not e:
                raise LookupError("Can't find this knowledgebase!")

            logging.info(
                f"[SmartExam] User KB details: id={kb.id}, name={kb.name}, tenant_id={kb.tenant_id}, embd_id={kb.embd_id}")

            # Upload file to KB
            err, files = FileService.upload_document(
                kb, [question_bank_file], user_id)
            if err:
                logging.error(f"[SmartExam] File upload errors: {err}")
                raise Exception("\n".join(err))
            if not files:
                logging.error(f"[SmartExam] No files uploaded successfully")
                raise Exception("No files uploaded successfully")

            doc_id = files[0][0]["id"]
            logging.info(
                f"[SmartExam] File uploaded successfully, doc_id: {doc_id}")

            # Read local content for generation
            question_bank_file.seek(0)
            try:
                file_content = question_bank_file.read().decode("utf-8", errors="ignore")
            except Exception:
                question_bank_file.seek(0)
                file_content = question_bank_file.read().decode("gbk", errors="ignore")

            logging.info(
                f"[SmartExam] File content read, length: {len(file_content)} characters")

            # Start async embedding
            try:
                _, tenant = TenantService.get_by_id(kb.tenant_id)
                doc_dict = files[0][0]
                doc_dict["tenant_id"] = kb.tenant_id
                info = {"run": 1, "progress": 0, "progress_msg": "",
                        "chunk_num": 0, "token_num": 0}
                DocumentService.update_by_id(doc_id, info)
                bucket, name = File2DocumentService.get_storage_address(
                    doc_id=doc_id)
                from api.db.services.task_service import queue_tasks
                queue_tasks(doc_dict, bucket, name, 0)
                logging.info(
                    f"[SmartExam] Started async embedding task for doc_id: {doc_id}")
            except Exception as e:
                logging.warning(
                    f"[SmartExam] Failed to start async embedding: {e}")

            # Schedule cleanup 2h later
            cleanup_job_id = None
            try:
                from api.tasks.lesson_plan_tasks import cleanup_kb_doc
                cleanup_async = cleanup_kb_doc.apply_async(
                    args=[None, kb_id, [doc_id]], countdown=7200)
                cleanup_job_id = cleanup_async.id
                logging.info(
                    f"[SmartExam] Scheduled cleanup task: {cleanup_job_id}")
            except Exception as e:
                logging.warning(
                    f"[SmartExam] Failed to schedule cleanup task: {e}")

            logging.info(f"[SmartExam] File upload completed successfully")
            return True, file_content, _UploadContext(kb_id, doc_id, cleanup_job_id)

        except Exception as e:
            logging.error(
                f"[SmartExam] File upload failed: {e}", exc_info=True)
            # Best effort cleanup
            try:
                if "doc_id" in locals():
                    doc_id_local = locals()["doc_id"]
                    e, doc_obj = DocumentService.get_by_id(doc_id_local)
                    if e and doc_obj:
                        doc_tenant_id = DocumentService.get_tenant_id(
                            doc_id_local)
                        DocumentService.remove_document(doc_obj, doc_tenant_id)
                        logging.info(
                            f"[SmartExam] Cleaned up failed document: {doc_id_local}")
            except Exception as cleanup_err:
                logging.error(
                    f"[SmartExam] Failed to cleanup document: {cleanup_err}")
            return False, "", None

    @staticmethod
    def _retrieve_template_text(canvas: Canvas, llm_id: str, user_id: str) -> str:
        """Retrieve smart exam template text using TemplateRetrieval component."""
        try:
            # Create template retrieval parameter
            tpl_param = TemplateRetrievalParam()
            tpl_param.tpl_kb_id = "tenant-smart-exam-template"
            tpl_param.top_n = 3

            # Create and run template retrieval component
            tpl_retrieval = TemplateRetrieval(
                canvas, "template_retrieval", tpl_param)
            tpl_df = tpl_retrieval._run(history=[])

            if not tpl_df.empty and "content" in tpl_df.columns:
                template_text = tpl_df["content"].iloc[0]
                logging.info(
                    "Retrieved smart exam template text, length: %d", len(template_text))
                return template_text
            else:
                logging.warning("No template content found")
                return ""
        except Exception as e:
            logging.warning("Template retrieval failed: %s", e)
            return ""

    # ------------------------------------------------------------------
    # Generation streaming helper
    # ------------------------------------------------------------------

    @staticmethod
    def _generate_smart_exam_stream(
        canvas: Canvas,
        param: SmartExamParam,
        upload_ctx: Optional[_UploadContext],
        last_event_header: str,
    ) -> Generator[str, None, None]:
        # -------------- New decoupled pipeline --------------
        from agent.smart_exam import (
            Retriever,
            Difficulty,
            QuestionMethod,
            QuestionDistribution,
            ExamContext,
            SmartExamGenerator,
        )
        from api import settings
        from api.db.services.llm_service import LLMBundle
        from api.db import LLMType

        # parse last event
        try:
            last_event_id = int(last_event_header) if last_event_header else -1
        except ValueError:
            last_event_id = -1

        # Embedding wait generator if uploaded file present
        embedding_generator = (
            SmartExamService._wait_for_embedding(upload_ctx.kb_id)
            if upload_ctx is not None
            else None
        )

        tenant_id = canvas.get_tenant_id()

        # ---------------- Build ExamContext ----------------
        dist = QuestionDistribution(
            single_choice=param.single_choice_count,
            multiple_choice=param.multiple_choice_count,
            fill_blank=param.fill_blank_count,
            true_false=param.true_false_count,
            short_answer=param.short_answer_count,
            ordering=param.ordering_count,
        )

        ctx = ExamContext(
            exam_name=param.exam_name,
            knowledge_points=param.knowledge_points,
            difficulty=Difficulty(
                param.difficulty) if param.difficulty else Difficulty.MEDIUM,
            question_method=QuestionMethod(param.question_method),
            distribution=dist,
            tenant_id=tenant_id,
            user_id=canvas.get_user_id(),
        )

        # Retrieval postponed to _stream after embedding wait
        retriever = Retriever(settings.retrievaler)
        # template text remains
        ctx.template_text = param.template_text or ""

        # ---------------- LLM & Generator ----------------
        llm_bundle_holder = LLMBundle(tenant_id, LLMType.CHAT, param.llm_id)

        def _stream():
            yield f"event:init\ndata:{json.dumps({'message': 'Smart exam generation started'})}\n\n"

            if embedding_generator is not None:
                for ev in embedding_generator:
                    yield ev

            # Template retrieval event (already done before main generation)
            yield f"event:retrieving_template\ndata:{json.dumps({'msg': '试题模板检索中...'})}\n\n"

            # ---------------- Retrieval (after embedding ready) ----------------
            if not ctx.user_content or not ctx.preset_content:
                if param.user_kb_id:
                    uc = retriever.retrieve(
                        tenant_id=tenant_id,
                        kb_id=param.user_kb_id,
                        query=param.knowledge_points,
                        top_n=param.user_kb_top_n,
                        similarity_threshold=param.similarity_threshold,
                    )
                    ctx.user_content = uc.content

                if param.preset_kb_id:
                    pc = retriever.retrieve(
                        tenant_id=tenant_id,
                        kb_id=param.preset_kb_id,
                        query=param.knowledge_points,
                        top_n=param.preset_kb_top_n,
                        similarity_threshold=param.similarity_threshold,
                    )
                    ctx.preset_content = pc.content

            yield f"event:generating\ndata:{json.dumps({'msg': '智能组卷生成中...'})}\n\n"

            sse_buffer = SSEStreamBuffer(send_interval=1.0)
            for chunk in SmartExamGenerator(llm_bundle_holder, stream=True).stream_generate(ctx):
                if chunk.strip():
                    event_data = sse_buffer.add_chunk(chunk, last_event_id)
                    if event_data:
                        yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"

            event_data = sse_buffer.flush()
            if event_data:
                yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"

            yield "id:-2\nevent:done\ndata:{}\n\n"

        return _stream()

    @staticmethod
    def _wait_for_embedding(kb_id: str):
        """Yield SSE events while waiting for file embedding to finish."""
        from rag.utils.redis_conn import REDIS_CONN
        from api import settings

        timeout = getattr(settings, "SMART_EXAM_WAIT_TIMEOUT", 300)
        interval = getattr(settings, "SMART_EXAM_WAIT_INTERVAL", 2)

        start_time = time.time()
        channel = f"embedding_done:{kb_id}"

        try:
            pubsub = REDIS_CONN.pubsub(ignore_subscribe_messages=True)
            pubsub.subscribe(channel)
            # Wait up to 30s for message; then fall back to polling
            message = pubsub.get_message(timeout=30)
            if message:
                logging.info(
                    f"[SmartExam] Received embedding_done signal for {kb_id}")
                return  # exit generator early, embedding done
        except Exception as e:
            logging.warning(f"[SmartExam] PubSub subscribe failed: {e}")

        check_count = 0
        while time.time() - start_time < timeout:
            try:
                check_count += 1
                done, status_msg = KnowledgebaseService.is_parsed_done(kb_id)
                elapsed_time = time.time() - start_time

                if done:
                    logging.info(
                        f"[SmartExam] Embedding completed for KB {kb_id} after {elapsed_time:.1f}s")
                    break

                yield "event:waiting_embedding\ndata:" + json.dumps({"msg": f"试题文件向量化中... ({elapsed_time:.0f}s)"}) + "\n\n"
                time.sleep(interval)
            except Exception as e:
                logging.warning(
                    f"[SmartExam] Error checking embedding status for KB {kb_id}: {e}")
                yield "event:waiting_embedding\ndata:" + json.dumps({"msg": "试题文件向量化中..."}) + "\n\n"
                time.sleep(interval)
        else:
            logging.warning(
                f"[SmartExam] Embedding wait timeout for kb_id: {kb_id} after {timeout}s")
