@import url(./inter.less);

html {
  height: 100%;
  overflow: hidden; // The content of the DatasetSettings page is too high, which will cause scroll bars to appear on the html tags. Setting the maximum height in DatasetSettings does not work either. I don't understand.
}

body {
  font-family: 'SF Pro SC', 'SF Pro Text', 'SF Pro Icons', 'PingFang SC',
    'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  margin: 0;
  height: 100%;
}

#root {
  height: 100%;
  overflow: auto;
}

.ant-app {
  height: 100%;
  width: 100%;
}

// /* Scroll bar stylings */
// ::-webkit-scrollbar {
//   width: 10px;
//   height: 10px;
// }

// /* Track */
// ::-webkit-scrollbar-track {
//   background: rgb(219, 218, 218);
// }

// /* Handle */
// ::-webkit-scrollbar-thumb {
//   background: #aaaaaa;
//   border-radius: 5px;
// }

// /* Handle on hover */
// ::-webkit-scrollbar-thumb:hover {
//   background: #888;
// }

// &::-webkit-scrollbar {
//   width: 6px;
// }

// &::-webkit-scrollbar-thumb {
//   background-color: var(--neutral-400);
//   border-radius: var(--border-radius-pill);
// }

// &::-webkit-scrollbar-track {
//   background-color: transparent;
// }
