from __future__ import annotations

import logging
from typing import Any, List, Dict

import pandas as pd

from agent.component.file_keyword_search import FileKeywordSearch, FileKeywordSearchParam


class ResourceSearchParam(FileKeywordSearchParam):
    """Parameters reusing FileKeywordSearchParam (no change)."""

    # No extra fields for now.
    pass


class ResourceSearch(FileKeywordSearch):
    """Search Drive files relevant to a given content_text.

    This component is functionally identical to FileKeywordSearch but is given
    a dedicated *component_name* so that front-end / DAG references can
    distinguish it from generic file search operations.
    """

    component_name = "ResourceSearch"

    def _run(self, history, **kwargs):
        # We simply delegate to parent implementation and then make sure the
        # output format aligns with the SSE `recommendations` event expected
        # by the front-end – i.e. a list of file dicts in `content` column.
        df = super()._run(history, **kwargs)

        # Ensure content column exists; if parent already returns DataFrame
        # with proper JSON string we leave unchanged.
        if isinstance(df, pd.DataFrame) and "content" in df.columns:
            return df

        # Fallback: wrap raw result
        return ResourceSearch.be_output(df)
