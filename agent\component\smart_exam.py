import json
import logging
from abc import ABC
import pandas as pd
from typing import Generator, Optional, Any, Tuple

from api.db import LLMType
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.llm_service import LLMBundle
from api import settings
from agent.component.base import ComponentBase, ComponentParamBase
from api.utils.llm_logger import llm_interaction_context, log_llm_stream_response

DEFAULT_PRESET_KB_NAME = "tenant-exam-question"


class SmartExamParam(ComponentParamBase):
    """
    Parameters for the Smart Exam Generation component.
    """

    def __init__(self):
        super().__init__()
        self.llm_id = "deepseek-r1:32b@Ollama"
        self.exam_name = ""  # 试卷名称
        self.knowledge_points = ""  # 知识点
        self.difficulty = "medium"  # 难度：low, medium, high

        # Question generation method
        self.question_method = "random"  # random or by_type
        self.question_count = 10  # For random method

        # For by_type method
        self.single_choice_count = 0
        self.multiple_choice_count = 0
        self.fill_blank_count = 0
        self.true_false_count = 0
        self.short_answer_count = 0
        self.ordering_count = 0

        # File upload content from frontend
        self.question_bank_file_content = ""  # Content extracted from uploaded file

        # Template text from template retrieval
        self.template_text = ""  # Retrieved template content

        # Knowledge base IDs for retrieval
        self.user_kb_id = None  # KB created from uploaded file
        self.preset_kb_id = None  # Preset question bank KB

        # Retrieval parameters
        self.user_kb_top_n = 15  # 50% from user uploaded content
        self.preset_kb_top_n = 15  # 50% from preset knowledge base
        self.similarity_threshold = 0.1  # Lowered from 0.2 for better retrieval

        # Streaming support
        self.stream = True

    def check(self):
        self.check_empty(self.llm_id, "[SmartExam] LLM ID cannot be empty.")
        self.check_empty(
            self.exam_name, "[SmartExam] Exam name cannot be empty.")
        self.check_empty(self.knowledge_points,
                         "[SmartExam] Knowledge points cannot be empty.")

        # Check question counts
        if self.question_method == "random":
            if self.question_count <= 0:
                raise ValueError(
                    "[SmartExam] Question count must be greater than 0 for random method.")
        elif self.question_method == "by_type":
            total_questions = (
                self.single_choice_count + self.multiple_choice_count +
                self.fill_blank_count + self.true_false_count +
                self.short_answer_count + self.ordering_count
            )
            if total_questions <= 0:
                raise ValueError(
                    "[SmartExam] At least one question type count must be greater than 0 for by_type method.")
        else:
            raise ValueError(
                "[SmartExam] Invalid question method. Must be 'random' or 'by_type'.")


class SmartExam(ComponentBase, ABC):
    component_name = "SmartExam"

    def _get_total_questions(self) -> int:
        """Get total number of questions to generate."""
        param = self._param
        if param.question_method == "random":
            return param.question_count
        else:
            return (
                param.single_choice_count + param.multiple_choice_count +
                param.fill_blank_count + param.true_false_count +
                param.short_answer_count + param.ordering_count
            )

    def _get_question_distribution_prompt(self) -> str:
        """Generate prompt for question type distribution."""
        param = self._param
        if param.question_method == "random":
            return f"随机生成{param.question_count}道试题，题型可以包括单选题、多选题、填空题、判断题、简答题等。"

        parts = []
        if param.single_choice_count > 0:
            parts.append(f"{param.single_choice_count}道单选题")
        if param.multiple_choice_count > 0:
            parts.append(f"{param.multiple_choice_count}道多选题")
        if param.fill_blank_count > 0:
            parts.append(f"{param.fill_blank_count}道填空题")
        if param.true_false_count > 0:
            parts.append(f"{param.true_false_count}道判断题")
        if param.short_answer_count > 0:
            parts.append(f"{param.short_answer_count}道简答题")
        if param.ordering_count > 0:
            parts.append(f"{param.ordering_count}道排序题")

        return "生成试题的类型及数量为：" + "、".join(parts) + "。"

    def _get_difficulty_prompt(self) -> str:
        """Generate prompt for difficulty level."""
        difficulty_map = {
            "low": "简单（基础概念理解）",
            "medium": "中等（知识点应用）",
            "high": "困难（综合分析）"
        }
        return f"试题难度要求：{difficulty_map.get(self._param.difficulty, '中等')}"

    def _retrieve_from_kb(self, tenant_id: str, kb_id: str, query: str, top_n: int) -> Tuple[str, list]:
        """Retrieve content from knowledge base."""
        if not kb_id:
            logging.warning(
                f"[SmartExam] _retrieve_from_kb called with empty kb_id")
            return "", []

        logging.info(
            f"[SmartExam] Starting retrieval from KB {kb_id} with query: '{query}', top_n: {top_n}")

        try:
            kb_result = KnowledgebaseService.get_by_id(kb_id)
            if not kb_result or not kb_result[0] or not kb_result[1]:
                logging.warning(
                    f"[SmartExam] Knowledge base not found: {kb_id}")
                return "", []

            kb_obj = kb_result[1]
            logging.info(
                f"[SmartExam] KB found: id={kb_obj.id}, name={kb_obj.name}, tenant_id={kb_obj.tenant_id}, embd_id={kb_obj.embd_id}, doc_num={kb_obj.doc_num}, chunk_num={kb_obj.chunk_num}")

            # If doc_num or chunk_num counters haven't been updated correctly we still
            # want to try retrieval – the underlying chunks may already exist in the
            # vector store.  Therefore we log a warning but do NOT abort early.
            if kb_obj.doc_num == 0:
                logging.warning(
                    f"[SmartExam] KB {kb_id} doc_num=0, continuing with retrieval attempt anyway.")

            if kb_obj.chunk_num == 0:
                logging.warning(
                    f"[SmartExam] KB {kb_id} chunk_num=0, continuing with retrieval attempt anyway.")

            # Use the actual tenant_id from the knowledge base object
            # This is important for preset knowledge bases that might belong to different tenants
            actual_tenant_id = kb_obj.tenant_id
            logging.info(
                f"[SmartExam] Using actual_tenant_id: {actual_tenant_id} (passed tenant_id was: {tenant_id})")

            # Get embedding model
            embd_id = kb_obj.embd_id
            if not embd_id:
                logging.warning(
                    f"[SmartExam] No embedding ID in KB, trying default for tenant {actual_tenant_id}")
                default_embd = LLMBundle(actual_tenant_id, LLMType.EMBEDDING)
                if not default_embd.llm_name:
                    logging.warning(
                        f"[SmartExam] No embedding model configured for tenant {actual_tenant_id}")
                    return "", []
                embd_id = default_embd.llm_name

            logging.info(f"[SmartExam] Using embedding model: {embd_id}")

            embd_mdl = LLMBundle(actual_tenant_id, LLMType.EMBEDDING, embd_id)
            logging.info(
                f"[SmartExam] Created embedding model bundle successfully")

            # Log retrieval parameters
            retrieval_params = {
                "query": query,
                "tenant_ids": [actual_tenant_id],
                "kb_ids": [kb_id],
                "page": 1,
                "top_n": top_n,
                "similarity_threshold": self._param.similarity_threshold,
                "vector_similarity_weight": 0.5
            }
            logging.info(
                f"[SmartExam] Retrieval parameters: {retrieval_params}")

            # Perform retrieval using the actual tenant_id from the knowledge base
            kbinfos = settings.retrievaler.retrieval(
                query,
                embd_mdl,
                [actual_tenant_id],
                [kb_id],
                1,  # page
                top_n,
                self._param.similarity_threshold,
                vector_similarity_weight=0.5,
                aggs=False
            )

            logging.info(
                f"[SmartExam] Retrieval completed. Result type: {type(kbinfos)}")

            if not kbinfos:
                logging.warning(
                    f"[SmartExam] Retrieval returned None/empty result")
                return "", []

            if not kbinfos.get("chunks"):
                logging.warning(
                    f"[SmartExam] No chunks in retrieval result. Keys in result: {list(kbinfos.keys())}")

                # Try a fallback retrieval with a broader query and lower threshold
                logging.info(
                    f"[SmartExam] Attempting fallback retrieval with broader query")
                try:
                    fallback_kbinfos = settings.retrievaler.retrieval(
                        "试题",  # Generic term for questions
                        embd_mdl,
                        [actual_tenant_id],
                        [kb_id],
                        1,  # page
                        top_n,
                        0.05,  # Much lower threshold
                        vector_similarity_weight=0.3,
                        aggs=False
                    )

                    if fallback_kbinfos and fallback_kbinfos.get("chunks"):
                        logging.info(
                            f"[SmartExam] Fallback retrieval found {len(fallback_kbinfos['chunks'])} chunks")
                        kbinfos = fallback_kbinfos
                    else:
                        logging.warning(
                            f"[SmartExam] Fallback retrieval also failed")
                        return "", []
                except Exception as fallback_e:
                    logging.error(
                        f"[SmartExam] Fallback retrieval error: {fallback_e}")
                    return "", []

            chunks = kbinfos["chunks"]
            logging.info(
                f"[SmartExam] Retrieved {len(chunks)} chunks from KB {kb_id}")

            # Log first few chunks for debugging
            for i, chunk in enumerate(chunks[:3]):
                content_preview = chunk.get('content', '')[
                    :100] + '...' if len(chunk.get('content', '')) > 100 else chunk.get('content', '')
                logging.info(
                    f"[SmartExam] Chunk {i+1}: similarity={chunk.get('similarity', 'N/A')}, content_length={len(chunk.get('content', ''))}, preview='{content_preview}'")

            # Extract content with length limits
            content_list = []
            total_chars = 0
            MAX_CONTENT_LENGTH = 20000
            MAX_CHUNK_LENGTH = 2000

            for chunk in chunks:
                # Adapt to new field names: some retrieval pipeline returns 'content_with_weight' instead of 'content'.
                chunk_content = chunk.get('content')
                if not chunk_content:
                    chunk_content = chunk.get('content_with_weight')

                if not chunk_content:
                    logging.warning(
                        f"[SmartExam] Chunk missing 'content'/'content_with_weight' fields: {chunk.keys()}")
                    continue

                if len(chunk_content) > MAX_CHUNK_LENGTH:
                    chunk_content = chunk_content[:MAX_CHUNK_LENGTH] + "..."

                if total_chars + len(chunk_content) > MAX_CONTENT_LENGTH:
                    if content_list:
                        break
                    else:
                        chunk_content = chunk_content[:MAX_CONTENT_LENGTH-100] + "..."

                content_list.append(chunk_content)
                total_chars += len(chunk_content)

                if total_chars >= MAX_CONTENT_LENGTH:
                    break

            final_content = "\n\n".join(content_list)
            logging.info(
                f"[SmartExam] Final extracted content length: {len(final_content)} characters from {len(content_list)} chunks")

            return final_content, chunks

        except Exception as e:
            logging.error(
                f"[SmartExam] Error retrieving from KB {kb_id}: {e}", exc_info=True)
            return "", []

    def _generate_exam_prompt(self, user_content: str, preset_content: str) -> str:
        """Generate the complete prompt for exam generation."""
        param = self._param
        total_questions = self._get_total_questions()
        distribution_prompt = self._get_question_distribution_prompt()
        difficulty_prompt = self._get_difficulty_prompt()

        prompt_parts = [
            f"你是一个专业的试卷命题专家。请基于提供的材料为'{param.exam_name}'生成一份高质量的考试试卷。",
            "",
            f"**基本要求：**",
            f"- 考试名称：{param.exam_name}",
            f"- 考察知识点：{param.knowledge_points}",
            f"- {difficulty_prompt}",
            f"- {distribution_prompt}",
            f"- 总题数：{total_questions}道",
            "",
            "**内容来源分配：**",
            "- 50%的试题应基于用户上传的试题文件内容",
            "- 50%的试题应基于内置试题知识库内容",
            "- 确保试题类型和难度分布均匀",
            ""
        ]

        # Add template information if available
        if param.template_text and param.template_text.strip():
            prompt_parts.extend([
                "**试卷模板参考：**",
                "以下是从试卷模板库中检索到的相关模板，请参考其结构和格式：",
                "```",
                param.template_text,
                "```",
                ""
            ])

        if user_content:
            prompt_parts.extend([
                "**用户上传的试题文件内容（占比50%）：**",
                "```",
                user_content,
                "```",
                ""
            ])

        if preset_content:
            prompt_parts.extend([
                "**内置试题知识库内容（占比50%）：**",
                "```",
                preset_content,
                "```",
                ""
            ])

        prompt_parts.extend([
            "**输出要求：**",
            "1. 严格按照指定的题型和数量生成试题",
            "2. 试题内容要准确、清晰、有针对性",
            "3. 选择题要有明确的选项和正确答案",
            "4. 每道题目都要有详细的解析说明",
            "5. 按照标准试卷格式输出，使用Markdown格式",
            "6. 确保50%试题来源于用户上传内容，50%来源于内置知识库",
            "",
            "请开始生成试卷："
        ])

        return "\n".join(prompt_parts)

    def stream_run(self, history: list = None) -> Generator[str, None, None]:
        """Stream-based execution for real-time generation."""
        param = self._param
        tenant_id = self._canvas.get_tenant_id()

        logging.info(
            f"[SmartExam] Starting stream_run with tenant_id: {tenant_id}")
        logging.info(
            f"[SmartExam] Parameters - user_kb_id: {param.user_kb_id}, preset_kb_id: {param.preset_kb_id}")
        logging.info(
            f"[SmartExam] Knowledge points: '{param.knowledge_points}'")

        try:
            # Initialize LLM
            chat_mdl = LLMBundle(tenant_id, LLMType.CHAT, param.llm_id)
            # Be tolerant to different LLMBundle versions: check both llm_name and model_name
            _mdl_name = getattr(chat_mdl, "llm_name", None) or getattr(
                chat_mdl, "model_name", None)
            if not _mdl_name:
                error_msg = "错误：找不到指定的LLM模型\n"
                logging.error(f"[SmartExam] {error_msg.strip()}")
                yield error_msg
                return

            logging.info(f"[SmartExam] LLM model initialized: {_mdl_name}")

            # Retrieve content from user uploaded KB (50%)
            user_content = ""
            if param.user_kb_id:
                logging.info(
                    f"[SmartExam] Starting user KB retrieval from {param.user_kb_id}")
                user_content, _ = self._retrieve_from_kb(
                    tenant_id, param.user_kb_id, param.knowledge_points, param.user_kb_top_n
                )
                if user_content:
                    logging.info(
                        f"[SmartExam] User KB retrieval successful, content length: {len(user_content)}")
                    yield f"✅ 已检索用户上传试题库内容\n"
                else:
                    logging.warning(
                        f"[SmartExam] User KB retrieval failed or returned empty content")
                    yield f"⚠️ 用户试题库检索无结果，将增加内置库占比\n"
            else:
                logging.warning(f"[SmartExam] No user KB ID provided")

            # Retrieve content from preset KB (50%)
            preset_content = ""
            if param.preset_kb_id:
                logging.info(
                    f"[SmartExam] Starting preset KB retrieval from {param.preset_kb_id}")
                preset_content, _ = self._retrieve_from_kb(
                    tenant_id, param.preset_kb_id, param.knowledge_points, param.preset_kb_top_n
                )
                if preset_content:
                    logging.info(
                        f"[SmartExam] Preset KB retrieval successful, content length: {len(preset_content)}")
                    yield f"✅ 已检索内置试题库内容\n"
                else:
                    logging.warning(
                        f"[SmartExam] Preset KB retrieval failed or returned empty content")
                    yield f"⚠️ 内置试题库检索无结果，将增加用户库占比\n"
            else:
                logging.warning(f"[SmartExam] No preset KB ID provided")

            # Check if we have any content
            if not user_content and not preset_content:
                error_msg = "❌ 错误：无法从任何知识库检索到相关内容，请检查试题文件和知识库配置\n"
                logging.error(f"[SmartExam] {error_msg.strip()}")
                yield error_msg
                return

            # If only one source has content, inform user but continue
            if not user_content and preset_content:
                warning_msg = f"⚠️ 注意：仅从内置试题库检索到内容，将全部使用内置库生成试题\n"
                logging.info(f"[SmartExam] {warning_msg.strip()}")
                yield warning_msg
            elif user_content and not preset_content:
                warning_msg = f"⚠️ 注意：仅从用户试题库检索到内容，将全部使用用户库生成试题\n"
                logging.info(f"[SmartExam] {warning_msg.strip()}")
                yield warning_msg

            logging.info(
                f"[SmartExam] Content summary - user: {len(user_content)} chars, preset: {len(preset_content)} chars")
            yield f"🎯 开始生成试卷...\n\n"

            # Generate exam prompt
            final_prompt = self._generate_exam_prompt(
                user_content, preset_content)

            # Configure LLM parameters
            llm_config = {
                "max_tokens": 8192,
                "temperature": 0.3,
                "stream": True
            }

            # Ensure we have valid messages
            if not final_prompt.strip():
                error_msg = "❌ 错误：无法生成有效的提示内容\n"
                logging.error(f"[SmartExam] {error_msg.strip()}")
                yield error_msg
                return

            logging.info(
                f"[SmartExam] Generated prompt, length: {len(final_prompt)} characters")

            # Stream generation
            messages = [
                {"role": "user", "content": final_prompt}
            ]

            # Use LLM interaction context for logging
            with llm_interaction_context(
                agent_type="smart_exam",
                tenant_id=tenant_id,
                user_id=getattr(self._canvas, 'user_id', tenant_id),
                llm_model=param.llm_id,
                system="",  # No system message for this agent
                history=messages,
                gen_conf=llm_config
            ) as tracker:
                # Use chat_mdl's streaming capability
                if hasattr(chat_mdl, 'chat_streamly'):
                    logging.info(f"[SmartExam] Starting streaming generation")
                    last_content = ""

                    # Wrap the streaming generator with logging
                    stream_generator = chat_mdl.chat_streamly(
                        messages[0]["content"], [], llm_config)
                    logged_stream = log_llm_stream_response(
                        tracker, stream_generator)

                    for chunk in logged_stream:
                        if isinstance(chunk, int):
                            # Token count, skip
                            continue
                        if chunk and isinstance(chunk, str):
                            # Extract delta (incremental content)
                            delta = chunk[len(last_content):]
                            if delta:
                                yield delta
                                last_content = chunk
                else:
                    # Fallback to regular chat if streaming not available
                    logging.info(
                        f"[SmartExam] Streaming not available, using regular chat")
                    response = chat_mdl.chat(
                        messages[0]["content"], [], llm_config)
                    if isinstance(response, tuple):
                        response = response[0]

                    # Log non-streaming response
                    from api.utils.llm_logger import log_llm_response
                    log_llm_response(tracker, response)

                    yield str(response)

        except Exception as e:
            error_msg = f"❌ 生成过程中发生错误：{str(e)}\n"
            logging.error(
                f"[SmartExam] Error in stream_run: {e}", exc_info=True)
            yield error_msg

    def _run(self, history: list = None) -> pd.DataFrame:
        """Non-streaming execution (fallback)."""
        # Collect all streamed content
        content = ""
        for chunk in self.stream_run(history):
            content += chunk

        return SmartExam.be_output(content)
