from __future__ import annotations

from typing import Any, Dict

from api.db.db_models import LessonPlanSession, LessonPlanDoc, DB
from api.utils import get_uuid


class LessonPlanService:
    """Helper CRUD operations for Lesson Plan V2 tables."""

    @staticmethod
    @DB.connection_context()
    def create_session(user_id: str, dsl: Dict[str, Any] | None = None, jobs: Dict[str, Any] | None = None) -> str:
        sid = get_uuid()
        LessonPlanSession.insert(
            id=sid, user_id=user_id, dsl=dsl or {}, jobs=jobs or {}).execute()
        return sid

    @staticmethod
    @DB.connection_context()
    def update_session(sid: str, **fields):
        LessonPlanSession.update(
            **fields).where(LessonPlanSession.id == sid).execute()

    @staticmethod
    @DB.connection_context()
    def create_doc(session_id: str, content_md: str, iteration: int = 0, satisfied: bool = False, source_files=None) -> str:
        did = get_uuid()
        LessonPlanDoc.insert(id=did, session_id=session_id, content_md=content_md, iteration=iteration,
                             satisfied=satisfied, source_files=source_files or []).execute()
        return did

    @staticmethod
    @DB.connection_context()
    def mark_doc_satisfied(doc_id: str):
        LessonPlanDoc.update(satisfied=True).where(
            LessonPlanDoc.id == doc_id).execute()

    @staticmethod
    @DB.connection_context()
    def add_job(sid: str, key: str, job_id: str):
        e, sess = LessonPlanSession.get_or_none(
            LessonPlanSession.id == sid), None
        if not e:
            return
        jobs = sess.jobs or {}
        jobs[key] = job_id
        LessonPlanSession.update(jobs=jobs).where(
            LessonPlanSession.id == sid).execute()
