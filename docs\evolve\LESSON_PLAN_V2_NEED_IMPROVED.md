对照《LESSON_PLAN_V2_FINAL.md》“10 步落地清单”与目前代码，尚有以下缺口／需打磨之处：

──────────────── 1. 灰度发布 / Feature-Flag ────────────────  
• `/lesson-plan-v2/*` 各端点仅限登录但未校验 `X-Lesson-Plan-Version:2`；前端 fetch 亦未携带该 Header。  
• 租户表 `feature_flags` 分流逻辑未落地（owner / beta tenant 白名单等）。

──────────────── 2. 监控与配额 ───────────────────────────  
• Prometheus 指标 `lessonplan_generation_seconds`、`lessonplan_tokens_total` 尚未注册；Celery 任务耗时 / 失败率未暴露。  
• “同用户 iteration_count ≤ 3/day” 只在代码注释中，未真正校验；满意/不满意循环可无限迭代。

──────────────── 3. 前端交互体验 ─────────────────────────  
A. 状态机 UI  
 – 已有内部 status，但缺少可视化（Step/进度条/Skeleton）；`waiting_embedding`、`retrieving_template` 没有加载提示。  
B. 表单 & 按钮启用/禁用  
 – 导出 Word/再次生成按钮在任务 running 或 ask_feedback 阶段仍可点击。  
C. 断线重连增强  
 – 后端已支持 Last-Event-ID，但前端未持久化 cursor；页面刷新或多 Tab 不会自动续传。

──────────────── 4. Celery cleanup 细节 ───────────────────  
• `cleanup_kb_doc` 仅删 DB 记录，未清理对象存储 blob（依赖 Storage 实现）。  
• 若浏览器关闭前端 `cancel_cleanup` 未调用，用户另处下载 Word 后仍会被清理；需在导出 Docx 之外提供显式“保存”入口或后台任务延后检查 Session.status。

──────────────── 5. Prompt & Token 预算 ───────────────────  
• 现按固定阈值裁剪，若模板+课程内容极端长仍可能 > 8192 Tok；缺少“剩余 token 动态分配”策略（优先课程内容 → 反馈 → 模板）。  
• LLM 调参仅固定 `temperature`/`max_tokens`，未暴露 `top_p` 等高级配置给前端。

──────────────── 6. 单元测试覆盖 ─────────────────────────  
• waiting_embedding 循环、Last-Event-ID 断线重连、satisfied=true 分支与 cleanup revoke、超过 3/day 限流 等场景尚未纳入测试集。

──────────────── 7. 文档与示例 ───────────────────────────  
• `docs/evolve/LESSON_PLAN_V2.md` 未创建；API Reference / SDK Example / 前端使用说明缺失。

──────────────── 8. 代码健壮性 / 错误处理 ─────────────────  
• 多处 `except Exception` 直接吞日志，无 JSON 友好错误码。  
• cleanup 任务若在 2 h 内重跑（因 worker 重启）可能重复删除；需幂等保障。

──────────────── 结论 ────────────────────────────────  
核心功能已齐：DAG、三组件、SSE 全事件、文件上传 + embedding、Prompt 进阶、反馈迭代、清理任务、基础测试。  
上线前仍需补完以上 1-8 项，优先顺序建议：

1. 灰度 & 配额  → 2) 监控  → 3) UI 完善  → 4) 单测补全  → 5) Blob 清理  → 6) 文档。

----///////////////////////////////////////////////////////////////////////////////////////----

从功能角度看，当前 Lesson-Plan-V2 Agent 已可跑通“文件上传 → SSE 流式生成 → 反馈迭代 → 临时资源清理”主流程。但若从工程化、可维护与可运维的最佳实践衡量，仍有若干可改进之处，可分为代码质量、架构稳定性、运维可观测性及团队协作四大维度：

──────────────── 1. 代码质量 / 可维护性 ────────────────

1. 完整类型提示  
   • 后端大量 `dict`/`Any`，可引入 Pydantic 或 `TypedDict` 明确输入输出结构，降低后期改动破坏风险。
2. 分层目录结构  
   • `agent_app.py` 现包含 ~900 行路由逻辑，建议拆分为蓝图子模块（upload、stream、feedback、cleanup），并将 SSE 相关工具提取为 `utils/sse.py`。
3. 单一职责原则  
   • `LessonPlan` 组件集成了清洗、裁剪、拼 Prompt、LLM 调用，可拆成子函数或子类，便于 UT 和替换模型。
4. 配置集中  
   • `max_tokens 8192`、`chunk_size 300`、清理倒计时 `7200` 秒散落在代码中；应放到 `conf/lesson_plan.json` 或 `.env` 并用 Dynaconf / Pydantic Settings 加载。
5. 错误码约定  
   • 目前 JSON 返回多用 `code=400/500` 和自然语言；可结合业务错误码（如 10001=PARAM_ERROR, 10002=EMBEDDING_PENDING）便于前端细粒度处理。

──────────────── 2. 稳定性 / 异常处理 ─────────────────

1. 幂等 & 回滚  
   • Celery cleanup、文档上传均需幂等设计；建议为每步写入事务日志，失败可重试或 Rollback。
2. 避免阻塞式轮询  
   • `KnowledgebaseService.is_parsed_done` 轮询 + `time.sleep(2)` 消耗 worker 线程；可改为 Redis Pub/Sub（方案文档已有），或采用 Celery `AsyncResult` 回调。
3. SSE 大文件传输  
   • stream chunk 固定 300 字符，对长文本会导致事件过多；可根据 token 统计动态 chunk 或采用 gzip 压缩传输（需前端解压）。
4. 安全性  
   • 文件上传路径 & 名称未做 XSS / 路径穿透过滤；需要 `secure_filename` 或自定义校验。

──────────────── 3. 运维可观测性 ─────────────────────

1. 统一日志规范  
   • 日志级别建议遵循 `STRUCTURED JSON` 格式，方便 Loki / ELK 检索；SSE 单元日志可带 `session_id` trace_id。
2. Metrics & Tracing  
   • 在调用 LLM、Embedding、ResourceSearch、Celery Task 前后打 `Histogram` 与 `Counter`；同时注入 OpenTelemetry trace_id→ 前端方便定位一次生成全链路。
3. 健康检查  
   • `/healthz` 增加检查 Redis、DB、Celery broker 可达性，否则 Pod 就绪探针失败。

──────────────── 4. 测试 / CI-CD / 文档 ──────────────

1. UT 组织  
   • 当前 UT 放在 `api/test/`，建议以 _Given-When-Then_ 命名，测试名称可读性更强；使用 `pytest-mock` & `freezegun` 模拟时间。
2. 集成测试  
   • 以 Docker Compose 启动 Redis+Celery worker+API，使用 `pytest-asyncio` 播放真实 SSE，保证端到端可靠性。
3. CI Pipeline  
   • 接入 `pre-commit`（black/ruff/isort/mypy），PR 必须触发 UT + lint；Celery task 可跑在 GitHub Action services。
4. 开发者文档  
   • README 中提供 “如何本地启动完整链” 步骤、`.env.sample`；`docs/evolve/LESSON_PLAN_V2.md` 应包含 API 参数、SSE 事件表、错误码表。

──────────────── 总结 ─────────────────────────────  
• 现阶段可上线灰度验证，但建议优先补齐 **配置集中、错误码、幂等/回滚、统一日志** 四项“低成本高收益”改进，再逐步完善监控与 CI-CD。  
• 对于未来团队扩展，早期投入在类型提示、目录分层、测试覆盖，会显著降低后续维护成本。
