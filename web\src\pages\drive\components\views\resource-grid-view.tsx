import { RenameDialog } from '@/components/rename-dialog';
import { UseRowSelectionType } from '@/hooks/logic-hooks/use-row-selection';
import { useDownloadFile, useFetchFileList } from '@/hooks/use-drive-request';
import { IFile } from '@/interfaces/database/file-manager';
import { Inbox } from 'lucide-react';
import { useState } from 'react';
import { EditTagsDialog } from '../../dialogs/edit-tags/edit-tags-dialog';
import { useEditTags } from '../../dialogs/edit-tags/use-edit-tags';
import { useHandleDeleteFile } from '../../features/file-operations/use-delete-file';
import { UseMoveDocumentShowType } from '../../features/file-operations/use-move-file';
import { useRenameCurrentFile } from '../../hooks/hooks';
import { useFilePreview } from '../../hooks/use-file-preview';
import { useResourceNavigation } from '../../hooks/use-resource-navigation';
import { isFolderType } from '../../utils';
import { ResourceCard } from '../cards/resource-card';
import { DeleteConfirmationDialog } from '../delete-confirmation-dialog';
import { DriveFilePreview } from '../file-preview';

type ResourceGridViewProps = Pick<
  ReturnType<typeof useFetchFileList>,
  'files' | 'loading' | 'pagination' | 'setPagination' | 'total'
> &
  Pick<UseRowSelectionType, 'rowSelection' | 'setRowSelection'> &
  UseMoveDocumentShowType;

export function ResourceGridView({
  files,
  loading,
  rowSelection,
  setRowSelection,
  showMoveFileModal,
}: ResourceGridViewProps) {
  const { navigateToFolder } = useResourceNavigation();
  const [deleteFile, setDeleteFile] = useState<IFile | null>(null);

  const { handleRemoveFile } = useHandleDeleteFile();
  const { downloadFile } = useDownloadFile();

  // 文件预览功能
  const { filePreviewVisible, currentFile, showPreview, hidePreview } =
    useFilePreview();

  const {
    fileRenameVisible,
    hideFileRenameModal,
    showFileRenameModal,
    onFileRenameOk,
    initialFileName,
    fileRenameLoading,
  } = useRenameCurrentFile();

  const {
    editTagsVisible,
    editTagsLoading,
    currentFile: currentEditFile,
    showEditTagsModal,
    hideEditTagsModal,
    onEditTagsOk,
  } = useEditTags();

  const handleCardClick = (file: IFile) => {
    if (isFolderType(file.type)) {
      navigateToFolder(file.id);
    }
  };

  const handleSelect = (fileId: string, selected: boolean) => {
    const newSelection = { ...rowSelection };
    if (selected) {
      newSelection[fileId] = true;
    } else {
      delete newSelection[fileId];
    }
    setRowSelection(newSelection);
  };

  const showRenameModal = (file: IFile) => {
    showFileRenameModal(file);
  };

  const showDeleteModal = (file: IFile) => {
    setDeleteFile(file);
  };

  const showPreviewModal = (file: IFile) => {
    showPreview(file);
  };

  const handleDeleteConfirm = async () => {
    if (deleteFile) {
      try {
        const code = await handleRemoveFile([deleteFile.id]);
        if (code === 0) {
          setDeleteFile(null);
        }
      } catch (error) {
        console.error('删除文件失败:', error);
        // 即使失败也关闭对话框，让用户可以重试
        setDeleteFile(null);
      }
    }
  };

  const handleDeleteCancel = () => {
    setDeleteFile(null);
  };

  const handleDownload = (file: IFile) => {
    downloadFile({
      id: file.id,
      filename: file.name,
    });
  };

  // 拆分为文件夹和文件，并按更新时间倒序排序
  const folderList = files
    ?.filter((f) => isFolderType(f.type))
    .sort((a, b) => b.update_time - a.update_time);

  const fileList = files
    ?.filter((f) => !isFolderType(f.type))
    .sort((a, b) => b.update_time - a.update_time);

  if (loading) {
    return (
      <div className="resource-grid-loading">
        {Array.from({ length: 12 }).map((_, index) => (
          <div key={index} className="resource-card-skeleton">
            <div className="skeleton-icon"></div>
            <div className="skeleton-content">
              <div className="skeleton-title"></div>
              <div className="skeleton-subtitle"></div>
              <div className="skeleton-meta"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="resource-grid-container">
      {folderList && folderList.length > 0 && (
        <div className="resource-grid folder-grid mb-4">
          {folderList.map((file) => (
            <ResourceCard
              key={file.id}
              file={file}
              selected={!!rowSelection[file.id]}
              onSelect={(selected) => handleSelect(file.id, selected)}
              onClick={() => handleSelect(file.id, !rowSelection[file.id])}
              onDoubleClick={() => navigateToFolder(file.id)}
              showMoveFileModal={showMoveFileModal}
              showEditTagsModal={showEditTagsModal}
              showRenameModal={showRenameModal}
              showDeleteModal={showDeleteModal}
              showPreviewModal={showPreviewModal}
              onDownload={handleDownload}
            />
          ))}
        </div>
      )}

      {fileList && fileList.length > 0 && (
        <div className="resource-grid file-grid">
          {fileList.map((file) => (
            <ResourceCard
              key={file.id}
              file={file}
              selected={!!rowSelection[file.id]}
              onSelect={(selected) => handleSelect(file.id, selected)}
              onClick={() => handleSelect(file.id, !rowSelection[file.id])}
              showMoveFileModal={showMoveFileModal}
              showEditTagsModal={showEditTagsModal}
              showRenameModal={showRenameModal}
              showDeleteModal={showDeleteModal}
              showPreviewModal={showPreviewModal}
              onDownload={handleDownload}
            />
          ))}
        </div>
      )}

      {files && files.length === 0 && !loading && (
        <div className="resource-empty-state flex flex-col items-center justify-center">
          <div className="empty-icon mb-4">
            <Inbox className="h-16 w-16 mx-auto text-gray-400" />
          </div>
          <h3 className="empty-title">暂无资源文件</h3>
          <p className="empty-description">
            开始上传您的第一个文件或创建文件夹来组织资源
          </p>
        </div>
      )}

      {/* 文件预览对话框 */}
      <DriveFilePreview
        visible={filePreviewVisible}
        onClose={hidePreview}
        file={currentFile}
      />

      {editTagsVisible && (
        <EditTagsDialog
          visible={editTagsVisible}
          hideModal={hideEditTagsModal}
          onOk={onEditTagsOk}
          loading={editTagsLoading}
          file={currentEditFile}
        />
      )}

      {fileRenameVisible && (
        <RenameDialog
          hideModal={hideFileRenameModal}
          onOk={onFileRenameOk}
          initialName={initialFileName}
          loading={fileRenameLoading}
        />
      )}

      <DeleteConfirmationDialog
        open={!!deleteFile}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        title={deleteFile ? `确定要删除 "${deleteFile.name}" 吗？` : ''}
      />
    </div>
  );
}
