#!/usr/bin/env python3
"""Test script to verify enhanced error handling in ExtrapolateService."""

import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))

# Configure logging to see our enhanced logging output
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def test_cleanup_error_handling():
    """Test the _handle_cleanup_errors method."""
    from api.services.extrapolate_service import ExtrapolateService

    print("Testing cleanup error handling...")

    # Test with context
    test_error = Exception("Test cleanup failure")
    context = {"doc_id": "test_doc_123", "user_id": "test_user_456"}

    ExtrapolateService._handle_cleanup_errors(
        "test cleanup operation",
        test_error,
        context
    )

    # Test without context
    ExtrapolateService._handle_cleanup_errors(
        "test cleanup operation without context",
        test_error
    )

    print("✓ Cleanup error handling test completed")


def test_filter_think_blocks():
    """Test the think block filtering functionality."""
    from api.services.extrapolate_service import ExtrapolateService

    print("Testing think block filtering...")

    # Test cases
    test_cases = [
        ("Hello <think>internal thought</think> world",
         False, ("Hello  world", False)),
        ("<think>start thinking", False, ("", True)),
        ("still thinking</think> done", True, (" done", False)),
        ("Normal text without think blocks", False,
         ("Normal text without think blocks", False)),
        ("", False, ("", False)),
    ]

    for input_text, input_mode, expected in test_cases:
        result = ExtrapolateService._filter_think_blocks(
            input_text, input_mode)
        if result == expected:
            print(f"✓ Filter test passed: '{input_text}' -> '{result[0]}'")
        else:
            print(
                f"✗ Filter test failed: '{input_text}' -> expected {expected}, got {result}")

    print("✓ Think block filtering test completed")


if __name__ == "__main__":
    print("Running ExtrapolateService error handling tests...")

    try:
        test_cleanup_error_handling()
        test_filter_think_blocks()
        print("\n✓ All tests completed successfully!")
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
