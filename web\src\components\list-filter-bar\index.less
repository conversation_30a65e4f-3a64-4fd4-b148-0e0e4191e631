@import '@/theme/high-tech-theme.less';

.list-filter-bar {
  // 搜索输入框样式
  .search-input-wrapper {
    // 使用最高优先级的选择器覆盖 Ant Design 样式
    :global {
      // 覆盖外层容器 - 默认状态
      span.ant-input-affix-wrapper,
      .ant-input-affix-wrapper,
      span.ant-input-affix-wrapper.css-dev-only-do-not-override {
        background: #f3f4f6 !important;
        background-color: #f3f4f6 !important;
        border: 1px solid rgba(0, 0, 0, 0) !important;
        border-width: 1px !important;
        border-color: rgba(0, 0, 0, 0) !important;
        box-shadow: none !important;
        border-radius: 8px !important;
        padding: 8px 12px !important;
        transition: border-color 0.2s ease !important;

        // hover 状态
        &:hover {
          background: #f3f4f6 !important;
          background-color: #f3f4f6 !important;
          border: 1px solid rgba(0, 0, 0, 0) !important;
          border-width: 1px !important;
          border-color: rgba(0, 0, 0, 0) !important;
          box-shadow: none !important;
          outline: none !important;
        }

        // focus 状态 - 添加蓝色边框
        &:focus,
        &:focus-within,
        &:active,
        &.ant-input-affix-wrapper-focused,
        &.ant-input-affix-wrapper-borderless {
          background: #f3f4f6 !important;
          background-color: #f3f4f6 !important;
          border: 1px solid #3b82f6 !important;
          border-width: 1px !important;
          border-color: #3b82f6 !important;
          box-shadow: none !important;
          outline: none !important;
        }

        // 覆盖内部输入框
        input.ant-input,
        .ant-input {
          background: transparent !important;
          background-color: transparent !important;
          border: none !important;
          border-width: 0 !important;
          box-shadow: none !important;
          outline: none !important;
          color: #374151 !important;

          &::placeholder {
            color: #9ca3af !important;
          }

          &:hover,
          &:focus,
          &:active {
            background: transparent !important;
            background-color: transparent !important;
            border: none !important;
            border-width: 0 !important;
            box-shadow: none !important;
            outline: none !important;
          }
        }

        // 图标样式
        .ant-input-prefix,
        .ant-input-suffix {
          color: #9ca3af !important;

          .anticon {
            color: #9ca3af !important;
          }
        }
      }
    }
  }
}
