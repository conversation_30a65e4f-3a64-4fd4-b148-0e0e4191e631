# GenerateCube Loading Component

一个基于3D动画效果的自定义loading组件，具有四个立方体循环移动的动画效果。

## 功能特性

- ✨ 3D立方体动画效果
- 🎨 可自定义主体颜色（自动生成阴影色）
- 📏 可自定义尺寸
- 🔄 悬停暂停/激活继续动画
- 💫 平滑的ease-in-out动画
- 🎯 TypeScript支持

## Props

| 属性名      | 类型     | 默认值    | 描述                           |
| ----------- | -------- | --------- | ------------------------------ |
| `className` | `string` | -         | 自定义CSS类名                  |
| `color`     | `string` | `#FED74C` | 立方体主体颜色（十六进制格式） |
| `size`      | `number` | `50`      | 立方体尺寸（像素）             |

## 使用示例

### 基本用法

```tsx
import { GenerateCube } from '@/components/generate-cube';

// 默认黄色，50px大小
<GenerateCube />;
```

### 自定义颜色

```tsx
// 蓝色主题
<GenerateCube color="#3B82F6" />

// 红色主题
<GenerateCube color="#EF4444" />

// 绿色主题
<GenerateCube color="#10B981" />
```

### 自定义尺寸

```tsx
// 小尺寸
<GenerateCube size={30} />

// 大尺寸
<GenerateCube size={70} />
```

### 组合使用

```tsx
<GenerateCube color="#8B5CF6" size={60} className="my-custom-loading" />
```

## 颜色自动生成说明

组件会根据传入的主体颜色自动生成两种装饰颜色：

- `::before` 伪元素：主体颜色减去50的RGB值（较暗）
- `::after` 伪元素：主体颜色减去30的RGB值（中等暗度）

这样确保立方体的3D效果和视觉层次感。

## 交互效果

- **悬停（Hover）**：动画暂停
- **点击（Active）**：动画继续播放

## 技术实现

- 使用CSS模块化
- CSS变量实现动态颜色
- CSS3动画和3D变换
- React Hook和内联样式结合

## 文件结构

```
web/src/components/
├── generate-cube.tsx           # 主组件文件
├── generate-cube.module.css    # 样式文件
├── generate-cube-example.tsx   # 使用示例
└── generate-cube-README.md     # 说明文档
```

## 浏览器兼容性

支持所有现代浏览器，需要CSS3动画和3D变换支持。
