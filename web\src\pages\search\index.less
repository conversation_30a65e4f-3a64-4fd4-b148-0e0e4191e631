.searchPage {
  background-position: center;
  background-size: cover;
  .card {
    width: 100%;
    :global(.ant-card-body) {
      padding: 14px;
    }
    p {
      margin: 0;
    }
  }
  .tag {
    padding: 4px 8px;
    font-size: 14px;
    cursor: pointer;
  }
}

.searchSide {
  position: relative;
  max-width: 400px !important;
  min-width: auto !important;

  :global(.ant-layout-sider-children) {
    height: auto;
  }
  inset-inline-start: 0;

  .modelForm {
    display: flex;
    padding: 24px;
  }

  .checkGroup {
    width: 100%;
    height: 100%;
  }
  .list {
    padding-top: 10px;
    width: 100%;
    height: calc(100vh - 76px);
    overflow: auto;
  }
  .checkbox {
    width: 100%;
  }
  .knowledgeName {
    width: 116px;
    max-width: 270px;
  }
  .embeddingId {
    width: 170px;
  }
}

.firstRenderContent {
  height: 100%;
}

.content {
  height: 100%;
  overflow: auto;
  width: 100%;
  padding: 20px 16% 10px;
  .hide {
    display: none;
  }

  .main {
    margin: 0 auto;
    width: 100%;
    max-width: 1200px;
  }

  .highlightContent {
    .multipleLineEllipsis(2);
    em {
      color: red;
      font-style: normal;
    }
  }
  .documentReference {
    cursor: pointer;
  }
  .pagination {
    padding-bottom: 16px;
  }
}
.answerWrapper {
  margin-top: 16px;
  background: rgba(232 242 251, 1);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  :global(.ant-card-head) {
    background-color: #e6f4ff23;
  }
  & p {
    margin: 0;
  }
}

.input() {
  :global(.ant-input-affix-wrapper) {
    padding: 4px 12px;
    border-start-start-radius: 30px !important;
    border-end-start-radius: 30px !important;
  }
  // :global(.ant-input-group-addon) {
  //   background-color: transparent;
  // }
  input {
    height: 40px;
  }
  button {
    height: 50px !important;
    border-start-end-radius: 30px !important;
    border-end-end-radius: 30px !important;
  }
}

.globalInput {
  width: 600px;
  position: sticky;
  top: 30%;
  z-index: 1;
  .input();
}
.partialInput {
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 1;
  .input();
}

.searchInput {
  :global(.ant-input-search-button) {
    display: none;
  }
}

.appIcon {
  display: inline-block;
  vertical-align: middle;
  width: 60px;
}

.appName {
  vertical-align: middle;
  font-family: var(--font-family);
  font-size: 40px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;

  background: linear-gradient(to right, #095fab 10%, #25abe8 50%, #55c8dd 60%);
  background-size: auto auto;
  background-clip: border-box;
  background-size: 200% auto;
  color: #fff;
  background-clip: text;
  text-fill-color: transparent;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: textclip 1.5s linear infinite;
}

@keyframes textclip {
  to {
    background-position: 200% center;
  }
}

.popupMarkdown {
  width: 60vw;
  max-height: 40vh;
  overflow: auto;
}

.mindMapFloatButton {
  top: 20%;
  width: 60px;
  height: 60px;
  :global(.ant-float-btn-content, .ant-float-btn-icon) {
    width: auto !important;
  }
}
