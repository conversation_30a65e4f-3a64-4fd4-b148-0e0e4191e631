import { ConfirmDeleteDialog } from '@/components/confirm-delete-dialog';
import { Button } from '@/components/ui/button';
import { useDownloadFile } from '@/hooks/use-drive-request';
import { IFile } from '@/interfaces/database/file-manager';
import { getExtension } from '@/utils/document-util';
import { CellContext } from '@tanstack/react-table';
import {
  ArrowDownToLine,
  Eye,
  FolderInput,
  FolderPen,
  Tag,
  Trash2,
} from 'lucide-react';
import { useCallback } from 'react';
import { UseEditTagsReturnType } from '../../dialogs/edit-tags/use-edit-tags';
import { UseMoveDocumentShowType } from '../../features/file-operations/use-move-file';
import {
  UseHandleConnectToKnowledgeReturnType,
  UseRenameCurrentFileReturnType,
} from '../../hooks';
import { UseFilePreviewReturnType } from '../../hooks/use-file-preview';
import { isFolderType, isSupportedPreviewType } from '../../utils';

type IProps = Pick<CellContext<IFile, unknown>, 'row'> &
  Pick<UseHandleConnectToKnowledgeReturnType, 'showConnectToKnowledgeModal'> &
  Pick<UseRenameCurrentFileReturnType, 'showFileRenameModal'> &
  Pick<UseEditTagsReturnType, 'showEditTagsModal'> &
  Pick<UseFilePreviewReturnType, 'showPreview'> &
  UseMoveDocumentShowType;

export function ActionCell({
  row,
  showConnectToKnowledgeModal,
  showFileRenameModal,
  showMoveFileModal,
  showEditTagsModal,
  showPreview,
}: IProps) {
  const record = row.original;
  const documentId = record.id;
  const { downloadFile } = useDownloadFile();
  const isFolder = isFolderType(record.type);
  const extension = getExtension(record.name);
  const canPreview =
    !isFolder && isSupportedPreviewType(record.name, record.type);

  const handleShowConnectToKnowledgeModal = useCallback(() => {
    showConnectToKnowledgeModal(record);
  }, [record, showConnectToKnowledgeModal]);

  const onDownloadDocument = useCallback(() => {
    downloadFile({
      id: documentId,
      filename: record.name,
    });
  }, [documentId, downloadFile, record.name]);

  const handleShowFileRenameModal = useCallback(() => {
    showFileRenameModal(record);
  }, [record, showFileRenameModal]);

  const handleShowMoveFileModal = useCallback(() => {
    showMoveFileModal([record.id]);
  }, [record, showMoveFileModal]);

  const handleShowEditTagsModal = useCallback(() => {
    showEditTagsModal(record);
  }, [record, showEditTagsModal]);

  const handleShowPreview = useCallback(() => {
    showPreview(record);
  }, [record, showPreview]);

  return (
    <section className="flex gap-4 items-center text-text-sub-title-invert">
      <Button variant="ghost" size={'sm'} onClick={handleShowMoveFileModal}>
        <FolderInput />
      </Button>

      <Button variant="ghost" size={'sm'} onClick={handleShowFileRenameModal}>
        <FolderPen />
      </Button>

      <Button variant="ghost" size={'sm'} onClick={handleShowEditTagsModal}>
        <Tag />
      </Button>

      {isFolder || (
        <Button variant={'ghost'} size={'sm'} onClick={onDownloadDocument}>
          <ArrowDownToLine />
        </Button>
      )}

      {canPreview && (
        <Button variant={'ghost'} size={'sm'} onClick={handleShowPreview}>
          <Eye />
        </Button>
      )}

      <ConfirmDeleteDialog>
        <Button variant="ghost" size={'sm'}>
          <Trash2 />
        </Button>
      </ConfirmDeleteDialog>
    </section>
  );
}
