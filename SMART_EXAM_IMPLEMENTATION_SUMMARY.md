# 智能组卷功能实现总结

## 概述

根据「教案生成 V2」的后台架构，实现了「智能组卷」功能，支持用户上传试题文件与内置试题知识库的混合检索生成，实现 50%/50%的试题比例分配。

## 架构设计

### 1. 组件层 (Component Layer)

- **文件**: `agent/component/smart_exam.py`
- **主要类**:
  - `SmartExamParam`: 参数配置类
  - `SmartExam`: 核心组件类，支持流式生成

### 2. 服务层 (Service Layer)

- **文件**: `api/services/smart_exam_service.py`
- **主要类**: `SmartExamService`
- **功能**:
  - 文件上传与 embedding 处理
  - 流式响应生成
  - 清理任务调度

### 3. API 层 (API Layer)

- **文件**: `api/apps/agent_app.py`
- **路由**:
  - `POST /v1/agent/smart-exam/start` - 开始生成
  - `POST /v1/agent/smart-exam/<session_id>/cancel_cleanup` - 取消清理

### 4. 前端层 (Frontend Layer)

- **文件**: `web/src/pages/agent/smart-exam/index.tsx`
- **功能**: 流式 SSE 响应处理，Markdown 渲染

## 业务流程

### 1. 文件上传与处理

```mermaid
graph TD
    A[用户上传试题文件] --> B[文件解析与内容提取]
    B --> C[创建用户知识库]
    C --> D[异步embedding处理]
    D --> E[调度2小时后清理任务]
```

### 2. 知识检索与生成

```mermaid
graph TD
    A[接收生成请求] --> B[从用户KB检索内容50%]
    B --> C[从内置KB检索内容50%]
    C --> D[构建生成prompt]
    D --> E[LLM流式生成]
    E --> F[返回试卷内容]
```

## 核心特性

### 1. 混合检索机制

- **用户上传内容**: 50%试题来源，通过向量检索获取
- **内置知识库**: 50%试题来源，从`tenant-exam-question`检索
- **内容分配**: 智能平衡两个来源的试题分布

### 2. 流式生成

- 支持 SSE 流式响应
- 实时显示生成进度
- 边生成边显示内容

### 3. 文件处理

- 支持多种格式：Excel (.xlsx/.xls/.csv)
- 自动编码检测（UTF-8/GBK）
- 异步 embedding 处理
- 自动清理机制

### 4. 试题配置

- **出题方式**: 随机出题 / 按题型出题
- **难度等级**: 低/中/高三个等级
- **题型支持**: 单选、多选、填空、判断、简答、排序

## 技术实现

### 1. 组件参数配置

```python
class SmartExamParam(ComponentParamBase):
    def __init__(self):
        self.llm_id = "deepseek-r1:32b@Ollama"
        self.exam_name = ""  # 试卷名称
        self.knowledge_points = ""  # 知识点
        self.difficulty = "medium"  # 难度
        self.question_method = "random"  # 出题方式
        self.user_kb_top_n = 15  # 用户库检索数量
        self.preset_kb_top_n = 15  # 内置库检索数量
```

### 2. 流式生成实现

```python
def stream_run(self, history: list = None) -> Generator[str, None, None]:
    # 检索用户上传内容
    user_content, _ = self._retrieve_from_kb(
        tenant_id, param.user_kb_id, param.knowledge_points, param.user_kb_top_n
    )

    # 检索内置知识库内容
    preset_content, _ = self._retrieve_from_kb(
        tenant_id, param.preset_kb_id, param.knowledge_points, param.preset_kb_top_n
    )

    # 构建prompt并流式生成
    final_prompt = self._generate_exam_prompt(user_content, preset_content)
    for chunk in chat_mdl.chat_streamly(final_prompt, [], llm_config):
        yield chunk
```

### 3. 前端流式处理

```typescript
// SSE流处理
const reader = resp.body?.getReader()
const decoder = new TextDecoder('utf-8')
let accumContent = ''

while (true) {
  const { value, done } = await reader.read()
  if (done) break

  const chunk = decoder.decode(value, { stream: true })
  // 解析SSE事件并更新UI
  switch (eventName) {
    case 'stream':
      accumContent += data.chunk || ''
      setGeneratedExam(accumContent)
      break
  }
}
```

## 配置文件

### 1. 组件模板

- **文件**: `agent/templates/smart_exam.json`
- **用途**: Canvas 流程配置模板

### 2. 前端服务接口

- **文件**: `web/src/services/agent_service.ts`
- **接口**: `SmartExamGenerateParams`

## 部署说明

### 1. 数据库要求

- 无需额外数据库表（复用现有知识库表）
- 依赖现有的 Document、File、Knowledgebase 表

### 2. 知识库要求

- 需要预置`tenant-exam-question`知识库
- 包含各种题型的试题范例

### 3. 环境依赖

- 支持文件上传的存储服务
- Celery 异步任务队列（用于 embedding 和清理）
- 支持流式响应的 LLM 服务

## 使用流程

1. **准备试题文件**: 上传 Excel 格式的试题文件
2. **配置参数**: 设置试卷名称、知识点、难度等
3. **选择出题方式**: 随机出题或按题型分配
4. **开始生成**: 点击"一键组卷"按钮
5. **实时查看**: 观察流式生成过程
6. **导出结果**: 生成完成后可导出试卷

## 扩展性

### 1. 支持的扩展

- 更多文件格式支持
- 自定义试题比例分配
- 多知识库混合检索
- 试卷模板定制

### 2. 集成能力

- 可与现有知识库系统无缝集成
- 支持租户级别的配置隔离
- 兼容现有的 LLM 和 embedding 服务

---

**实现完成日期**: 2024 年 12 月
**技术栈**: Python + Flask + React + TypeScript
**架构模式**: 微服务 + 流式处理 + 组件化设计
