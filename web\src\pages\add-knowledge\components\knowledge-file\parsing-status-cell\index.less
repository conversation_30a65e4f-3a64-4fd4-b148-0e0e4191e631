@import '@/theme/high-tech-theme.less';

.popoverContent {
  width: 40vw;
  max-width: 500px;
  padding: var(--spacing-md);

  .popoverContentItem {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-sm);

    b {
      color: var(--text-primary);
      min-width: 120px;
    }
  }

  .popoverContentText {
    white-space: pre-line;
    max-height: 50vh;
    overflow: auto;
    padding: var(--spacing-sm);
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-md);
    font-family: monospace;
    font-size: 0.9rem;
    color: var(--text-secondary);

    .popoverContentErrorLabel {
      color: var(--error-color);
      font-weight: 500;
    }
  }
}

.operationIcon {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
  font-size: 14px; // 控制icon font的大小
  color: #6b7280;

  &:hover {
    cursor: pointer;
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
    transform: scale(1.1);
  }
}

.operationIconSpin {
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .popoverContentText {
    background-color: var(--neutral-700);
  }

  .operationIcon {
    color: #9ca3af;

    &:hover {
      background-color: rgba(59, 130, 246, 0.15);
      color: var(--primary-color);
    }
  }
}
