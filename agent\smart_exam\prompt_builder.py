from __future__ import annotations

"""Prompt building utilities for Smart Exam generation.

This layer converts an `ExamContext` with retrieved content into an LLM-ready prompt.
The builder is *pure* (no side effects) which makes it easy to unit-test.
"""

from typing import List
from .models import ExamContext, Diffic<PERSON>y, QuestionMethod


class PromptBuilder:
    """Abstract base class of prompt builder."""

    def __init__(self, ctx: ExamContext):
        self.ctx = ctx

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------
    def build(self) -> str:
        """Return full prompt string."""
        raise NotImplementedError

    # ------------------------------------------------------------------
    # Helpers (can be reused by subclasses)
    # ------------------------------------------------------------------
    def _difficulty_desc(self) -> str:
        mapping = {
            Difficulty.LOW: "简单（基础概念理解）",
            Difficulty.MEDIUM: "中等（知识点应用）",
            Difficulty.HIGH: "困难（综合分析）",
        }
        return mapping.get(self.ctx.difficulty, "中等（知识点应用）")

    def _distribution_desc(self) -> str:
        dist = self.ctx.distribution
        parts: List[str] = []
        if dist.single_choice:
            parts.append(f"{dist.single_choice}道单选题")
        if dist.multiple_choice:
            parts.append(f"{dist.multiple_choice}道多选题")
        if dist.fill_blank:
            parts.append(f"{dist.fill_blank}道填空题")
        if dist.true_false:
            parts.append(f"{dist.true_false}道判断题")
        if dist.short_answer:
            parts.append(f"{dist.short_answer}道简答题")
        if dist.ordering:
            parts.append(f"{dist.ordering}道排序题")

        if not parts:
            # fallback random method desc
            return f"随机生成{self.ctx.total_questions()}道试题，题型可以包括单选题、多选题、填空题、判断题、简答题等。"

        return "生成试题的类型及数量为：" + "、".join(parts) + "。"


class MarkdownPromptBuilder(PromptBuilder):
    """Concrete builder that outputs prompt in Chinese with markdown fences."""

    def build(self) -> str:  # noqa: C901  (complexity acceptable for builder)
        ctx = self.ctx
        prompt_parts: List[str] = [
            f"你是一个专业的试卷命题专家。请基于提供的材料为'{ctx.exam_name}'生成一份高质量的考试试卷。",
            "",
            "**基本要求：**",
            f"- 考试名称：{ctx.exam_name}",
            f"- 考察知识点：{ctx.knowledge_points}",
            f"- 试题难度要求：{self._difficulty_desc()}",
            f"- {self._distribution_desc()}",
            f"- 总题数：{ctx.total_questions()}道",
            "",
            "**内容来源分配：**",
            "- 50%的试题应基于用户上传的试题文件内容",
            "- 50%的试题应基于内置试题知识库内容",
            "- 确保试题类型和难度分布均匀",
            "",
        ]

        if ctx.template_text.strip():
            prompt_parts.extend(
                [
                    "**试卷模板参考：**",
                    "以下是从试卷模板库中检索到的相关模板，请参考其结构和格式：",
                    "```",
                    ctx.template_text,
                    "```",
                    "",
                ]
            )

        if ctx.user_content.strip():
            prompt_parts.extend(
                [
                    "**用户上传的试题文件内容（占比50%）：**",
                    "```",
                    ctx.user_content,
                    "```",
                    "",
                ]
            )

        if ctx.preset_content.strip():
            prompt_parts.extend(
                [
                    "**内置试题知识库内容（占比50%）：**",
                    "```",
                    ctx.preset_content,
                    "```",
                    "",
                ]
            )

        prompt_parts.extend(
            [
                "**输出要求：**",
                "1. 严格按照指定的题型和数量生成试题",
                "2. 试题内容要准确、清晰、有针对性",
                "3. 选择题要有明确的选项和正确答案",
                "4. 每道题目都要有详细的解析说明",
                "5. 按照标准试卷格式输出，使用Markdown格式",
                "6. 确保50%试题来源于用户上传内容，50%来源于内置知识库",
                "",
                "请开始生成试卷：",
            ]
        )

        return "\n".join(prompt_parts)
