"""Custom error & logging helpers for unified error handling across services."""

from __future__ import annotations

import logging
import re
from typing import Any

__all__ = [
    "UserVisibleError",
    "InternalError",
    "redact",
]


class UserVisibleError(Exception):
    """Exception raised for expected user-facing errors (4xx)."""

    def __init__(self, message: str, code: int = 400):
        super().__init__(message)
        self.code = code


class InternalError(Exception):
    """Exception raised for unexpected server-side failures (5xx)."""

    def __init__(self, message: str = "Internal server error", *args: Any):
        super().__init__(message, *args)
        self.code = 500


# ------------------------------------------------------------------
# Logging redactor utilities
# ------------------------------------------------------------------


SENSITIVE_PATTERNS = [
    re.compile(r"(?:sk-|rk-)[A-Za-z0-9]{16,}"),  # API keys like sk-***
    # JWT tokens
    re.compile(r"(?:Bearer )?[A-Za-z0-9\-_=]{20,}\.[A-Za-z0-9\-_=]{10,}"),
]


def redact(text: str) -> str:
    """Redact common sensitive substrings from log text."""
    redacted = text
    for pattern in SENSITIVE_PATTERNS:
        redacted = pattern.sub("***", redacted)
    # Truncate extremely long prompt to avoid log pollution
    MAX_LEN = 500
    if len(redacted) > MAX_LEN:
        redacted = redacted[:MAX_LEN] + "...<truncated>"
    return redacted


# convenience wrapper

def log_info_redacted(msg: str, *args, **kwargs):
    logging.info(redact(msg), *args, **kwargs)
