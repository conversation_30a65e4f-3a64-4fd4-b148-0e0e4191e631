@import '@/theme/high-tech-theme.less';

.testingControlWrapper {
  width: 350px;
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-lg);
  overflow: auto;
  height: calc(100vh - 180px);
  border: 1px solid var(--border-color);

  .title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
  }

  .description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    font-size: 0.9rem;
  }

  .historyTitle {
    padding: var(--spacing-lg) 0 var(--spacing-md);
  }

  .historyIcon {
    vertical-align: middle;
    color: var(--primary-color);
  }

  .historyCardWrapper {
    width: 100%;
  }

  .historyCard {
    width: 100%;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
    }

    :global(.ant-card-body) {
      padding: var(--spacing-sm);
    }
  }

  .historyText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    color: var(--text-secondary);
  }

  :global {
    .ant-card-head-title {
      font-weight: 600;
      color: var(--text-primary);
    }

    .ant-card {
      border-radius: var(--border-radius-md);
      border-color: var(--border-color);
    }

    .ant-btn-primary {
      background: var(--primary-color);

      &:hover {
        background: var(--primary-hover);
      }
    }
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .testingControlWrapper {
    background-color: var(--neutral-800);

    .historyCard {
      background-color: var(--neutral-800);
    }
  }
}
