#!/usr/bin/env python3
"""
简化的Llama.cpp Embedding测试脚本
"""

import requests
import numpy as np
import json

def num_tokens_from_string(text):
    """简单的token计数估算"""
    return len(text.split())

class LlamaCppEmbed:
    def __init__(self, key, model_name, base_url=None):
        if not base_url:
            raise ValueError("Llama.cpp embedding model url cannot be None")
        self.key = key
        self.model_name = model_name.split("___")[0] if model_name else "llama-cpp-embedding"
        self.base_url = base_url.rstrip('/')
        
    def encode(self, texts: list):
        embeddings = []
        token_count = 0
        for text in texts:
            response = requests.post(
                f"{self.base_url}/embedding",
                json={"content": text},
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            if response.status_code == 200:
                result = response.json()
                # 根据您提供的响应格式: [{"index":0,"embedding":[[-0.6760782599449158,1.340750813484192,...]]}]
                if isinstance(result, list) and len(result) > 0 and "embedding" in result[0]:
                    embedding = result[0]["embedding"][0]  # 取第一个embedding向量
                    embeddings.append(embedding)
                    token_count += num_tokens_from_string(text)
                else:
                    raise Exception(f"Unexpected response format: {result}")
            else:
                raise Exception(f"Llama.cpp embedding error: {response.status_code} - {response.text}")
        
        return np.array(embeddings), token_count
    
    def encode_queries(self, text):
        response = requests.post(
            f"{self.base_url}/embedding",
            json={"content": text},
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        if response.status_code == 200:
            result = response.json()
            if isinstance(result, list) and len(result) > 0 and "embedding" in result[0]:
                embedding = result[0]["embedding"][0]
                return np.array(embedding), num_tokens_from_string(text)
            else:
                raise Exception(f"Unexpected response format: {result}")
        else:
            raise Exception(f"Llama.cpp embedding error: {response.status_code} - {response.text}")

def test_llamacpp_embedding():
    """测试LlamaCpp embedding模型"""
    
    # 配置参数
    base_url = "http://192.168.3.124:8080"
    model_name = "llama-cpp-embedding"
    api_key = "dummy"
    
    print(f"测试Llama.cpp Embedding模型")
    print(f"Base URL: {base_url}")
    print(f"Model Name: {model_name}")
    print("-" * 50)
    
    try:
        # 初始化模型
        embed_model = LlamaCppEmbed(
            key=api_key,
            model_name=model_name,
            base_url=base_url
        )
        print("✓ 模型初始化成功")
        
        # 测试单个文本embedding
        test_text = "从另一台主机发来的请求"
        print(f"\n测试文本: {test_text}")
        
        embedding, token_count = embed_model.encode_queries(test_text)
        print(f"✓ 单文本embedding成功")
        print(f"  - Embedding维度: {embedding.shape}")
        print(f"  - Token数量: {token_count}")
        print(f"  - Embedding前5个值: {embedding[:5]}")
        
        # 测试批量文本embedding
        test_texts = [
            "从另一台主机发来的请求",
            "这是第二个测试文本"
        ]
        print(f"\n测试批量文本: {test_texts}")
        
        embeddings, total_tokens = embed_model.encode(test_texts)
        print(f"✓ 批量embedding成功")
        print(f"  - Embeddings形状: {embeddings.shape}")
        print(f"  - 总Token数量: {total_tokens}")
        print(f"  - 第一个embedding前5个值: {embeddings[0][:5]}")
        
        print("\n" + "=" * 50)
        print("✓ 所有测试通过！Llama.cpp embedding适配器工作正常")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        print("注意：如果是网络连接错误，这是正常的，因为测试环境无法访问您的服务器")
        return False
    
    return True

if __name__ == "__main__":
    success = test_llamacpp_embedding()
    print(f"\n测试结果: {'成功' if success else '失败（预期的网络错误）'}")
