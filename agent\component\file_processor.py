#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import io
import logging
import pandas as pd
from agent.component.base import ComponentBase, ComponentParamBase


class FileProcessorParam(ComponentParamBase):
    """
    Define the FileProcessor component parameters.
    """
    def __init__(self):
        super().__init__()
        self.file_input_key = "file_content"  # Key for file input from Begin component
        self.max_content_length = 50000  # Maximum content length to prevent token overflow
        self.supported_formats = ["txt", "doc", "docx", "pdf", "xlsx", "xls", "csv"]
        
    def check(self):
        self.check_string(self.file_input_key, "File input key")
        self.check_positive_integer(self.max_content_length, "Max content length")


class FileProcessor(ComponentBase):
    component_name = "FileProcessor"

    def _run(self, history, **kwargs):
        """
        Execute the file processor component to parse uploaded files.
        """
        try:
            # Get file content from input
            file_content = self._get_file_content()
            
            if not file_content:
                return FileProcessor.be_output("未提供课程材料文件")
            
            # Parse file content
            parsed_content = self._parse_file_content(file_content)
            
            # Limit content length to prevent token overflow
            if len(parsed_content) > self._param.max_content_length:
                parsed_content = parsed_content[:self._param.max_content_length] + "\n\n[内容过长，已截断...]"
            
            return FileProcessor.be_output(parsed_content)
            
        except Exception as e:
            logging.error(f"FileProcessor component error: {str(e)}")
            return FileProcessor.be_output(f"文件处理失败: {str(e)}")

    def _get_file_content(self):
        """
        Get file content from Begin component or input.
        """
        try:
            # Try to get from Begin component
            inputs = self.get_input()
            if "content" in inputs and len(inputs["content"]) > 0:
                content = inputs["content"][0]
                if isinstance(content, str) and content.strip():
                    return content
            
            return None
            
        except Exception as e:
            logging.error(f"Failed to get file content: {str(e)}")
            return None

    def _parse_file_content(self, file_content: str) -> str:
        """
        Parse file content based on file type indicators in the content.
        """
        if not file_content or not isinstance(file_content, str):
            return "无效的文件内容"
        
        try:
            # If content looks like it's already text, return as is
            if self._is_plain_text(file_content):
                return file_content
            
            # Try to determine file type from content
            if self._is_csv_content(file_content):
                return self._parse_csv_content(file_content)
            elif self._is_excel_content(file_content):
                return self._parse_excel_content(file_content)
            else:
                # Treat as plain text
                return file_content
                
        except Exception as e:
            logging.error(f"Failed to parse file content: {str(e)}")
            return f"文件解析失败: {str(e)}"

    def _is_plain_text(self, content: str) -> bool:
        """
        Check if content is already plain text.
        """
        # Simple heuristic: if content doesn't contain binary markers, treat as text
        binary_markers = ['\x00', '\xff', '\xfe', 'PK\x03\x04']  # Common binary file markers
        return not any(marker in content for marker in binary_markers)

    def _is_csv_content(self, content: str) -> bool:
        """
        Check if content looks like CSV.
        """
        lines = content.strip().split('\n')
        if len(lines) < 2:
            return False
        
        # Check if first few lines have consistent comma/tab separators
        first_line_sep_count = content.count(',') if ',' in content else content.count('\t')
        return first_line_sep_count > 0

    def _is_excel_content(self, content: str) -> bool:
        """
        Check if content is Excel binary data.
        """
        return content.startswith('PK') or 'xl/' in content

    def _parse_csv_content(self, content: str) -> str:
        """
        Parse CSV content into readable text.
        """
        try:
            # Try different separators
            for sep in [',', '\t', ';']:
                try:
                    df = pd.read_csv(io.StringIO(content), sep=sep, encoding='utf-8')
                    if len(df.columns) > 1:  # Successful parsing
                        return self._format_dataframe(df)
                except:
                    continue
            
            # If parsing fails, return as is
            return content
            
        except Exception as e:
            logging.error(f"CSV parsing failed: {str(e)}")
            return content

    def _parse_excel_content(self, content: str) -> str:
        """
        Parse Excel content (this is a placeholder - actual Excel parsing would need binary data).
        """
        return "Excel文件内容（需要二进制数据进行解析）"

    def _format_dataframe(self, df: pd.DataFrame) -> str:
        """
        Format DataFrame into readable text.
        """
        try:
            # Convert DataFrame to string with better formatting
            formatted = "## 表格数据\n\n"
            
            # Add column headers
            formatted += "| " + " | ".join(str(col) for col in df.columns) + " |\n"
            formatted += "|" + "---|" * len(df.columns) + "\n"
            
            # Add rows (limit to prevent too much content)
            max_rows = min(50, len(df))
            for i in range(max_rows):
                row = df.iloc[i]
                formatted += "| " + " | ".join(str(val) for val in row.values) + " |\n"
            
            if len(df) > max_rows:
                formatted += f"\n[表格包含 {len(df)} 行数据，仅显示前 {max_rows} 行]\n"
            
            return formatted
            
        except Exception as e:
            logging.error(f"DataFrame formatting failed: {str(e)}")
            return df.to_string(index=False, max_rows=50) 