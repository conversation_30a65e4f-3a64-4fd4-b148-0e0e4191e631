@import '@/theme/high-tech-theme.less';

.testingResultWrapper {
  flex: 1;
  background-color: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  padding: var(--spacing-lg);
  overflow: auto;
  height: calc(100vh - 180px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid var(--border-color);

  .selectFilesCollapse {
    :global(.ant-collapse-header) {
      padding-left: var(--spacing-md);
      background-color: var(--background-tertiary);
      border-radius: var(--border-radius-md);
      transition: all var(--transition-normal);

      &:hover {
        background-color: var(--background-tertiary);
      }
    }

    :global(.ant-collapse-content) {
      background-color: var(--card-background);
      border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
    }

    margin-bottom: var(--spacing-lg);
    overflow-y: auto;
  }

  .selectFilesTitle {
    padding-right: var(--spacing-sm);
    color: var(--text-primary);
    font-weight: 500;
  }

  .similarityCircle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0 var(--spacing-xs);
  }

  .similarityText {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--text-secondary);
  }

  .chunkCard {
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-color);
    overflow: hidden;

    &:hover {
      border-color: var(--primary-color);
      transform: translateY(-2px);
      box-shadow: var(--card-hover-shadow);
    }

    :global(.ant-card-head) {
      background-color: var(--background-tertiary);
      border-bottom-color: var(--border-color);
      min-height: 40px;

      .ant-card-head-title {
        padding: var(--spacing-sm) 0;
      }
    }
  }

  .image {
    width: 100px;
    height: 100px;
    object-fit: contain;
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);

    &:hover {
      border-color: var(--primary-color);
    }
  }
}

.imagePreview {
  display: block;
  max-width: 45vw;
  max-height: 40vh;
  border-radius: var(--border-radius-md);
}

/* Dark mode adjustments */
:global(.dark) {
  .testingResultWrapper {
    background-color: var(--neutral-800);

    .selectFilesCollapse {
      :global(.ant-collapse-header) {
        background-color: var(--neutral-700);
      }

      :global(.ant-collapse-content) {
        background-color: var(--neutral-800);
      }
    }

    .similarityCircle {
      background-color: rgba(59, 130, 246, 0.2);
    }

    .chunkCard {
      background-color: var(--neutral-800);

      :global(.ant-card-head) {
        background-color: var(--neutral-700);
      }
    }
  }
}
