import classNames from 'classnames';
import React from 'react';
import './CircleSpin.less';
import { CircleSpinProps } from './types';

const CircleSpin: React.FC<CircleSpinProps> = ({
  spinning = false,
  tip,
  size = 'default',
  className,
  style,
  children,
}) => {
  // 渲染CSS loading圆环
  const renderLoadingCircle = () => (
    <span className="loading-circle">
      <span className="outerCircle"></span>
    </span>
  );

  // 如果不是loading状态，直接返回子组件
  if (!spinning) {
    return <>{children}</>;
  }

  // 如果没有子组件，只显示loading指示器
  if (!children) {
    return (
      <div
        className={classNames('circle-spin-standalone', className)}
        style={style}
      >
        <div className={classNames('circle-spin-indicator', `size-${size}`)}>
          {renderLoadingCircle()}
        </div>
        {tip && <div className="circle-spin-tip">{tip}</div>}
      </div>
    );
  }

  // 包装模式：有子组件时，创建遮罩层
  return (
    <div
      className={classNames('circle-spin-container', className)}
      style={style}
    >
      <div className="circle-spin-content">{children}</div>
      <div className="circle-spin-overlay">
        <div className={classNames('circle-spin-indicator', `size-${size}`)}>
          {renderLoadingCircle()}
        </div>
        {tip && <div className="circle-spin-tip">{tip}</div>}
      </div>
    </div>
  );
};

export default CircleSpin;
