@import '@/theme/high-tech-theme.less';

.messageItem {
  padding: var(--spacing-lg) 0;
  position: relative;

  .messageItemSection {
    display: inline-block;
  }

  .messageItemSectionLeft {
    width: 100%;
  }

  .assistantHeader {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-sm);

    .assistantAvatar {
      border: 2px solid var(--primary-light);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .assistantTitle {
      font-size: var(--font-size-md);
      .gradient-text(var(--primary-color), var(--accent-color));
    }
  }

  .assistantActionButtons {
    margin-top: 0;
    padding-left: 0;
    position: relative;
    /* Align with message content edge */
  }

  .userActionButtons {
    margin-top: 8px;
    text-align: right;
    /* Adjust to right align for user messages */
    padding-right: 0;
    margin-right: 0;
    /* Align with user message content */
  }

  .messageItemContent {
    display: inline-flex;
    gap: var(--spacing-sm);
    align-items: flex-start;
    width: 100%;
  }

  .messageItemContentReverse {
    flex-direction: row-reverse;
  }

  .messageTextBase() {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    position: relative;
    transition: all var(--transition-normal);

    &:hover {
      box-shadow: var(--card-hover-shadow);
    }

    & > p {
      margin: 0;
    }
  }

  .messageText {
    background: transparent;
    border: none;
    padding: 0;
    word-break: break-word;
    position: relative;
    font-size: 16px;
    line-height: 1.75;
    color: #333;
    box-shadow: none;
    border-radius: 0;
    transition: none;

    &:hover {
      box-shadow: none;
    }

    &::before {
      display: none;
    }

    & > p {
      margin-bottom: 12px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: #111;
      font-weight: 600;
      margin: 16px 0 8px 0;
    }

    ul,
    ol {
      margin: 12px 0;
      padding-left: 20px;
    }

    li {
      margin-bottom: 4px;
    }

    code {
      background-color: #f1f3f4;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 14px;
    }

    pre {
      background-color: #f8f9fa;
      padding: 16px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 12px 0;
    }
  }

  .messageTextDark {
    .messageTextBase();
    background: linear-gradient(
      135deg,
      rgba(30, 64, 175, 0.6) 0%,
      rgba(29, 78, 216, 0.8) 100%
    );
    border-left: 3px solid var(--primary-light);
    word-break: break-word;
    border-bottom-left-radius: 0;

    &::before {
      content: '';
      position: absolute;
      left: -10px;
      bottom: 0;
      width: 10px;
      height: 20px;
      border-bottom-right-radius: var(--border-radius-md);
      box-shadow: 5px 5px 0 0 rgba(29, 78, 216, 0.8);
    }

    :global(section.think) {
      color: var(--neutral-300);
      border-left-color: var(--neutral-600);
    }

    :global(.snippet-container) {
      background-color: rgba(15, 23, 42, 0.6);
      border-color: rgba(37, 99, 235, 0.4);
    }
  }

  .messageUserText {
    background-color: #f1f3f4;
    border-radius: 20px 4px 20px 20px;
    padding: 12px 16px;
    word-break: break-word;
    text-align: left;
    font-size: 16px;
    line-height: 1.75;
    color: #333;
    max-width: 80%;
    width: fit-content;
    margin-left: auto;
    margin-right: 0;
    box-shadow: none;
    border: none;
    transition: none;

    &:hover {
      box-shadow: none;
    }

    &::before {
      display: none;
    }

    & > p {
      margin: 0;
    }
  }

  .messageEmpty {
    width: 300px;
  }

  .thumbnailImg {
    max-width: 32px;
    border-radius: var(--border-radius-sm);
  }

  .legacyDocList {
    display: none;
    /* Hide legacy document list when using source cards */
  }
}

.messageItemLeft {
  text-align: left;

  &::after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      var(--border-color) 20%,
      var(--border-color) 80%,
      transparent
    );
    margin-top: var(--spacing-md);
    opacity: 0.6;
  }
}

.messageItemRight {
  text-align: right;

  .messageItemSection {
    width: 100%;
    text-align: right;
  }

  .messageItemContent {
    justify-content: flex-end;
  }

  .messageUserText {
    display: block;
    max-width: 80%;
    width: fit-content;
    margin-left: auto;
    margin-right: 0;
  }
}

/* Dark mode overrides */
:global(.dark) .messageItem {
  .messageUserText {
    background: linear-gradient(
      135deg,
      var(--neutral-800) 0%,
      var(--neutral-900) 100%
    );

    &::before {
      box-shadow: -5px 5px 0 0 var(--neutral-900);
    }
  }
}

/* Global message styling overrides */
:global {
  .message-actions {
    margin-top: 0;
    display: inline-block;

    .ant-btn {
      border-radius: var(--border-radius-md);
      transition: all var(--transition-normal);

      &:hover {
        transform: translateY(-2px);
      }
    }
  }

  .reference-list-container {
    margin-top: var(--spacing-md);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1px solid var(--border-color);

    .ant-list-header {
      background-color: var(--background-secondary);
      font-weight: 600;
      padding: var(--spacing-xs) var(--spacing-md);
    }

    .ant-list-item {
      transition: all var(--transition-normal);

      &:hover {
        background-color: var(--primary-ultralight);
      }
    }
  }

  // Code block styling
  .snippet-container {
    background-color: var(--background-secondary);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    margin: var(--spacing-md) 0;

    pre {
      margin: 0;
      padding: var(--spacing-md);
    }

    code {
      font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo,
        monospace;
      font-size: 0.9em;
    }
  }
}
