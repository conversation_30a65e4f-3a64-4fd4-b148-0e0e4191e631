from __future__ import annotations

"""Prompt builder for AI Question generation."""

from typing import List

from agent.common_exam.prompt_builder_base import PromptBuilderBase
from agent.common_exam.enums import Difficulty, QuestionDistribution
from .models import AIQuestionContext


class AIQuestionPromptBuilder(PromptBuilderBase):
    """Build Chinese markdown prompt for AIQuestion."""

    def __init__(self, ctx: AIQuestionContext):
        super().__init__()
        self.ctx = ctx

    def build(self) -> str:  # noqa: C901 acceptable complexity
        ctx = self.ctx
        parts: List[str] = [
            "你是一位资深铁路电务领域的专家教师，擅长根据知识点出题。请按照要求生成高质量考题。",
            "",
            "**基本信息：**",
            f"- 考察知识点：{ctx.knowledge_points}",
            f"- 难度：{self.difficulty_desc(ctx.difficulty)}",
            f"- {self.distribution_desc(ctx.distribution)}",
            f"- 总题数：{ctx.total_questions()}道",
            "",
            "**内容来源：**",
            "- 参考 Exam 模板库示例 (30%)",
            "- 参考用户知识库内容 (70%)",
            "",
        ]

        if ctx.template_content.strip():
            parts.extend([
                "**模板示例：**",
                "```",
                ctx.template_content.strip(),
                "```",
                "",
            ])
        if ctx.user_kb_content.strip():
            parts.extend([
                "**用户知识库内容：**",
                "```",
                ctx.user_kb_content.strip(),
                "```",
                "",
            ])
        if ctx.exam_kb_content.strip():
            parts.extend([
                "**Exam 知识库内容：**",
                "```",
                ctx.exam_kb_content.strip(),
                "```",
                "",
            ])

        parts.extend([
            "**输出要求：**",
            "1. 每题包含题干、选项、正确答案与解析",
            "2. 使用Markdown格式，题目之间用 `---` 分隔",
            "3. 题号从1开始递增",
            "",
            "请开始生成题目：",
        ])

        return "\n".join(parts)
