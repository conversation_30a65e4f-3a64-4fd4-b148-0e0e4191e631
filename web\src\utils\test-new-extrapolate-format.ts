import { parseMarkdownQuestions } from './markdown-to-excel-utils';

// 新的举一反三格式测试数据
const newExtrapolateTestData = `

### 新生成的试题

#### 【单选题】
**问题：**  
ZYJ7型电液转辙机的油泵在动作时，应保证油缸有足够的工作压力。当油泵停止工作时，溢流阀的作用是（ ）。  

**选项：**  
A. 维持油路的压力平衡  
B. 防止油路中压力过高  
C. 确保油缸内压力缓慢释放  
D. 提供额外的液压动力  

**答案：** C  
**解析：** 溢流阀的作用是当油泵停止工作时，确保油缸内的压力能够缓慢释放，从而保护设备免受突然降压的影响。

---

#### 【多选题】
**问题：**  
在CTC（调度集中）系统中，以下哪些功能属于其核心功能？  

**选项：**  
A. 列车运行计划的调整与优化  
B. 车站信号设备的状态监控  
C. 与LKJ系统的数据交互  
D. 应急情况下的人工干预能力  

**答案：** ABD  
**解析：** CTC系统的核心功能包括列车运行计划的调整、车站信号设备的监控以及在应急情况下的人工干预。与LKJ系统的数据交互并不是CTC的核心功能。

---

#### 【判断题】
**问题：**  
在6502电气集中系统中，FDGJ（发车表示继电器）的作用是监督区段是否空闲，并在列车占用区段时断开相关的信号电路。  

**答案：** 正确  
**解析：** FDGJ用于监督区段的占用状态，在列车占用时会切断相关信号的显示电路，确保信号安全。

---

#### 【填空题】
**问题：**  
DS6-K5B计算机联锁系统中，控显分机的主要功能是（ ）和（ ）。  

**答案：** 显示站场信息、接收操作命令  
**解析：** 控显分机负责将车站的站场状态显示在控制台上，并接收操作人员的操作指令。

---

#### 【问答题】
**问题：**  
请简述LKJ系统中"过机校正"的作用及实现方法。  

**答案：**  
过机校正是指当列车通过信号机时，LKJ系统根据实际通过位置对车载设备的定位误差进行调整，以确保行车数据的准确性。其主要实现方法包括：  
1. 人工校正：通过操作[车位]键及相关功能键（如[向前]/[向后]/[自动校正]）完成校正；  
2. 自动校正：系统根据信号机位置和速度传感器信息，自动调整定位误差。  

**解析：** 过机校正是LKJ系统确保列车定位准确性的重要手段，通过人工或自动方式对设备的定位偏差进行修正，从而提高行车的安全性和效率。

---
`;

export const testNewExtrapolateFormat = () => {
  console.log('=== 测试新举一反三格式解析 ===');
  console.log('原始数据长度:', newExtrapolateTestData.length);
  console.log('原始数据预览:', newExtrapolateTestData.slice(0, 200) + '...');

  const result = parseMarkdownQuestions(newExtrapolateTestData);

  console.log('\n=== 解析结果 ===');
  console.log('解析出的题目数量:', result.length);

  result.forEach((question, index) => {
    console.log(`\n--- 题目 ${index + 1} ---`);
    console.log('题型:', question.题型);
    console.log(
      '题目内容:',
      question.题目内容.slice(0, 80) +
        (question.题目内容.length > 80 ? '...' : ''),
    );
    console.log('正确答案:', question.正确答案);
    console.log('选项A:', question.答案A);
    console.log('选项B:', question.答案B);
    console.log('选项C:', question.答案C);
    console.log('选项D:', question.答案D);
    console.log(
      '解析:',
      question.解析.slice(0, 80) + (question.解析.length > 80 ? '...' : ''),
    );

    // 检查题目的关键信息
    const hasType = question.题型 && question.题型.trim() !== '';
    const hasContent = question.题目内容 && question.题目内容.trim() !== '';
    const hasAnswer = question.正确答案 && question.正确答案.trim() !== '';
    const hasExplanation = question.解析 && question.解析.trim() !== '';

    console.log('✓ 完整性检查:');
    console.log(`  - 题型: ${hasType ? '✓' : '✗'}`);
    console.log(`  - 内容: ${hasContent ? '✓' : '✗'}`);
    console.log(`  - 答案: ${hasAnswer ? '✓' : '✗'}`);
    console.log(`  - 解析: ${hasExplanation ? '✓' : '✗'}`);

    // 检查选择题的选项
    if (question.题型.includes('选')) {
      const hasOptionsA = question.答案A && question.答案A.trim() !== '';
      const hasOptionsB = question.答案B && question.答案B.trim() !== '';
      console.log(`  - 选项A: ${hasOptionsA ? '✓' : '✗'}`);
      console.log(`  - 选项B: ${hasOptionsB ? '✓' : '✗'}`);
    }
  });

  // 统计分析
  const validQuestions = result.filter((q) => q.题目内容 && q.题型);
  const questionsWithAnswers = result.filter(
    (q) => q.正确答案 && q.正确答案.trim() !== '',
  );
  const questionsWithExplanation = result.filter(
    (q) => q.解析 && q.解析.trim() !== '',
  );

  console.log('\n=== 验证结果 ===');
  console.log('有效题目数量:', validQuestions.length);
  console.log('有答案的题目数量:', questionsWithAnswers.length);
  console.log('有解析的题目数量:', questionsWithExplanation.length);
  console.log('预期题目数量: 5');
  console.log(
    '解析成功率:',
    `${Math.round((validQuestions.length / 5) * 100)}%`,
  );
  console.log(
    '答案提取成功率:',
    `${Math.round((questionsWithAnswers.length / 5) * 100)}%`,
  );
  console.log(
    '解析提取成功率:',
    `${Math.round((questionsWithExplanation.length / 5) * 100)}%`,
  );

  // 错误诊断
  if (validQuestions.length === 0) {
    console.error('❌ 没有解析出任何有效题目!');
    console.log('🔍 调试信息:');
    console.log(
      '- 是否包含 #### 【:',
      newExtrapolateTestData.includes('#### 【'),
    );
    console.log(
      '- 是否包含 **问题：**:',
      newExtrapolateTestData.includes('**问题：**'),
    );
    console.log(
      '- 是否包含 **选项：**:',
      newExtrapolateTestData.includes('**选项：**'),
    );
  } else if (validQuestions.length < 5) {
    console.warn(`⚠️ 只解析出 ${validQuestions.length}/5 个题目`);

    // 分析缺失的题目
    console.log('\n🔍 缺失题目分析:');
    result.forEach((q, i) => {
      if (!q.题目内容 || !q.题型) {
        console.log(
          `- 题目 ${i + 1}: 题型="${q.题型}", 内容长度=${q.题目内容?.length || 0}`,
        );
      }
    });
  } else if (questionsWithAnswers.length < 5) {
    console.warn(`⚠️ 只有 ${questionsWithAnswers.length}/5 个题目有答案`);
    console.log('缺少答案的题目:');
    result.forEach((q, i) => {
      if (!q.正确答案 || q.正确答案.trim() === '') {
        console.log(
          `- 题目 ${i + 1}: ${q.题型} - ${q.题目内容.slice(0, 30)}...`,
        );
      }
    });
  } else {
    console.log('✅ 解析成功，所有题目信息完整!');
  }

  return result;
};
