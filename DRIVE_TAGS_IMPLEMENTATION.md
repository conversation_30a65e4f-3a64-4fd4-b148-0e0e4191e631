# Drive 服务 Tags 功能实现

## 概述

本次实现为 RAGFlow 的 drive 服务添加了 tags 字段支持，允许用户在上传文件时为文件添加标签，便于文件的分类和管理。

## 实现内容

### 1. 数据库层面

#### 1.1 数据库模型修改 (`api/db/db_models.py`)

在`File`模型中添加了`tags`字段：

```python
class File(DataBaseModel):
    # ... 其他字段 ...
    tags = JSONField(null=True, default=[], help_text="file tags")
```

#### 1.2 数据库迁移

在`migrate_db`函数中添加了迁移逻辑：

```python
try:
    migrate(migrator.add_column("file", "tags", JSONField(null=True, default=[], help_text="file tags")))
except Exception:
    pass
```

### 2. 后端 API 层面

#### 2.1 Drive 上传接口修改 (`api/apps/drive_app.py`)

修改了`/drive/upload`接口，添加了对 tags 参数的处理：

```python
# 获取tags参数
tags_str = request.form.get("tags")
tags = []
if tags_str:
    try:
        import json
        tags = json.loads(tags_str)
        if not isinstance(tags, list):
            tags = []
    except (json.JSONDecodeError, TypeError):
        tags = []

# 在创建文件时包含tags
file = {
    "id": get_uuid(),
    "parent_id": last_folder.id,
    "tenant_id": current_user.id,
    "created_by": current_user.id,
    "type": filetype,
    "name": filename,
    "location": location,
    "size": len(blob),
    "tags": tags,  # 新增tags字段
}
```

### 3. 前端接口层面

#### 3.1 TypeScript 接口定义 (`web/src/interfaces/database/file-manager.ts`)

在`IFile`接口中添加了`tags`字段：

```typescript
export interface IFile {
  // ... 其他字段 ...
  tags?: string[]
}
```

## 功能特性

### 1. Tags 数据格式

- **数据类型**: JSON 数组
- **存储格式**: `["tag1", "tag2", "tag3"]`
- **默认值**: 空数组 `[]`
- **可选性**: 可选字段，不传递 tags 时默认为空数组

### 2. API 使用方式

#### 2.1 上传带 tags 的文件

```javascript
const formData = new FormData()
formData.append('file', fileObject)
formData.append('tags', JSON.stringify(['标签1', '标签2', '标签3']))

fetch('/v1/drive/upload', {
  method: 'POST',
  body: formData
})
```

#### 2.2 前端组件使用

前端的`FileUploadDialog`组件已经支持 tags 输入：

```typescript
interface FileUploadData {
  files: File[]
  tags: string[]
}
```

### 3. 错误处理

- 如果 tags 参数不是有效的 JSON 格式，会默认设置为空数组
- 如果 tags 不是数组类型，会默认设置为空数组
- 不传递 tags 参数时，文件的 tags 字段为空数组

## 兼容性

### 1. 向后兼容

- 现有的文件上传功能完全兼容，不传递 tags 参数时正常工作
- 现有数据库中的文件记录会自动获得默认的空 tags 数组

### 2. 数据库兼容

- 使用数据库迁移机制，自动为现有的 file 表添加 tags 字段
- 迁移过程是安全的，不会影响现有数据

## 测试

### 1. 测试脚本

提供了`test_drive_tags.py`测试脚本，可以验证：

- 带 tags 的文件上传
- 不带 tags 的文件上传
- tags 数据的正确存储和返回

### 2. 测试用例

```bash
# 运行测试脚本
python test_drive_tags.py
```

## 使用示例

### 1. 前端使用

```typescript
// 上传文件时包含tags
const uploadData: FileUploadData = {
  files: selectedFiles,
  tags: ['文档', '重要', '项目A']
}

await uploadFile(uploadData)
```

### 2. 后端 API 调用

```bash
# 使用curl测试
curl -X POST http://localhost:9380/v1/drive/upload \
  -F "file=@test.txt" \
  -F "tags=[\"测试\",\"文档\",\"重要\"]"
```

## 注意事项

### 1. 数据库迁移

- 首次部署时需要运行数据库迁移
- 迁移会自动执行，无需手动干预

### 2. JSON 格式

- Tags 必须以 JSON 数组格式传递
- 前端会自动处理 JSON 序列化

### 3. 性能考虑

- Tags 存储为 JSON 字段，查询性能良好
- 建议单个文件的 tags 数量控制在合理范围内（如 10 个以内）

## 后续扩展

### 1. 可能的功能扩展

- 基于 tags 的文件搜索和过滤
- Tags 的自动补全功能
- Tags 的统计和分析
- 批量修改文件 tags

### 2. 优化建议

- 可以考虑添加 tags 的索引以提高查询性能
- 可以添加 tags 的验证规则（如长度限制、特殊字符限制等）

## 总结

本次实现成功为 drive 服务添加了完整的 tags 功能支持，包括：

- ✅ 数据库模型和迁移
- ✅ 后端 API 接口修改
- ✅ 前端接口类型定义
- ✅ 错误处理和兼容性
- ✅ 测试脚本和文档

该功能已经可以投入使用，为用户提供更好的文件管理体验。
