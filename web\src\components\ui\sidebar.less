/* UI Sidebar Component Styles */
@import '@/theme/high-tech-theme.less';

/* Sidebar Width Constants - Unified with theme variables */
:root {
  /* Desktop sidebar widths */
  --ui-sidebar-width: var(--sidebar-width); /* 170px from theme */
  --ui-sidebar-width-icon: var(--sidebar-collapsed-width); /* 72px from theme */

  /* Mobile sidebar widths */
  --ui-sidebar-width-mobile: 288px; /* Mobile specific width */
}

/* Base sidebar styles */
.ui-sidebar-provider {
  /* CSS custom properties for the provider */
  --sidebar-width: var(--ui-sidebar-width);
  --sidebar-width-icon: var(--ui-sidebar-width-icon);
}

.ui-sidebar {
  /* Use theme variables for consistent width */
  width: var(--ui-sidebar-width);

  &[data-variant='floating'] {
    width: var(--ui-sidebar-width);
  }

  &[data-variant='inset'] {
    width: var(--ui-sidebar-width);
  }
}

/* Mobile sidebar styles */
.ui-sidebar-mobile {
  width: var(--ui-sidebar-width-mobile) !important;
}

/* Collapsed sidebar styles */
.ui-sidebar-collapsed {
  width: var(--ui-sidebar-width-icon) !important;
}

/* Responsive behavior */
@media (max-width: 768px) {
  .ui-sidebar {
    width: var(--ui-sidebar-width-mobile);
  }
}

/* Integration with existing high-tech theme */
.ui-sidebar-integration {
  /* Ensure compatibility with existing sidebar styles */

  .highTechSidebar {
    width: var(--ui-sidebar-width);

    &.sidebarCollapsed {
      width: var(--ui-sidebar-width-icon);
    }
  }

  .contentArea {
    margin-left: var(--ui-sidebar-width);

    &.contentExpanded {
      margin-left: var(--ui-sidebar-width-icon);
    }
  }
}
