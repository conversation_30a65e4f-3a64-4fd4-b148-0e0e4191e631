@import '@/theme/high-tech-theme.less';

.testingContainer {
  width: 100%;
  min-height: calc(100vh - var(--header-height));
}

.sectionTitle {
  margin-bottom: var(--spacing-lg);
  font-weight: 600;
  .gradient-text();
}

.testingWrapper {
  display: flex;
  gap: var(--spacing-lg);
  height: calc(100vh - var(--header-height) - 120px);
  min-height: 600px;
}

.leftPanel {
  width: 320px;
  min-width: 320px;
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  overflow-y: hidden;
  overflow-x: hidden;

  &:hover {
    box-shadow: var(--card-hover-shadow);
  }
}

.rightPanel {
  flex: 1;
  min-width: 300px;
  background: var(--card-background);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--card-shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;

  &:hover {
    box-shadow: var(--card-hover-shadow);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .testingWrapper {
    flex-direction: column;
    height: auto;
    min-height: auto;
  }

  .leftPanel {
    width: 100%;
    min-width: auto;
    order: 1;
  }

  .rightPanel {
    min-width: auto;
    order: 2;
    min-height: 400px;
  }
}

@media (max-width: 1024px) {
  .leftPanel {
    width: 280px;
    min-width: 280px;
  }
}
