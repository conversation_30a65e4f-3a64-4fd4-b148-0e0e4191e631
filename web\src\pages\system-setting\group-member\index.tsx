import {
  useListAllTenants,
  useListTenantUsers,
} from '@/hooks/system-setting-hooks';
import { ITenantUser } from '@/interfaces/database/user-setting';
import {
  DeleteOutlined,
  DownOutlined,
  EditOutlined,
  MoreOutlined,
  TeamOutlined,
  UserAddOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Avatar,
  Badge,
  Button,
  Card,
  Dropdown,
  Input,
  MenuProps,
  Pagination,
  Space,
  Spin,
  Table,
  Tree,
  Typography,
} from 'antd';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import AddMemberModal from './add-member-modal';
import styles from './index.less';
import InviteMemberModal from './invite-member-modal';

const { Title, Text } = Typography;
const { Search } = Input;

interface DepartmentNode {
  title: string;
  key: string;
  icon?: React.ReactNode;
  children?: DepartmentNode[];
}

const GroupMember = () => {
  const { t } = useTranslation();
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['tenants']);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [currentPage, setCurrent] = useState(1);
  const [searchText, setSearchText] = useState('');
  const [addMemberModalVisible, setAddMemberModalVisible] = useState(false);
  const [inviteMemberModalVisible, setInviteMemberModalVisible] =
    useState(false);

  // 获取所有租户列表
  const { data: tenants, loading: tenantsLoading } = useListAllTenants();

  // 获取当前选中租户的用户列表
  const selectedTenantId =
    selectedKeys.length > 0 && selectedKeys[0] !== 'tenants'
      ? (selectedKeys[0] as string)
      : undefined;
  const { data: tenantUsers, loading: usersLoading } =
    useListTenantUsers(selectedTenantId);

  // 构造租户树数据
  const departmentTreeData: DepartmentNode[] = useMemo(() => {
    const tenantNodes: DepartmentNode[] = tenants.map((tenant) => ({
      title: tenant.nickname || `租户 ${tenant.tenant_id}`,
      key: tenant.tenant_id,
      icon: <UserOutlined />,
    }));

    return [
      {
        title: '全部租户',
        key: 'tenants',
        icon: <TeamOutlined />,
        children: tenantNodes,
      },
    ];
  }, [tenants]);

  // 获取当前选中租户信息
  const selectedTenant = useMemo(() => {
    if (!selectedTenantId) return null;
    return tenants.find((tenant) => tenant.tenant_id === selectedTenantId);
  }, [selectedTenantId, tenants]);

  // 过滤用户数据
  const filteredUsers = useMemo(() => {
    if (!searchText) return tenantUsers;
    return tenantUsers.filter(
      (user) =>
        user.nickname?.toLowerCase().includes(searchText.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchText.toLowerCase()),
    );
  }, [tenantUsers, searchText]);

  const handleInviteUser = () => {
    if (!selectedTenantId) {
      return;
    }
    setInviteMemberModalVisible(true);
  };

  const handleAddMember = () => {
    if (!selectedTenantId) {
      // message.warning('请先选择租户');
      return;
    }
    setAddMemberModalVisible(true);
  };

  const handleExport = () => {
    console.log('导出');
  };

  // 操作菜单
  const getActionMenu = (record: ITenantUser): MenuProps => ({
    items: [
      {
        key: 'detail',
        label: '详情',
        icon: <EditOutlined />,
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
      },
    ],
  });

  // 表格列配置
  const columns = [
    {
      title: '用户名',
      dataIndex: 'nickname',
      key: 'nickname',
      render: (text: string, record: ITenantUser) => (
        <Space>
          <Avatar
            src={record.avatar}
            style={{
              backgroundColor: record.avatar ? undefined : '#1890ff',
            }}
          >
            {!record.avatar && (text || record.email)?.charAt(0).toUpperCase()}
          </Avatar>
          <span>{text || record.email}</span>
        </Space>
      ),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => <Text style={{ color: '#666' }}>{email}</Text>,
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => (
        <Badge
          status={
            role === 'owner'
              ? 'success'
              : role === 'admin'
                ? 'processing'
                : 'default'
          }
          text={
            role === 'owner'
              ? '拥有者'
              : role === 'admin'
                ? '管理员'
                : role === 'normal'
                  ? '普通成员'
                  : role
          }
        />
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Badge
          status={status === '1' ? 'success' : 'processing'}
          text={status === '1' ? '正常' : '待激活'}
        />
      ),
    },
    {
      title: '最后活跃时间',
      dataIndex: 'update_date',
      key: 'update_date',
      render: (date: string) => (
        <Text style={{ color: '#666' }}>{new Date(date).toLocaleString()}</Text>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ITenantUser) => (
        <Space>
          <Button type="link" size="small">
            详情
          </Button>
          <Dropdown menu={getActionMenu(record)} trigger={['click']}>
            <Button type="text" size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.groupMemberWrapper}>
      {/* 顶部操作栏 */}
      {/* <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Space>
            <Button icon={<TeamOutlined />}>
              {selectedTenant
                ? selectedTenant.nickname || `租户 ${selectedTenant.tenant_id}`
                : '请选择租户'}
            </Button>
            <Text type="secondary">总人数 {filteredUsers.length}</Text>
            {selectedTenant && (
              <>
                <Button type="link" size="small">
                  <ExportOutlined />
                  提醒
                </Button>
                <Button type="link" size="small" onClick={handleExport}>
                  <ExportOutlined />
                  导出
                </Button>
              </>
            )}
          </Space>
        </div>
        <div className={styles.headerRight}>
          <Space>
            <Button icon={<UserAddOutlined />} onClick={handleInviteUser}>
              邀请成员
            </Button>
            <Button
              type="primary"
              icon={<UserAddOutlined />}
              onClick={handleAddMember}
            >
              添加成员
            </Button>
          </Space>
        </div>
      </div> */}

      {/* 主要内容区域 */}
      <div className={styles.mainContent}>
        {/* 左侧租户树 */}
        <div className={styles.leftPanel}>
          {/* <Card
            size="small"
            title={
              <Space>
                <TeamOutlined />
                租户管理
              </Space>
            }
            className={styles.addDepartmentCard}
          >
            <Button type="dashed" block icon={<UserAddOutlined />}>
              新建租户
            </Button>
          </Card> */}

          <div className={styles.treeContainer}>
            <Spin spinning={tenantsLoading}>
              <Tree
                showIcon
                defaultExpandAll
                expandedKeys={expandedKeys}
                selectedKeys={selectedKeys}
                onExpand={setExpandedKeys}
                onSelect={setSelectedKeys}
                treeData={departmentTreeData}
                className={styles.departmentTree}
              />
            </Spin>
          </div>
        </div>

        {/* 右侧成员表格 */}
        <div className={styles.rightPanel}>
          <Card>
            <div className={styles.tableHeader}>
              <div className={styles.tableFilters}>
                <Space>
                  <Button>
                    账号状态
                    <DownOutlined />
                  </Button>
                  <Button>
                    角色筛选
                    <DownOutlined />
                  </Button>
                  <Button icon={<TeamOutlined />} />
                </Space>
              </div>
              <div className={styles.tableActions}>
                <Space>
                  <Button type="text" icon={<MoreOutlined />} />
                  <Button
                    icon={<UserAddOutlined />}
                    onClick={handleInviteUser}
                    disabled={!selectedTenantId}
                  >
                    邀请成员
                  </Button>
                  <Button
                    type="primary"
                    icon={<UserAddOutlined />}
                    onClick={handleAddMember}
                    disabled={!selectedTenantId}
                  >
                    添加成员
                  </Button>
                </Space>
              </div>
            </div>

            <Search
              placeholder="请输入用户名、邮箱..."
              style={{ width: 300, marginBottom: 16 }}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />

            <Spin spinning={usersLoading}>
              <Table
                columns={columns}
                dataSource={filteredUsers}
                pagination={false}
                size="middle"
                className={styles.memberTable}
                rowKey="user_id"
                locale={{
                  emptyText: selectedTenantId
                    ? '该租户下暂无用户'
                    : '请先选择租户查看用户列表',
                }}
              />
            </Spin>

            {filteredUsers.length > 0 && (
              <div className={styles.pagination}>
                <Pagination
                  current={currentPage}
                  total={filteredUsers.length}
                  pageSize={20}
                  showSizeChanger
                  showQuickJumper
                  showTotal={(total, range) =>
                    `${range[0]}-${range[1]} of ${total} items`
                  }
                  onChange={setCurrent}
                />
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* 添加成员弹窗 */}
      <AddMemberModal
        visible={addMemberModalVisible}
        onCancel={() => setAddMemberModalVisible(false)}
        tenantId={selectedTenantId}
        tenantName={selectedTenant?.nickname || `租户 ${selectedTenantId}`}
      />

      {/* 邀请成员弹窗 */}
      <InviteMemberModal
        visible={inviteMemberModalVisible}
        onCancel={() => setInviteMemberModalVisible(false)}
        tenantId={selectedTenantId}
        tenantName={selectedTenant?.nickname || `租户 ${selectedTenantId}`}
        tenantUsers={tenantUsers}
      />
    </div>
  );
};

export default GroupMember;
