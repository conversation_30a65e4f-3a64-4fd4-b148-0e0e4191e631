declare module 'docx' {
  export class Document {
    constructor(options: any);
  }

  export class Paragraph {
    constructor(options: any);
  }

  export class TextRun {
    constructor(options: any);
  }

  export enum HeadingLevel {
    HEADING_1 = 'Heading1',
    HEADING_2 = 'Heading2',
    HEADING_3 = 'Heading3',
    TITLE = 'Title',
  }

  export class Packer {
    static toBuffer(doc: Document): Promise<Buffer>;
  }

  export enum AlignmentType {
    LEFT = 'left',
    CENTER = 'center',
    RIGHT = 'right',
    JUSTIFIED = 'justified',
  }

  export class Table {
    constructor(options: any);
  }

  export class TableRow {
    constructor(options: any);
  }

  export class TableCell {
    constructor(options: any);
  }

  export enum BorderStyle {
    SINGLE = 'single',
    DOUBLE = 'double',
    DASH = 'dash',
    NONE = 'none',
  }

  export enum WidthType {
    AUTO = 'auto',
    DXA = 'dxa',
    PERCENTAGE = 'percentage',
  }

  export enum UnderlineType {
    SINGLE = 'single',
    DOUBLE = 'double',
    NONE = 'none',
  }
}
