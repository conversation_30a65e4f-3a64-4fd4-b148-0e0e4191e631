# FileKeywordSearch 组件

## 概述

FileKeywordSearch 是一个智能文件搜索组件，它结合了 LLM 关键词提取和数据库查询功能，能够：

1. 从用户输入的句子中提取关键词
2. 根据关键词权重在租户文件中搜索匹配的文件
3. 返回按权重排序的文件列表

**注意**: 组件默认使用 `deepseek-r1:32b@Ollama` 本地模型进行关键词提取，无需配置云端 API 密钥，确保数据隐私和稳定性。如果 LLM 调用失败，系统会自动降级到基于规则的关键词提取方法。

## 功能特性

- **智能关键词提取**: 使用 LLM 从自然语言中提取重要关键词
- **权重排序**: 关键词按重要性权重(1-10)排序
- **文件匹配**: 在当前租户的文件中搜索匹配的文件
- **结果去重**: 自动去除重复文件，保留最高权重匹配
- **租户隔离**: 只搜索当前用户所属租户的文件

## 参数配置

### FileKeywordSearchParam

| 参数名         | 类型   | 默认值                   | 说明               |
| -------------- | ------ | ------------------------ | ------------------ |
| `llm_id`       | string | "deepseek-r1:32b@Ollama" | LLM 模型 ID        |
| `max_keywords` | int    | 5                        | 最大提取关键词数量 |
| `max_results`  | int    | 20                       | 最大返回文件数量   |
| `temperature`  | float  | 0.3                      | LLM 生成温度参数   |

## 输入格式

组件接受包含 `content` 字段的 DataFrame 作为输入：

```python
input_data = pd.DataFrame({
    "content": ["帮我找一些关于机器学习和人工智能的文档"]
})
```

## 输出格式

组件返回 JSON 格式的结果：

```json
{
  "keywords": [
    { "keyword": "机器学习", "weight": 9.0 },
    { "keyword": "人工智能", "weight": 8.5 },
    { "keyword": "文档", "weight": 6.0 }
  ],
  "matched_files": [
    {
      "id": "file_id_1",
      "name": "机器学习入门.pdf",
      "matched_keyword": "机器学习",
      "keyword_weight": 9.0,
      "size": 1024000,
      "type": "pdf",
      "create_time": "2025-01-01 10:00:00"
    }
  ],
  "total_matches": 5,
  "message": "Found 5 files"
}
```

## 使用示例

### DSL 配置示例

```json
{
  "components": {
    "begin": {
      "component_name": "Begin",
      "params": {}
    },
    "file_search": {
      "component_name": "FileKeywordSearch",
      "params": {
        "llm_id": "deepseek-r1:32b@Ollama",
        "max_keywords": 5,
        "max_results": 10,
        "temperature": 0.3
      }
    },
    "answer": {
      "component_name": "Answer",
      "params": {
        "llm_id": "deepseek-r1:32b@Ollama"
      }
    }
  },
  "path": [
    ["begin", "file_search"],
    ["file_search", "answer"]
  ]
}
```

### Python 代码示例

```python
from agent.component.file_keyword_search import FileKeywordSearch, FileKeywordSearchParam
import pandas as pd

# 创建参数
param = FileKeywordSearchParam()
param.llm_id = "deepseek-r1:32b@Ollama"
param.max_keywords = 5
param.max_results = 10

# 创建组件
component = FileKeywordSearch(param, canvas, "file_search")

# 设置输入
input_data = pd.DataFrame({
    "content": ["找一些Python编程相关的资料"]
})
component.set_input(input_data)

# 执行搜索
result = component.debug()
print(result)
```

## 工作流程

1. **输入验证**: 检查输入是否包含有效内容
2. **关键词提取**: 使用 LLM 从输入句子中提取关键词
3. **权重解析**: 解析 LLM 返回的关键词和权重信息
4. **文件搜索**: 在租户文件中搜索匹配的文件
5. **结果处理**: 去重、排序并限制返回数量
6. **格式化输出**: 返回结构化的搜索结果

## 错误处理

- **LLM 调用失败**: 自动回退到简单文本处理提取关键词
- **数据库查询失败**: 记录错误日志并返回空结果
- **权限问题**: 只搜索当前租户的文件，确保数据安全

## 注意事项

1. 确保配置的 `llm_id` 在系统中可用
2. 组件依赖 `DriveService` 进行文件查询
3. 搜索范围限制在当前租户的根目录及子目录
4. 关键词匹配基于文件名，不包括文件内容
5. 权重值范围为 1-10，数值越高表示重要性越高

## 扩展建议

- 支持文件内容搜索
- 添加文件类型过滤
- 支持模糊匹配和同义词搜索
- 添加搜索历史记录功能
