以下内容是在深入研读 ragflow 现有实现（Flask-API + Canvas-DAG + 搜索/embedding 体系）后，对「Lesson-Plan-V2」方案做的“落地级”修订。  
重点保证：① 与当前代码、模型、工具链无缝对接；② 充分利用已有能力（bge-large-zh-v1.5、`document_app.py.upload_and_parse`、`rag.nlp.search.retrieval`）；③ 只增量改动即可上线灰度。

────────────────────────────────────────

1. 组件级 DAG 调整  
   ────────────────────────────────────────  
   现有组件可直接复用，新增 3 个超薄组件：

| 组件              | 继承                | 作用                                             | 关键实现点                                                        |
| ----------------- | ------------------- | ------------------------------------------------ | ----------------------------------------------------------------- |
| TemplateRetrieval | `Retrieval`         | 从 `tenant-lesson-plan-template` KB 获取模板片段 | 固定 `kb_ids=[tpl_kb_id]`；prompt 裁剪到 ≤4096 tokens             |
| ResourceSearch    | `FileKeywordSearch` | 从用户文件或文本里提取关键词并查 Drive           | 增加 `content_text` → keyword 提取；结果通过 `set_output` 写入    |
| LessonPlanV2      | `Generate`          | 生成/迭代教案                                    | 支持 history 中 `feedback`；prompt 自动拼装「模板+课程内容+参数」 |

最终 DAG：

```
begin ─┬─► file_processor ─► template_retrieval ─► lesson_plan_v2 ─► answer
       └─► resource_search ───────────────────────────────────────►↑ (写 reference)
```

────────────────────────────────────────  
2. SSE 事件落地  
────────────────────────────────────────  
FastAPI StreamingResponse 示例：

```python
async def sse_event(event, data):
    yield f"event:{event}\ndata:{json.dumps(data,ensure_ascii=False)}\n\n"
```

完整枚举（新增 ok）：

```
init | waiting_embedding | retrieving_template
generating | stream | recommendations | done
ask_feedback | error
```

ResourceSearch 组件在完成后调用

```python
yield from sse_event("recommendations", {"resources": res_df.to_dict("records")})
```

────────────────────────────────────────  
3. 文件上传与临时 KB  
────────────────────────────────────────  
• 复用 `document_app.upload_and_parse`：前端直传文件，立即返回 `doc_id` & `kb_id_user`.  
• Celery task `embed_doc` 内部使用 **BAAI/bge-large-zh-v1.5**：

```python
embd_mdl = LLMBundle(tenant_id, LLMType.EMBEDDING,
                     "BAAI/bge-large-zh-v1.5@BAAI")
```

• task 结束后通过 Redis Pub/Sub `lesson_plan:{session_id}` 推送 `"embedding_done"`；SSE 协程监听转为 `done`.  
• `cleanup_kb_doc`：延迟任务，2h 后删除 doc & 文件 blob；若用户点击「保存 Word」则取消任务。

────────────────────────────────────────  
4. Prompt 细化  
────────────────────────────────────────

```
System: 你是一位资深教学设计师…
User:
### 模板示例
{tpl_excerpt}

### 课程参数
课程名称: {course_name}
课型: {class_type}
课时(分钟): {class_hours}
授课对象: {target_audience}

### 课程内容
{content_text}

### 生成要求 …
{feedback_block}            # 若有上一轮反馈
```

• `tpl_excerpt` 仅取检索 top-3，each ≤1200 Tok。  
• `content_text` 若 >6000Tok，按重要段落抽样（simple ratio）。  
• `LessonPlanV2.gen_conf()` 设置 `max_tokens=8192`（deepseek-r1:32b 长上下文）。

────────────────────────────────────────  
5. 推荐资源检索  
────────────────────────────────────────

```
kw_df = KeywordExtract(...).run()
resource_df = DriveService.search_by_keywords(user_id, kw_df, top_n=20)
```

结果以 `ResourceItem` 结构返回，前端 ↑ 现有 `RecommendedResources` 组件即可渲染。

────────────────────────────────────────  
6. 会话与持久化  
────────────────────────────────────────

```
lesson_plan_sessions(id, user_id, version, status, dsl_json, jobs_json,…)
lesson_plan_docs(id, session_id, version, content_md, satisfied, source_files,…)
```

• 把 `jobs_json` 存 celery task_id，便于查询／取消。  
• 当用户点“满意”→ `satisfied=true`；“不满意”→ 插入 feedback 并 `iteration_count+=1`。

────────────────────────────────────────  
7. 前端交互补充  
────────────────────────────────────────  
A. 状态机

```
idle → uploading → waiting_embedding → generating → streaming → ask_feedback
```

B. SSE reconnect：丢线后根据 `session_id`+`cursor` 继续拉取。  
C. 禁用输入框直到收到 `ask_feedback` 或 `error`.

────────────────────────────────────────  
8. 监控与速率  
────────────────────────────────────────  
• Prometheus：`lessonplan_generation_seconds`, `lessonplan_tokens_total`.  
• 配额：同用户 `iteration_count<=3/day`.

────────────────────────────────────────  
9. 灰度发布  
────────────────────────────────────────  
• API Header `X-Lesson-Plan-Version:2` 或在租户表新增 `feature_flags`.  
• Canvas 模板文件存放 `agent/templates/lesson_plan_v2.json`；前端根据版本选择。

────────────────────────────────────────  
10. 下一步实现清单  
────────────────────────────────────────

1. 新建组件：`TemplateRetrieval`, `ResourceSearch`, `LessonPlanV2`（extends Generate）。
2. 新增 DAG JSON & 路由 `/start`, `/chat` in `lesson_plan_app.py`.
3. SSE 协程：监听 celery embed channel & Canvas.run(stream=True).
4. 前端页面 /store ：状态机实现 & SSE 处理。
5. 单元测试：
   - 文件上传 →embedding→ 生成路径
   - 手动文本 → 直接生成
   - 反馈流程 2 轮
6. 文档：`docs/evolve/LESSON_PLAN_V2.md` 持续同步更新。

以上即与 ragflow 现有技术栈高度融合、可立即落地的「Lesson-Plan-V2」最终方案。如再发现实现细节差异，可继续在此文档增量修订。
