import { useTranslate } from '@/hooks/common-hooks';
import { useSecondPathName } from '@/hooks/route-hook';
import type { MenuProps } from 'antd';
import { Menu } from 'antd';
import React, { useMemo } from 'react';
import { useNavigate } from 'umi';
import {
  SystemSettingBaseKey,
  SystemSettingIconMap,
  SystemSettingRouteKey,
} from '../constants';
import styles from './index.less';

type MenuItem = Required<MenuProps>['items'][number];

const SideBar = () => {
  const navigate = useNavigate();
  const pathName = useSecondPathName();
  const { t } = useTranslate('setting');

  function getItem(
    label: string,
    key: React.Key,
    icon?: React.ReactNode,
    children?: MenuItem[],
    type?: 'group',
  ): MenuItem {
    return {
      key,
      icon,
      children,
      label: t(label),
      type,
    } as MenuItem;
  }

  const items: MenuItem[] = Object.values(SystemSettingRouteKey).map((value) =>
    getItem(value, value, SystemSettingIconMap[value]),
  );

  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    navigate(`/${SystemSettingBaseKey}/${key}`);
  };

  const selectedKeys = useMemo(() => {
    return [pathName];
  }, [pathName]);

  return (
    <section className={styles.sideBarWrapper}>
      <Menu
        selectedKeys={selectedKeys}
        mode="inline"
        items={items}
        onClick={handleMenuClick}
        style={{ width: 312 }}
      />
    </section>
  );
};

export default SideBar;
