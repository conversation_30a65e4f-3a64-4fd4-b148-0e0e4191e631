export enum Routes {
  Login = '/login',
  Register = '/register',
  Home = '/home',
  Datasets = '/datasets',
  DatasetBase = '/dataset',
  Dataset = `${Routes.DatasetBase}${Routes.DatasetBase}`,
  Agent = '/agent',
  AgentTemplates = '/agent-templates',
  Agents = '/agents',
  Searches = '/next-searches',
  Search = '/next-search',
  Chats = '/next-chats',
  Chat = '/next-chat',
  Files = '/files',
  Drive = '/drive',
  ProfileSetting = '/profile-setting',
  SystemSetting = '/system-setting',
  DatasetTesting = '/testing',
  DatasetSetting = '/setting',
  Chunk = '/chunk',
  ChunkResult = `${Chunk}${Chunk}`,
  Parsed = '/parsed',
  ParsedResult = `${Chunk}${Parsed}`,
  Result = '/result',
  ResultView = `${Chunk}${Result}`,
}

const routes = [
  {
    path: '/login',
    component: '@/pages/login',
    layout: false,
  },
  {
    path: '/register',
    component: '@/pages/register',
    layout: false,
  },
  {
    path: '/login-next',
    component: '@/pages/login-next',
    layout: false,
  },
  {
    path: '/chat/share',
    component: '@/pages/chat/share',
    layout: false,
  },
  {
    path: '/',
    component: '@/layouts',
    layout: false,
    wrappers: ['@/wrappers/auth'],
    routes: [
      { path: '/', redirect: '/workbench' },
      {
        path: '/knowledge',
        component: '@/pages/knowledge',
      },
      {
        path: '/resource',
        component: `@/pages${Routes.Drive}`,
      },
      {
        path: '/workbench',
        component: '@/pages/workbench',
      },
      {
        path: '/ask',
        component: '@/pages/chat',
      },
      {
        path: '/train',
        component: '@/pages/train',
      },
      {
        path: '/training-method/detail/:method',
        component: '@/pages/training-method/detail',
      },
      {
        path: '/training-method',
        component: '@/pages/training-method',
      },
      {
        path: '/data-training',
        component: '@/pages/data-training',
      },
      {
        path: '/inspect',
        component: '@/pages/inspect',
      },
      {
        path: '/retrieval-test',
        component: '@/pages/retrieval-test',
      },
      {
        path: '/query-test',
        component: '@/pages/query-test',
      },
      {
        path: '/api-management',
        component: '@/pages/api-management',
      },
      {
        path: '/knowledge',
        component: '@/pages/add-knowledge',
        routes: [
          {
            path: '/knowledge/dataset',
            component: '@/pages/add-knowledge/components/knowledge-dataset',
            routes: [
              {
                path: '/knowledge/dataset',
                component: '@/pages/add-knowledge/components/knowledge-file',
              },
              {
                path: '/knowledge/dataset/chunk',
                component: '@/pages/add-knowledge/components/knowledge-chunk',
              },
            ],
          },
          {
            path: '/knowledge/configuration',
            component: '@/pages/add-knowledge/components/knowledge-setting',
          },
          {
            path: '/knowledge/testing',
            component: '@/pages/add-knowledge/components/knowledge-testing',
          },
          {
            path: '/knowledge/knowledgeGraph',
            component: '@/pages/add-knowledge/components/knowledge-graph',
          },
        ],
      },

      {
        path: '/data-training',
        component: '@/pages/data-training/knowledge',
        routes: [
          {
            path: '/data-training/dataset',
            component:
              '@/pages/data-training/knowledge/components/knowledge-dataset',
            routes: [
              {
                path: '/data-training/dataset',
                component:
                  '@/pages/data-training/knowledge/components/knowledge-file',
              },
              {
                path: '/data-training/dataset/chunk',
                component:
                  '@/pages/data-training/knowledge/components/knowledge-chunk',
              },
            ],
          },
          {
            path: '/data-training/configuration',
            component:
              '@/pages/data-training/knowledge/components/knowledge-setting',
          },
          {
            path: '/data-training/testing',
            component:
              '@/pages/data-training/knowledge/components/knowledge-testing',
          },
          {
            path: '/data-training/knowledgeGraph',
            component:
              '@/pages/data-training/knowledge/components/knowledge-graph',
          },
        ],
      },
      {
        path: '/chat',
        component: '@/pages/chat',
      },
      {
        path: '/user-profile',
        component: '@/pages/user-setting/setting-profile',
      },
      {
        path: '/user-setting',
        component: '@/pages/user-setting',
        routes: [
          { path: '/user-setting', redirect: '/user-setting/profile' },
          {
            path: '/user-setting/profile',
            component: '@/pages/user-setting/setting-profile',
          },
          {
            path: '/user-setting/locale',
            component: '@/pages/user-setting/setting-locale',
          },
          {
            path: '/user-setting/password',
            component: '@/pages/user-setting/setting-password',
          },
          {
            path: '/user-setting/model',
            component: '@/pages/user-setting/setting-model',
          },
          {
            path: '/user-setting/team',
            component: '@/pages/user-setting/setting-team',
          },
          {
            path: '/user-setting/system',
            component: '@/pages/user-setting/setting-system',
          },
          {
            path: '/user-setting/api',
            component: '@/pages/user-setting/setting-api',
          },
        ],
      },
      {
        path: '/system-setting',
        component: '@/pages/system-setting',
      },
      {
        path: '/system-setting/group-member',
        component: '@/pages/system-setting',
      },
      {
        path: '/system-setting/model-setting',
        component: '@/pages/system-setting',
      },
      {
        path: '/file',
        component: '@/pages/file-manager',
      },
      {
        path: '/flow',
        component: '@/pages/flow/list',
      },
      {
        path: '/flow/:id',
        component: '@/pages/flow',
      },
      {
        path: '/search',
        component: '@/pages/search',
      },
    ],
  },
  {
    path: '/document/:id',
    component: '@/pages/document-viewer',
    layout: false,
  },
  {
    path: '/*',
    component: '@/pages/404',
    layout: false,
  },
  {
    path: Routes.Home,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Home,
        component: `@/pages${Routes.Home}`,
      },
    ],
  },
  {
    path: Routes.Datasets,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Datasets,
        component: `@/pages${Routes.Datasets}`,
      },
    ],
  },
  {
    path: Routes.Chats,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Chats,
        component: `@/pages${Routes.Chats}`,
      },
    ],
  },
  {
    path: Routes.Chat,
    layout: false,
    component: `@/pages${Routes.Chats}/chat`,
  },
  {
    path: Routes.Searches,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Searches,
        component: `@/pages${Routes.Searches}`,
      },
    ],
  },
  {
    path: Routes.Search,
    layout: false,
    component: `@/pages${Routes.Search}`,
  },
  {
    path: Routes.Agents,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Agents,
        component: `@/pages${Routes.Agents}`,
      },
    ],
  },
  {
    path: `${Routes.Agent}/:id`,
    layout: false,
    component: `@/pages${Routes.Agent}`,
  },
  {
    path: Routes.AgentTemplates,
    layout: false,
    component: `@/pages${Routes.Agents}${Routes.AgentTemplates}`,
  },
  {
    path: Routes.Files,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Files,
        component: `@/pages${Routes.Files}`,
      },
    ],
  },
  {
    path: Routes.Drive,
    layout: false,
    component: '@/layouts/next',
    routes: [
      {
        path: Routes.Drive,
        component: `@/pages${Routes.Drive}`,
      },
    ],
  },
  {
    path: Routes.DatasetBase,
    layout: false,
    component: '@/layouts/next',
    routes: [{ path: Routes.DatasetBase, redirect: Routes.Dataset }],
  },
  {
    path: Routes.DatasetBase,
    layout: false,
    component: `@/pages${Routes.DatasetBase}`,
    routes: [
      {
        path: `${Routes.Dataset}/:id`,
        component: `@/pages${Routes.Dataset}`,
      },
      {
        path: `${Routes.DatasetBase}${Routes.DatasetSetting}/:id`,
        component: `@/pages${Routes.DatasetBase}${Routes.DatasetSetting}`,
      },
      {
        path: `${Routes.DatasetBase}${Routes.DatasetTesting}/:id`,
        component: `@/pages${Routes.DatasetBase}${Routes.DatasetTesting}`,
      },
    ],
  },
  {
    path: Routes.Chunk,
    layout: false,
    routes: [
      {
        path: Routes.Chunk,
        component: `@/pages${Routes.Chunk}`,
        routes: [
          {
            path: `${Routes.ParsedResult}/:id`,
            component: `@/pages${Routes.Chunk}/parsed-result`,
          },
          {
            path: `${Routes.ChunkResult}/:id`,
            component: `@/pages${Routes.Chunk}/chunk-result`,
          },
          {
            path: `${Routes.ResultView}/:id`,
            component: `@/pages${Routes.Chunk}/result-view`,
          },
        ],
      },
    ],
  },
  {
    path: Routes.Chunk,
    layout: false,
    component: `@/pages${Routes.Chunk}`,
  },
  {
    path: Routes.ProfileSetting,
    layout: false,
    component: `@/pages${Routes.ProfileSetting}`,
    routes: [
      {
        path: Routes.ProfileSetting,
        redirect: `${Routes.ProfileSetting}/profile`,
      },
      {
        path: `${Routes.ProfileSetting}/profile`,
        component: `@/pages${Routes.ProfileSetting}/profile`,
      },
      {
        path: `${Routes.ProfileSetting}/team`,
        component: `@/pages${Routes.ProfileSetting}/team`,
      },
      {
        path: `${Routes.ProfileSetting}/plan`,
        component: `@/pages${Routes.ProfileSetting}/plan`,
      },
      {
        path: `${Routes.ProfileSetting}/model`,
        component: `@/pages${Routes.ProfileSetting}/model`,
      },
      {
        path: `${Routes.ProfileSetting}/prompt`,
        component: `@/pages${Routes.ProfileSetting}/prompt`,
      },
    ],
  },
  {
    path: '/agent/:id',
    component: '@/pages/agent',
  },
  {
    path: '/agent/ai-question',
    component: '@/pages/agent/ai-question',
  },
  {
    path: '/agent/lesson-plan',
    component: '@/pages/agent/lesson-plan',
  },
  {
    path: '/agent/lesson-plan-v2',
    component: '@/pages/agent/lesson-plan-v2',
  },
  {
    path: '/agent/smart-exam',
    component: '@/pages/agent/smart-exam',
  },
  {
    path: '/agent/extrapolate',
    component: '@/pages/agent/extrapolate',
  },
];

export default routes;
