/* 全局搜索框样式覆盖 - 确保优先级最高 */

/* 覆盖 ListFilterBar 中的搜索框 - 默认状态 */
.list-filter-bar .search-input-wrapper span.ant-input-affix-wrapper,
.list-filter-bar .search-input-wrapper .ant-input-affix-wrapper,
.resource-filter-bar .search-input-wrapper span.ant-input-affix-wrapper,
.resource-filter-bar .search-input-wrapper .ant-input-affix-wrapper,
.search-input-container span.ant-input-affix-wrapper,
.search-input-container .ant-input-affix-wrapper,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq {
  background: #f3f4f6 !important;
  background-color: #f3f4f6 !important;
  border: 1px solid rgba(0, 0, 0, 0) !important;
  border-width: 1px !important;
  border-color: rgba(0, 0, 0, 0) !important;
  box-shadow: none !important;
  border-radius: 8px !important;
  padding: 8px 12px !important;
  transition: border-color 0.2s ease !important;
}

/* 覆盖 hover 状态 */
.list-filter-bar .search-input-wrapper span.ant-input-affix-wrapper:hover,
.list-filter-bar .search-input-wrapper .ant-input-affix-wrapper:hover,
.resource-filter-bar .search-input-wrapper span.ant-input-affix-wrapper:hover,
.resource-filter-bar .search-input-wrapper .ant-input-affix-wrapper:hover,
.search-input-container span.ant-input-affix-wrapper:hover,
.search-input-container .ant-input-affix-wrapper:hover,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq:hover {
  background: #f3f4f6 !important;
  background-color: #f3f4f6 !important;
  border: 1px solid rgba(0, 0, 0, 0) !important;
  border-width: 1px !important;
  border-color: rgba(0, 0, 0, 0) !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 覆盖 focus 状态 - 添加蓝色边框 */
.list-filter-bar .search-input-wrapper span.ant-input-affix-wrapper:focus,
.list-filter-bar .search-input-wrapper .ant-input-affix-wrapper:focus,
.list-filter-bar
  .search-input-wrapper
  span.ant-input-affix-wrapper:focus-within,
.list-filter-bar .search-input-wrapper .ant-input-affix-wrapper:focus-within,
.list-filter-bar .search-input-wrapper span.ant-input-affix-wrapper:active,
.list-filter-bar .search-input-wrapper .ant-input-affix-wrapper:active,
.list-filter-bar
  .search-input-wrapper
  span.ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.list-filter-bar
  .search-input-wrapper
  .ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.resource-filter-bar .search-input-wrapper span.ant-input-affix-wrapper:focus,
.resource-filter-bar .search-input-wrapper .ant-input-affix-wrapper:focus,
.resource-filter-bar
  .search-input-wrapper
  span.ant-input-affix-wrapper:focus-within,
.resource-filter-bar
  .search-input-wrapper
  .ant-input-affix-wrapper:focus-within,
.resource-filter-bar .search-input-wrapper span.ant-input-affix-wrapper:active,
.resource-filter-bar .search-input-wrapper .ant-input-affix-wrapper:active,
.resource-filter-bar
  .search-input-wrapper
  span.ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.resource-filter-bar
  .search-input-wrapper
  .ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.search-input-container span.ant-input-affix-wrapper:focus,
.search-input-container .ant-input-affix-wrapper:focus,
.search-input-container span.ant-input-affix-wrapper:focus-within,
.search-input-container .ant-input-affix-wrapper:focus-within,
.search-input-container span.ant-input-affix-wrapper:active,
.search-input-container .ant-input-affix-wrapper:active,
.search-input-container
  span.ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
.search-input-container
  .ant-input-affix-wrapper.ant-input-affix-wrapper-focused,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq:focus,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq:focus-within,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq:active,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq.ant-input-affix-wrapper-focused {
  background: #f3f4f6 !important;
  background-color: #f3f4f6 !important;
  border: 1px solid #3b82f6 !important;
  border-width: 1px !important;
  border-color: #3b82f6 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 覆盖内部输入框 - 默认状态 */
.list-filter-bar .search-input-wrapper input.ant-input,
.list-filter-bar .search-input-wrapper .ant-input,
.resource-filter-bar .search-input-wrapper input.ant-input,
.resource-filter-bar .search-input-wrapper .ant-input,
.search-input-container input.ant-input,
.search-input-container .ant-input,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  input.ant-input,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq .ant-input {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-width: 0 !important;
  box-shadow: none !important;
  outline: none !important;
  color: #374151 !important;
}

/* 覆盖内部输入框的所有状态 */
.list-filter-bar .search-input-wrapper input.ant-input:hover,
.list-filter-bar .search-input-wrapper .ant-input:hover,
.list-filter-bar .search-input-wrapper input.ant-input:focus,
.list-filter-bar .search-input-wrapper .ant-input:focus,
.list-filter-bar .search-input-wrapper input.ant-input:active,
.list-filter-bar .search-input-wrapper .ant-input:active,
.resource-filter-bar .search-input-wrapper input.ant-input:hover,
.resource-filter-bar .search-input-wrapper .ant-input:hover,
.resource-filter-bar .search-input-wrapper input.ant-input:focus,
.resource-filter-bar .search-input-wrapper .ant-input:focus,
.resource-filter-bar .search-input-wrapper input.ant-input:active,
.resource-filter-bar .search-input-wrapper .ant-input:active,
.search-input-container input.ant-input:hover,
.search-input-container .ant-input:hover,
.search-input-container input.ant-input:focus,
.search-input-container .ant-input:focus,
.search-input-container input.ant-input:active,
.search-input-container .ant-input:active,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  input.ant-input:hover,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input:hover,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  input.ant-input:focus,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input:focus,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  input.ant-input:active,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input:active {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-width: 0 !important;
  box-shadow: none !important;
  outline: none !important;
}

/* 占位符样式 */
.list-filter-bar .search-input-wrapper input.ant-input::placeholder,
.list-filter-bar .search-input-wrapper .ant-input::placeholder,
.resource-filter-bar .search-input-wrapper input.ant-input::placeholder,
.resource-filter-bar .search-input-wrapper .ant-input::placeholder,
.search-input-container input.ant-input::placeholder,
.search-input-container .ant-input::placeholder,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  input.ant-input::placeholder,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input::placeholder {
  color: #9ca3af !important;
}

/* 图标样式 */
.list-filter-bar .search-input-wrapper .ant-input-prefix,
.list-filter-bar .search-input-wrapper .ant-input-suffix,
.resource-filter-bar .search-input-wrapper .ant-input-prefix,
.resource-filter-bar .search-input-wrapper .ant-input-suffix,
.search-input-container .ant-input-prefix,
.search-input-container .ant-input-suffix,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input-prefix,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input-suffix {
  color: #9ca3af !important;
}

.list-filter-bar .search-input-wrapper .ant-input-prefix .anticon,
.list-filter-bar .search-input-wrapper .ant-input-suffix .anticon,
.resource-filter-bar .search-input-wrapper .ant-input-prefix .anticon,
.resource-filter-bar .search-input-wrapper .ant-input-suffix .anticon,
.search-input-container .ant-input-prefix .anticon,
.search-input-container .ant-input-suffix .anticon,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input-prefix
  .anticon,
span.ant-input-affix-wrapper.css-dev-only-do-not-override-1wxecgq
  .ant-input-suffix
  .anticon {
  color: #9ca3af !important;
}
