import {
  Bread<PERSON>rumb,
  B<PERSON><PERSON><PERSON><PERSON>I<PERSON>,
  B<PERSON><PERSON>rumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ChevronRight, Home } from 'lucide-react';
import {
  useResourceBreadcrumb,
  useResourceNavigation,
} from '../../hooks/use-resource-navigation';

export function ResourceBreadcrumb() {
  const { breadcrumbItems } = useResourceBreadcrumb();
  const { navigateToFolder, navigateToRoot } = useResourceNavigation();

  if (breadcrumbItems.length === 0) {
    return null;
  }

  return (
    <Breadcrumb className="resource-breadcrumb">
      <BreadcrumbList>
        {/* 根目录 */}
        <BreadcrumbItem
          onClick={navigateToRoot}
          className="cursor-pointer flex items-center gap-1 hover:text-primary-color transition-colors"
        >
          <Home className="h-4 w-4" />
          <span>资源</span>
        </BreadcrumbItem>

        {breadcrumbItems.map((item, idx) => (
          <div key={item.folderId} className="flex items-center gap-2">
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem
              onClick={() => navigateToFolder(item.folderId)}
              className={
                idx === breadcrumbItems.length - 1
                  ? 'text-primary-color font-medium'
                  : 'cursor-pointer hover:text-primary-color transition-colors'
              }
            >
              <TooltipProvider delayDuration={300}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    {idx === breadcrumbItems.length - 1 ? (
                      <BreadcrumbPage>
                        <span className="max-w-[200px] truncate inline-block align-middle">
                          {item.title}
                        </span>
                      </BreadcrumbPage>
                    ) : (
                      <span className="max-w-[200px] truncate inline-block align-middle">
                        {item.title}
                      </span>
                    )}
                  </TooltipTrigger>
                  <TooltipContent>{item.title}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </BreadcrumbItem>
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
