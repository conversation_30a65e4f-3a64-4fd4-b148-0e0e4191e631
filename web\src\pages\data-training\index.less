@import '@/theme/high-tech-theme.less';

.dataTrainingContainer {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  gap: var(--spacing-md);
}

.header {
  flex-shrink: 0;
}

.pageTitle {
  margin: 0;
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Dark mode adjustments */
:global(.dark) {
  .chartSection,
  .datasetSection {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
  }
}

.divider {
  margin: 0;
  border-color: var(--border-color);
}
