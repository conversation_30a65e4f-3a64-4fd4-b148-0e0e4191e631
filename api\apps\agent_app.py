from rag.utils.redis_conn import REDIS_CONN
import logging
import traceback
from flask import request, jsonify, Blueprint, Response
from flask_login import login_required, current_user
import json

from agent.component.ai_question import AIQuestion, AIQuestionParam
from agent.component.extrapolate import Extrapolate, ExtrapolateParam
from agent.component.lesson_plan import LessonPlan, LessonPlanParam
from agent.component.begin import Begin
from agent.component.answer import Answer
from agent.canvas import Canvas  # Import Canvas class
from api.utils.api_utils import get_json_result, get_data_from_request, server_error_response, get_data_error_result
# For potential direct LLM or KB interactions if needed outside component
from api.db import LLMType
from api.db.services.user_service import TenantService, UserTenantService
from api.db import StatusEnum
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.utils import get_uuid
# Import lesson plan database models
from api.db.db_models import LessonPlanSession, LessonPlanDoc

# If <PERSON><PERSON> needs tenant_id, and it comes from current_user or similar:
# from api.db.services.user_service import UserService # If needed to get full tenant info


class SSEStreamBuffer:
    """SSE流式消息缓冲器，控制消息发送频率（纯增量模式）"""

    def __init__(self, send_interval=1.0):
        """
        Args:
            send_interval: 发送时间间隔（秒）
        """
        import time
        self.send_interval = send_interval
        self.last_send_time = 0  # 初始化为0，确保第一次立即发送
        self.chunk_id = 0
        self.pending_chunks = []  # 待发送的chunk列表

    def add_chunk(self, chunk, last_event_id=-1):
        """添加内容到缓冲区（纯增量模式）"""
        import time

        # 跳过已发送的chunk（用于断线重连）
        if self.chunk_id <= last_event_id:
            self.chunk_id += 1
            return None

        # 添加到待发送列表
        self.pending_chunks.append(chunk)

        current_time = time.time()

        # 检查是否需要发送（时间间隔控制）
        time_elapsed = current_time - self.last_send_time
        should_send = time_elapsed >= self.send_interval

        if should_send and self.pending_chunks:
            # 合并所有待发送的chunks
            content_to_send = ''.join(self.pending_chunks)
            self.pending_chunks = []  # 清空待发送列表
            self.last_send_time = current_time

            event_data = {
                'id': self.chunk_id,
                'event': 'stream',
                'data': json.dumps({'chunk': content_to_send})  # 只发送增量内容
            }

            self.chunk_id += 1
            return event_data

        return None

    def flush(self):
        """强制发送缓冲区剩余内容"""
        if self.pending_chunks:
            content_to_send = ''.join(self.pending_chunks)
            self.pending_chunks = []

            event_data = {
                'id': self.chunk_id,
                'event': 'stream',
                'data': json.dumps({'chunk': content_to_send})
            }

            self.chunk_id += 1
            return event_data

        return None


# Define a MinimalCanvas class that inherits from Canvas for direct API usage


class MinimalCanvas(Canvas):
    """A simplified Canvas for direct API usage without requiring a full agent flow setup."""

    def __init__(self, tenant_id, user_id):
        # Initialize with minimal dsl structure
        minimal_dsl = json.dumps({
            "components": {
                "begin_node": {
                    "obj": {
                        "component_name": "Begin",
                        "params": {}
                    },
                    "downstream": ["ai_question_node_1"],
                    "upstream": [],
                    "parent_id": ""
                },
                "ai_question_node_1": {
                    "obj": {
                        "component_name": "AIQuestion",
                        "params": {}
                    },
                    "downstream": ["answer_node"],
                    "upstream": ["begin_node"],
                    "parent_id": ""
                },
                "answer_node": {
                    "obj": {
                        "component_name": "Answer",
                        "params": {}
                    },
                    "downstream": [],
                    "upstream": ["ai_question_node_1"],
                    "parent_id": ""
                }
            },
            "history": [],
            "messages": [],
            "reference": [],
            "path": [],
            "answer": []
        })
        super().__init__(dsl=minimal_dsl, tenant_id=tenant_id)
        self._user_id = user_id
        self._tenant_id = tenant_id  # Store tenant_id explicitly
        self.history = []

    # Override load to skip component instantiation and validation
    def load(self):
        """Override Canvas.load to skip component instantiation and validation."""
        self.components = self.dsl["components"]
        self.path = self.dsl.get("path", [])
        self.history = self.dsl.get("history", [])
        self.messages = self.dsl.get("messages", [])
        self.answer = self.dsl.get("answer", [])
        self.reference = self.dsl.get("reference", [])
        self._embed_id = self.dsl.get("embed_id", "")

    def set_component_infor(self, component_id, info):
        """Store component execution information."""
        logging.info(f"Component info for {component_id}: {info}")
        if component_id in self.components:
            self.components[component_id]["info"] = info
        return True

    def get_user_id(self):
        """Return the user ID."""
        return self._user_id

    def get_tenant_id(self):
        """Return the tenant ID."""
        return self._tenant_id


# Define a Blueprint for agent-related endpoints
# The name 'agent_app' will be part of the URL prefix, e.g., /v1/agent_app/ai_question/generate
manager = Blueprint('agent_app', __name__)


@manager.route('/ai_question/generate', methods=['POST'])
@login_required
def generate_ai_question_route():
    # Deprecated after SSE refactor – see /v1/ai-question/start
    return get_json_result(data={}, code=410, message='Deprecated. Use /v1/ai-question/start')
    try:
        # -------- 灰度发布：仅 Header=X-Agent-Version:2 允许访问 --------
        # if request.headers.get('X-Agent-Version') != '2':
        #     return get_json_result(data={}, code=404, message='Not enabled')

        # -------- 速率限制 --------
        user_id_limit = current_user.id
        if _hit_limit(user_id_limit, 'hour', 10, 3600):
            return get_json_result(data={}, code=429, message='Rate limit: 3 lesson plans per hour')
        if _hit_limit(user_id_limit, 'day', 100, 86400):
            return get_json_result(data={}, code=429, message='Rate limit: 10 lesson plans per day')

        # Extract data from request, matching AIQuestionGenerateParams from frontend
        data = get_data_from_request(request)
        logging.info(f"Received data for AI Question generation: {data}")

        # Create and populate AIQuestionParam
        params = AIQuestionParam()
        # Map incoming data to param fields carefully
        params.llm_id = data.get('llm_id')

        # Debug log the actual values being set to params
        knowledge_points = data.get('knowledge_points')

        params.knowledge_points = knowledge_points

        params.exam_scene = data.get('exam_scene')
        params.objective = data.get('objective')
        params.random_questions_enabled = data.get(
            'random_questions_enabled', True)
        params.question_count = int(data.get('question_count', 10))
        params.by_question_type_enabled = data.get(
            'by_question_type_enabled', False)
        params.single_choice_count = int(data.get('single_choice_count', 0))
        params.multiple_choice_count = int(
            data.get('multiple_choice_count', 0))
        params.fill_blank_count = int(data.get('fill_blank_count', 0))
        params.true_false_count = int(data.get('true_false_count', 0))
        params.short_answer_count = int(data.get('short_answer_count', 0))
        params.ordering_count = int(data.get('ordering_count', 0))
        params.other_requirements = data.get('other_requirements', '')
        params.exam_kb_id = data.get('exam_kb_id')  # Optional
        params.kb_ids = data.get('kb_ids', [])       # Optional, for other KBs

        # Add any other params from AIQuestionParam that should be settable
        # params.exam_kb_top_n = int(data.get('exam_kb_top_n', 5))
        # params.other_kb_top_n = int(data.get('other_kb_top_n', 10))
        # params.similarity_threshold = float(data.get('similarity_threshold', 0.2))

        # Validate parameters
        # params.check()

        # Get tenant_id from TenantService
        tenants = TenantService.get_info_by(current_user.id)
        if not tenants:
            # If user doesn't have an owner tenant, try to get any tenant they belong to
            user_tenants = UserTenantService.query(user_id=current_user.id)
            if not user_tenants:
                return get_data_error_result(message="No tenant found for this user!")
            tenant_id = user_tenants[0].tenant_id
        else:
            tenant_id = tenants[0]["tenant_id"]

        user_id = current_user.id
        canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user_id)

        # Instantiate the AIQuestion component
        # The component ID ('ai_question_node_1') is arbitrary for this standalone use
        ai_question_component = AIQuestion(
            canvas=canvas, id='ai_question_node_1', param=params)

        # Run the component
        # The _run method of ComponentBase expects history and **kwargs
        # For this direct call, history might be empty or not used by AIQuestion's _run
        result_df = ai_question_component.run(history=[])

        if result_df is None or result_df.empty:
            logging.warning(
                "AIQuestion agent returned empty or None DataFrame.")
            return get_json_result(data={'generated_questions': 'Agent returned no content.'}, code=400, message="Agent returned no content.")

        # Extract the generated questions (assuming it's in a 'content' column, row 0)
        # AIQuestion.be_output(answer) creates a DataFrame with a 'content' column.
        generated_text = result_df['content'].iloc[0] if 'content' in result_df.columns and not result_df.empty else ''

        response_data = {'generated_questions': generated_text}
        return get_json_result(data=response_data)

    except ValueError as ve:
        logging.error(f"ValueError in AI Question generation: {ve}")
        return get_json_result(data={}, code=400, message=str(ve))
    except Exception as e:
        logging.error(
            f"Exception in AI Question generation: {e}", exc_info=True)
        return get_json_result(data={}, code=500, message="Internal server error during question generation.")


@manager.route('/extrapolate/generate', methods=['POST'])
@login_required  # Re-enable login requirement like AI Question agent
def generate_extrapolate_questions_route():
    # Deprecated after SSE refactor – see /v1/extrapolate/start
    return get_json_result(data={}, code=410, message='Deprecated. Use /v1/extrapolate/start')
    try:
        # Handle both JSON and FormData requests
        if request.content_type and 'application/json' in request.content_type:
            data = get_data_from_request(request)
        else:
            # Handle FormData request
            data = request.form.to_dict()

        logging.info(f"Received data for Extrapolate generation: {data}")

        # Create and populate ExtrapolateParam
        params = ExtrapolateParam()
        params.llm_id = data.get('llm_id')
        params.question_type = data.get('question_type', 'same')
        params.custom_question_type = data.get('custom_question_type', '')
        params.structure = data.get('structure', 'same')
        params.custom_structure = data.get('custom_structure', '')
        params.knowledge_points = data.get('knowledge_points', 'same_context')
        params.custom_knowledge_points = data.get(
            'custom_knowledge_points', '')
        params.difficulty = data.get('difficulty', 'same')
        params.quantity = int(data.get('quantity', 5))

        # Handle file upload
        sample_file = request.files.get('sample_file')
        if sample_file:
            params.sample_file = sample_file
        else:
            return get_json_result(data={}, code=400, message="示例试题文件是必需的")

        # Validate parameters
        params.check()

        # Get tenant_id from TenantService (same logic as AI Question agent)
        logging.info(f"Current user info: {current_user}")
        logging.info(
            f"Current user id: {getattr(current_user, 'id', 'No ID')}")

        tenants = TenantService.get_info_by(current_user.id)
        logging.info(f"TenantService.get_info_by result: {tenants}")

        if not tenants:
            # If user doesn't have an owner tenant, try to get any tenant they belong to
            user_tenants = UserTenantService.query(user_id=current_user.id)
            logging.info(f"UserTenantService.query result: {user_tenants}")
            if not user_tenants:
                logging.error(f"No tenant found for user {current_user.id}")
                return get_data_error_result(message="No tenant found for this user!")
            tenant_id = user_tenants[0].tenant_id
        else:
            tenant_id = tenants[0]["tenant_id"]

        logging.info(f"Selected tenant_id: {tenant_id}")
        user_id = current_user.id
        canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user_id)

        # Instantiate the Extrapolate component
        extrapolate_component = Extrapolate(
            canvas, 'extrapolate_node_1', params)

        # Run the component
        result_df = extrapolate_component.run(history=[])

        if result_df is None or result_df.empty:
            logging.warning(
                "Extrapolate agent returned empty or None DataFrame.")
            return get_json_result(data={'generated_questions': 'Agent returned no content.'}, code=400, message="Agent returned no content.")

        # Extract the generated questions
        generated_text = result_df['content'].iloc[0] if 'content' in result_df.columns and not result_df.empty else ''

        response_data = {'generated_questions': generated_text}
        return get_json_result(data=response_data)

    except ValueError as ve:
        logging.error(f"ValueError in Extrapolate generation: {ve}")
        return get_json_result(data={}, code=400, message=str(ve))
    except Exception as e:
        logging.error(
            f"Exception in Extrapolate generation: {e}", exc_info=True)
        return get_json_result(data={}, code=500, message="Internal server error during extrapolate generation.")


@manager.route('/lesson_plan/generate', methods=['POST'])
@login_required
def generate_lesson_plan_route():
    """
    教案生成接口 - 使用LessonPlan组件直接生成
    """
    try:
        # Handle both JSON and FormData requests
        if request.content_type and 'application/json' in request.content_type:
            data = get_data_from_request(request)
        else:
            # Handle FormData request
            data = request.form.to_dict()

        logging.info(f"Received data for LessonPlan generation: {data}")

        # 验证必填字段
        required_fields = ['course_name', 'target_audience']
        for field in required_fields:
            if not data.get(field) or not data.get(field).strip():
                return get_json_result(data={}, code=400, message=f"Required field '{field}' is missing or empty.")

        # 验证course_content和material_file_content二选一
        course_content = data.get('course_content', '').strip()
        material_file_content = ""

        # 处理文件上传（可选）
        course_file = request.files.get('course_file')
        if course_file and course_file.filename:
            try:
                filename = course_file.filename.lower()
                if filename.endswith('.txt'):
                    # 处理编码问题
                    try:
                        material_file_content = course_file.read().decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            course_file.seek(0)  # 重置文件指针
                            material_file_content = course_file.read().decode('gbk')
                        except UnicodeDecodeError:
                            course_file.seek(0)
                            material_file_content = course_file.read().decode('utf-8', errors='ignore')

                    # 限制文件内容长度
                    if len(material_file_content) > 10000:
                        material_file_content = material_file_content[:10000]
                        logging.info(
                            "File content truncated to 10000 characters")
                elif filename.endswith(('.docx', '.doc')):
                    # 简单处理Word文档
                    try:
                        from docx import Document
                        doc = Document(course_file)
                        text_content = []
                        for paragraph in doc.paragraphs:
                            if paragraph.text.strip():
                                text_content.append(paragraph.text.strip())
                        material_file_content = '\n'.join(text_content)
                        if len(material_file_content) > 10000:
                            material_file_content = material_file_content[:10000]
                            logging.info(
                                "Word document content truncated to 10000 characters")
                    except ImportError:
                        logging.warning(
                            "python-docx not available, cannot parse .docx files")
                        return get_json_result(data={}, code=400, message="Word文档解析功能需要安装python-docx库")
                    except Exception as e:
                        logging.error(f"Error parsing Word document: {e}")
                        return get_json_result(data={}, code=400, message=f"Word文档解析出错: {str(e)}")
                else:
                    logging.warning(
                        f"Unsupported file type: {course_file.filename}")
                    return get_json_result(data={}, code=400, message=f"不支持的文件类型: {course_file.filename}，请上传.txt或.docx文件")
            except Exception as e:
                logging.error(f"Error processing uploaded file: {e}")
                return get_json_result(data={}, code=400, message=f"文件处理出错: {str(e)}")

        # 验证至少有一个内容源
        if not course_content and not material_file_content:
            return get_json_result(data={}, code=400, message="Either course_content or material file is required.")

        # 获取课时时长，默认为40分钟，并进行范围验证
        class_hours = data.get('lesson_duration', data.get('class_hours', 40))
        if isinstance(class_hours, str):
            try:
                class_hours = int(class_hours)
            except ValueError:
                return get_json_result(data={}, code=400, message="Class hours must be a valid integer.")

        # 验证课时范围
        if class_hours <= 0 or class_hours > 480:  # 最大8小时
            return get_json_result(data={}, code=400, message="Class hours must be between 1 and 480 minutes.")

        # 获取LLM ID并验证其存在性
        llm_id = data.get('llm_id')
        if not llm_id:
            return get_json_result(data={}, code=400, message="LLM ID is required.")

        # 获取tenant_id
        from api.db.services.user_service import TenantService, UserTenantService
        try:
            tenants = TenantService.get_info_by(current_user.id)
            if not tenants:
                user_tenants = UserTenantService.query(user_id=current_user.id)
                if not user_tenants:
                    return get_data_error_result(message="No tenant found for this user!")
                tenant_id = user_tenants[0].tenant_id
            else:
                tenant_id = tenants[0]["tenant_id"]
        except Exception as e:
            logging.error(f"Error getting tenant_id: {e}")
            return get_json_result(data={}, code=500, message="Failed to get tenant information.")

        # 验证LLM是否存在
        try:
            from api.db.services.llm_service import TenantLLMService
            from api.db import LLMType

            # 验证LLM是否存在且可用
            model_config = TenantLLMService.get_model_config(
                tenant_id, LLMType.CHAT, llm_id)
            if not model_config:
                return get_json_result(data={}, code=400, message=f"LLM model '{llm_id}' not found or not available.")
        except Exception as e:
            logging.error(f"Error validating LLM: {e}")
            return get_json_result(data={}, code=400, message=f"Failed to validate LLM: {str(e)}")

        # 创建LessonPlan组件
        from agent.component.lesson_plan import LessonPlan, LessonPlanParam

        # 创建参数对象
        param = LessonPlanParam()
        param.course_name = data.get('course_name', '').strip()
        param.class_hours = class_hours
        param.target_audience = data.get('target_audience', '').strip()
        param.course_content = course_content
        param.material_file_content = material_file_content
        param.other_requirements = data.get('other_requirements', '').strip()
        param.llm_id = llm_id

        # 验证参数
        try:
            param.check()
        except ValueError as ve:
            return get_json_result(data={}, code=400, message=str(ve))

        # 使用MinimalCanvas - 不需要复杂的Canvas结构，参考extrapolate的实现
        user_id = current_user.id
        canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user_id)

        # 创建LessonPlan组件实例 - 直接使用，不需要Canvas.load()
        try:
            lesson_plan = LessonPlan(canvas, "lesson_plan", param)
        except Exception as e:
            logging.error(f"Error creating LessonPlan component: {e}")
            return get_json_result(data={}, code=500, message="Failed to create lesson plan component.")

        # 执行生成
        try:
            result = lesson_plan._run(history=[])
        except Exception as e:
            logging.error(f"Error executing LessonPlan component: {e}")
            return get_json_result(data={}, code=500, message=f"Failed to generate lesson plan: {str(e)}")

        # 提取结果
        try:
            if hasattr(result, 'iloc') and len(result) > 0:
                # DataFrame格式
                final_result = result.iloc[0]['content'] if 'content' in result.columns else str(
                    result.iloc[0, 0])
            elif isinstance(result, dict) and 'content' in result:
                final_result = result['content']
            else:
                final_result = str(result)

            if not final_result or final_result.strip() == "":
                logging.warning("LessonPlan component returned empty result")
                final_result = "教案生成失败，请检查输入参数并重试。"

            # 检查是否为错误响应
            if final_result.startswith("教案生成失败"):
                return get_json_result(data={}, code=500, message=final_result)
        except Exception as e:
            logging.error(f"Error processing result: {e}")
            return get_json_result(data={}, code=500, message="Failed to process lesson plan result.")

        # 返回结果，保持与原接口兼容的格式
        response_data = {'generated_lesson_plan': final_result}
        return get_json_result(data=response_data)

    except ValueError as ve:
        logging.error(f"ValueError in LessonPlan generation: {ve}")
        return get_json_result(data={}, code=400, message=str(ve))
    except Exception as e:
        logging.error(
            f"Exception in LessonPlan generation: {e}", exc_info=True)
        return get_json_result(data={}, code=500, message="Internal server error during lesson plan generation.")


@manager.route('/file_keyword_search/search', methods=['POST'])
@login_required
def file_keyword_search_route():
    """
    文件关键词搜索接口 - 使用FileKeywordSearch组件搜索相关文件
    支持Redis缓存，缓存30分钟避免重复LLM调用
    """
    try:
        data = get_data_from_request(request)
        logging.info(f"Received data for FileKeywordSearch: {data}")

        # 验证必填字段
        content = data.get('content', '').strip()
        if not content:
            return get_json_result(data={}, code=400, message="Content is required.")

        llm_id = data.get('llm_id')
        if not llm_id:
            return get_json_result(data={}, code=400, message="LLM ID is required.")

        # 获取可选参数
        max_keywords = data.get('max_keywords', 5)
        max_results = data.get('max_results', 5)  # 默认最多返回5个结果
        temperature = data.get('temperature', 0.3)

        # 参数验证
        try:
            max_keywords = int(max_keywords)
            max_results = int(max_results)
            temperature = float(temperature)
        except (ValueError, TypeError):
            return get_json_result(data={}, code=400, message="Invalid parameter types.")

        if max_keywords <= 0 or max_keywords > 20:
            return get_json_result(data={}, code=400, message="max_keywords must be between 1 and 20.")

        if max_results <= 0 or max_results > 50:
            return get_json_result(data={}, code=400, message="max_results must be between 1 and 50.")

        if temperature < 0 or temperature > 2:
            return get_json_result(data={}, code=400, message="temperature must be between 0 and 2.")

        # 获取tenant_id using enhanced tenant utils
        from api.utils.tenant_utils import get_tenant_with_fallback
        try:
            success, tenant = get_tenant_with_fallback(current_user.id)
            if not success or not tenant:
                return get_data_error_result(message="Unable to determine tenant for user")
            tenant_id = tenant.id
        except Exception as e:
            logging.error(f"Error getting tenant_id: {e}")
            return get_json_result(data={}, code=500, message="Failed to get tenant information.")

        # 生成缓存键 - 基于用户ID、内容和主要参数
        import hashlib
        import json
        import time
        cache_data = {
            'user_id': current_user.id,
            'tenant_id': tenant_id,
            'content': content.strip(),  # 去除首尾空格，避免因空格差异导致缓存失效
            'llm_id': llm_id,
            'max_keywords': max_keywords,
            'max_results': max_results,
            # 注意：temperature不包含在缓存键中，因为它主要影响LLM输出的随机性
            # 为了获得一致的缓存效果，我们基于内容和关键参数进行缓存
        }
        cache_key_raw = json.dumps(
            cache_data, sort_keys=True, ensure_ascii=False)
        cache_key = f"file_keyword_search:v1:{hashlib.md5(cache_key_raw.encode('utf-8')).hexdigest()}"

        # 尝试从缓存获取结果
        from rag.utils.redis_conn import REDIS_CONN
        try:
            if REDIS_CONN.is_alive():
                cached_result = REDIS_CONN.get(cache_key)
                if cached_result:
                    logging.info(
                        f"[FileKeywordSearch] Cache hit for key: {cache_key}")
                    try:
                        cached_data = json.loads(cached_result)
                        # 兼容旧格式和新格式
                        if isinstance(cached_data, dict) and 'data' in cached_data:
                            # 新格式：包含元数据
                            search_result = cached_data['data']
                            cached_at = cached_data.get('cached_at', 0)
                            logging.info(
                                f"[FileKeywordSearch] Using cached result from {time.time() - cached_at:.1f}s ago")
                        else:
                            # 旧格式：直接是搜索结果
                            search_result = cached_data
                        return get_json_result(data=search_result)
                    except json.JSONDecodeError:
                        logging.warning(
                            f"[FileKeywordSearch] Invalid cached data, proceeding with fresh search")
                else:
                    logging.info(
                        f"[FileKeywordSearch] Cache miss for key: {cache_key}")
        except Exception as e:
            logging.warning(
                f"[FileKeywordSearch] Cache check failed: {e}, proceeding with fresh search")

        # 验证LLM是否存在
        try:
            from api.db.services.llm_service import TenantLLMService
            from api.db import LLMType

            model_config = TenantLLMService.get_model_config(
                tenant_id, LLMType.CHAT, llm_id)
            if not model_config:
                return get_json_result(data={}, code=400, message=f"LLM model '{llm_id}' not found or not available.")
        except Exception as e:
            logging.error(f"Error validating LLM: {e}")
            return get_json_result(data={}, code=400, message=f"Failed to validate LLM: {str(e)}")

        # 创建FileKeywordSearch组件
        from agent.component.file_keyword_search import FileKeywordSearch, FileKeywordSearchParam

        # 创建参数对象
        param = FileKeywordSearchParam()
        param.llm_id = llm_id
        param.max_keywords = max_keywords
        param.max_results = max_results
        param.temperature = temperature

        # 验证参数
        try:
            param.check()
        except ValueError as ve:
            return get_json_result(data={}, code=400, message=str(ve))

        # 使用MinimalCanvas
        user_id = current_user.id
        canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user_id)

        # 创建FileKeywordSearch组件实例
        try:
            file_search = FileKeywordSearch(
                canvas, "file_keyword_search", param)
        except Exception as e:
            logging.error(f"Error creating FileKeywordSearch component: {e}")
            return get_json_result(data={}, code=500, message="Failed to create file search component.")

        # 设置输入数据
        file_search._param.debug_inputs = [{"value": content}]

        # 执行搜索
        try:
            result = file_search._run(history=[])
        except Exception as e:
            logging.error(f"Error executing FileKeywordSearch component: {e}")
            return get_json_result(data={}, code=500, message=f"Failed to search files: {str(e)}")

        # 提取结果
        try:
            if hasattr(result, 'iloc') and len(result) > 0:
                # DataFrame格式
                result_content = result.iloc[0]['content'] if 'content' in result.columns else str(
                    result.iloc[0, 0])
            elif isinstance(result, dict) and 'content' in result:
                result_content = result['content']
            else:
                result_content = str(result)

            # 解析JSON结果
            try:
                search_result = json.loads(result_content)
            except json.JSONDecodeError:
                logging.error(
                    f"Failed to parse search result as JSON: {result_content}")
                return get_json_result(data={}, code=500, message="Failed to parse search results.")

            # 将结果保存到缓存（60分钟 = 3600秒）
            try:
                if REDIS_CONN.is_alive():
                    # 添加缓存元数据
                    cacheable_result = {
                        'data': search_result,
                        'cached_at': time.time(),
                        'cache_version': 'v1'
                    }
                    cache_success = REDIS_CONN.set_obj(
                        cache_key, cacheable_result, exp=3600)
                    if cache_success:
                        logging.info(
                            f"[FileKeywordSearch] Result cached for key: {cache_key}")
                    else:
                        logging.warning(
                            f"[FileKeywordSearch] Failed to cache result for key: {cache_key}")
            except Exception as e:
                logging.warning(f"[FileKeywordSearch] Cache save failed: {e}")

            # 返回结果
            return get_json_result(data=search_result)

        except Exception as e:
            logging.error(f"Error processing search result: {e}")
            return get_json_result(data={}, code=500, message="Failed to process search results.")

    except ValueError as ve:
        logging.error(f"ValueError in FileKeywordSearch: {ve}")
        return get_json_result(data={}, code=400, message=str(ve))
    except Exception as e:
        logging.error(f"Exception in FileKeywordSearch: {e}", exc_info=True)
        return get_json_result(data={}, code=500, message="Internal server error during file search.")


# ===================== Lesson Plan V2 Streaming API =====================

@manager.route('/lesson-plan-v2/start', methods=['POST'])  # noqa: F821
@login_required
def lesson_plan_v2_start():
    """Streaming SSE endpoint - delegate to LessonPlanV2Service"""
    try:
        # -------- 速率限制 --------
        user_id_limit = current_user.id
        if _hit_limit_with_prefix(user_id_limit, 'hour', 10, 3600, 'lessonplan'):
            return get_json_result(data={}, code=429, message='Rate limit: 8 lesson plans per hour')
        if _hit_limit_with_prefix(user_id_limit, 'day', 100, 86400, 'lessonplan'):
            return get_json_result(data={}, code=429, message='Rate limit: 30 lesson plans per day')

        from api.services.lesson_plan_v2_service import LessonPlanV2Service
        return LessonPlanV2Service.build_start_response(request, current_user)
    except Exception as e:
        logging.error(f"Exception in lesson_plan_v2_start: {e}", exc_info=True)
        return server_error_response(e)


@manager.route('/lesson-plan-v2/<session_id>/chat', methods=['PUT'])  # noqa: F821
@login_required
def lesson_plan_v2_chat(session_id):
    """Delegate lesson plan chat iteration to LessonPlanV2Service."""
    from api.services.lesson_plan_v2_service import LessonPlanV2Service
    return LessonPlanV2Service.build_chat_response(session_id, request, current_user)


# ---------- Cancel cleanup when user saves doc ----------


@manager.route('/lesson-plan-v2/<session_id>/cancel_cleanup', methods=['POST'])  # noqa: F821
@login_required
def lesson_plan_v2_cancel_cleanup(session_id):
    """Delegate cleanup cancellation to LessonPlanV2Service."""
    from api.services.lesson_plan_v2_service import LessonPlanV2Service
    return LessonPlanV2Service.cancel_cleanup(session_id)


# ===================== Smart Exam Streaming API =====================

@manager.route('/smart-exam/start', methods=['POST'])  # noqa: F821
@login_required
def smart_exam_start():
    """Streaming SSE endpoint - delegate to SmartExamService"""
    try:
        # -------- 速率限制 --------
        user_id_limit = current_user.id
        if _hit_limit_with_prefix(user_id_limit, 'hour', 10, 3600, 'smartexam'):
            return get_json_result(data={}, code=429, message='Rate limit: 5 smart exams per hour')
        if _hit_limit_with_prefix(user_id_limit, 'day', 100, 86400, 'smartexam'):
            return get_json_result(data={}, code=429, message='Rate limit: 20 smart exams per day')

        from api.services.smart_exam_service import SmartExamService
        return SmartExamService.build_start_response(request, current_user)
    except Exception as e:
        logging.error(f"Exception in smart_exam_start: {e}", exc_info=True)
        return server_error_response(e)


@manager.route('/smart-exam/<session_id>/cancel_cleanup', methods=['POST'])  # noqa: F821
@login_required
def smart_exam_cancel_cleanup(session_id):
    """Delegate cleanup cancellation to SmartExamService."""
    from api.services.smart_exam_service import SmartExamService
    return SmartExamService.cancel_cleanup(session_id)


# ----------------- Simple in-process rate limiter (Redis) -----------------


def _hit_limit(user_id: str, bucket: str, limit: int, ttl: int) -> bool:
    """Increment redis counter; return True if limit exceeded."""
    key = f"lpv2:{bucket}:{user_id}"
    try:
        cnt = REDIS_CONN.incr(key)
        if cnt == 1:
            REDIS_CONN.expire(key, ttl)
        return cnt > limit
    except Exception:
        # Fail-open: if Redis down, don't block user
        return False


def _hit_limit_with_prefix(user_id: str, bucket: str, limit: int, ttl: int, prefix: str) -> bool:
    """Increment redis counter with custom prefix; return True if limit exceeded."""
    key = f"{prefix}:{bucket}:{user_id}"
    try:
        cnt = REDIS_CONN.incr(key)
        if cnt == 1:
            REDIS_CONN.expire(key, ttl)
        return cnt > limit
    except Exception:
        # Fail-open: if Redis down, don't block user
        return False


# ------------------------------------------------------------------
# New SSE streaming endpoints (v2 refactor)
# ------------------------------------------------------------------

@manager.route('/ai-question/start', methods=['POST'])  # noqa: F821
@login_required
def ai_question_start():
    """AI Question – SSE streaming start endpoint (POST)."""
    from api.services.ai_question_service import AIQuestionService
    return AIQuestionService.build_start_response(request, current_user)


@manager.route('/extrapolate/start', methods=['POST'])  # noqa: F821
@login_required
def extrapolate_start():
    """Extrapolate – SSE streaming start endpoint (POST)."""
    from api.services.extrapolate_service import ExtrapolateService
    return ExtrapolateService.build_start_response(request, current_user)
