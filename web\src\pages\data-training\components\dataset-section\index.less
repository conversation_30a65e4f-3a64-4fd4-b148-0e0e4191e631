@import '@/theme/high-tech-theme.less';

.datasetWrapper {
  height: 60vh;
  min-height: 400px;
  display: flex;
  flex-direction: column;
}

.datasetHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  flex-shrink: 0;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}

.titleSection {
  flex: 1;
}

.datasetTitle {
  margin: 0 0 var(--spacing-xs) 0;
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.datasetDescription {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

.actionSection {
  flex-shrink: 0;

  @media (max-width: 768px) {
    width: 100%;
  }
}
