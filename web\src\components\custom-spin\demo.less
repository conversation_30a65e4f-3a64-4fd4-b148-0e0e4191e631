// Custom Spin组件库演示样式

.spin-demo {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .ant-card {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    .ant-card-head {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 8px 8px 0 0;

      .ant-card-head-title {
        color: white;
        font-weight: 500;
      }
    }
  }

  .ant-space-item {
    > div {
      text-align: center;

      .ant-typography {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #666;
      }
    }
  }

  // 演示区域样式
  .demo-section {
    background: white;
    padding: 16px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    margin-bottom: 16px;

    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  // 自定义样式演示
  .custom-style-demo {
    .ant-space-item {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      padding: 16px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #40a9ff;
        background: rgba(24, 144, 255, 0.04);
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 12px;

    .ant-card .ant-card-body {
      padding: 16px;
    }

    .ant-space-item {
      width: 100%;

      > div {
        width: 100%;
      }
    }
  }
}

// 提升代码可读性的样式
.spin-demo {
  .ant-typography h4 {
    color: #1890ff;
    margin-bottom: 16px;
    font-weight: 600;
  }

  .ant-btn {
    border-radius: 6px;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }
  }

  // 加载演示区域
  .loading-demo-area {
    .ant-card {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
      }
    }
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .spin-demo {
    background-color: #141414;
    color: #ffffff;

    .ant-card {
      background-color: #1f1f1f;
      border-color: #303030;

      .ant-card-head {
        background: linear-gradient(135deg, #434343 0%, #000000 100%);
        border-color: #303030;
      }
    }

    .demo-section {
      background: #1f1f1f;
      border-color: #303030;
    }

    .ant-typography h4 {
      color: #40a9ff;
    }
  }
}

// 动画效果
.spin-demo {
  .ant-card {
    animation: fadeInUp 0.6s ease-out;
  }

  .ant-space-item {
    animation: fadeInUp 0.8s ease-out;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
