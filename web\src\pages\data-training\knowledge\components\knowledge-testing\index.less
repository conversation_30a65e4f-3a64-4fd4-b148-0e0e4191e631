@import '@/theme/high-tech-theme.less';

.testingContainer {
  width: 100%;
  padding: var(--spacing-lg);
}

.sectionTitle {
  margin-bottom: var(--spacing-lg);
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.testingWrapper {
  flex: 1;
  height: 100%;
  gap: var(--spacing-lg);
}
