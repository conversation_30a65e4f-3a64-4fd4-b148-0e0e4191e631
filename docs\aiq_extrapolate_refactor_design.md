# AI Question & Extrapolate Refactor – Design Document

> Version: 0.1 (Draft skeleton)  
> Status: WIP – to be iterated during Phase-0 ‑ design-doc task

## 1. Overview

简述本次重构的背景与价值：将「AI 出题」与「举一反三」对齐到 Smart Exam 的三层架构，提升可维护性、可测试性与用户体验。

## 2. Scope

- AIQuestion 组件、API、前端改造
- Extrapolate 组件、API、前端改造
- 公共工具抽取 (`agent/common_exam/`)
- Canvas 模板新增 (ai-question.json & extrapolate.json)

## 3. Goals & Non-Goals

| Goals                                        | Non-Goals                |
| -------------------------------------------- | ------------------------ |
| 1. 三层分层 (Domain / Pure helper / Service) | 重写前端 UI 布局         |
| 2. SSE 流式输出                              | 改动现有 Smart Exam 功能 |
| 3. Prompt 文件化、版本化                     | 引入新向量数据库         |
| 4. 单测覆盖 ≥ 70%                            | 自动调优 prompt          |

## 4. High-Level Architecture

```mermaid
flowchart LR
    subgraph UI/Frontend
        F1[Form – AI Question]
        F2[Form – Extrapolate]
    end
    subgraph API Service
        AQ[AIQuestionService]
        EX[ExtrapolateService]
    end
    subgraph Agent Layer
        AQG[AIQuestionGenerator]
        EXG[ExtrapolateGenerator]
    end
    subgraph Common
        RETRIEVER[Retriever]
        STREAMER[Streamer]
        PROMPT[PromptBuilderBase]
    end
    F1 --> AQ
    F2 --> EX
    AQ --> AQG
    EX --> EXG
    AQG & EXG --> RETRIEVER
    AQG & EXG --> PROMPT
    AQG & EXG --> STREAMER
```

## 5. Domain Models

- `Difficulty`, `QuestionMethod`, `QuestionDistribution`
- `AIQuestionContext`, `ExtrapolateContext`
- Param models (Pydantic) + LegacyAdapter

## 6. Module Breakdown

| Package             | Responsibility                                               |
| ------------------- | ------------------------------------------------------------ |
| `agent/common_exam` | enums, Retriever, Streamer, PromptBuilderBase                |
| `agent/ai_question` | models, prompt_builder, generator, sample_parser, components |
| `agent/extrapolate` | models, prompt_builder, generator, file_parser, components   |
| `api/services`      | ai_question_service.py, extrapolate_service.py               |
| `agent/templates`   | ai_question.json, extrapolate.json                           |

## 7. Canvas Workflows

- **AI Question Flow**  
  Nodes: Begin → SampleFileParser → TemplateRetrieval → Retriever(s) → AIQuestionGenerator → Answer
- **Extrapolate Flow**  
  Nodes: Begin → SampleFileParser → StructureDetector → ExtrapolateGenerator → Answer

## 8. Data Contracts

- SampleFileParser → returns `{ sample_content, headers }`
- Retriever → returns `{ content, chunks }`
- Generator → yields `str` stream (markdown)

## 9. Prompt Management

- `conf/prompt/ai_question/v1.md`
- `conf/prompt/extrapolate/v1.md`
- Param `prompt_version` 决定加载文件

## 10. Error Handling & Retry Strategy

- File parse fail → fallback no-file path
- KB empty → fallback query + log WARN
- LLM timeout → Circuit breaker, returns partial output

## 11. Streaming & SSE

- 使用 `SSEStreamBuffer`，1s flush，一次事件字段：`id`, `event: stream`, `data: {chunk}`
- 事件类型：`init`, `waiting_embedding`, `generating`, `done`

## 12. Observability & Logging

- `trace_id` 注入 Canvas Context
- 关键节点 INFO，异常 ERROR 带 stack

## 13. Testing Strategy

- Unit: PromptBuilder snapshot, Retriever fallback, SampleParser edge cases
- Integration: Flask test-client + SSE parsing
- Frontend Cypress SSE flow

## 14. Deployment & Rollback

1. Behind feature flag `AIQ_V2_ENABLED`
2. Canary release → 10% users
3. Metrics: avg first token latency, total generation time, error rate
4. Rollback → toggle flag & revert route

## 15. Risks & Mitigations

| Risk         | Mitigation                   |
| ------------ | ---------------------------- |
| KB 检索慢    | 预热向量索引；调小 top_n     |
| LLM 费用上升 | Temperature 0.3；prompt 精简 |
| Prompt 漂移  | 版本化 + A/B test            |

## 16. Timeline / Milestones

(对应 TODO 看板)

| Phase | Target                       | Week |
| ----- | ---------------------------- | ---- |
| P1    | common modules merged        | W1   |
| P2    | AIQuestion module + service  | W2   |
| P3    | Extrapolate module + service | W3   |
| P4    | Frontend SSE + E2E           | W4   |
| P5    | Legacy off                   | W5   |

## 17. Appendix

- API contract examples
- Sample Canvas JSON
- Open questions list
