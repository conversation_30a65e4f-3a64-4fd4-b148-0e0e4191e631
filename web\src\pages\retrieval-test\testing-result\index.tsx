import Image from '@/components/image';
import { useTranslate } from '@/hooks/common-hooks';
import { ITestingChunk } from '@/interfaces/database/knowledge';
import {
  Card,
  Empty,
  Flex,
  Pagination,
  PaginationProps,
  Popover,
  Typography,
} from 'antd';
import camelCase from 'lodash/camelCase';

import { RetrievalResultIcon } from '@/assets/icon/Icon';
import {
  useSelectIsTestingSuccess,
  useSelectTestingResult,
} from '@/hooks/knowledge-hooks';
import { useGetPaginationWithRouter } from '@/hooks/logic-hooks';
import { BarChartOutlined, FileTextOutlined } from '@ant-design/icons';
import { PackageSearch } from 'lucide-react';
import styles from './index.less';

const { Title, Text } = Typography;

const similarityList: Array<{
  field: keyof ITestingChunk;
  label: string;
  icon: React.ReactNode;
}> = [
  {
    field: 'similarity',
    label: 'Hybrid Similarity',
    icon: <BarChartOutlined />,
  },
  {
    field: 'term_similarity',
    label: 'Term Similarity',
    icon: <FileTextOutlined />,
  },
  {
    field: 'vector_similarity',
    label: 'Vector Similarity',
    icon: <BarChartOutlined />,
  },
];

const ChunkTitle = ({ item }: { item: ITestingChunk }) => {
  const { t } = useTranslate('knowledgeDetails');
  return (
    <Flex gap={16} align="center" className={styles.similarityContainer}>
      {similarityList.map((x) => (
        <div key={x.field} className={styles.similarityItem}>
          <Text className={styles.similarityValue}>
            {((item[x.field] as number) * 100).toFixed(2)}
          </Text>
          <Text type="secondary" className={styles.similarityLabel}>
            {t(camelCase(x.field))}
          </Text>
        </div>
      ))}
    </Flex>
  );
};

interface IProps {
  handleTesting: (documentIds?: string[]) => Promise<any>;
  selectedDocumentIds: string[];
}

const TestingResult = ({ handleTesting, selectedDocumentIds }: IProps) => {
  const { chunks, total } = useSelectTestingResult();
  const { t } = useTranslate('knowledgeDetails');
  const { pagination } = useGetPaginationWithRouter();
  const isSuccess = useSelectIsTestingSuccess();

  const onChange: PaginationProps['onChange'] = (pageNumber, pageSize) => {
    pagination.onChange?.(pageNumber, pageSize);
    handleTesting(selectedDocumentIds);
  };

  return (
    <section className={styles.testingResultWrapper}>
      <div className={styles.resultHeader}>
        <Flex justify="space-between" align="center">
          <Title level={4} className={styles.resultTitle}>
            <RetrievalResultIcon className={styles.resultIcon} />
            检索结果
          </Title>
          {isSuccess && chunks && chunks.length > 0 && (
            <Text type="secondary" className={styles.resultStats}>
              找到 {total} 个相关结果
            </Text>
          )}
        </Flex>
      </div>

      <div className={styles.resultContainer}>
        {isSuccess && chunks && chunks.length > 0 ? (
          chunks?.map((x) => (
            <Card
              key={x.chunk_id}
              title={<ChunkTitle item={x} />}
              className={styles.chunkCard}
              size="small"
            >
              <Flex gap={16} align="flex-start">
                {x.img_id && (
                  <Popover
                    placement="left"
                    title="Image Preview"
                    content={
                      <Image id={x.img_id} className={styles.imagePreview} />
                    }
                  >
                    <div>
                      <Image id={x.img_id} className={styles.image} />
                    </div>
                  </Popover>
                )}
                <div style={{ flex: 1 }}>{x.content_with_weight}</div>
              </Flex>
            </Card>
          ))
        ) : isSuccess && chunks && chunks.length === 0 ? (
          <Empty
            description="未找到匹配的内容。请尝试修改查询文本或调整相似度阈值。"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div className={styles.emptyState}>
            <PackageSearch className={styles.emptyIcon} />
            <Text type="secondary">
              请在左侧输入测试文本并点击"测试"按钮开始检索
            </Text>
          </div>
        )}
      </div>

      {total > 0 && (
        <div className={styles.paginationContainer}>
          <Pagination
            {...pagination}
            size="small"
            total={total}
            onChange={onChange}
            showSizeChanger={false}
            showQuickJumper
            showTotal={(total, range) =>
              `${range[0]}-${range[1]} / ${total} 条`
            }
          />
        </div>
      )}
    </section>
  );
};

export default TestingResult;
