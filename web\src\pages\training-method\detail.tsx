import { Button } from 'antd';
import React from 'react';
import { history, useParams } from 'umi';

import CategoryPanel from '@/pages/data-training/knowledge/components/knowledge-setting/category-panel';

const ChunkMethodDetailPage: React.FC = () => {
  const { method } = useParams<{ method: string }>();

  return (
    <div style={{ padding: 24 }}>
      <Button
        type="link"
        onClick={() => history.back()}
        style={{ marginBottom: 16 }}
      >
        ← 返回
      </Button>
      {/* 使用 CategoryPanel 复用说明 UI */}
      <CategoryPanel chunkMethod={method as string} />
    </div>
  );
};

export default ChunkMethodDetailPage;
