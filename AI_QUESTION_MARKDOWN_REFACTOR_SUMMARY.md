# AI Question Markdown 格式重构总结

## 背景

后台服务`generateAIQuestions`函数返回结果格式已更新为 markdown 格式字符串，不再返回 CSV 格式。由于后台服务返回格式不稳定，需要重构前端程序以适应多种 markdown 格式变体。

## 重构内容

### 1. 更新导入和状态管理

**文件**: `web/src/pages/agent/ai-question/index.tsx`

- 移除了对旧 CSV 处理函数的依赖 (`cleanCsvData`, `csvToMarkdown`, `verifyCsvParsing`, `csvToExcel`)
- 添加了新的 markdown 处理函数导入 (`markdownToExcel`, `testMarkdownParsing`)
- 简化状态管理，移除了`originalCsvData`状态，只保留`markdownData`状态
- 直接使用后台返回的 markdown 格式数据展示和导出

### 2. 创建新的 markdown 解析工具

**文件**: `web/src/utils/markdown-to-excel-utils.ts`

#### 主要函数:

- `parseMarkdownQuestions()`: 解析 markdown 格式题目为结构化数据
- `markdownToExcel()`: 将 markdown 格式题目转换为 Excel 文件
- `testMarkdownParsing()`: 测试和调试 markdown 解析功能

#### 支持的格式变体:

**题目标题格式:**

- 格式 1: `#### 1. 多选题`
- 格式 2: `#### 题目 1 （单选题）`

**题目内容提取:**

- 方法 1: 查找`**包围的内容**`（旧格式兼容）
- 方法 2: 查找标题后到选项之间的纯文本内容（新格式）

**选项格式:**

- 格式 1: `- A. 选项内容` (带前缀)
- 格式 2: `A. 选项内容` (直接开头)

**答案格式:**

- `**正确答案：C**`
- `**正确答案：A,B,C,D**`
- 自动处理判断题的`错误`→`错`，`正确`→`对`转换

**解析格式:**

- `**解析：内容**`
- `**答案解析：内容**`

**题型标准化:**

- 自动将各种题型名称标准化为: `单选题`, `多选题`, `判断题`, `简答题`, `填空题`

### 3. 更新导出功能

- **Word 导出**: 直接使用 markdown 数据调用`exportMarkdownToDocx()`
- **Excel 导出**: 使用新的`markdownToExcel()`函数，自动解析 markdown 并转换为 Excel 格式

### 4. 添加测试和调试功能

**文件**: `web/src/utils/markdown-parsing-test.ts`

- 创建了兼容性测试函数`testMarkdownCompatibility()`
- 包含新格式和旧格式的测试用例
- 在生产环境中添加了调试日志，便于问题排查

## 兼容性特点

### 支持的题型

- 单选题 (包括"选择题"变体)
- 多选题
- 判断题
- 简答题 (包括"问答题"变体)
- 填空题

### 智能解析特性

1. **多格式标题识别**: 自动识别不同的题目标题格式
2. **灵活的内容提取**: 支持有无`**`包围的题目内容
3. **选项格式兼容**: 支持带`-`前缀和不带前缀的选项
4. **答案格式标准化**: 自动处理判断题答案格式
5. **解析内容清理**: 自动移除多余的 markdown 标记

## 使用方式

### 生成结果展示

- 直接使用`response.data.generated_questions`的 markdown 内容
- 通过`HightLightMarkdown`组件渲染显示

### 导出 Word

```javascript
exportMarkdownToDocx(markdownData, fileName)
```

### 导出 Excel

```javascript
markdownToExcel(markdownData, fileName)
```

## 调试支持

- 在开发环境中，每次生成题目后会自动运行解析测试
- 浏览器控制台会输出详细的解析结果
- 可以手动调用`window.testMarkdownCompatibility()`进行兼容性测试

## 优势

1. **高兼容性**: 支持多种 markdown 格式变体
2. **智能解析**: 自动适配不同的格式特征
3. **错误容忍**: 即使部分格式不规范也能尽量提取有效信息
4. **易于维护**: 模块化设计，便于后续扩展和修改
5. **调试友好**: 丰富的日志和测试功能

## 注意事项

- 如果后台返回格式再次发生变化，主要需要更新`parseMarkdownQuestions()`函数中的正则表达式
- 建议在生产环境中保留调试日志一段时间，以便收集实际使用中的格式变体
- Excel 导出依赖于题目解析质量，如果解析失败会显示相应错误信息
