#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import io
import logging
import pandas as pd
import re
from typing import Optional
from agent.component.base import ComponentBase, ComponentParamBase
from api.utils.llm_logger import llm_interaction_context, log_llm_response, log_llm_stream_response

try:
    from rag.nlp.rag_tokenizer import naiveQie as tk  # type: ignore
except Exception:  # fallback simple split
    def tk(txt: str):
        return txt.split()


class LessonPlanParam(ComponentParamBase):
    """
    Define the LessonPlan component parameters.
    """

    def __init__(self):
        super().__init__()
        self.course_name = ""
        self.class_hours = 40  # 默认40分钟
        self.target_audience = ""
        self.course_content = ""
        self.material_file_content = ""
        self.other_requirements = ""
        self.llm_id = ""
        self.template_text = ""
        self.feedback = ""

    def check(self):
        if not self.course_name or not self.course_name.strip():
            raise ValueError("Course name is required and cannot be empty")

        if not self.target_audience or not self.target_audience.strip():
            raise ValueError("Target audience is required and cannot be empty")

        # course_content 和 material_file_content 二选一
        if not self.course_content.strip() and not self.material_file_content.strip():
            raise ValueError(
                "Either course content or material file content is required")

        if self.class_hours <= 0:
            raise ValueError("Class hours must be positive")

        if self.class_hours > 480:  # 最大8小时
            raise ValueError("Class hours cannot exceed 480 minutes (8 hours)")

        if not self.llm_id or not self.llm_id.strip():
            raise ValueError("LLM ID is required and cannot be empty")


class LessonPlan(ComponentBase):
    component_name = "LessonPlan"

    def _run(self, history, **kwargs):
        """
        Execute the lesson plan component to generate lesson plans based on course information.
        """
        try:
            # Parse material file content if provided
            try:
                material_content = self._parse_material_file_content()
            except Exception as e:
                logging.error(
                    f"Failed to parse material file content: {str(e)}")
                return LessonPlan.be_output(f"教案生成失败: 文件内容解析错误 - {str(e)}")

            # Generate lesson plan using LLM
            try:
                response = self._generate_lesson_plan(
                    material_content, history)
            except ValueError as ve:
                # ValueError typically contains user-friendly messages
                logging.error(f"LessonPlan generation failed: {str(ve)}")
                return LessonPlan.be_output(f"教案生成失败: {str(ve)}")
            except Exception as e:
                logging.error(
                    f"Unexpected error in lesson plan generation: {str(e)}")
                return LessonPlan.be_output(f"教案生成失败: 内部错误 - {str(e)}")

            # Validate response before returning
            if not response or not response.strip():
                logging.warning("Generated lesson plan is empty")
                return LessonPlan.be_output("教案生成失败: 生成的内容为空，请检查输入参数")

            # Check if response is too short (likely an error)
            if len(response.strip()) < 100:
                logging.warning(
                    f"Generated lesson plan is too short: {len(response.strip())} characters")
                return LessonPlan.be_output(f"教案生成失败: 生成的内容过短，可能存在问题。生成内容：{response[:200]}...")

            return LessonPlan.be_output(response)

        except Exception as e:
            logging.error(f"LessonPlan component error: {str(e)}")
            return LessonPlan.be_output(f"教案生成失败: 组件执行错误 - {str(e)}")

    def stream_run(self, history, **kwargs):
        """
        Execute the lesson plan component with streaming output for real-time generation.
        Yields chunks of generated content as they become available.
        """
        try:
            # Parse material file content if provided
            try:
                material_content = self._parse_material_file_content()
            except Exception as e:
                logging.error(
                    f"Failed to parse material file content: {str(e)}")
                yield f"错误: 文件内容解析错误 - {str(e)}"
                return

            # Generate lesson plan using streaming LLM
            try:
                # Build comprehensive prompt
                prompt = self._build_lesson_plan_prompt(material_content)

                # Stream generate lesson plan
                accumulated_content = ""
                for chunk in self._stream_call_llm(prompt, history):
                    if chunk and chunk.strip():
                        accumulated_content += chunk
                        yield chunk

                # Validate final content
                if not accumulated_content or len(accumulated_content.strip()) < 100:
                    yield "\n\n---\n**警告**: 生成的教案内容可能不完整，请检查输入参数或重试。"

            except ValueError as ve:
                logging.error(
                    f"Streaming LessonPlan generation failed: {str(ve)}")
                yield f"\n\n错误: {str(ve)}"
            except Exception as e:
                logging.error(
                    f"Unexpected error in streaming lesson plan generation: {str(e)}")
                yield f"\n\n错误: 生成过程中出现内部错误 - {str(e)}"

        except Exception as e:
            logging.error(f"LessonPlan streaming component error: {str(e)}")
            yield f"错误: 组件执行错误 - {str(e)}"

    def _stream_call_llm(self, prompt: str, history):
        """Call LLM with streaming to generate lesson plan in real-time."""
        try:
            # Get tenant_id from canvas
            tenant_id = self._canvas.get_tenant_id()

            # Import LLM services
            from api.db import LLMType
            from api.db.services.llm_service import LLMBundle

            # Create LLM bundle for chat
            chat_mdl = LLMBundle(tenant_id, LLMType.CHAT, self._param.llm_id)

            # Prepare messages for LLM
            system_message = "你是一位资深的教育专家和教学设计师，专注于创建高质量的教学教案。请严格按照用户提供的要求生成专业、实用的教案。"

            # Include limited history if available
            messages = []
            if history:
                limited_history = history[-3:] if len(history) > 3 else history
                for msg in limited_history:
                    if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                        content = msg['content']
                        if len(content) > 1000:
                            content = content[:1000] + "..."
                        messages.append({
                            'role': msg['role'],
                            'content': content
                        })

            # Add user message with prompt
            messages.append({"role": "user", "content": prompt})

            # Use LLM configuration with streaming enabled
            llm_config = {
                "temperature": 0.7,
                "max_tokens": 20480,
                "stream": True  # Enable streaming
            }

            # Use LLM interaction context for logging
            with llm_interaction_context(
                agent_type="lesson_plan",
                tenant_id=tenant_id,
                user_id=getattr(self._canvas, 'user_id', tenant_id),
                llm_model=self._param.llm_id,
                system=system_message,
                history=messages,
                gen_conf=llm_config
            ) as tracker:
                # Call LLM with streaming - check if chat_streamly method exists
                if hasattr(chat_mdl, 'chat_streamly'):
                    # Use the streaming method with logging
                    stream_generator = chat_mdl.chat_streamly(
                        system_message, messages, llm_config)
                    logged_stream = log_llm_stream_response(
                        tracker, stream_generator)

                    for chunk in logged_stream:
                        if isinstance(chunk, int):
                            # Token count, skip
                            continue
                        if hasattr(chunk, 'content') and chunk.content:
                            yield chunk.content
                        elif isinstance(chunk, str):
                            yield chunk
                        elif hasattr(chunk, 'delta') and hasattr(chunk.delta, 'content') and chunk.delta.content:
                            yield chunk.delta.content
                elif hasattr(chat_mdl, 'chat_stream'):
                    # Alternative streaming method name with logging
                    stream_generator = chat_mdl.chat_stream(
                        system_message, messages, llm_config)
                    logged_stream = log_llm_stream_response(
                        tracker, stream_generator)

                    for chunk in logged_stream:
                        if isinstance(chunk, int):
                            # Token count, skip
                            continue
                        if hasattr(chunk, 'content') and chunk.content:
                            yield chunk.content
                        elif isinstance(chunk, str):
                            yield chunk
                else:
                    # Fallback: if no streaming method, use regular chat with simulated streaming
                    logging.warning(
                        "LLM doesn't support streaming, falling back to simulated streaming")
                    response = chat_mdl.chat(system_message, messages, {
                        "temperature": 0.7,
                        "max_tokens": 8192
                    })

                    # Handle different response formats
                    if isinstance(response, tuple):
                        response = response[0]

                    if not response or not isinstance(response, str):
                        raise ValueError("LLM返回了无效的响应")

                    # Log non-streaming response
                    log_llm_response(tracker, response)

                    # Simulate streaming by yielding small chunks with delays
                    import time
                    chunk_size = 10  # Small chunks for better streaming effect
                    for i in range(0, len(response), chunk_size):
                        chunk = response[i:i+chunk_size]
                        yield chunk
                        time.sleep(0.05)  # 50ms delay to simulate typing

        except Exception as e:
            logging.error(f"Streaming LLM call failed: {str(e)}")
            raise ValueError(f"调用流式语言模型失败: {str(e)}")

    def _parse_material_file_content(self) -> str:
        """
        Parse the material file content. This is already in text format from API preprocessing.
        """
        if not self._param.material_file_content:
            return ""

        try:
            # Clean and limit the content
            cleaned_content = self._clean_content(
                self._param.material_file_content)

            # Limit to 8000 characters to prevent token overflow
            if len(cleaned_content) > 20480:
                cleaned_content = cleaned_content[:20480] + "..."
                logging.info(f"Material content truncated to 20480 characters")

            return cleaned_content

        except Exception as e:
            logging.error(f"Failed to parse material file content: {str(e)}")
            return ""

    def _clean_content(self, content: str) -> str:
        """Clean content by removing invalid characters and formatting properly"""
        if not content:
            return ""

        try:
            # Remove excessive whitespace and newlines
            content = re.sub(r'\n\s*\n', '\n', content)  # Remove empty lines
            # Replace multiple spaces with single space
            content = re.sub(r'\s+', ' ', content)
            # Replace tabs with spaces
            content = re.sub(r'\t+', ' ', content)

            # Keep Chinese characters, letters, numbers, spaces, and common punctuation
            content = re.sub(
                r'[^\w\s\u4e00-\u9fff\u3400-\u4dbf\uff00-\uffef.,;:!?()（）【】《》""''、。，；：！？—…·+*/=<>\[\]{}|\\-]', '', content)

            # Split into lines and clean each line
            lines = content.split('\n')
            cleaned_lines = []

            for line in lines:
                line = line.strip()
                if line and len(line) > 2:  # Only keep meaningful lines
                    cleaned_lines.append(line)

            result = '\n'.join(cleaned_lines)

            # Final validation
            if not result.strip():
                logging.warning("Content cleaning resulted in empty string")
                return ""

            return result

        except Exception as e:
            logging.error(f"Error cleaning content: {str(e)}")
            # Return original content if cleaning fails
            return content[:8000] if len(content) > 8000 else content

    def _generate_lesson_plan(self, material_content: str, history) -> str:
        """Generate lesson plan using LLM with the provided prompt template."""
        try:
            # Build comprehensive prompt
            prompt = self._build_lesson_plan_prompt(material_content)

            # Call LLM
            response = self._call_llm(prompt, history)

            if not response or not response.strip():
                raise ValueError("LLM返回了空的响应")

            return response

        except Exception as e:
            logging.error(f"Lesson plan generation error: {str(e)}")
            raise ValueError(f"生成教案失败: {str(e)}")

    def _build_lesson_plan_prompt(self, material_content: str) -> str:
        """Build comprehensive lesson plan generation prompt."""

        # ---------------- Token helpers -----------------
        def _trim_tokens(txt: str, max_toks: int) -> str:
            toks = tk(txt)
            if len(toks) <= max_toks:
                return txt
            return ' '.join(toks[:max_toks]) + '...'

        def _sample_long_text(txt: str, max_toks: int) -> str:
            toks = tk(txt)
            if len(toks) <= max_toks:
                return txt
            ratio = len(toks) / max_toks
            step = int(ratio)
            sampled = toks[::step][:max_toks]
            return ' '.join(sampled) + '...'

        # ---------------- Template excerpt -----------------
        template_excerpt = ""
        if self._param.template_text:
            # Split by blank lines to get fragments
            segments = [s.strip() for s in re.split(
                r"\n\n+", self._param.template_text) if s.strip()]
            top3 = segments[:3]
            processed = [_trim_tokens(seg, 1200) for seg in top3]
            template_excerpt = '\n\n'.join(processed)

        # ---------------- Content source -----------------
        content_source = ""
        if self._param.course_content.strip():
            content_source = _sample_long_text(
                self._param.course_content, 6000)
            content_source = f"""
## 课程内容要求
{content_source}"""

        if material_content.strip():
            material_sampled = _sample_long_text(material_content, 6000)
            content_source += f"""

## 课程材料内容
{material_sampled}"""

        # ---------------- Additional requirements -----------------
        additional_requirements = ""
        if self._param.other_requirements.strip():
            additional_requirements = f"""

## 其他要求
{self._param.other_requirements}"""

        # ---------------- Feedback block -----------------
        feedback_block = ""
        if self._param.feedback.strip():
            feedback_block = f"""

### 上一轮反馈
{self._param.feedback}"""

        prompt = f"""你是一位资深的教育专家和教学设计师，专注于创建高质量的教学教案。现在需要根据提供的信息生成一份详细的教学教案。
## 角色背景知识
- 精通教案设计的基本要素和结构。
- 有丰富的实践经验，能够根据不同的教学目标和学生情况设计合适的教案。
- 对课堂管理、学生互动以及如何提高学生学习兴趣和效率有独到见解。
- 熟悉多媒体等现代教学工具的应用，有强大的逻辑思维与整合能力。

## 专业技能
- 教案结构设计：掌握教案设计的基本结构，包括课题名称、课型、教学目标、教学重难点等。
- 目标设定：能够根据课程要求和学生实际情况明确教学目标。
- 重难点分析：识别教学内容中的重点和难点，设计有效的教学策略。
- 教学方法应用：根据教学内容和目标，灵活运用各种教学方法。
- 教学媒体运用：熟练使用教学媒体，如多媒体、模型等，以提高教学效果。
- 教学评估与反思：通过教学实践，评估教学效果，并进行反思优化。
- 思考与撰写：拥有强大的自我思考与撰写能力，可以根据用户需求提供最大限度的支持。

## 教案模板
{template_excerpt if template_excerpt else '（无）'}

## 课程基本信息
- 课程名称: {self._param.course_name}
- 课时: {self._param.class_hours}分钟
- 授课对象: {self._param.target_audience}
{content_source}
{additional_requirements}
{feedback_block}

## 输出要求
- 教案主体结构应遵照教案模板
- 使用Markdown格式，确保结构与层次清晰明了
- 语言表达专业、准确、易懂，确保教案内容全面、逻辑清晰

请现在开始生成教案。"""

        return prompt

    def _call_llm(self, prompt: str, history) -> str:
        """Call LLM to generate lesson plan based on the prompt."""
        try:
            # Get tenant_id from canvas
            tenant_id = self._canvas.get_tenant_id()

            # Import LLM services
            from api.db import LLMType
            from api.db.services.llm_service import LLMBundle

            # Create LLM bundle for chat
            chat_mdl = LLMBundle(tenant_id, LLMType.CHAT, self._param.llm_id)

            # Prepare messages for LLM
            system_message = "你是一位资深的教育专家和教学设计师，专注于创建高质量的教学教案。请严格按照用户提供的要求生成专业、实用的教案。"

            # Include limited history if available
            messages = []
            if history:
                limited_history = history[-3:] if len(history) > 3 else history
                for msg in limited_history:
                    if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                        content = msg['content']
                        if len(content) > 1000:
                            content = content[:1000] + "..."
                        messages.append({
                            'role': msg['role'],
                            'content': content
                        })

            # Add user message with prompt
            messages.append({"role": "user", "content": prompt})

            # Use LLM configuration with reasonable limits
            llm_config = {
                "temperature": 0.7,
                "max_tokens": 20480
            }

            # Use LLM interaction context for logging
            with llm_interaction_context(
                agent_type="lesson_plan",
                tenant_id=tenant_id,
                user_id=getattr(self._canvas, 'user_id', tenant_id),
                llm_model=self._param.llm_id,
                system=system_message,
                history=messages,
                gen_conf=llm_config
            ) as tracker:
                # Call LLM with proper error handling
                response = chat_mdl.chat(system_message, messages, llm_config)

                # Handle different response formats and error detection
                if isinstance(response, tuple):
                    response = response[0]

                # Check for error responses from LLM
                if isinstance(response, str) and response.startswith("**ERROR**"):
                    # Extract error details
                    error_msg = response.replace("**ERROR**", "").strip()
                    if ":" in error_msg:
                        error_msg = error_msg.split(":", 1)[1].strip()
                    raise ValueError(f"LLM returned error: {error_msg}")

                if not response or not isinstance(response, str):
                    raise ValueError("LLM返回了无效的响应")

                # Additional validation for empty or too short responses
                if len(response.strip()) < 50:
                    raise ValueError("LLM返回的响应过短，可能生成失败")

                # Log the response
                log_llm_response(tracker, response)

                return response

        except Exception as e:
            logging.error(f"LLM call failed: {str(e)}")
            # Re-raise with more context for upper level handling
            if "LLM returned error" in str(e):
                raise ValueError(str(e))
            else:
                raise ValueError(f"调用语言模型失败: {str(e)}")
