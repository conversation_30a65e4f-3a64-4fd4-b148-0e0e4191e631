import { useShowDeleteConfirm, useTranslate } from '@/hooks/common-hooks';
import { useRemoveNextDocument } from '@/hooks/document-hooks';
import { IDocumentInfo } from '@/interfaces/database/document';
import { downloadDocument } from '@/utils/file-util';
import {
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  InboxOutlined,
  MoreOutlined,
  ToolOutlined,
} from '@ant-design/icons';
import { Button, Dropdown, MenuProps } from 'antd';
import { isParserRunning } from '../utils';

import { useCallback } from 'react';
import { DocumentType } from '../constant';
import styles from './index.less';

interface IProps {
  record: IDocumentInfo;
  setCurrentRecord: (record: IDocumentInfo) => void;
  showRenameModal: () => void;
  showChangeParserModal: () => void;
  showSetMetaModal: () => void;
}

const ParsingActionCell = ({
  record,
  setCurrentRecord,
  showRenameModal,
  showChangeParserModal,
  showSetMetaModal,
}: IProps) => {
  const documentId = record.id;
  const isRunning = isParserRunning(record.run);
  const { t } = useTranslate('knowledgeDetails');
  const { removeDocument } = useRemoveNextDocument();
  const showDeleteConfirm = useShowDeleteConfirm();
  const isVirtualDocument = record.type === DocumentType.Virtual;

  const onRmDocument = () => {
    if (!isRunning) {
      showDeleteConfirm({
        onOk: () => removeDocument([documentId]),
        content: record?.parser_config?.graphrag?.use_graphrag
          ? t('deleteDocumentConfirmContent')
          : '',
      });
    }
  };

  const onDownloadDocument = () => {
    downloadDocument({
      id: documentId,
      filename: record.name,
    });
  };

  const setRecord = useCallback(() => {
    setCurrentRecord(record);
  }, [record, setCurrentRecord]);

  const onShowRenameModal = () => {
    setRecord();
    showRenameModal();
  };

  const onShowChangeParserModal = () => {
    setRecord();
    showChangeParserModal();
  };

  const onShowSetMetaModal = useCallback(() => {
    setRecord();
    showSetMetaModal();
  }, [setRecord, showSetMetaModal]);

  const menuItems: MenuProps['items'] = [
    {
      key: 'rename',
      icon: <EditOutlined className={styles.menuIcon} />,
      label: <span className={styles.menuText}>重命名</span>,
      onClick: onShowRenameModal,
      disabled: isRunning,
      className: styles.menuItem,
    },
    ...(isVirtualDocument
      ? []
      : [
          {
            key: 'chunk-method',
            icon: <ToolOutlined className={styles.menuIcon} />,
            label: <span className={styles.menuText}>解析方法</span>,
            onClick: onShowChangeParserModal,
            disabled: isRunning || record.parser_id === 'tag',
            className: styles.menuItem,
          },
          {
            key: 'meta-data',
            icon: <InboxOutlined className={styles.menuIcon} />,
            label: <span className={styles.menuText}>归档</span>,
            onClick: onShowSetMetaModal,
            disabled: isRunning,
            className: styles.menuItem,
          },
          {
            key: 'download',
            icon: <DownloadOutlined className={styles.menuIcon} />,
            label: <span className={styles.menuText}>下载</span>,
            onClick: onDownloadDocument,
            disabled: isRunning,
            className: styles.menuItem,
          },
        ]),
    {
      type: 'divider',
    },
    {
      key: 'delete',
      icon: <DeleteOutlined className={styles.menuIcon} />,
      label: <span className={styles.menuText}>删除</span>,
      onClick: onRmDocument,
      disabled: isRunning,
      className: `${styles.menuItem} ${styles.deleteItem}`,
    },
  ];

  return (
    <Dropdown
      menu={{ items: menuItems }}
      trigger={['click']}
      placement="bottomRight"
      overlayClassName={styles.dropdownOverlay}
    >
      <Button type="text" className={styles.moreButton}>
        <MoreOutlined />
      </Button>
    </Dropdown>
  );
};

export default ParsingActionCell;
