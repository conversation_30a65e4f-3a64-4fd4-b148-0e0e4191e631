import React from 'react';
import { GenerateCube } from './generate-cube';

export const GenerateCubeExample: React.FC = () => {
  return (
    <div
      style={{
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        gap: '40px',
      }}
    >
      <h2>Generate Cube Loading Component Examples</h2>

      {/* 默认样式 */}
      <div
        style={{
          height: '200px',
          border: '1px solid #eee',
          borderRadius: '8px',
        }}
      >
        <h3>Default (Yellow)</h3>
        <GenerateCube />
      </div>

      {/* 蓝色主题 */}
      <div
        style={{
          height: '200px',
          border: '1px solid #eee',
          borderRadius: '8px',
        }}
      >
        <h3>Blue Theme</h3>
        <GenerateCube color="#3B82F6" />
      </div>

      {/* 红色主题 */}
      <div
        style={{
          height: '200px',
          border: '1px solid #eee',
          borderRadius: '8px',
        }}
      >
        <h3>Red Theme</h3>
        <GenerateCube color="#EF4444" />
      </div>

      {/* 绿色主题，小尺寸 */}
      <div
        style={{
          height: '150px',
          border: '1px solid #eee',
          borderRadius: '8px',
        }}
      >
        <h3>Green Theme (Small Size)</h3>
        <GenerateCube color="#10B981" size={30} />
      </div>

      {/* 紫色主题，大尺寸 */}
      <div
        style={{
          height: '250px',
          border: '1px solid #eee',
          borderRadius: '8px',
        }}
      >
        <h3>Purple Theme (Large Size)</h3>
        <GenerateCube color="#8B5CF6" size={70} />
      </div>

      {/* 自定义类名 */}
      <div
        style={{
          height: '200px',
          border: '1px solid #eee',
          borderRadius: '8px',
        }}
      >
        <h3>With Custom className</h3>
        <GenerateCube color="#F59E0B" size={50} className="my-custom-cube" />
      </div>
    </div>
  );
};

export default GenerateCubeExample;
