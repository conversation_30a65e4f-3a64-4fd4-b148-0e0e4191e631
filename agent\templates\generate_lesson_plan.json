{"name": "生成教案", "description": "根据提供的课程信息和材料，生成一个结构完整的教案。支持上传课程材料文件，结合教案模板库生成专业的教学教案。", "dsl": {"components": {"Begin:Start": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "您好！我是教案生成助手，我可以帮您根据课程信息和材料生成专业的教学教案。请提供课程名称、课时、授课对象等信息。", "query": [{"key": "course_name", "name": "课程名称", "value": ""}, {"key": "class_hours", "name": "课时（分钟）", "value": ""}, {"key": "target_audience", "name": "授课对象", "value": ""}, {"key": "course_content", "name": "课程内容要求", "value": ""}, {"key": "material_file_content", "name": "课程材料文件内容（可选）", "value": ""}, {"key": "other_requirements", "name": "其他要求（可选）", "value": ""}]}}, "downstream": ["Template:QueryBuilder"]}, "Template:QueryBuilder": {"obj": {"component_name": "Template", "params": {"content": "教案模板 {Begin:Start@course_name} {Begin:Start@target_audience}", "parameters": []}}, "downstream": ["Retrieval:TemplateRetrieval"]}, "Retrieval:TemplateRetrieval": {"obj": {"component_name": "Retrieval", "params": {"kb_ids": ["6fdc95de3bb711f08a02428c2b71dd13"], "top_n": 5, "similarity_threshold": 0.3, "keywords_similarity_weight": 0.7, "empty_response": "未找到相关教案模板，将使用默认模板格式生成教案。"}}, "downstream": ["Generate:LessonPlan"]}, "Generate:LessonPlan": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-r1:32b@Ollama", "temperature": 0.6, "max_tokens": 20480, "prompt": "你是一位资深的教育专家和教学设计师，专注于创建高质量的教学教案。现在需要根据提供的信息生成一份详细的教学教案。\n\n## 课程基本信息\n- **课程名称**: {Begin:Start@course_name}\n- **课时**: {Begin:Start@class_hours}分钟\n- **授课对象**: {Begin:Start@target_audience}\n- **课程内容要求**: {Begin:Start@course_content}\n\n## 教案模板参考\n以下是从教案模板库中检索到的相关模板，请参考其结构和格式：\n{Retrieval:TemplateRetrieval}\n\n## 课程材料\n{Begin:Start@material_file_content}\n\n## 其他要求\n{Begin:Start@other_requirements}\n\n## 教案生成要求\n\n请严格按照以下结构生成完整的教学教案，确保内容专业、实用：\n\n### 1. 教学设计概述\n- 课程基本信息总结\n- 教学理念和方法\n\n### 2. 教学目标\n- **知识目标**: 学生应掌握的具体知识点\n- **能力目标**: 学生应培养的技能和能力\n- **情感目标**: 学生应形成的态度和价值观\n\n### 3. 教学重难点\n- **教学重点**: 本课程的核心内容\n- **教学难点**: 学生理解和掌握的困难点\n\n### 4. 教学准备\n- **教具准备**: 需要的教学工具和设备\n- **学习材料**: 学生需要的学习资源\n\n### 5. 教学过程\n请根据课时长度合理安排教学环节，包括：\n- **导入环节**（建议5-10分钟）: 激发学习兴趣，引入新课\n- **新课讲授**（主要部分）: 核心知识点的讲解和演示\n- **练习巩固**（建议10-15分钟）: 学生练习和互动环节\n- **总结提升**（建议5-10分钟）: 课程总结和知识升华\n- **作业布置**（建议2-5分钟）: 课后练习和拓展任务\n\n每个环节请详细说明：\n- 时间分配（分钟）\n- 教学活动内容\n- 教师具体行为\n- 学生预期活动\n- 设计意图说明\n\n### 6. 教学方法与策略\n- 采用的教学方法和策略\n- 教学手段和技术运用\n- 互动方式设计\n\n### 7. 评价方式\n- 课堂即时评价方法\n- 学习效果检测方式\n- 评价标准和指标\n\n### 8. 教学反思与改进\n- 预期教学效果分析\n- 可能遇到的问题及解决方案\n- 后续改进建议\n\n## 输出要求\n1. 使用Markdown格式，确保结构清晰\n2. 内容要具体可操作，避免空泛的描述\n3. 时间安排要合理，符合实际教学需要\n4. 教学方法要多样化，注重学生参与\n5. 语言表达要专业、准确、易懂\n6. 确保教案的完整性和实用性\n\n请现在开始生成教案："}}, "downstream": ["Generate:FormatOutput"]}, "Generate:FormatOutput": {"obj": {"component_name": "Generate", "params": {"llm_id": "deepseek-r1:32b@Ollama", "temperature": 0.2, "max_tokens": 20480, "prompt": "请将以下教案内容进行最终格式化和优化，确保：\n1. Markdown格式规范，标题层级清晰\n2. 内容结构完整，逻辑清楚\n3. 语言表达专业、准确\n4. 删除重复内容，保持简洁\n5. 确保所有部分都有具体内容，避免空项\n\n原始教案内容：\n{Generate:LessonPlan}\n\n请输出最终优化后的教案："}}, "downstream": ["Answer:Final"]}, "Answer:Final": {"obj": {"component_name": "Answer", "params": {"format": "markdown"}}, "upstream": ["Generate:FormatOutput"]}}}}