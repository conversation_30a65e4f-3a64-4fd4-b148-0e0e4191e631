# Document API Refactor Summary

## 概述 (Overview)

重构了 Python 后端服务中的文档列表 API，使其能够在不指定知识库 ID 的情况下自动查找默认的租户知识库 (`tenant-domain-knowledge-base`)。

## 更改内容 (Changes Made)

### 1. 修改现有 API 端点

**文件**: `api/apps/sdk/doc.py`

**端点**: `GET /api/v1/datasets/<dataset_id>/documents`

**更改**:

- 添加了对特殊 dataset_id 值的处理逻辑
- 当 dataset_id 为以下值时，自动查找默认知识库：
  - 空字符串 (`""`)
  - `"default"`
  - `"none"`
  - `"null"`

**实现逻辑**:

```python
# Handle default dataset lookup
if not dataset_id or dataset_id.lower() in ['default', 'none', 'null', '']:
    # Look for the default tenant knowledge base
    try:
        exists, kb = KnowledgebaseService.get_by_name('tenant-domain-knowledge-base', tenant_id)
        if not exists:
            return get_error_data_result(message="Default knowledge base 'tenant-domain-knowledge-base' not found.")
        dataset_id = kb.id
    except Exception as e:
        return get_error_data_result(message=f"Error finding default knowledge base: {str(e)}")
```

### 2. 新增 API 端点

**端点**: `GET /api/v1/documents`

**功能**: 直接列出默认租户知识库中的文档，无需指定 dataset_id

**特点**:

- 自动查找名为 `tenant-domain-knowledge-base` 的知识库
- 支持所有原有的查询参数（分页、排序、关键词搜索等）
- 返回格式与原 API 完全兼容

## API 使用方式 (API Usage)

### 原有方式（仍然支持）

```bash
# 指定具体的知识库ID
GET /api/v1/datasets/{actual_kb_id}/documents

# 使用特殊值访问默认知识库
GET /api/v1/datasets/default/documents
GET /api/v1/datasets/none/documents
GET /api/v1/datasets/null/documents
```

### 新增方式

```bash
# 直接访问默认知识库的文档
GET /api/v1/documents
```

### 查询参数 (Query Parameters)

所有端点都支持以下参数：

- `id`: 文档 ID 筛选
- `name`: 文档名称筛选
- `page`: 页码（默认：1）
- `page_size`: 每页条数（默认：30）
- `orderby`: 排序字段（默认：create_time）
- `desc`: 是否降序（默认：true）
- `keywords`: 关键词搜索

## 错误处理 (Error Handling)

### 默认知识库不存在

```json
{
  "code": 102,
  "message": "Default knowledge base 'tenant-domain-knowledge-base' not found."
}
```

### 权限不足

```json
{
  "code": 102,
  "message": "You don't own the dataset {dataset_id}."
}
```

### 查找默认知识库时发生错误

```json
{
  "code": 102,
  "message": "Error finding default knowledge base: {error_details}"
}
```

## 兼容性 (Compatibility)

- ✅ 完全向后兼容
- ✅ 原有 API 调用方式不受影响
- ✅ 响应格式保持一致
- ✅ 所有查询参数功能正常

## 测试 (Testing)

创建了测试脚本 `test_document_api.py` 来验证：

1. 新的 `/api/v1/documents` 端点
2. 使用 `default` 作为 dataset_id 的端点
3. 使用空 dataset_id 的端点

运行测试：

```bash
python test_document_api.py
```

## 前端调用示例 (Frontend Usage Examples)

### JavaScript/TypeScript

```typescript
// 使用新端点
const response = await fetch('/api/v1/documents', {
  headers: {
    Authorization: `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  }
})

// 或使用原端点的特殊值
const response = await fetch('/api/v1/datasets/default/documents', {
  headers: {
    Authorization: `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  }
})
```

### Python SDK

```python
# 如果SDK需要更新以支持新端点
documents = client.list_documents()  # 无需指定dataset_id

# 或使用现有方式
documents = client.list_documents(dataset_id='default')
```

## 实现细节 (Implementation Details)

### 依赖的服务方法

- `KnowledgebaseService.get_by_name()`: 根据名称查找知识库
- `KnowledgebaseService.accessible()`: 检查知识库访问权限
- `DocumentService.get_list()`: 获取文档列表

### 数据流程

1. 检查 dataset_id 是否为特殊值
2. 如果是，调用`get_by_name('tenant-domain-knowledge-base', tenant_id)`
3. 获取默认知识库的 ID
4. 继续执行原有的文档列表逻辑

## 注意事项 (Notes)

1. **默认知识库命名**: 系统必须存在名为 `tenant-domain-knowledge-base` 的知识库
2. **权限检查**: 用户必须对默认知识库有访问权限
3. **租户隔离**: 每个租户都有自己的默认知识库
4. **错误处理**: 如果默认知识库不存在，会返回明确的错误信息
