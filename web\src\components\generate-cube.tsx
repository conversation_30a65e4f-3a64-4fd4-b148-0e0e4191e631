import { cn } from '@/lib/utils';
import React from 'react';
import styles from './generate-cube.module.css';

interface GenerateCubeProps {
  className?: string;
  color?: string;
  size?: number;
}

export const GenerateCube: React.FC<GenerateCubeProps> = ({
  className,
  color = '#FED74C',
  size = 50,
}) => {
  // Generate darker and lighter colors for pseudo-elements
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : { r: 254, g: 215, b: 76 };
  };

  const rgb = hexToRgb(color);
  const beforeColor = `rgb(${Math.max(0, rgb.r - 50)}, ${Math.max(0, rgb.g - 50)}, ${Math.max(0, rgb.b - 50)})`;
  const afterColor = `rgb(${Math.min(255, rgb.r - 30)}, ${Math.max(0, rgb.g - 30)}, ${Math.max(0, rgb.b - 30)})`;

  const containerStyle: React.CSSProperties = {
    fontSize: `${size}px`,
  };

  const cubeStyle: React.CSSProperties = {
    background: color,
  };

  return (
    <div className={cn(styles.container, className)} style={containerStyle}>
      <div className={styles.box}>
        <div
          className={styles.cube}
          style={
            {
              ...cubeStyle,
              '--before-color': beforeColor,
              '--after-color': afterColor,
            } as React.CSSProperties & { [key: string]: string }
          }
        />
        <div
          className={styles.cube}
          style={
            {
              ...cubeStyle,
              '--before-color': beforeColor,
              '--after-color': afterColor,
            } as React.CSSProperties & { [key: string]: string }
          }
        />
        <div
          className={styles.cube}
          style={
            {
              ...cubeStyle,
              '--before-color': beforeColor,
              '--after-color': afterColor,
            } as React.CSSProperties & { [key: string]: string }
          }
        />
        <div
          className={styles.cube}
          style={
            {
              ...cubeStyle,
              '--before-color': beforeColor,
              '--after-color': afterColor,
            } as React.CSSProperties & { [key: string]: string }
          }
        />
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html: `
          .${styles.cube}::before {
            background-color: var(--before-color);
          }
          .${styles.cube}::after {
            background-color: var(--after-color);
          }
        `,
        }}
      />
    </div>
  );
};

export default GenerateCube;
