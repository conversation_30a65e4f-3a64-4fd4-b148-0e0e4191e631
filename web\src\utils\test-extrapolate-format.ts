import { parseMarkdownQuestions } from './markdown-to-excel-utils';

// 举一反三实际返回的数据格式（包含问答题格式）
const extrapolateTestData = `

### 1. 【单选题】  
在CTC系统中，当车站自律机与中心子系统的通信中断时，车站将转为（ ）模式。  
A. 车站控制  
B. 中心遥控  
C. 非常站控  
D. 分散自律  

**答案：** C  
**解析：** 当CTC系统中的车站自律机与中心子系统的通信中断时，车站将转为"非常站控"模式，以便人工干预和处理异常情况。其他选项中，"车站控制"是正常状态下的模式，而"分散自律"则是系统正常运行时的模式。

---

### 2. 【多选题】  
以下哪些设备属于CTCS-3级列控系统的组成部分？  
A. RBC（无线闭塞中心）  
B. 应答器  
C. CTC（调度集中系统）  
D. 车载ATP设备  

**答案：** ABD  
**解析：** CTCS-3级列控系统主要由RBC（无线闭塞中心）、应答器和车载ATP设备组成，用于实现对列车的实时监控和控制。CTC（调度集中系统）虽然与列控系统相关联，但并不直接属于CTCS-3级系统的组成部分。

---

### 3. 【判断题】  
在ZPW-2000A型无绝缘轨道电路中，调谐区的长度为29米。  

**答案：** 正确  
**解析：** ZPW-2000A型无绝缘轨道电路的调谐区标准长度为29米，其功能是平滑过渡信号频率，并确保轨道电路的正常工作。

---

### 4. 【填空题】  
在驼峰自动化系统中，用于测量车组溜放速度的设备是（）。  

**答案：** 测速雷达  
**解析：** 测速雷达是驼峰自动化系统中的关键设备之一，用于实时测量车组的溜放速度，并为系统的控制和调整提供数据支持。

---

### 5. 【问答题】  
请简述CTCS-2级列控系统的主要特点及其适用场景。  

**答案：**  
CTCS-2级列控系统主要特点是基于轨道电路传输列车运行许可信息，适用于既有线路的改造和高速铁路的部分区段。其特点是能够实现对列车速度的有效控制，并支持双向行车功能。该系统适用于需要较高运营效率但基础设施较为传统的线路场景。

**解析：** CTCS-2级列控系统是中国铁路列车运行控制系统的重要组成部分，在铁路现代化建设中发挥着重要作用。

---
`;

export const testExtrapolateFormat = () => {
  console.log('=== 测试举一反三格式解析（包含问答题） ===');
  console.log('原始数据长度:', extrapolateTestData.length);
  console.log('原始数据预览:', extrapolateTestData.slice(0, 200) + '...');

  const result = parseMarkdownQuestions(extrapolateTestData);

  console.log('\n=== 解析结果 ===');
  console.log('解析出的题目数量:', result.length);

  result.forEach((question, index) => {
    console.log(`\n--- 题目 ${index + 1} ---`);
    console.log('题型:', question.题型);
    console.log(
      '题目内容:',
      question.题目内容.slice(0, 50) +
        (question.题目内容.length > 50 ? '...' : ''),
    );
    console.log('正确答案:', question.正确答案);
    console.log('选项A:', question.答案A);
    console.log('选项B:', question.答案B);
    console.log('选项C:', question.答案C);
    console.log('选项D:', question.答案D);
    console.log(
      '解析:',
      question.解析.slice(0, 50) + (question.解析.length > 50 ? '...' : ''),
    );

    // 特别检查第5题的答案提取
    if (index === 4) {
      // 第5题（索引4）
      console.log('\n🔍 第5题详细检查:');
      console.log('题型是否为问答题:', question.题型 === '问答题');
      console.log('答案是否为空:', question.正确答案 === '');
      console.log('答案长度:', question.正确答案.length);
      if (question.正确答案) {
        console.log('答案内容预览:', question.正确答案.slice(0, 100) + '...');
      }
    }
  });

  // 检查是否所有题目都有有效内容
  const validQuestions = result.filter((q) => q.题目内容 && q.题型);
  const questionsWithAnswers = result.filter(
    (q) => q.正确答案 && q.正确答案.trim() !== '',
  );

  console.log('\n=== 验证结果 ===');
  console.log('有效题目数量:', validQuestions.length);
  console.log('有答案的题目数量:', questionsWithAnswers.length);
  console.log('预期题目数量: 5');
  console.log(
    '解析成功率:',
    `${Math.round((validQuestions.length / 5) * 100)}%`,
  );
  console.log(
    '答案提取成功率:',
    `${Math.round((questionsWithAnswers.length / 5) * 100)}%`,
  );

  if (validQuestions.length === 0) {
    console.error('❌ 没有解析出任何有效题目!');
  } else if (validQuestions.length < 5) {
    console.warn(`⚠️ 只解析出 ${validQuestions.length}/5 个题目`);
  } else if (questionsWithAnswers.length < 5) {
    console.warn(`⚠️ 只有 ${questionsWithAnswers.length}/5 个题目有答案`);
    console.log('缺少答案的题目:');
    result.forEach((q, i) => {
      if (!q.正确答案 || q.正确答案.trim() === '') {
        console.log(
          `- 题目 ${i + 1}: ${q.题型} - ${q.题目内容.slice(0, 30)}...`,
        );
      }
    });
  } else {
    console.log('✅ 解析成功，所有题目都有答案!');
  }

  return result;
};
