from __future__ import annotations

"""Shared enumeration & domain structures for exam-related agents."""

from dataclasses import dataclass
from enum import Enum


class Difficulty(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class QuestionMethod(str, Enum):
    RANDOM = "random"
    BY_TYPE = "by_type"


@dataclass(frozen=True)
class QuestionDistribution:
    """Number of questions per type (all optional)."""

    single_choice: int = 0
    multiple_choice: int = 0
    fill_blank: int = 0
    true_false: int = 0
    short_answer: int = 0
    ordering: int = 0

    @property
    def total(self) -> int:
        return (
            self.single_choice
            + self.multiple_choice
            + self.fill_blank
            + self.true_false
            + self.short_answer
            + self.ordering
        )
