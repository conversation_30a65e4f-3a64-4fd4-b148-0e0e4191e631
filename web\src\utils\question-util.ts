import { message } from 'antd';
import { parseMarkdownV2 } from './markdown-to-excel-utils';

// ParsedQuestion 接口定义（与 markdown-to-excel-utils.ts 中的接口保持一致）
interface ParsedQuestion {
  题型: string;
  题目内容: string;
  难易度: string;
  正确答案: string;
  答案A: string;
  答案B: string;
  答案C: string;
  答案D: string;
  答案E: string;
  答案F: string;
  答案G: string;
  解析: string;
  标签?: string;
}

// JSON 结构接口定义
interface QuestionObject {
  detail: string;
  questionType: string;
  answer: string;
  answerA: string;
  answerB: string;
  answerC: string;
  answerD: string;
  answerE: string;
  answerF: string;
  answerG: string;
  answerExplain: string;
  labelName?: string;
  difficulty?: string;
}

interface QuestionPayload {
  token: string;
  obj: QuestionObject[];
}

/**
 * 将 ParsedQuestion 转换为 QuestionObject
 */
const convertParsedQuestionToObject = (
  question: ParsedQuestion,
): QuestionObject => {
  return {
    detail: question.题目内容 || '',
    questionType: question.题型 || '',
    difficulty: question.难易度 || '',
    answer: question.正确答案 || '',
    answerA: question.答案A || '',
    answerB: question.答案B || '',
    answerC: question.答案C || '',
    answerD: question.答案D || '',
    answerE: question.答案E || '',
    answerF: question.答案F || '',
    answerG: question.答案G || '',
    answerExplain: question.解析 || '',
    labelName: question.标签 || '',
  };
};

/**
 * 将 markdown 内容转换为 JSON 格式并发送到指定端点
 * @param markdownContent - markdown 格式的题目内容
 * @param token - 认证令牌
 * @param endpoint - 目标端点 URL
 * @returns Promise<boolean> - 发送是否成功
 */
export const convertAndSendQuestions = async (
  markdownContent: string,
  token: string,
  endpoint: string,
): Promise<boolean> => {
  try {
    // 验证输入参数
    if (!markdownContent || markdownContent.trim() === '') {
      message.error('没有数据可转换');
      return false;
    }

    if (!token || token.trim() === '') {
      message.error('缺少认证令牌');
      return false;
    }

    if (!endpoint || endpoint.trim() === '') {
      message.error('缺少目标端点');
      return false;
    }

    // 使用 parseMarkdownV2 解析 markdown 内容
    const questions = parseMarkdownV2(markdownContent);

    if (questions.length === 0) {
      message.error('未找到有效的题目数据，请检查markdown格式是否正确');
      return false;
    }

    // 转换为目标 JSON 格式
    const questionObjects: QuestionObject[] = questions.map(
      convertParsedQuestionToObject,
    );

    // 构建最终的 payload
    const payload: QuestionPayload = {
      token: token.trim(),
      obj: questionObjects,
    };

    // 发送 POST 请求
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('发送请求失败:', response.status, errorText);
      message.error(`发送失败: ${response.status} ${response.statusText}`);
      return false;
    }

    // 尝试解析响应
    let responseData;
    try {
      responseData = await response.json();
    } catch (e) {
      // 如果响应不是 JSON 格式，获取文本内容
      responseData = await response.text();
    }

    console.log('发送成功，响应:', responseData);
    message.success(`成功发送 ${questionObjects.length} 道题目到指定端点`);
    return true;
  } catch (error) {
    console.error('转换或发送题目失败:', error);
    message.error('转换或发送题目失败，请检查网络连接和端点地址');
    return false;
  }
};

/**
 * 仅转换 markdown 内容为 JSON 格式（不发送）
 * @param markdownContent - markdown 格式的题目内容
 * @param token - 认证令牌
 * @returns QuestionPayload | null - 转换后的 JSON 数据或 null（如果转换失败）
 */
export const convertQuestionsToJson = (
  markdownContent: string,
  token: string,
): QuestionPayload | null => {
  try {
    // 验证输入参数
    if (!markdownContent || markdownContent.trim() === '') {
      console.error('没有数据可转换');
      return null;
    }

    if (!token || token.trim() === '') {
      console.error('缺少认证令牌');
      return null;
    }

    // 使用 parseMarkdownV2 解析 markdown 内容
    const questions = parseMarkdownV2(markdownContent);

    if (questions.length === 0) {
      console.error('未找到有效的题目数据');
      return null;
    }

    // 转换为目标 JSON 格式
    const questionObjects: QuestionObject[] = questions.map(
      convertParsedQuestionToObject,
    );

    // 构建最终的 payload
    const payload: QuestionPayload = {
      token: token.trim(),
      obj: questionObjects,
    };

    return payload;
  } catch (error) {
    console.error('转换题目失败:', error);
    return null;
  }
};

/**
 * 预览转换结果（用于调试）
 * @param markdownContent - markdown 格式的题目内容
 * @param token - 认证令牌
 */
export const previewConversion = (
  markdownContent: string,
  token: string,
): void => {
  const result = convertQuestionsToJson(markdownContent, token);
  if (result) {
    console.log('转换预览结果:');
    console.log('题目数量:', result.obj.length);
    console.log('完整数据:', JSON.stringify(result, null, 2));

    // 显示第一道题目的详细信息
    if (result.obj.length > 0) {
      console.log('第一道题目详情:', result.obj[0]);
    }
  } else {
    console.log('转换失败');
  }
};
