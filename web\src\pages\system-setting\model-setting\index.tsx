import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Grid,
  Input,
  Row,
  Space,
  Tag,
  Typography,
} from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './index.less';

const { Title } = Typography;
const { useBreakpoint } = Grid;

interface ModelProvider {
  name: string;
  icon: string;
  type: 'LLM' | 'Embedding' | 'Rerank' | 'TTS' | 'ASR';
  models?: string[];
}

const ModelSetting = () => {
  const { t } = useTranslation();
  const screens = useBreakpoint();

  // 模拟模型数据，基于图片中显示的内容
  const modelProviders: ModelProvider[] = [
    {
      name: 'deepseek',
      icon: '🔵',
      type: 'LLM',
      models: ['deepseek-chat'],
    },
    {
      name: '4.0-8k',
      icon: '⭐',
      type: 'LLM',
      models: ['ERNIE-Lite-8K-...'],
    },
    {
      name: 'qwen-plus',
      icon: '🌟',
      type: 'LLM',
      models: ['qwen-plus'],
    },
    {
      name: 'llama3:8b',
      icon: '🦙',
      type: 'LLM',
      models: ['llama3:8b'],
    },
    {
      name: 'qwen:0.5',
      icon: '🤖',
      type: 'LLM',
      models: ['qwen:0.5b'],
    },
    {
      name: 'general',
      icon: '🔄',
      type: 'LLM',
      models: ['general'],
    },
    {
      name: 'GLM-4',
      icon: '✨',
      type: 'LLM',
      models: [],
    },
    {
      name: 'openai-kimi',
      icon: '🌀',
      type: 'LLM',
      models: [],
    },
  ];

  const getTypeColor = (type: ModelProvider['type']) => {
    switch (type) {
      case 'LLM':
        return 'blue';
      case 'Embedding':
        return 'green';
      case 'Rerank':
        return 'orange';
      case 'TTS':
        return 'purple';
      case 'ASR':
        return 'red';
      default:
        return 'default';
    }
  };

  const handleAddModel = () => {
    // TODO: 实现添加模型功能
    console.log('添加模型');
  };

  return (
    <div className={styles.modelSettingWrapper}>
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <Title level={2} style={{ margin: 0 }}>
            模型设置
          </Title>
        </div>
        <div className={styles.headerRight}>
          <Space>
            <Input
              placeholder="搜索模型"
              prefix={<SearchOutlined />}
              style={{ width: 300 }}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddModel}
            >
              添加模型
            </Button>
          </Space>
        </div>
      </div>

      <div className={styles.providersSection}>
        <div className={styles.sectionHeader}>
          <Title level={4}>供应商</Title>
          <Title level={4}>全部模型</Title>
        </div>

        <Row gutter={[16, 16]}>
          {modelProviders.map((provider, index) => (
            <Col key={index} xs={24} sm={12} md={8} lg={6} xl={6}>
              <Card
                className={styles.modelCard}
                bodyStyle={{ padding: '16px' }}
              >
                <div className={styles.modelHeader}>
                  <Space>
                    <span className={styles.modelIcon}>{provider.icon}</span>
                    <div>
                      <div className={styles.modelName}>{provider.name}</div>
                      <Space size="small">
                        <span className={styles.modelType}>模型类型</span>
                        <Tag color={getTypeColor(provider.type)}>
                          {provider.type}
                        </Tag>
                      </Space>
                      {provider.models && provider.models.length > 0 && (
                        <div>
                          <span className={styles.modelType}>基础模型</span>
                          <span className={styles.modelValue}>
                            {provider.models[0]}
                          </span>
                        </div>
                      )}
                    </div>
                  </Space>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default ModelSetting;
