import { Chart } from '@antv/g2';
import { useCallback, useEffect, useMemo, useRef } from 'react';

const WordCloudChart: React.FC = () => {
  const domRef = useRef<HTMLDivElement>(null);
  let chartRef = useRef<Chart>();

  // 中文铁路专业词汇词云数据
  const data = useMemo(() => {
    const words = [
      { text: '高速铁路', value: 100, color: '#1f77b4' },
      { text: '道岔转辙机', value: 95, color: '#ff7f0e' },
      { text: '信号系统', value: 85, color: '#2ca02c' },
      { text: '轨道电路', value: 80, color: '#d62728' },
      { text: '联锁设备', value: 75, color: '#9467bd' },
      { text: '调车信号', value: 70, color: '#8c564b' },
      { text: '区间信号', value: 65, color: '#e377c2' },
      { text: '机车信号', value: 60, color: '#7f7f7f' },
      { text: '微机联锁', value: 55, color: '#bcbd22' },
      { text: '计算机联锁', value: 50, color: '#17becf' },
      { text: '电务段', value: 48, color: '#1f77b4' },
      { text: '车站信号楼', value: 45, color: '#ff7f0e' },
      { text: '牵引供电', value: 42, color: '#2ca02c' },
      { text: '接触网', value: 40, color: '#d62728' },
      { text: '变电所', value: 38, color: '#9467bd' },
      { text: '电力机车', value: 35, color: '#8c564b' },
      { text: '动车组', value: 32, color: '#e377c2' },
      { text: '铁路局', value: 30, color: '#7f7f7f' },
      { text: 'CTC系统', value: 28, color: '#bcbd22' },
      { text: 'ATP系统', value: 25, color: '#17becf' },
      { text: '闭塞设备', value: 23, color: '#1f77b4' },
      { text: '进路表示器', value: 20, color: '#ff7f0e' },
      { text: '信号继电器', value: 18, color: '#2ca02c' },
      { text: '道岔表示器', value: 16, color: '#d62728' },
      { text: '轨道绝缘', value: 15, color: '#9467bd' },
      { text: '钢轨连接', value: 14, color: '#8c564b' },
      { text: '防护信号', value: 12, color: '#e377c2' },
      { text: '调车作业', value: 10, color: '#7f7f7f' },
      { text: '站场设备', value: 8, color: '#bcbd22' },
      { text: '通信基站', value: 7, color: '#17becf' },
      { text: '列车监控', value: 6, color: '#1f77b4' },
      { text: '安全防护', value: 5, color: '#ff7f0e' },
    ];

    return words.map((word) => ({
      name: word.text,
      text: word.text,
      value: word.value,
      color: word.color,
    }));
  }, []);

  const renderWordCloud = useCallback(() => {
    if (domRef.current && data.length) {
      // 清除之前的图表
      if (chartRef.current) {
        chartRef.current.destroy();
      }

      chartRef.current = new Chart({
        container: domRef.current,
        autoFit: true,
      });

      chartRef.current.options({
        type: 'wordCloud',
        data: data,
        layout: {
          spiral: 'rectangular',
          fontSize: [8, 32],
          padding: 2,
        },
        encode: {
          color: 'color',
        },
        style: {
          fontFamily: 'Arial, sans-serif',
          fontWeight: 'bold',
        },
        legend: false,
        tooltip: {
          title: 'name',
          items: ['value'],
        },
      });

      chartRef.current.render();
    }
  }, [data]);

  useEffect(() => {
    renderWordCloud();

    return () => {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    };
  }, [renderWordCloud]);

  return (
    <div
      ref={domRef}
      style={{ width: '100%', height: '100%', minHeight: '200px' }}
    ></div>
  );
};

export default WordCloudChart;
