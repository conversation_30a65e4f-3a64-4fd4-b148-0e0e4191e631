# 教案生成页面相关文件推荐功能实现

## 功能概述

在「教案生成」页面的右侧「生成结果」视图中，在 markdown 组件下方添加了相关文件推荐列表。当用户点击「一键生成」按钮后，系统会并行执行教案生成和相关文件搜索，然后在教案内容下方显示推荐的相关资源文件。

## 技术实现

### 1. 后端 Agent 组件

#### FileKeywordSearch Agent (`agent/component/file_keyword_search.py`)

**核心功能**：

- 使用 LLM 从输入文本中提取关键词并按权重排序
- 在当前租户的文件系统中搜索匹配的文件
- 返回按权重排序的文件列表

**主要参数**：

- `llm_id`: LLM 模型 ID
- `max_keywords`: 最大关键词数量 (默认 5)
- `max_results`: 最大返回文件数 (默认 5)
- `temperature`: LLM 生成温度 (默认 0.3)

**工作流程**：

1. 输入验证和关键词提取
2. 使用 DriveService 搜索匹配文件
3. 结果去重和权重排序
4. 返回 JSON 格式的搜索结果

#### API 端点 (`api/apps/agent_app.py`)

**路由**: `/v1/agent/file_keyword_search/search`

**请求格式**：

```json
{
  "content": "课程内容文本",
  "llm_id": "模型ID",
  "max_keywords": 5,
  "max_results": 5,
  "temperature": 0.3
}
```

**响应格式**：

```json
{
  "code": 0,
  "data": {
    "keywords": [
      {"keyword": "关键词", "weight": 权重}
    ],
    "matched_files": [
      {
        "id": "文件ID",
        "name": "文件名",
        "matched_keyword": "匹配的关键词",
        "keyword_weight": 权重,
        "size": 文件大小,
        "type": "文件类型",
        "create_time": "创建时间"
      }
    ],
    "total_matches": 匹配数量,
    "message": "状态消息"
  }
}
```

### 2. 前端实现

#### API 服务 (`web/src/services/agent_service.ts`)

添加了 `searchRelatedFiles` 函数：

```typescript
export const searchRelatedFiles = async (
  params: FileKeywordSearchParams,
): Promise<FileKeywordSearchResponse>
```

#### 页面组件修改 (`web/src/pages/agent/lesson-plan/index.tsx`)

**新增状态**：

- `relatedFiles`: 存储相关文件列表
- `searchingFiles`: 搜索状态标识

**核心修改**：

1. **并行执行**：教案生成和文件搜索同时进行
2. **数据转换**：将文件数据转换为 SourceCard 格式
3. **UI 集成**：使用现有的 SourceSection 组件显示结果

**关键代码片段**：

```typescript
// 并行执行教案生成和相关文件搜索
const promises = []
promises.push(generateLessonPlan(formData))
promises.push(
  searchRelatedFiles({
    content: searchContent,
    llm_id: data.llm_id,
    max_keywords: 5,
    max_results: 5,
    temperature: 0.3
  })
)

const [lessonPlanResponse, relatedFilesResponse] = await Promise.all(promises)
```

#### UI 组件复用

使用现有的 `SourceSection` 和 `SourceCard` 组件：

```tsx
<SourceSection title="相关资源" sources={relatedFiles} onSourceClick={handleSourceClick} />
```

## 功能特性

### 1. 智能关键词提取

- 使用 LLM 理解课程内容语义
- 自动提取最相关的关键词
- 按重要性权重排序

### 2. 精准文件匹配

- 基于关键词在文件名中搜索
- 支持租户隔离，确保数据安全
- 按匹配权重排序结果

### 3. 用户体验优化

- 并行执行，不影响教案生成速度
- 最多显示 5 个相关文件
- 点击文件可查看详情（可扩展）

### 4. 错误处理

- LLM 调用失败时的回退机制
- 网络错误的优雅处理
- 空结果的友好提示

## 使用流程

1. **用户输入**：在教案生成表单中填写课程信息
2. **点击生成**：点击「一键生成」按钮
3. **并行处理**：
   - 系统调用 LessonPlan Agent 生成教案
   - 同时调用 FileKeywordSearch Agent 搜索相关文件
4. **结果展示**：
   - 显示生成的教案内容
   - 在下方显示相关文件推荐列表
5. **交互操作**：用户可点击文件查看详情

## 配置参数

### 搜索参数

- **max_keywords**: 5 (提取的最大关键词数量)
- **max_results**: 5 (显示的最大文件数量)
- **temperature**: 0.3 (LLM 生成的温度参数)

### 显示格式

- **文件标题**: 文件名
- **匹配信息**: 显示匹配的关键词
- **文件描述**: 文件大小和创建时间

## 扩展建议

### 1. 功能增强

- 支持文件内容搜索（不仅仅是文件名）
- 添加文件类型过滤
- 支持模糊匹配和同义词搜索

### 2. 用户体验

- 添加文件预览功能
- 支持文件下载
- 添加收藏和标记功能

### 3. 性能优化

- 实现搜索结果缓存
- 支持分页加载
- 添加搜索历史记录

## 技术亮点

1. **组件复用**: 充分利用现有的 SourceCard 组件
2. **并行处理**: 提高用户体验，不增加等待时间
3. **错误处理**: 完善的异常处理机制
4. **类型安全**: 完整的 TypeScript 类型定义
5. **租户隔离**: 确保数据安全和权限控制

## 总结

成功实现了教案生成页面的相关文件推荐功能，通过智能关键词提取和文件匹配，为用户提供了更丰富的教学资源推荐。该功能与现有系统无缝集成，提升了教案生成工具的实用性和用户体验。
