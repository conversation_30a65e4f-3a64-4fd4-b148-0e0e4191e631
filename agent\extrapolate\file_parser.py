from __future__ import annotations

"""File parsing utilities for Extrapolate agent.

This reuses logic from AIQuestion's SampleFileParser but can be extended to
handle Word/Excel/PDF in the future.
"""

import csv
import logging
from io import String<PERSON>
from typing import Tuple, List

MAX_CONTENT_LENGTH = 10_240


def _clean_text(text: str) -> str:
    return "\n".join(line.strip() for line in text.splitlines() if line.strip())


def parse_txt(content_bytes: bytes) -> str:
    try:
        text = content_bytes.decode("utf-8", errors="ignore")
    except UnicodeDecodeError:
        text = content_bytes.decode("gbk", errors="ignore")
    return _clean_text(text)


def parse_csv(content_bytes: bytes) -> Tuple[str, List[str]]:
    try:
        text = content_bytes.decode("utf-8", errors="ignore")
    except UnicodeDecodeError:
        text = content_bytes.decode("gbk", errors="ignore")
    reader = csv.reader(StringIO(text))
    rows = list(reader)
    if not rows:
        return "", []
    headers = rows[0]
    body_lines = [", ".join(row) for row in rows[1:]]
    return _clean_text("\n".join(body_lines)), headers


# ---------------- Excel (.xlsx/.xls) ----------------
def parse_excel(content_bytes: bytes) -> Tuple[str, List[str]]:  # noqa: D401 E501
    """Parse Excel bytes into plain text; return cleaned content & headers."""
    try:
        import pandas as pd  # heavy import delayed
        from io import BytesIO

        df = pd.read_excel(BytesIO(content_bytes), dtype=str, header=0)
        headers = list(df.columns.astype(str))
        # concatenate rows as CSV-like lines
        body_lines = []
        for _, row in df.iterrows():
            line = ", ".join(row.fillna("").astype(str))
            body_lines.append(line)
        return _clean_text("\n".join(body_lines)), headers
    except Exception as e:
        logging.warning("[ExtrapolateFileParser] Excel parse failed: %s", e)
        return "", []


class SampleFileParser:
    SUPPORTED = {"txt": parse_txt, "csv": parse_csv,
                 "xlsx": parse_excel, "xls": parse_excel}

    def __call__(self, filename: str, file_bytes: bytes) -> Tuple[str, List[str]]:
        suffix = filename.lower().rsplit(
            ".", 1)[-1] if "." in filename else "txt"
        parser = self.SUPPORTED.get(suffix)
        if not parser:
            # Unsupported format – raise explicit error so caller can handle
            raise ValueError(
                f"Unsupported file type: .{suffix}. Only {', '.join(self.SUPPORTED.keys())} are supported.")
        content, *rest = parser(file_bytes)
        content = content[:MAX_CONTENT_LENGTH] + \
            "..." if len(content) > MAX_CONTENT_LENGTH else content
        headers = rest[0] if rest else []
        return content, headers
