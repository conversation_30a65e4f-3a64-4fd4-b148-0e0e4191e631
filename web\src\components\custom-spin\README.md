# Custom Spin 组件库

一个功能丰富的自定义加载动画组件库，提供多种风格的加载动画，完全兼容 Ant Design 的 Spin 组件 API。

## 特性

- 🎨 **多种动画风格**：CircleSpin（圆形）、LeafSpin（旋转点）等
- 🔄 **完全兼容 Ant Design**：API 与 antd Spin 组件完全一致
- 🎯 **TypeScript 支持**：完整的类型定义
- 🌈 **主题适配**：支持亮色/暗色主题
- 📱 **响应式设计**：适配移动端和桌面端
- ⚡ **性能优化**：CSS 动画，硬件加速
- 🎪 **自定义样式**：支持 CSS 变量自定义

## 组件列表

### CircleSpin - 圆形加载动画

经典的圆形加载动画，使用 CSS 实现的 C 形弧线旋转效果。

### LeafSpin - 旋转点加载动画

精美的4个旋转点加载动画，采用独特的错位旋转效果，具有流畅的动画体验。

## 安装和使用

```tsx
// 方式1：导入特定组件
import { CircleSpin, LeafSpin } from '@/components/custom-spin';

// 方式2：导入默认组件（向后兼容）
import CustomSpin from '@/components/custom-spin';

// 方式3：动态导入
import { createSpinComponent } from '@/components/custom-spin';
const SpinComponent = createSpinComponent('circle');
```

## 基础用法

### CircleSpin 示例

```tsx
import React from 'react';
import { CircleSpin } from '@/components/custom-spin';

// 基础用法
<CircleSpin spinning={true} />

// 带提示文本
<CircleSpin spinning={true} tip="加载中..." />

// 包装内容
<CircleSpin spinning={loading} tip="正在加载数据...">
  <div>这里是你的内容</div>
</CircleSpin>

// 不同尺寸
<CircleSpin spinning={true} size="small" />
<CircleSpin spinning={true} size="default" />
<CircleSpin spinning={true} size="large" />
```

### LeafSpin 示例

```tsx
import React from 'react';
import { LeafSpin } from '@/components/custom-spin';

// 基础用法
<LeafSpin spinning={true} />

// 带提示文本
<LeafSpin spinning={true} tip="处理中..." />

// 包装内容
<LeafSpin spinning={loading} tip="正在处理数据...">
  <div>这里是你的内容</div>
</LeafSpin>

// 使用自定义文本
<LeafSpin spinning={true} text="加载中..." />

// 不显示文本
<LeafSpin spinning={true} />
```

## API 参数

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| spinning | 是否为加载中状态 | boolean | false |
| tip | 当作为包裹元素时，可以自定义描述文案 | string | - |
| size | 组件大小 | `'small'` \| `'default'` \| `'large'` | 'default' |
| className | 自定义类名 | string | - |
| style | 自定义样式 | CSSProperties | - |
| children | 被包裹的子元素 | ReactNode | - |
| text | 自定义加载文本（仅LeafSpin支持） | string | - |

## 自定义样式

### CircleSpin 自定义

```tsx
<CircleSpin
  spinning={true}
  style={
    {
      '--primary-color': '#52c41a', // 自定义主色
      '--spin-overlay-bg': 'rgba(255, 255, 255, 0.9)', // 遮罩背景
      '--spin-tip-color': '#666', // 提示文本颜色
    } as React.CSSProperties
  }
/>
```

### LeafSpin 自定义

```tsx
<LeafSpin
  spinning={true}
  text="自定义加载文本"
  style={
    {
      '--primary-color': '#ff6b6b', // 旋转点颜色
      '--spin-overlay-bg': 'rgba(0, 0, 0, 0.1)', // 遮罩背景
    } as React.CSSProperties
  }
/>
```

## 主题适配

组件自动适配系统主题和项目主题：

```css
/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  /* 自动适配暗色主题 */
}

/* 项目主题 */
[data-theme='dark'] {
  /* 适配项目暗色主题 */
}
```

## 替换 Ant Design Spin

### 1. 全局替换

```tsx
// 替换前
import { Spin } from 'antd';

// 替换后
import { CircleSpin as Spin } from '@/components/custom-spin';
```

### 2. 批量替换正则表达式

```bash
# 查找
from 'antd'

# 替换为
from 'antd'
import { CircleSpin as Spin } from '@/components/custom-spin'

# 然后移除 Spin 从 antd 的导入
```

### 3. 渐进式替换

```tsx
import { Spin } from 'antd';
import { CircleSpin } from '@/components/custom-spin';

// 新功能使用自定义组件
<CircleSpin spinning={loading} tip="加载中...">
  <NewComponent />
</CircleSpin>

// 旧功能保持不变
<Spin spinning={loading}>
  <LegacyComponent />
</Spin>
```

## 性能优化

- **CSS 动画**：使用 CSS 而非 JavaScript 动画，性能更好
- **硬件加速**：启用 GPU 加速，动画更流畅
- **按需加载**：只导入需要的组件
- **缓存优化**：组件内部优化重复渲染

## 浏览器兼容性

- **现代浏览器**：Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **移动端**：iOS Safari 12+, Android Chrome 60+
- **CSS 特性**：需要支持 CSS 变量、`transform`、`animation`

## 开发指南

### 添加新的 Spin 组件

1. 创建组件文件：`CustomSpin.tsx`
2. 创建样式文件：`CustomSpin.less`
3. 更新类型定义：`types.ts`
4. 更新主入口：`index.ts`
5. 添加演示：`demo.tsx`

### 组件开发模板

```tsx
import classNames from 'classnames';
import React from 'react';
import { CustomSpinProps } from './types';
import './CustomSpin.less';

const CustomSpin: React.FC<CustomSpinProps> = ({
  spinning = false,
  tip,
  size = 'default',
  className,
  style,
  children,
}) => {
  // 渲染加载动画
  const renderLoadingAnimation = () => (
    <div className="custom-animation">{/* 你的自定义动画 */}</div>
  );

  // 组件逻辑...
};

export default CustomSpin;
```

## 更新日志

### v2.0.0 (重构版本)

- 🎉 重构为多组件架构
- ✨ 新增 CircleSpin 和 LeafSpin 组件
- 🔄 保持向后兼容性
- 📝 完善文档和类型定义

### v1.0.0 (初始版本)

- 🎊 基础 CustomSpin 组件
- 🎨 CSS 圆形加载动画
- 📱 响应式设计支持

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

---

**提示**：如果你正在从 Ant Design 的 Spin 组件迁移，所有的 API 都保持兼容，只需要更改导入路径即可。
