import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { IFile } from '@/interfaces/database/file-manager';
import '@/pages/drive/modal-styles.less';
import { Tag, X } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface EditTagsDialogProps {
  visible: boolean;
  hideModal: () => void;
  onOk: (tags: string[]) => void;
  loading: boolean;
  file: IFile | null;
}

export function EditTagsDialog({
  visible,
  hideModal,
  onOk,
  loading,
  file,
}: EditTagsDialogProps) {
  const { t } = useTranslation('translation', {
    keyPrefix: 'fileManager',
  });
  const [tags, setTags] = useState<string[]>([]);
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    if (file && visible) {
      setTags(file.tags || []);
    }
  }, [file, visible]);

  const handleAddTag = () => {
    const trimmedValue = inputValue.trim();
    if (trimmedValue && !tags.includes(trimmedValue)) {
      setTags([...tags, trimmedValue]);
      setInputValue('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const handleSubmit = () => {
    onOk(tags);
  };

  const handleCancel = () => {
    setTags(file?.tags || []);
    setInputValue('');
    hideModal();
  };

  return (
    <Dialog open={visible} onOpenChange={handleCancel}>
      <DialogContent className="ht-modal-content">
        <DialogHeader className="ht-modal-header">
          <DialogTitle className="ht-modal-title">
            <Tag className="h-5 w-5" />
            {t('editTags')}
          </DialogTitle>
        </DialogHeader>

        <div className="ht-modal-body space-y-4">
          <div className="form-group">
            <Label htmlFor="fileName">{t('fileName')}</Label>
            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
              {file?.name}
            </div>
          </div>

          <div className="form-group">
            <Label htmlFor="tagInput">{t('tags')}</Label>
            <div className="flex gap-2">
              <Input
                id="tagInput"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={t('enterTagName')}
                className="flex-1"
              />
              <Button
                type="button"
                onClick={handleAddTag}
                disabled={
                  !inputValue.trim() || tags.includes(inputValue.trim())
                }
                className="ht-modal-btn secondary"
              >
                {t('add', { keyPrefix: 'common' })}
              </Button>
            </div>
          </div>

          <div className="form-group">
            <Label>{t('currentTags')}</Label>
            <div className="min-h-[60px] p-3 border rounded-md bg-gray-50">
              {tags.length === 0 ? (
                <div className="text-sm text-gray-400 text-center py-2">
                  {t('noTags')}
                </div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
                    >
                      <span>{tag}</span>
                      <button
                        type="button"
                        onClick={() => handleRemoveTag(tag)}
                        className="hover:bg-blue-200 rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="ht-modal-footer">
          <Button
            onClick={handleCancel}
            disabled={loading}
            className="ht-modal-btn secondary"
          >
            {t('cancel', { keyPrefix: 'common' })}
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading}
            className="ht-modal-btn primary"
          >
            {loading
              ? t('saving', { keyPrefix: 'common' })
              : t('save', { keyPrefix: 'common' })}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
