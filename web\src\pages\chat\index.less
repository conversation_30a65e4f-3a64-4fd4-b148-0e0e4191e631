@import '@/theme/high-tech-theme.less';

.chatWrapper {
  width: 100%;
  height: calc(100vh - var(--header-height) + 4px);
  background-color: var(--card-background);
  // border-radius: 12px;
  // border: 1px solid var(--border-color);
  overflow: hidden;
  display: flex;

  .chatTitleWrapper {
    width: 260px;
    min-width: 260px;
    max-width: 260px;
    padding: 8px 0;
    background-color: var(--card-background);
    position: relative;
    flex-shrink: 0;
  }

  .chatTitle {
    padding: 0 var(--spacing-md);

    b {
      font-size: var(--font-size-lg);
      background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      display: inline-block;
    }

    .btnCircle {
      width: 32px;
      height: 32px;
      padding: 6px;
    }

    :global {
      .ant-tag {
        background-color: #0000000f;
        border-color: #0000000f;
      }
    }
  }

  .chatTitleContent {
    padding: 0 var(--spacing-md);
    overflow: auto;
    height: calc(100vh - 140px);
  }

  .chatSpin {
    :global(.ant-spin-container) {
      display: flex;
      flex-direction: column;
      gap: 0;
    }
  }

  .chatTitleCard {
    margin-bottom: 1px;
    cursor: pointer;
    position: relative;
    transition: all var(--transition-fast);
    border-radius: var(--border-radius-md);
    border: none;
    background: transparent;

    :global(.ant-card-body) {
      padding: 6px 12px;
      min-height: 36px;
      height: 36px;
      background: transparent;
      border-radius: var(--border-radius-md);
      transition: all var(--transition-fast);
      display: flex;
      align-items: center;
    }

    &:hover {
      background-color: #f1f3f4;

      :global(.ant-card-body) {
        background-color: #f1f3f4;
      }
    }

    .moreIcon {
      color: var(--text-tertiary);
      font-size: 14px;
      font-weight: bold;
      padding: 4px;
      border-radius: var(--border-radius-sm);
      transition: all var(--transition-fast);
      cursor: pointer;

      &:hover {
        color: var(--text-secondary);
        background-color: var(--neutral-200);
      }
    }

    .chatTitleCardDropdown {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      opacity: 0;
      transition: opacity var(--transition-fast);
    }

    &:hover .chatTitleCardDropdown {
      opacity: 1;
    }

    :global(.ant-typography) {
      font-size: var(--font-size-sm);
      line-height: 1.5;
      color: var(--text-primary);
      font-weight: 400;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .chatTitleCardSelected {
    background-color: #0000000f;

    :global(.ant-card-body) {
      background-color: transparent;
    }

    :global(.ant-typography) {
      color: var(--text-primary);
      font-weight: 400;
    }

    .chatTitleCardDropdown {
      opacity: 1;
    }

    .moreIcon {
      color: var(--text-secondary);

      &:hover {
        background-color: rgba(59, 130, 246, 0.15);
      }
    }

    &:hover {
      background-color: #d6ebfa;

      :global(.ant-card-body) {
        background-color: #d6ebfa;
      }
    }
  }

  .chatTitleCardSelectedDark {
    background-color: rgba(59, 130, 246, 0.15);

    :global(.ant-card-body) {
      background-color: rgba(59, 130, 246, 0.15);
    }

    :global(.ant-typography) {
      color: var(--text-primary);
      font-weight: 400;
    }

    .chatTitleCardDropdown {
      opacity: 1;
    }

    .moreIcon {
      color: var(--text-secondary);

      &:hover {
        background-color: rgba(59, 130, 246, 0.2);
      }
    }

    &:hover {
      background-color: rgba(59, 130, 246, 0.25);

      :global(.ant-card-body) {
        background-color: rgba(59, 130, 246, 0.25);
      }
    }
  }

  .divider {
    margin: 0;
    height: 100%;
    border-color: #0000000f;
  }

  .chatTitleDivider {
    margin: 0 0 12px 0;
    border-color: #00000000;
  }
}

/* Dark theme adjustments */
.dark {
  .chatWrapper {
    .chatTitleCard {
      &:hover {
        background-color: var(--neutral-800);

        :global(.ant-card-body) {
          background-color: var(--neutral-800);
        }
      }

      .moreIcon {
        &:hover {
          background-color: var(--neutral-700);
        }
      }
    }

    .chatTitleCardSelected {
      background-color: rgba(59, 130, 246, 0.15);

      :global(.ant-card-body) {
        background-color: rgba(59, 130, 246, 0.15);
      }

      :global(.ant-typography) {
        color: var(--text-primary);
        font-weight: 400;
      }
    }
  }
}

/* Button styling */
:global {
  .ant-btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);

    &:hover {
      background-color: var(--primary-hover) !important;
      border-color: var(--primary-hover) !important;
    }
  }

  .ant-divider {
    border-color: var(--border-color);
  }

  .ant-tag {
    border-radius: var(--border-radius-pill);
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }

  .ant-typography {
    line-height: 1.4;
  }
}
