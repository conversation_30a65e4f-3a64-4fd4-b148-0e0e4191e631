import json
import logging
from abc import ABC
import pandas as pd

from api.db import LLMType
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.llm_service import LLMBundle
from api import settings
from agent.component.base import ComponentBase, ComponentParamBase
from rag.prompts import kb_prompt  # May need a custom prompt strategy
from api.utils.llm_logger import llm_interaction_context, log_llm_response

DEFAULT_EXAM_KB_NAME = "tenant-exam-question"  # Or make this configurable
# Default Exam knowledge base ID
DEFAULT_EXAM_KB_ID = "e8c714443bba11f087f66e73e1d4fed3"


class AIQuestionParam(ComponentParamBase):
    """
    Parameters for the AI Question Generation component.
    """

    def __init__(self):
        super().__init__()
        self.llm_id = "deepseek-r1:32b@Ollama"
        self.knowledge_points = ""  # User input: "内容及知识点"
        self.exam_scene = ""       # User input: "出题场景"
        self.objective = ""        # User input: "出题目标"

        # "出题方式" - How questions are counted
        # Corresponds to 'random' in UI (question_generation_type)
        self.random_questions_enabled = True
        # Total questions if random_questions_enabled is true
        self.question_count = 10

        self.by_question_type_enabled = False  # Corresponds to 'byType' in UI
        self.single_choice_count = 10
        self.multiple_choice_count = 10
        self.fill_blank_count = 10
        self.true_false_count = 10
        self.short_answer_count = 10
        self.ordering_count = 10

        self.other_requirements = ""  # Optional additional instructions

        # Knowledge base configuration
        # ID of the "Exam" knowledge base (source of templates)
        self.exam_kb_id = DEFAULT_EXAM_KB_ID
        self.kb_ids = []  # Will be populated dynamically with user's KBs + DEFAULT_EXAM_KB_ID

        # Retrieval parameters (can be fine-tuned)
        self.exam_kb_top_n = 10  # How many templates/examples to fetch from Exam KB
        self.other_kb_top_n = 10  # How many chunks to fetch from other KBs
        self.similarity_threshold = 0.2

        # Initialize output attribute to prevent AttributeError
        self.output = None

    def check(self):
        # Don't call super().check() as it raises NotImplementedError
        self.check_empty(self.llm_id, "[AIQuestion] LLM ID cannot be empty.")

        kp_is_empty_or_whitespace = not self.knowledge_points or \
            (isinstance(self.knowledge_points, str)
             and self.knowledge_points.strip() == "")

        if kp_is_empty_or_whitespace:
            logging.info(
                f"[AIQuestionParam.check] knowledge_points is empty. Attempting fallbacks.")

            es_is_valid_fallback = self.exam_scene and isinstance(
                self.exam_scene, str) and self.exam_scene.strip() != ""
            logging.info(
                f"[AIQuestionParam.check] exam_scene ('{self.exam_scene}') is valid for fallback: {es_is_valid_fallback}")

            if es_is_valid_fallback:
                self.knowledge_points = self.exam_scene
                logging.warning(
                    f"[AIQuestion] Empty knowledge_points, using exam_scene as fallback: {self.knowledge_points}")
            else:
                obj_is_valid_fallback = self.objective and isinstance(
                    self.objective, str) and self.objective.strip() != ""
                logging.info(
                    f"[AIQuestionParam.check] objective ('{self.objective}') is valid for fallback: {obj_is_valid_fallback}")
                if obj_is_valid_fallback:
                    self.knowledge_points = self.objective
                    logging.warning(
                        f"[AIQuestion] Empty knowledge_points, using objective as fallback: {self.knowledge_points}")
                else:
                    logging.error(
                        f"[AIQuestionParam.check] All fields (knowledge_points, exam_scene, objective) are effectively empty. Raising ValueError.")
                    raise ValueError(
                        "[AIQuestion] Knowledge points cannot be empty. Please provide knowledge points, exam_scene, or objective.")

        # self.check_empty(self.exam_kb_id, "[AIQuestion] Exam KB ID cannot be empty.") # Might be optional if a default name is used

        if self.by_question_type_enabled:
            total_type_questions = (
                self.single_choice_count +
                self.multiple_choice_count +
                self.fill_blank_count +
                self.true_false_count +
                self.short_answer_count +
                self.ordering_count
            )
            if total_type_questions <= 0:
                raise ValueError(
                    "[AIQuestion] If 'by question type' is enabled, at least one question type count must be greater than 0.")
        elif self.random_questions_enabled:
            if self.question_count <= 0:
                raise ValueError(
                    "[AIQuestion] If 'random questions' is enabled, question count must be greater than 0.")
        else:  # Should not happen based on UI logic
            raise ValueError(
                "[AIQuestion] A question generation mode (random or by type) must be selected.")


class AIQuestion(ComponentBase, ABC):
    component_name = "AIQuestion"

    def _get_total_questions_to_generate(self):
        param = self._param
        if param.by_question_type_enabled:
            return (
                param.single_choice_count +
                param.multiple_choice_count +
                param.fill_blank_count +
                param.true_false_count +
                param.short_answer_count +
                param.ordering_count
            )
        return param.question_count

    def _get_question_type_distribution_prompt(self):
        param = self._param
        if not param.by_question_type_enabled:
            return ""

        parts = []
        if param.single_choice_count > 0:
            parts.append(f"{param.single_choice_count}道单选题")
        if param.multiple_choice_count > 0:
            parts.append(f"{param.multiple_choice_count}道多选题")
        if param.fill_blank_count > 0:
            parts.append(f"{param.fill_blank_count}道填空题")
        if param.true_false_count > 0:
            parts.append(f"{param.true_false_count}道判断题")
        if param.short_answer_count > 0:
            parts.append(f"{param.short_answer_count}道简答题")
        if param.ordering_count > 0:
            parts.append(f"{param.ordering_count}道排序题")

        if not parts:
            return ""
        return "生成考题的类型及数量为：" + ", ".join(parts) + "."

    def _retrieve_knowledge(self, tenant_id, kb_ids, query, top_n=10, is_exam_kb=False):
        """
        Retrieves knowledge from the specified knowledge bases.

        Args:
            tenant_id: The tenant ID
            kb_ids: List of knowledge base IDs to search in
            query: The search query
            top_n: Number of results to return
            is_exam_kb: Whether this is for the exam KB (changes search behavior)

        Returns:
            Tuple of (concatenated text string, list of retrieved chunks)
        """
        if not kb_ids:
            return "", []

        try:
            # Get the knowledge bases by IDs
            kbs = KnowledgebaseService.get_by_ids(kb_ids)
            if not kbs:
                logging.warning(f"No knowledge bases found for IDs: {kb_ids}")
                return "", []

            # Get the embedding model from the first KB or use default tenant embedding
            embd_id = None
            if kbs and kbs[0].embd_id:
                embd_id = kbs[0].embd_id

            if not embd_id:
                # Try to get a default embedding model
                default_embd = LLMBundle(tenant_id, LLMType.EMBEDDING)
                if not default_embd.model_name:
                    logging.warning(
                        f"No embedding model configured for KB or tenant")
                    return "", []
                embd_id = default_embd.model_name

            # Create embedding model bundle
            embd_mdl = LLMBundle(tenant_id, LLMType.EMBEDDING, embd_id)

            # Adapt the query if this is for retrieving exam templates
            retrieval_query = query
            if is_exam_kb:
                retrieval_query = "不同题型的考题范例"

            # Use retrievaler service to get results from KB
            from api import settings
            kbinfos = settings.retrievaler.retrieval(
                retrieval_query,
                embd_mdl,
                [kb.tenant_id for kb in kbs],  # Should be the same tenant_id
                kb_ids,
                1,  # page
                top_n,
                self._param.similarity_threshold,
                vector_similarity_weight=0.5,  # Default, can be tuned
                aggs=False  # No aggregations needed
            )

            if not kbinfos or not kbinfos.get("chunks"):
                logging.info(
                    f"No chunks retrieved for query '{retrieval_query}'")
                return "", []

            # Format for LLM with content length limits to prevent resource exhaustion
            content_list = []
            total_chars = 0
            # Increased from 8000/12000 to support longer context
            MAX_CONTENT_LENGTH = 36000 if is_exam_kb else 72000
            MAX_CHUNK_LENGTH = 4096  # Increased from 1000 to allow longer individual chunks

            for chunk in kbinfos["chunks"]:
                if 'content' not in chunk:
                    continue

                chunk_content = chunk['content']

                # Truncate individual chunk if too long
                if len(chunk_content) > MAX_CHUNK_LENGTH:
                    chunk_content = chunk_content[:MAX_CHUNK_LENGTH] + "..."

                # Check if adding this chunk would exceed total limit
                if total_chars + len(chunk_content) > MAX_CONTENT_LENGTH:
                    if content_list:  # We have some content already
                        break
                    else:  # First chunk is too long, take part of it
                        chunk_content = chunk_content[:MAX_CONTENT_LENGTH-100] + "..."

                content_list.append(chunk_content)
                total_chars += len(chunk_content)

                if total_chars >= MAX_CONTENT_LENGTH:
                    break

            concatenated_content = "\n\n".join(content_list)

            # Log content length for monitoring
            logging.info(
                f"Retrieved {len(content_list)} chunks, total length: {len(concatenated_content)} chars")

            return concatenated_content, kbinfos["chunks"]

        except Exception as e:
            logging.error(f"Error retrieving knowledge: {e}", exc_info=True)
            return "", []

    def _run(self, history, **kwargs):
        param: AIQuestionParam = self._param
        tenant_id = self._canvas.get_tenant_id()

        # 动态获取当前用户的所有知识库
        try:
            # 获取当前用户的ID
            user_id = None
            if hasattr(self._canvas, 'get_user_id'):
                user_id = self._canvas.get_user_id()
            elif hasattr(self._canvas, '_user_id'):
                user_id = self._canvas._user_id

            # 如果无法获取用户ID，使用tenant_id作为用户ID
            if not user_id:
                user_id = tenant_id
                logging.warning(
                    f"[AIQuestion] Could not get user_id, using tenant_id {tenant_id} as fallback")

            # 获取用户加入的租户列表（包括自己的租户）
            from api.db.services.user_service import TenantService
            joined_tenants = TenantService.get_joined_tenants_by_user_id(
                user_id)
            tenant_ids = [t["tenant_id"]
                          for t in joined_tenants] if joined_tenants else [tenant_id]

            # 获取用户可访问的所有知识库
            kbs, _ = KnowledgebaseService.get_by_tenant_ids(
                tenant_ids, user_id,
                page_number=0, items_per_page=0,  # 获取所有
                orderby="create_time", desc=True, keywords="",
                parser_id=None
            )
            user_kb_ids = [kb["id"] for kb in kbs if kb and "id" in kb]

            # 确保DEFAULT_EXAM_KB_ID在列表中（如果不在的话添加）
            if DEFAULT_EXAM_KB_ID not in user_kb_ids:
                user_kb_ids.append(DEFAULT_EXAM_KB_ID)

            # 更新param中的kb_ids
            param.kb_ids = user_kb_ids

            logging.info(
                f"[AIQuestion] Using knowledge bases: {len(user_kb_ids)} KBs for user {user_id}")
            logging.debug(f"[AIQuestion] KB IDs: {user_kb_ids}")

        except Exception as e:
            logging.error(
                f"Error getting user knowledge bases: {e}", exc_info=True)
            # 回退到只使用DEFAULT_EXAM_KB_ID
            param.kb_ids = [DEFAULT_EXAM_KB_ID]
            logging.warning(
                f"[AIQuestion] Falling back to default Exam KB only: {DEFAULT_EXAM_KB_ID}")

        # 1. Determine Exam KB ID
        exam_kb_id = param.exam_kb_id
        if not exam_kb_id:
            # Try to find by default name if ID is not provided
            exists, exam_kb = KnowledgebaseService.get_by_name(
                kb_name=DEFAULT_EXAM_KB_NAME, tenant_id=tenant_id)
            if exists and exam_kb:
                exam_kb_id = exam_kb.id
                logging.info(
                    f"Using '{DEFAULT_EXAM_KB_NAME}' (ID: {exam_kb_id}) as the Exam KB.")
            else:
                # If no Exam KB is found by default name or ID, we might proceed without templates, or raise an error.
                # For now, we'll proceed and rely more on other KBs and the LLM's general knowledge of question types.
                logging.warning(
                    f"Exam KB ID not provided and default '{DEFAULT_EXAM_KB_NAME}' not found. Proceeding without question templates from an Exam KB.")
                exam_kb_id = None  # Ensure it's None if not found

        # 2. Retrieve from "Exam" KB (for templates/style)
        exam_templates_str = """题型,题目内容,难易度,正确答案,答案A,答案B,答案C,答案D,答案E,答案F,答案G,答案解析
多选题,车轮传感器适应车速范围为分（   ）两种。,难,"A,C",0km/h～40km/h,0km/h～30km/h,3km/h～40km/h,3km/h～50km/h,,,,驼峰"""
        # exam_retrieved_chunks = [] # If we need to pass raw chunks for citation later
        # if exam_kb_id:
        #     exam_templates_str, _ = self._retrieve_knowledge(
        #         tenant_id=tenant_id,
        #         kb_ids=[exam_kb_id],
        #         query="丰富多样的试题示例，涵盖选择题、填空题、判断题、简答题（主观题）和排序题等不同类型。", # Broad query for templates
        #         top_n=param.exam_kb_top_n,
        #         is_exam_kb=True
        #     )

        # 3. Retrieve from user's knowledge bases (for content/facts)
        user_kbs_content_str = ""
        # user_retrieved_chunks = []
        if param.kb_ids:
            user_kbs_content_str, _ = self._retrieve_knowledge(
                tenant_id=tenant_id,
                kb_ids=param.kb_ids,
                query=param.knowledge_points,  # Use user's "内容及知识点" as query
                top_n=param.other_kb_top_n
            )

        if not exam_templates_str and not user_kbs_content_str:
            # No retrieval results: proceed with generation using provided knowledge points only
            logging.warning(
                "No information retrieved from Exam KB or user's KBs. Proceeding with generation based on provided knowledge points.")
            # continue without early return, rely on user-provided knowledge_points and general LLM knowledge

        # 4. Construct the prompt for the LLM
        total_questions = self._get_total_questions_to_generate()
        question_type_prompt = self._get_question_type_distribution_prompt()

        prompt_lines = [
            f"你是一个铁路知识专家，核心任务是：基于已有知识库，生成{total_questions}道高质量的铁路专业知识考题。任务的基本要求如下：",
            f"考试场景：{param.exam_scene}",
            f"出题目标：{param.objective}",
            f"考题的考察范围应基于这些内容及知识点：{param.knowledge_points}",
        ]

        if question_type_prompt:
            prompt_lines.append(question_type_prompt)
        else:  # random_questions_enabled
            prompt_lines.append(f"可酌情混合使用各种问题类型，或侧重于场景和目标常用的问题类型。")

        if exam_templates_str:
            prompt_lines.extend([
                "以下来自 tenant-exam-question 试题知识库的示例主要作为试题结构、风格和难度的参考。除非根据试题知识库中的信息抽取考题，否则不要直接逐字复制这些示例。",
                "如下是CSV格式的题型模板示例：",
                "--- 开始题型模板 ---",
                exam_templates_str,
                "--- 结束题型模板 ---",
            ])
        # else:
            # prompt_lines.append("根据指定场景和目标的标准教学实践生成问题。")

        if user_kbs_content_str:
            prompt_lines.extend([
                "问题的事实内容主要来源于以下知识库中的数据集。确保试题准确反映这些信息。",
                "知识库数据集：",
                "--- 开始知识库数据集 ---",
                user_kbs_content_str,
                "--- 结束知识库数据集 ---",
            ])
        else:
            # If no "user KBs" content, the LLM will have to rely on its general knowledge + exam_templates_str (if any)
            # or the knowledge_points string directly. This might be less grounded.
            prompt_lines.append(
                f"根据指定场景和目标的标准教学实践生成问题：'{param.knowledge_points}'。")
            if not exam_templates_str:  # No templates and no user_kb_content
                prompt_lines.append("您需要根据所提供的知识点、场景和目标，依靠自己的推理能力来拟定适当的问题。")

        if param.other_requirements:
            prompt_lines.extend([
                "其他要求：",
                param.other_requirements
            ])

        prompt_lines.append(f"总共生成{total_questions}道考题。")
        prompt_lines.append("请清晰地呈现生成的考题，以markdown段落形式输出，避免用表格包裹考题。")

        final_prompt = "\n\n".join(prompt_lines)

        # Check prompt length and truncate if necessary to prevent resource exhaustion
        # Increased from 20000 to support longer prompts for 20480 token limit
        MAX_PROMPT_LENGTH = 12800
        if len(final_prompt) > MAX_PROMPT_LENGTH:
            logging.warning(
                f"Prompt too long ({len(final_prompt)} chars), truncating to {MAX_PROMPT_LENGTH} chars")
            # Try to preserve the structure by keeping the beginning and end
            keep_start = MAX_PROMPT_LENGTH // 2
            keep_end = MAX_PROMPT_LENGTH // 4
            final_prompt = (final_prompt[:keep_start] +
                            f"\n\n[... 内容过长已截断 ...]\n\n" +
                            final_prompt[-keep_end:])

        logging.info(
            f"Final prompt for AI Question generation (length: {len(final_prompt)} chars)")

        # 5. Call LLM
        chat_mdl = LLMBundle(tenant_id, LLMType.CHAT, param.llm_id)

        # Prepare messages for LLM (basic system + user prompt for now)
        # Consider message_fit_in if history is to be used or prompt is very long.
        # For this task, a single comprehensive prompt is likely sufficient.
        messages = [
            {"role": "system", "content": "你是一位资深的铁路电务领域的专家教师，专注于电务信号、通信设备的考试命题。请严格按照用户提供的要求生成高质量的试题。"},
            {"role": "user", "content": final_prompt}
        ]

        try:
            # Set LLM configuration with larger max_tokens to prevent truncation
            llm_config = {
                "max_tokens": 20480,  # Increased from 4000 to allow longer question generation
                "temperature": 0.5   # Default temperature
            }

            # Override with param values if available
            if hasattr(param, 'max_tokens') and param.max_tokens > 0:
                llm_config["max_tokens"] = min(
                    param.max_tokens, 20480)  # Cap at 20480
            if hasattr(param, 'temperature') and param.temperature > 0:
                llm_config["temperature"] = param.temperature

            # Use LLM interaction context for logging
            with llm_interaction_context(
                agent_type="ai_question",
                tenant_id=tenant_id,
                user_id=getattr(self._canvas, 'user_id', tenant_id),
                llm_model=param.llm_id,
                system=messages[0]["content"],
                history=messages[1:],
                gen_conf=llm_config
            ) as tracker:
                # Call chat with positional arguments to match LLMBundle.chat signature
                answer = chat_mdl.chat(
                    messages[0]["content"], messages[1:], llm_config)

                # Detect error response from LLM and raise exception to be caught
                if isinstance(answer, str) and answer.startswith("**ERROR**"):
                    raise Exception(f"LLM call failed: {answer}")

                # Log the response
                log_llm_response(tracker, answer)

            # Log LLM call details (consider adding to canvas info like in Generate component)
            self._canvas.set_component_infor(self._id, {
                "prompt_length": len(final_prompt),
                "llm_id": param.llm_id,
                "config": llm_config,
                "raw_response_preview": answer[:500] if isinstance(answer, str) else str(answer)[:500]
            })

        except Exception as e:
            logging.error(
                f"Error during LLM call for AI Question generation: {e}")
            # Consider specific error messages based on exception type
            return AIQuestion.be_output(f"Error generating questions: LLM call failed. Details: {str(e)}")

        # 6. Validate generated questions and output as CSV
        val_system = """你是一位资深的铁路电务领域的专家教师，专注于电务信号、通信设备的考试命题。请你对用户提供的试题内容进行校对，确保所有题目的类型及内容正确无误（包括题型、题目内容、选项、正确答案、解析），并严格按照试题模板格式整理后，重新输出试题内容。
---- 开始试题模板 ----
\n\n### 铁路信号工安全操作规程考题\n\n
#### 1. 多选题\n**在进行铁路信号设备维修时，以下哪些是必须执行的安全操作步骤？**\n\n- A. 检查工具是否齐全  \n- B. 切断电源并悬挂“禁止合闸”警示牌  \n- C. 确认设备已完全断电后方可开始作业  \n- D. 佩戴绝缘手套和防护眼镜  \n\n**正确答案：B,C,D**  \n**解析：**在维修信号设备时，必须确保切断电源并采取相应的安全措施，以避免触电或设备损坏。\n\n---\n\n
#### 2. 单选题\n**在进行轨道电路检查时，以下哪项操作是正确的？**\n\n- A. 使用潮湿的工具接触电路元件  \n- B. 在未断开电源的情况下拆卸电路板  \n- C. 确保所有人员远离作业区域后方可开始操作  \n- D. 忽略设备上的警示标志  \n\n**正确答案：C**  \n**解析：**在轨道电路检查时，必须确保所有人员远离作业区域，并严格按照安全规程操作。\n\n---\n\n
#### 3. 判断题\n**在进行信号设备维护时，可以使用潮湿的手接触设备电源部分。**\n\n- 对  \n- 错  \n\n**正确答案：错**  \n**解析：**潮湿的手接触电源部分会导致触电事故，必须避免。\n\n---\n\n
---- 结束试题模板 ----
注意点： 每一题都严格按照模板格式输出，不要漏掉任何内容。
"""

        val_answer = f"""
        --- 开始试题内容 ---
        {answer}
        --- 结束试题内容 ---
        """

        val_messages = [
            {"role": "system", "content": val_system},
            {"role": "user", "content": val_answer}
        ]
        try:
            validation_config = {"max_tokens": 20480, "temperature": 0.2}

            # Use LLM interaction context for validation logging
            with llm_interaction_context(
                agent_type="ai_question_validation",
                tenant_id=tenant_id,
                user_id=getattr(self._canvas, 'user_id', tenant_id),
                llm_model=param.llm_id,
                system=val_messages[0]["content"],
                history=val_messages[1:],
                gen_conf=validation_config
            ) as tracker:
                validated_answer = chat_mdl.chat(
                    val_messages[0]["content"], val_messages[1:], validation_config)

                # Log the validation response
                log_llm_response(tracker, validated_answer)

        except Exception as e:
            logging.error(
                f"Error validating generated questions: {e}", exc_info=True)
            # Fallback to original answer if validation fails
            return AIQuestion.be_output(answer)

        return AIQuestion.be_output(validated_answer)

# To register this component, it needs to be added to agent/component/__init__.py
# And potentially an icon in the frontend, and an entry in operator_contants.tsx
