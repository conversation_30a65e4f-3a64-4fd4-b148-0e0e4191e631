.markdownContentWrapper {
  :global(section.think) {
    padding-left: 10px;
    color: #999;
    border-left: 2px solid #efefef;
    margin-bottom: 10px;
    font-size: 12px;
  }

  :global(blockquote) {
    padding-left: 10px;
    border-left: 4px solid #ccc;
  }

  // 新的think section样式
  :global(details.think-section) {
    margin: 16px 0;
    border: none;
    background: transparent;

    &[open] {
      :global(.think-toggle) {
        background: #f5f5f5;

        &::after {
          transform: rotate(90deg);
        }
      }

      :global(.think-content) {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
      }
    }

    :global(.think-toggle) {
      cursor: pointer;
      outline: none;
      list-style: none;
      background: transparent;
      border: none;
      border-radius: 6px;
      padding: 6px 8px;
      font-size: 13px;
      color: #666;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      transition: all 0.2s ease;
      user-select: none;
      font-weight: normal;
      margin-left: -8px;
      margin-bottom: 4px;

      &:hover {
        background: #f5f5f5;
      }

      :global(.think-avatar) {
        font-size: 16px;
        line-height: 1;
        margin-right: 2px;

        // 当think-avatar是图片时的样式
        &[src] {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          object-fit: cover;
          vertical-align: middle;
        }
      }

      &::after {
        content: '';
        width: 0;
        height: 0;
        border-left: 4px solid #666;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        transition: transform 0.2s ease;
        margin-left: 4px;
      }

      &::marker,
      &::-webkit-details-marker {
        display: none;
      }
    }

    :global(.think-content) {
      display: none;
      margin-top: 8px;
      padding: 12px 0 12px 16px;
      border-left: 2px solid #e0e0e0;
      font-size: 13px;
      line-height: 1.5;
      color: #666;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
        'Helvetica Neue', Arial, sans-serif;

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(-4px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    }
  }

  // 保持旧的think样式以兼容
  :global(details.think) {
    margin: 10px 0;
    padding-left: 10px;
    border-left: 2px solid #efefef;

    summary {
      cursor: pointer;
      outline: none;
      list-style: none;
      color: #999;
      font-size: 12px;
    }

    /* Indent the inner content */
    > div {
      margin-top: 6px;
    }
  }
}

.referencePopoverWrapper {
  max-width: 50vw;
}

.referenceChunkImage {
  width: 10vw;
  object-fit: contain;
}

.referenceImagePreview {
  max-width: 45vw;
  max-height: 45vh;
}

.chunkContentText {
  .chunkText;
  max-height: 45vh;
  overflow-y: auto;
}

.documentLink {
  padding: 0;
}

.referenceIcon {
  padding: 0 6px;
}

.cursor {
  display: inline-block;
  width: 1px;
  height: 16px;
  background-color: black;
  animation: blink 0.6s infinite;
  vertical-align: text-top;

  @keyframes blink {
    0% {
      opacity: 1;
    }

    50% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }
}

.fileThumbnail {
  display: inline-block;
  max-width: 40px;
}
