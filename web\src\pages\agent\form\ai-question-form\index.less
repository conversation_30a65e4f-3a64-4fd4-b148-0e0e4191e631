@import '@/theme/high-tech-theme.less';

.questionTypeSection {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

// Radio Button 样式 - 与教案生成页面保持一致
.radioSelector {
  margin-top: var(--spacing-xs);

  .radioGroup {
    display: flex;
    gap: var(--spacing-lg);

    .radioOption {
      font-size: 14px;
      color: var(--text-primary);

      .ant-radio {
        .ant-radio-inner {
          border-color: var(--border-color);
          background-color: white;

          &::after {
            background-color: var(--primary-color);
          }
        }

        &.ant-radio-checked {
          .ant-radio-inner {
            border-color: var(--primary-color);
            background-color: white;
          }
        }
      }

      &:hover {
        .ant-radio {
          .ant-radio-inner {
            border-color: var(--primary-color);
          }
        }
      }
    }
  }
}

.questionTypeCard {
  margin-top: var(--spacing-xs);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color-light);
  box-shadow: var(--shadow-sm);
  background-color: var(--bg-subtle);

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(
      180deg,
      var(--primary-color),
      var(--accent-color)
    );
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
  }
}

.numberInput {
  width: 100%;
}

/* The high-tech styles for the form elements */
:global {
  .ant-input,
  .ant-input-number,
  .ant-select-selector,
  .ant-input-textarea {
    border-radius: var(--border-radius-md) !important;
    border: 1px solid var(--border-color) !important;
    background-color: white !important;
    transition: all var(--transition-normal);

    &:hover,
    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1) !important;
    }
  }

  textarea.text-base,
  input.text-base {
    background-color: white !important;
  }

  .ant-card-body {
    padding: var(--spacing-md);
  }

  .ant-form-item-label > label {
    font-weight: 500;
    color: var(--text-primary);
  }

  .ant-checkbox-wrapper {
    display: flex;
    align-items: center;
  }

  .ant-checkbox-inner {
    border-radius: 3px;
    border: 1.5px solid var(--border-color);
    background-color: var(--bg-input);

    &:after {
      border-color: white;
    }
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
  }
}
