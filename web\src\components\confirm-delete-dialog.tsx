import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Trash2 } from 'lucide-react';
import { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';

interface IProps {
  title?: string;
  onOk?: (...args: any[]) => any;
  onCancel?: (...args: any[]) => any;
  hidden?: boolean;
  open?: boolean;
}

export function ConfirmDeleteDialog({
  children,
  title,
  onOk,
  onCancel,
  hidden = false,
  open,
}: IProps & PropsWithChildren) {
  const { t } = useTranslation();

  if (hidden) {
    return children;
  }

  return (
    <AlertDialog open={open} onOpenChange={(isOpen) => !isOpen && onCancel?.()}>
      {children && <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>}
      <AlertDialogContent
        onSelect={(e) => e.preventDefault()}
        onClick={(e) => e.stopPropagation()}
        className="ht-modal-content"
      >
        <AlertDialogHeader className="ht-modal-header">
          <AlertDialogTitle className="ht-modal-title">
            <Trash2 className="h-5 w-5" />
            {title ?? t('common.deleteModalTitle')}
          </AlertDialogTitle>
        </AlertDialogHeader>
        <div className="ht-modal-body">
          <p className="text-sm text-gray-600">
            此操作无法撤销，确定要继续吗？
          </p>
        </div>
        <AlertDialogFooter className="ht-modal-footer">
          <AlertDialogCancel
            onClick={onCancel}
            className="ht-modal-btn secondary"
          >
            {t('common.cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            className="ht-modal-btn primary bg-red-600 hover:bg-red-700"
            onClick={onOk}
          >
            <Trash2 className="h-4 w-4" />
            {t('common.ok')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
