# 文件上传功能改进总结

## 需求

根据用户需求，对文件上传表单进行以下改进：

1. 去除「本地上传」和「S3 上传」的 Tabs 组件，只保留本地上传方式
2. 在文件上传组件下方添加一个 Tags 组件，可手工输入 Tag
3. 支持空格或回车键添加下一个 Tag
4. Tags 组件是必填项

## 实现的功能

### 1. 文件上传对话框改进 (`web/src/components/file-upload-dialog/index.tsx`)

- ✅ 移除了 Tabs 组件，简化为单一的本地上传方式
- ✅ 添加了 Tags 输入组件
- ✅ 使用 React Hook Form 和 Zod 进行表单验证
- ✅ Tags 字段设为必填项
- ✅ 支持新的 `FileUploadData` 接口，包含 `files` 和 `tags` 两个字段

### 2. Tags 组件功能增强 (`web/src/components/edit-tag/index.tsx`)

- ✅ 支持空格键触发标签添加
- ✅ 支持回车键触发标签添加
- ✅ 添加了用户友好的占位符提示："输入标签后按空格或回车键添加"
- ✅ 优化了输入处理逻辑，支持分号和空格分割多个标签

### 3. 后端接口适配

- ✅ 更新了 `useUploadFile` hooks 以支持 tags 参数
- ✅ 修改了 drive 和 files 目录中的上传逻辑
- ✅ 在 FormData 中添加了 tags 参数传递

### 4. 多语言支持

- ✅ 添加了中文翻译键：`selectFiles: '选择文件'`, `tags: '标签'`
- ✅ 添加了英文翻译键：`selectFiles: 'Select Files'`, `tags: 'Tags'`

### 5. 兼容性保持

- ✅ 更新了所有使用 `FileUploadDialog` 的地方：
  - `web/src/pages/drive/use-upload-file.ts`
  - `web/src/pages/files/use-upload-file.ts`
  - `web/src/pages/dataset/dataset/use-upload-document.ts`

## 技术细节

### 新增接口类型

```typescript
export interface FileUploadData {
  files: File[]
  tags: string[]
}
```

### 表单验证

使用 Zod schema 进行验证：

```typescript
const FormSchema = z.object({
  files: z.array(z.any()).min(1, t('common.pleaseSelect')),
  tags: z.array(z.string()).min(1, t('common.pleaseInput'))
})
```

### Tags 输入增强

- 支持实时空格键添加
- 支持回车键确认添加
- 支持输入框失焦时自动添加
- 防止重复标签

## 使用方式

用户现在可以：

1. 直接选择文件（无需选择上传方式）
2. 在下方的标签输入框中输入标签
3. 输入标签后按空格或回车键即可添加
4. 可以添加多个标签
5. 标签和文件都是必填项，确保数据完整性

## 注意事项

- Tags 数据以 JSON 字符串形式传递给后端 (`tags` 参数)
- 保持了与现有代码的兼容性
- 所有相关的上传 hooks 都已更新以支持新的接口
- Dataset 上传功能目前只传递文件，tags 支持可能需要后端配合

## 测试建议

1. 测试文件选择功能
2. 测试标签输入（空格键、回车键、失焦）
3. 测试表单验证（文件和标签的必填验证）
4. 测试不同模块的文件上传（drive、files、dataset）
5. 测试多语言切换时的界面显示
