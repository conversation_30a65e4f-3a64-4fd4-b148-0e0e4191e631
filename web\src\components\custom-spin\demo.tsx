import { <PERSON>ton, Card, Space, Typography } from 'antd';
import React, { useState } from 'react';
import './demo.less';
import { CircleSpin, LeafSpin } from './index';

const { Title, Text } = Typography;

const SpinDemo: React.FC = () => {
  const [circleLoading, setCircleLoading] = useState(false);
  const [leafLoading, setLeafLoading] = useState(false);

  const toggleCircleLoading = () => {
    setCircleLoading(!circleLoading);
  };

  const toggleLeafLoading = () => {
    setLeafLoading(!leafLoading);
  };

  return (
    <div className="spin-demo">
      <Title level={2}>自定义Spin组件库演示</Title>

      {/* CircleSpin演示 */}
      <Card title="CircleSpin - 圆形加载动画" style={{ marginBottom: 24 }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 基础使用 */}
          <div>
            <Title level={4}>基础使用</Title>
            <Space size="large">
              <div>
                <Text>Small</Text>
                <CircleSpin spinning size="small" />
              </div>
              <div>
                <Text>Default</Text>
                <CircleSpin spinning size="default" />
              </div>
              <div>
                <Text>Large</Text>
                <CircleSpin spinning size="large" />
              </div>
            </Space>
          </div>

          {/* 带提示文本 */}
          <div>
            <Title level={4}>带提示文本</Title>
            <CircleSpin spinning tip="加载中..." size="default" />
          </div>

          {/* 包装内容 */}
          <div>
            <Title level={4}>包装内容</Title>
            <Space>
              <Button onClick={toggleCircleLoading}>
                {circleLoading ? '停止' : '开始'}加载
              </Button>
            </Space>
            <div style={{ marginTop: 16 }}>
              <CircleSpin spinning={circleLoading} tip="正在加载数据...">
                <Card style={{ width: 300, height: 200 }}>
                  <p>这是一些内容...</p>
                  <p>加载状态会覆盖在这些内容上</p>
                  <p>内容会变成半透明状态</p>
                </Card>
              </CircleSpin>
            </div>
          </div>
        </Space>
      </Card>

      {/* LeafSpin演示 */}
      <Card title="LeafSpin - 旋转点加载动画" style={{ marginBottom: 24 }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 基础使用 */}
          <div>
            <Title level={4}>基础使用</Title>
            <Space size="large">
              <div>
                <Text>Small</Text>
                <LeafSpin spinning size="small" />
              </div>
              <div>
                <Text>Default</Text>
                <LeafSpin spinning size="default" />
              </div>
              <div>
                <Text>Large</Text>
                <LeafSpin spinning size="large" />
              </div>
            </Space>
          </div>

          {/* 带提示文本 */}
          <div>
            <Title level={4}>带提示文本</Title>
            <LeafSpin spinning tip="数据处理中..." size="default" />
          </div>

          {/* 带自定义文本 */}
          <div>
            <Title level={4}>带自定义文本</Title>
            <Space size="large">
              <div>
                <Text>使用text属性</Text>
                <LeafSpin spinning text="加载中..." size="default" />
              </div>
              <div>
                <Text>不显示文本</Text>
                <LeafSpin spinning size="default" />
              </div>
            </Space>
          </div>

          {/* 包装内容 */}
          <div>
            <Title level={4}>包装内容</Title>
            <Space>
              <Button onClick={toggleLeafLoading}>
                {leafLoading ? '停止' : '开始'}加载
              </Button>
            </Space>
            <div style={{ marginTop: 16 }}>
              <LeafSpin spinning={leafLoading} tip="正在处理数据...">
                <Card style={{ width: 300, height: 200 }}>
                  <p>这是4个旋转点的加载动画</p>
                  <p>具有流畅的旋转效果</p>
                  <p>适合需要精美动画的场景</p>
                </Card>
              </LeafSpin>
            </div>
          </div>
        </Space>
      </Card>

      {/* 对比演示 */}
      <Card title="对比演示">
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={4}>同时使用多种加载动画</Title>
            <Space size="large">
              <div>
                <Text>CircleSpin</Text>
                <CircleSpin spinning tip="圆形加载" />
              </div>
              <div>
                <Text>LeafSpin</Text>
                <LeafSpin spinning tip="旋转点加载" />
              </div>
            </Space>
          </div>

          <div>
            <Title level={4}>自定义样式</Title>
            <Space size="large">
              <CircleSpin
                spinning
                tip="自定义颜色"
                style={
                  {
                    '--primary-color': '#52c41a',
                    color: '#52c41a',
                  } as React.CSSProperties
                }
              />
              <LeafSpin
                spinning
                text="自定义样式"
                style={
                  {
                    '--primary-color': '#ff6b6b',
                    fontSize: '16px',
                  } as React.CSSProperties
                }
              />
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default SpinDemo;
