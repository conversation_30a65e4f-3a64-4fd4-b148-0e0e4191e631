import MessageItem from '@/components/message-item';
import { MessageType } from '@/constants/chat';
import { SmileOutlined } from '@ant-design/icons';
import { Flex, Spin, Typography } from 'antd';
import {
  useCreateConversationBeforeUploadDocument,
  useGetFileIcon,
  useGetSendButtonDisabled,
  useSendButtonDisabled,
  useSendNextMessage,
} from '../hooks';
import { buildMessageItemReference } from '../utils';

import MessageInput from '@/components/message-input';
import PdfDrawer from '@/components/pdf-drawer';
import { useClickDrawer } from '@/components/pdf-drawer/hooks';
import {
  useFetchNextConversation,
  useGetChatSearchParams,
} from '@/hooks/chat-hooks';
import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import { buildMessageUuidWithRole } from '@/utils/chat';
import { throttle } from 'lodash';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.less';

const { Title, Text } = Typography;

interface IProps {
  controller: AbortController;
  onUserQueryChange?: (query: string) => void;
  onReferenceDocumentsChange?: (
    docs: Array<{ doc_id: string; doc_name: string; url?: string }>,
  ) => void;
}

const ChatContainer = ({
  controller,
  onUserQueryChange,
  onReferenceDocumentsChange,
}: IProps) => {
  const { conversationId } = useGetChatSearchParams();
  const { data: conversation } = useFetchNextConversation();

  const {
    value,
    ref,
    loading,
    sendLoading,
    derivedMessages,
    handleInputChange,
    handlePressEnter,
    regenerateMessage,
    removeMessageById,
    stopOutputMessage,
  } = useSendNextMessage(controller);

  const { visible, hideModal, documentId, selectedChunk, clickDocumentButton } =
    useClickDrawer();
  const disabled = useGetSendButtonDisabled();
  const sendDisabled = useSendButtonDisabled(value);
  useGetFileIcon();
  const { data: userInfo } = useFetchUserInfo();
  const { createConversationBeforeUploadDocument } =
    useCreateConversationBeforeUploadDocument();

  // 添加滚动容器的引用
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const messageRefs = useRef<Map<string, HTMLDivElement>>(new Map());
  const [currentVisibleMessageId, setCurrentVisibleMessageId] =
    useState<string>('');

  // 获取最后一条用户消息的内容，用于相关资源检索
  const lastUserMessage = useMemo(() => {
    if (!derivedMessages || derivedMessages.length === 0) return null;

    for (let i = derivedMessages.length - 1; i >= 0; i--) {
      if (derivedMessages[i].role === MessageType.User) {
        return derivedMessages[i];
      }
    }
    return null;
  }, [derivedMessages]);

  // Get the last assistant message to show tabs under it
  const lastAssistantMessage = useMemo(() => {
    if (!derivedMessages || derivedMessages.length === 0) return null;

    for (let i = derivedMessages.length - 1; i >= 0; i--) {
      if (derivedMessages[i].role === MessageType.Assistant) {
        return derivedMessages[i];
      }
    }
    return null;
  }, [derivedMessages]);

  // 根据当前可见的消息ID获取对应的用户消息
  const currentVisibleUserMessage = useMemo(() => {
    if (!currentVisibleMessageId || !derivedMessages) return null;

    // 找到当前可见消息的索引
    const currentIndex = derivedMessages.findIndex(
      (msg) => buildMessageUuidWithRole(msg) === currentVisibleMessageId,
    );

    if (currentIndex === -1) return null;

    // 如果当前可见的是助手消息，找到对应的用户消息
    if (derivedMessages[currentIndex].role === MessageType.Assistant) {
      // 向前查找最近的用户消息
      for (let i = currentIndex - 1; i >= 0; i--) {
        if (derivedMessages[i].role === MessageType.User) {
          return derivedMessages[i];
        }
      }
    } else if (derivedMessages[currentIndex].role === MessageType.User) {
      // 如果当前可见的就是用户消息，直接返回
      return derivedMessages[currentIndex];
    }

    return null;
  }, [currentVisibleMessageId, derivedMessages]);

  // 根据当前可见的消息获取对应的引用文档
  const currentVisibleReferenceDocuments = useMemo(() => {
    if (
      !currentVisibleMessageId ||
      !derivedMessages ||
      !conversation?.reference
    )
      return [];

    // 找到当前可见消息的索引
    const currentIndex = derivedMessages.findIndex(
      (msg) => buildMessageUuidWithRole(msg) === currentVisibleMessageId,
    );

    if (currentIndex === -1) return [];

    const currentMessage = derivedMessages[currentIndex];
    const docs: Array<{ doc_id: string; doc_name: string; url?: string }> = [];
    const seenIds = new Set<string>();

    // 如果当前消息是助手消息，获取其引用文档
    if (currentMessage.role === MessageType.Assistant) {
      const reference = buildMessageItemReference(
        {
          message: derivedMessages,
          reference: conversation.reference,
        },
        currentMessage,
      );

      if (reference?.doc_aggs) {
        reference.doc_aggs.forEach((doc) => {
          if (!seenIds.has(doc.doc_id)) {
            docs.push({
              doc_id: doc.doc_id,
              doc_name: doc.doc_name,
              url: doc.url,
            });
            seenIds.add(doc.doc_id);
          }
        });
      }
    } else if (currentMessage.role === MessageType.User) {
      // 如果当前是用户消息，找到下一条助手消息的引用文档
      for (let i = currentIndex + 1; i < derivedMessages.length; i++) {
        if (derivedMessages[i].role === MessageType.Assistant) {
          const reference = buildMessageItemReference(
            {
              message: derivedMessages,
              reference: conversation.reference,
            },
            derivedMessages[i],
          );

          if (reference?.doc_aggs) {
            reference.doc_aggs.forEach((doc) => {
              if (!seenIds.has(doc.doc_id)) {
                docs.push({
                  doc_id: doc.doc_id,
                  doc_name: doc.doc_name,
                  url: doc.url,
                });
                seenIds.add(doc.doc_id);
              }
            });
          }
          break; // 只找第一条对应的助手消息
        }
      }
    }

    return docs;
  }, [currentVisibleMessageId, derivedMessages, conversation?.reference]);

  // 汇聚所有聊天回复中的引用文档（保留作为后备）
  const allReferenceDocuments = useMemo(() => {
    if (!derivedMessages || !conversation?.reference) return [];

    const allDocs: Array<{ doc_id: string; doc_name: string; url?: string }> =
      [];
    const seenIds = new Set<string>();

    // 从conversation.reference中汇聚所有引用文档
    conversation.reference.forEach((ref) => {
      if (ref?.doc_aggs) {
        ref.doc_aggs.forEach((doc) => {
          if (!seenIds.has(doc.doc_id)) {
            allDocs.push({
              doc_id: doc.doc_id,
              doc_name: doc.doc_name,
              url: doc.url,
            });
            seenIds.add(doc.doc_id);
          }
        });
      }
    });

    // 从每条消息的reference中汇聚引用文档
    derivedMessages.forEach((message) => {
      if (
        message.role === MessageType.Assistant &&
        message.reference?.doc_aggs
      ) {
        message.reference.doc_aggs.forEach((doc) => {
          if (!seenIds.has(doc.doc_id)) {
            allDocs.push({
              doc_id: doc.doc_id,
              doc_name: doc.doc_name,
              url: doc.url,
            });
            seenIds.add(doc.doc_id);
          }
        });
      }
    });

    return allDocs;
  }, [derivedMessages, conversation?.reference]);

  // 节流的滚动处理函数
  const handleScroll = useCallback(
    throttle(() => {
      const container = messageContainerRef.current;
      if (!container || messageRefs.current.size === 0) return;

      const containerRect = container.getBoundingClientRect();
      const containerCenter = containerRect.top + containerRect.height / 2;

      let closestMessageId = '';
      let minDistance = Infinity;

      // 遍历所有消息元素，找到最接近视口中心的消息
      messageRefs.current.forEach((element, messageId) => {
        const elementRect = element.getBoundingClientRect();
        const elementCenter = elementRect.top + elementRect.height / 2;
        const distance = Math.abs(elementCenter - containerCenter);

        // 确保元素在视口内
        const isVisible =
          elementRect.bottom > containerRect.top &&
          elementRect.top < containerRect.bottom;

        if (isVisible && distance < minDistance) {
          minDistance = distance;
          closestMessageId = messageId;
        }
      });

      if (closestMessageId && closestMessageId !== currentVisibleMessageId) {
        setCurrentVisibleMessageId(closestMessageId);
      }
    }, 200),
    [currentVisibleMessageId],
  );

  // 设置消息元素的引用
  const setMessageRef = useCallback(
    (messageId: string, element: HTMLDivElement | null) => {
      if (element) {
        messageRefs.current.set(messageId, element);
      } else {
        messageRefs.current.delete(messageId);
      }
    },
    [],
  );

  // 监听滚动事件
  useEffect(() => {
    const container = messageContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);
    // 初始调用一次以设置初始状态
    handleScroll();

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // 当消息列表变化时，重新设置初始可见消息
  useEffect(() => {
    if (
      derivedMessages &&
      derivedMessages.length > 0 &&
      !currentVisibleMessageId
    ) {
      // 默认设置最后一条消息为可见
      const lastMessage = derivedMessages[derivedMessages.length - 1];
      setCurrentVisibleMessageId(buildMessageUuidWithRole(lastMessage));
    }
  }, [derivedMessages, currentVisibleMessageId]);

  // 通知父组件用户查询变化
  useMemo(() => {
    // 区分两种场景：
    // 1. 新消息发送完成后的搜索 - 等待sendLoading完成
    // 2. 滚动时的搜索 - 立即触发
    const targetMessage = currentVisibleUserMessage || lastUserMessage;
    if (onUserQueryChange && targetMessage?.content) {
      // 无论是滚动触发还是新消息发送，都立即通知父组件更新搜索关键字。
      onUserQueryChange(targetMessage.content);
    }
  }, [
    currentVisibleUserMessage,
    lastUserMessage,
    onUserQueryChange,
    sendLoading,
  ]);

  // 通知父组件引用文档变化
  useMemo(() => {
    if (!onReferenceDocumentsChange) return;

    // 处于回答生成阶段(sendLoading 为 true) 时，清空引用文档列表，
    // 以便右侧资源区在加载期间显示 0 并保持 loading 状态。
    if (sendLoading) {
      onReferenceDocumentsChange([]);
      return;
    }

    // 优先使用当前可见的引用文档，如果没有则使用所有引用文档
    const targetDocs =
      currentVisibleReferenceDocuments.length > 0
        ? currentVisibleReferenceDocuments
        : allReferenceDocuments;
    onReferenceDocumentsChange(targetDocs);
  }, [
    currentVisibleReferenceDocuments,
    allReferenceDocuments,
    onReferenceDocumentsChange,
    sendLoading,
  ]);

  return (
    <>
      <Flex flex={1} className={styles.chatContainer} vertical>
        {conversation?.name && (
          <div className={styles.conversationHeader}>
            <Title level={4} className={styles.conversationTitle}>
              {conversation.name}
            </Title>
          </div>
        )}
        <Flex
          flex={1}
          vertical
          className={styles.messageContainer}
          ref={messageContainerRef}
        >
          <div className={styles.messagesSection}>
            <Spin spinning={loading}>
              {derivedMessages?.length > 0 ? (
                derivedMessages.map((message, i) => {
                  const messageId = buildMessageUuidWithRole(message);
                  return (
                    <div
                      key={messageId}
                      ref={(el) => setMessageRef(messageId, el)}
                      className={
                        currentVisibleMessageId === messageId
                          ? styles.visibleMessage
                          : ''
                      }
                    >
                      <MessageItem
                        loading={
                          message.role === MessageType.Assistant &&
                          sendLoading &&
                          derivedMessages.length - 1 === i
                        }
                        item={message}
                        nickname={userInfo.nickname}
                        avatar={userInfo.avatar}
                        avatarDialog={conversation.avatar}
                        reference={buildMessageItemReference(
                          {
                            message: derivedMessages,
                            reference: conversation.reference,
                          },
                          message,
                        )}
                        clickDocumentButton={clickDocumentButton}
                        index={i}
                        removeMessageById={removeMessageById}
                        regenerateMessage={regenerateMessage}
                        sendLoading={sendLoading}
                      />
                    </div>
                  );
                })
              ) : (
                <div className={styles.emptyState}>
                  <div className={styles.emptyStateIcon}>
                    <SmileOutlined />
                  </div>
                  <Title level={4}>开启新的对话</Title>
                  <Text type="secondary">在下方输入消息，开始聊天</Text>
                </div>
              )}
            </Spin>
          </div>

          <div ref={ref} />
        </Flex>

        <div className={styles.messageInputWrapper}>
          <MessageInput
            disabled={disabled}
            sendDisabled={sendDisabled}
            sendLoading={sendLoading}
            value={value}
            onInputChange={handleInputChange}
            onPressEnter={handlePressEnter}
            conversationId={conversationId}
            createConversationBeforeUploadDocument={
              createConversationBeforeUploadDocument
            }
            stopOutputMessage={stopOutputMessage}
          ></MessageInput>
        </div>
      </Flex>
      <PdfDrawer
        visible={visible}
        hideModal={hideModal}
        documentId={documentId}
        chunk={selectedChunk}
      ></PdfDrawer>
    </>
  );
};

export default memo(ChatContainer);
