import { KnowledgeRouteKey } from '@/constants/knowledge';
import { IKnowledge } from '@/interfaces/database/knowledge';
import { formatDate } from '@/utils/date';
import {
  CalendarOutlined,
  FileTextOutlined,
  TagOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Badge, Card, Space, Tag, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'umi';

import OperateDropdown from '@/components/operate-dropdown';
import { useDeleteKnowledge } from '@/hooks/knowledge-hooks';
import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import styles from './index.less';

// Extended interface to include optional properties we're using
interface ExtendedKnowledge extends IKnowledge {
  tags?: string[];
  user_avatar?: string;
}

interface IProps {
  item: ExtendedKnowledge;
}

const KnowledgeCard = ({ item }: IProps) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { data: userInfo } = useFetchUserInfo();
  const { deleteKnowledge } = useDeleteKnowledge();

  const removeKnowledge = async () => {
    return deleteKnowledge(item.id);
  };

  const handleCardClick = () => {
    navigate(`/knowledge/${KnowledgeRouteKey.Dataset}?id=${item.id}`, {
      state: { from: 'list' },
    });
  };

  // Function to generate a visual identifier based on the knowledge name
  const getAvatarColor = (name: string) => {
    const colors = [
      '#3b82f6',
      '#0ea5e9',
      '#10b981',
      '#6366f1',
      '#8b5cf6',
      '#ec4899',
      '#f43f5e',
      '#f59e0b',
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  // Generate avatar for knowledge base
  const avatarColor = getAvatarColor(item.name);
  const avatarContent = item.name.substring(0, 2).toUpperCase();

  return (
    <Badge.Ribbon
      text={item?.nickname}
      color={userInfo?.nickname === item?.nickname ? '#3b82f6' : '#ec4899'}
      className={item.permission !== 'team' ? styles.hideRibbon : styles.ribbon}
      style={{ display: 'none' }}
    >
      <Card className={styles.card} onClick={handleCardClick}>
        <div className={styles.container}>
          <div className={styles.content}>
            <Avatar
              size={42}
              style={{
                backgroundColor: avatarColor,
                color: 'white',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              {item.avatar ? (
                <img
                  src={item.avatar}
                  alt={item.name}
                  style={{ width: '100%' }}
                />
              ) : (
                avatarContent
              )}
            </Avatar>
            <OperateDropdown deleteItem={removeKnowledge} />
          </div>

          <div className={styles.titleWrapper}>
            <h3 className={styles.title}>{item.name}</h3>
            <p className={styles.description}>{item.description}</p>
          </div>

          {item.tags && item.tags.length > 0 && (
            <Space size="small" style={{ marginBottom: '8px' }}>
              {item.tags.slice(0, 2).map((tag: string) => (
                <Tag key={tag} color="processing" icon={<TagOutlined />}>
                  {tag}
                </Tag>
              ))}
              {item.tags.length > 2 && (
                <Tooltip title={item.tags.slice(2).join(', ')}>
                  <Tag color="default">+{item.tags.length - 2}</Tag>
                </Tooltip>
              )}
            </Space>
          )}

          <div className={styles.footer}>
            <div className={styles.footerTop}>
              <div className={styles.bottomLeft}>
                <FileTextOutlined className={styles.leftIcon} />
                <span className={styles.rightText}>
                  <Space>
                    {item.doc_num}
                    {t('knowledgeList.doc')}
                  </Space>
                </span>
              </div>
            </div>
            <div className={styles.bottom}>
              <div className={styles.bottomLeft}>
                <CalendarOutlined className={styles.leftIcon} />
                <span className={styles.rightText}>
                  {formatDate(item.update_time)}
                </span>
              </div>

              <Tooltip title={`Created by ${item.nickname}`}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  src={item.user_avatar}
                  style={{ backgroundColor: '#1677ff' }}
                />
              </Tooltip>
            </div>
          </div>
        </div>
      </Card>
    </Badge.Ribbon>
  );
};

export default KnowledgeCard;
