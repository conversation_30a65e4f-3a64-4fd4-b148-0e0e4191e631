#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import re
import logging
from flask import request
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash

from api import settings
from api.db import UserTenantRole, StatusEnum
from api.db.db_models import UserTenant
from api.db.services.user_service import UserTenantService, UserService, TenantService

from api.utils import get_uuid, delta_seconds, get_format_time, decrypt
from api.utils.api_utils import get_json_result, validate_request, server_error_response, get_data_error_result


@manager.route("/<tenant_id>/user/list", methods=["GET"])  # noqa: F821
@login_required
def user_list(tenant_id):
    if current_user.id != tenant_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    try:
        users = UserTenantService.get_by_tenant_id(tenant_id)
        for u in users:
            u["delta_seconds"] = delta_seconds(str(u["update_date"]))
        return get_json_result(data=users)
    except Exception as e:
        return server_error_response(e)


@manager.route('/<tenant_id>/user', methods=['POST'])  # noqa: F821
@login_required
@validate_request("email")
def create(tenant_id):
    if current_user.id != tenant_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    req = request.json
    invite_user_email = req["email"]
    invite_users = UserService.query(email=invite_user_email)
    if not invite_users:
        return get_data_error_result(message="User not found.")

    user_id_to_invite = invite_users[0].id
    user_tenants = UserTenantService.query(
        user_id=user_id_to_invite, tenant_id=tenant_id)
    if user_tenants:
        user_tenant_role = user_tenants[0].role
        if user_tenant_role == UserTenantRole.NORMAL:
            return get_data_error_result(message=f"{invite_user_email} is already in the team.")
        if user_tenant_role == UserTenantRole.OWNER:
            return get_data_error_result(message=f"{invite_user_email} is the owner of the team.")
        return get_data_error_result(message=f"{invite_user_email} is in the team, but the role: {user_tenant_role} is invalid.")

    UserTenantService.save(
        id=get_uuid(),
        user_id=user_id_to_invite,
        tenant_id=tenant_id,
        invited_by=current_user.id,
        role=UserTenantRole.INVITE,
        status=StatusEnum.VALID.value)

    usr = invite_users[0].to_dict()
    usr = {k: v for k, v in usr.items() if k in [
        "id", "avatar", "email", "nickname"]}

    return get_json_result(data=usr)


@manager.route('/<tenant_id>/user/register_and_add', methods=['POST'])  # noqa: F821
@login_required
@validate_request("nickname", "email", "password")
def register_and_add_user(tenant_id):
    """
    Register a new user and automatically add them to the specified tenant.
    This is a combined operation for user registration + tenant invitation + auto-acceptance.
    """
    # Check if current user has permission to add users to this tenant
    if current_user.id != tenant_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    req = request.json
    email_address = req["email"]
    nickname = req["nickname"]
    password = req["password"]

    try:
        # Validate the email address
        if not re.match(r"^[\w\._-]+@([\w_-]+\.)+[\w-]{2,}$", email_address):
            return get_json_result(
                data=False,
                message=f"Invalid email address: {email_address}!",
                code=settings.RetCode.OPERATING_ERROR,
            )

        # Check if the email address is already used
        existing_users = UserService.query(email=email_address)
        if existing_users:
            # If user exists, check if they're already in this tenant
            user_id = existing_users[0].id
            user_tenants = UserTenantService.query(
                user_id=user_id, tenant_id=tenant_id)
            if user_tenants:
                user_tenant_role = user_tenants[0].role
                if user_tenant_role == UserTenantRole.NORMAL:
                    return get_data_error_result(message=f"{email_address} is already in the team.")
                if user_tenant_role == UserTenantRole.OWNER:
                    return get_data_error_result(message=f"{email_address} is the owner of the team.")
                # If user has INVITE role, update to NORMAL
                UserTenantService.filter_update(
                    [UserTenant.tenant_id == tenant_id,
                        UserTenant.user_id == user_id],
                    {"role": UserTenantRole.NORMAL}
                )
            else:
                # Add existing user to tenant
                UserTenantService.save(
                    id=get_uuid(),
                    user_id=user_id,
                    tenant_id=tenant_id,
                    invited_by=current_user.id,
                    role=UserTenantRole.NORMAL,
                    status=StatusEnum.VALID.value
                )

            usr = existing_users[0].to_dict()
            usr = {k: v for k, v in usr.items() if k in [
                "id", "avatar", "email", "nickname"]}
            return get_json_result(data=usr, message=f"User {nickname} has been added to the tenant.")

        # Create new user
        user_id = get_uuid()
        user_dict = {
            "id": user_id,
            "access_token": get_uuid(),
            "email": email_address,
            "nickname": nickname,
            "password": decrypt(password),
            "login_channel": "password",
            "last_login_time": get_format_time(),
            "language": "Chinese",
            "is_superuser": False,
        }

        # Save the new user
        if not UserService.save(**user_dict):
            return get_json_result(
                data=False,
                message="Failed to create user.",
                code=settings.RetCode.EXCEPTION_ERROR,
            )

        # Add user to the specified tenant with NORMAL role (auto-accept invitation)
        UserTenantService.save(
            id=get_uuid(),
            user_id=user_id,
            tenant_id=tenant_id,
            invited_by=current_user.id,
            role=UserTenantRole.NORMAL,
            status=StatusEnum.VALID.value
        )

        # Return user info
        new_user = UserService.query(email=email_address)[0]
        usr = new_user.to_dict()
        usr = {k: v for k, v in usr.items() if k in [
            "id", "avatar", "email", "nickname"]}

        return get_json_result(data=usr, message=f"User {nickname} has been registered and added to the tenant.")

    except Exception as e:
        logging.exception(f"Error in register_and_add_user: {str(e)}")
        # Rollback: try to clean up if user was created but tenant addition failed
        try:
            if 'user_id' in locals():
                UserService.filter_delete([UserService.model.id == user_id])
                UserTenantService.filter_delete(
                    [UserTenant.user_id == user_id, UserTenant.tenant_id == tenant_id])
        except Exception:
            pass
        return server_error_response(e)


@manager.route('/<tenant_id>/user/<user_id>', methods=['DELETE'])  # noqa: F821
@login_required
def rm(tenant_id, user_id):
    if current_user.id != tenant_id and current_user.id != user_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    try:
        UserTenantService.filter_delete(
            [UserTenant.tenant_id == tenant_id, UserTenant.user_id == user_id])
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/list", methods=["GET"])  # noqa: F821
@login_required
def tenant_list():
    try:
        users = UserTenantService.get_tenants_by_user_id(current_user.id)
        for u in users:
            u["delta_seconds"] = delta_seconds(str(u["update_date"]))
        return get_json_result(data=users)
    except Exception as e:
        return server_error_response(e)


@manager.route("/agree/<tenant_id>", methods=["PUT"])  # noqa: F821
@login_required
def agree(tenant_id):
    try:
        UserTenantService.filter_update([UserTenant.tenant_id == tenant_id, UserTenant.user_id == current_user.id], {
                                        "role": UserTenantRole.NORMAL})
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)
