import {
  ChatVariableEnabledField,
  EmptyConversationId,
} from '@/constants/chat';
import { Message } from '@/interfaces/database/chat';
import { IMessage } from '@/pages/chat/interface';
import { omit } from 'lodash';
import { v4 as uuid } from 'uuid';

export const isConversationIdExist = (conversationId: string) => {
  return conversationId !== EmptyConversationId && conversationId !== '';
};

export const buildMessageUuid = (message: Partial<Message | IMessage>) => {
  if ('id' in message && message.id) {
    return message.id;
  }
  return uuid();
};

export const buildMessageListWithUuid = (messages?: Message[]) => {
  return (
    messages?.map((x: Message | IMessage) => ({
      ...omit(x, 'reference'),
      id: buildMessageUuid(x),
    })) ?? []
  );
};

export const getConversationId = () => {
  return uuid().replace(/-/g, '');
};

// When rendering each message, add a prefix to the id to ensure uniqueness.
export const buildMessageUuidWithRole = (
  message: Partial<Message | IMessage>,
) => {
  return `${message.role}_${message.id}`;
};

// Preprocess LaTeX equations to be rendered by KaTeX
// ref: https://github.com/remarkjs/react-markdown/issues/785

export const preprocessLaTeX = (content: string) => {
  const blockProcessedContent = content.replace(
    /\\\[([\s\S]*?)\\\]/g,
    (_, equation) => `$$${equation}$$`,
  );
  const inlineProcessedContent = blockProcessedContent.replace(
    /\\\(([\s\S]*?)\\\)/g,
    (_, equation) => `$${equation}$`,
  );
  return inlineProcessedContent;
};

export function replaceThinkToSection(
  text: string = '',
  avatarDialog?: string | null,
) {
  // First handle complete <think>...</think> blocks
  const completePattern = /<think>([\s\S]*?)<\/think>/g;
  let processedText = text.replace(
    completePattern,
    (_, inner) =>
      `<details class="think-section"><summary class="think-toggle">显示思路</summary><div class="think-content">${inner}</div></details>`,
  );

  // Then handle incomplete <think> blocks (without closing </think>)
  // Check if there's a <think> tag but no corresponding </think> tag after it
  const thinkStartIndex = processedText.lastIndexOf('<think>');
  const thinkEndIndex = processedText.indexOf('</think>', thinkStartIndex);

  if (thinkStartIndex !== -1 && thinkEndIndex === -1) {
    // Found <think> without closing </think>
    const beforeThink = processedText.substring(0, thinkStartIndex);
    const thinkContent = processedText.substring(thinkStartIndex + 7); // 7 is length of '<think>'

    processedText =
      beforeThink +
      `<details class="think-section"><summary class="think-toggle">显示思路</summary><div class="think-content">${thinkContent}</div></details>`;
  }

  return processedText;
}

export function setInitialChatVariableEnabledFieldValue(
  field: ChatVariableEnabledField,
) {
  return field !== ChatVariableEnabledField.MaxTokensEnabled;
}
