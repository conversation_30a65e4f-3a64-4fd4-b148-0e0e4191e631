#!/usr/bin/env node

/**
 * 修复 Tailwind CSS 编译超时问题的脚本
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 开始修复 Tailwind CSS 编译问题...\n');

// 1. 清理缓存
console.log('1. 清理缓存...');
try {
  // 清理 npm 缓存
  execSync('npm cache clean --force', { stdio: 'inherit' });
  
  // 清理 .umi 缓存
  const umiCachePath = path.join(__dirname, '.umi');
  if (fs.existsSync(umiCachePath)) {
    fs.rmSync(umiCachePath, { recursive: true, force: true });
    console.log('   ✅ 已清理 .umi 缓存');
  }
  
  // 清理 node_modules/.cache
  const cacheDir = path.join(__dirname, 'node_modules', '.cache');
  if (fs.existsSync(cacheDir)) {
    fs.rmSync(cacheDir, { recursive: true, force: true });
    console.log('   ✅ 已清理 node_modules/.cache');
  }
  
  console.log('   ✅ 缓存清理完成\n');
} catch (error) {
  console.log('   ⚠️  缓存清理失败，继续执行...\n');
}

// 2. 检查配置文件
console.log('2. 检查配置文件...');

// 检查 tailwind.config.js
const tailwindConfigPath = path.join(__dirname, 'tailwind.config.js');
if (fs.existsSync(tailwindConfigPath)) {
  const config = fs.readFileSync(tailwindConfigPath, 'utf8');
  if (config.includes('generateTimeout')) {
    console.log('   ✅ tailwind.config.js 已配置超时时间');
  } else {
    console.log('   ⚠️  tailwind.config.js 缺少超时配置');
  }
} else {
  console.log('   ❌ tailwind.config.js 不存在');
}

// 检查 .umirc.ts
const umircPath = path.join(__dirname, '.umirc.ts');
if (fs.existsSync(umircPath)) {
  const config = fs.readFileSync(umircPath, 'utf8');
  if (config.includes('tailwindcss') && config.includes('timeout')) {
    console.log('   ✅ .umirc.ts 已配置 Tailwind 超时时间');
  } else {
    console.log('   ⚠️  .umirc.ts 可能缺少 Tailwind 超时配置');
  }
} else {
  console.log('   ❌ .umirc.ts 不存在');
}

console.log('');

// 3. 检查依赖
console.log('3. 检查依赖...');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
  
  if (deps.tailwindcss) {
    console.log(`   ✅ tailwindcss: ${deps.tailwindcss}`);
  } else {
    console.log('   ❌ 缺少 tailwindcss 依赖');
  }
  
  if (deps['@umijs/plugins']) {
    console.log(`   ✅ @umijs/plugins: ${deps['@umijs/plugins']}`);
  } else {
    console.log('   ❌ 缺少 @umijs/plugins 依赖');
  }
  
  console.log('');
} catch (error) {
  console.log('   ❌ 无法读取 package.json\n');
}

// 4. 提供解决方案
console.log('🛠️  解决方案：');
console.log('');
console.log('如果问题仍然存在，请尝试以下步骤：');
console.log('');
console.log('1. 重新安装依赖：');
console.log('   rm -rf node_modules package-lock.json');
console.log('   npm install');
console.log('');
console.log('2. 使用更长的超时时间启动：');
console.log('   TAILWIND_TIMEOUT=120000 npm run start');
console.log('');
console.log('3. 如果是开发环境，可以尝试禁用 Tailwind JIT：');
console.log('   在 tailwind.config.js 中添加 mode: "jit" 并设为 false');
console.log('');
console.log('4. 检查系统资源：');
console.log('   - 确保有足够的内存（建议 8GB+）');
console.log('   - 关闭其他占用资源的程序');
console.log('   - 检查磁盘空间是否充足');
console.log('');
console.log('5. 如果是 Windows 系统，可能需要：');
console.log('   - 使用 PowerShell 或 Git Bash');
console.log('   - 检查防病毒软件是否影响编译');
console.log('');

// 5. 尝试启动开发服务器
console.log('🚀 现在尝试启动开发服务器...');
console.log('如果仍然超时，请按 Ctrl+C 停止，然后手动执行上述解决方案。');
console.log('');

try {
  execSync('npm run start', { stdio: 'inherit' });
} catch (error) {
  console.log('\n❌ 启动失败，请手动执行解决方案中的步骤。');
  process.exit(1);
}
