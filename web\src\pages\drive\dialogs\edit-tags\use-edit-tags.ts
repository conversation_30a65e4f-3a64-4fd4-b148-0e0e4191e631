import { useTranslate } from '@/hooks/common-hooks';
import { IFile } from '@/interfaces/database/file-manager';
import driveService from '@/services/drive-service';
import { message } from 'antd';
import { useCallback, useState } from 'react';

export function useEditTags() {
  const { t } = useTranslate('fileManager');
  const [editTagsVisible, setEditTagsVisible] = useState(false);
  const [editTagsLoading, setEditTagsLoading] = useState(false);
  const [currentFile, setCurrentFile] = useState<IFile | null>(null);

  const showEditTagsModal = useCallback((file: IFile) => {
    setCurrentFile(file);
    setEditTagsVisible(true);
  }, []);

  const hideEditTagsModal = useCallback(() => {
    setEditTagsVisible(false);
    setCurrentFile(null);
  }, []);

  const onEditTagsOk = useCallback(
    async (tags: string[]) => {
      if (!currentFile) return;

      setEditTagsLoading(true);
      try {
        await driveService.updateFileTags({
          file_id: currentFile.id,
          tags,
        });

        message.success(t('updateTagsSuccess'));
        hideEditTagsModal();

        // Trigger a refresh of the file list
        window.location.reload();
      } catch (error) {
        console.error('Failed to update tags:', error);
        message.error(t('updateTagsError'));
      } finally {
        setEditTagsLoading(false);
      }
    },
    [currentFile, hideEditTagsModal, t],
  );

  return {
    editTagsVisible,
    editTagsLoading,
    currentFile,
    showEditTagsModal,
    hideEditTagsModal,
    onEditTagsOk,
  };
}

export type UseEditTagsReturnType = ReturnType<typeof useEditTags>;
