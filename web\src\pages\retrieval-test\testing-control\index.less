@import '@/theme/high-tech-theme.less';

.testingControlWrapper {
  height: 100%;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  overflow-y: auto;
}

.sectionCard {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-normal);
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    border-color: var(--primary-color);
    box-shadow: var(--card-hover-shadow);
  }

  :global(.ant-card-head) {
    background: linear-gradient(
      135deg,
      var(--primary-ultralight) 0%,
      rgba(56, 189, 248, 0.05) 100%
    );
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
    padding: var(--spacing-sm) var(--spacing-md);
    min-height: auto;
  }

  :global(.ant-card-head-title) {
    font-size: 0.9rem;
    font-weight: 500;
    padding: 0;
  }

  :global(.ant-card-body) {
    padding: var(--spacing-md);
  }

  &:nth-child(1) {
    flex: 1 1 auto;
    min-height: 200px;
    overflow: hidden;
  }

  &:nth-child(2) {
    flex: 0 0 auto;
    max-height: 300px;
  }
}

.sectionHeader {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--text-primary);
}

.sectionIcon {
  font-size: 1rem;
  color: var(--primary-color);
}

/* 权重设置部分 */
.weightSettings {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);

  :global(.ant-form-item) {
    margin-bottom: var(--spacing-xs);
  }

  :global(.ant-slider) {
    .ant-slider-rail {
      background-color: var(--neutral-200);
    }

    .ant-slider-track {
      background-color: var(--primary-color);
    }

    .ant-slider-handle {
      border-color: var(--primary-color);

      &:hover {
        border-color: var(--primary-hover);
      }
    }
  }
}

/* 文档选择部分 */
.documentSection {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 150px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-sm);
}

.documentList {
  width: 100%;
  overflow: hidden;
  .documentListContent {
    max-height: 180px;
    overflow-y: auto;
  }
}

.selectAllContainer {
  padding: 0 0 var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-sm);
}

.documentItem {
  padding: var(--spacing-xs) 0 !important;
  border-bottom: 1px solid var(--neutral-100) !important;
  overflow: hidden;

  &:last-child {
    border-bottom: none !important;
  }

  :global(.ant-list-item-meta) {
    margin-bottom: 0;
  }

  :global(.ant-checkbox-wrapper) {
    width: 100%;
    overflow: hidden;

    :global(.ant-checkbox) {
      flex-shrink: 0;
    }
  }

  :global(.ant-space) {
    width: 100%;
    overflow: hidden;
  }

  :global(.ant-space-item) {
    width: 100%;
    overflow: hidden;
  }

  :global(.ant-typography-ellipsis) {
    width: 100%;
    max-width: 240px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.documentName {
  font-size: 0.9rem;
  color: var(--text-primary);
  font-weight: 400;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  :global(.ant-typography) {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.documentInfo {
  font-size: 0.8rem;
  color: var(--text-tertiary);
  width: 100%;
  max-width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selectionHint {
  text-align: center;
  font-size: 0.8rem;
  margin-top: var(--spacing-xs);
  display: block;
}

.documentTable {
  :global(.ant-table-tbody) {
    .ant-table-row {
      transition: all var(--transition-fast);

      &:hover {
        background-color: var(--primary-ultralight);
      }

      &.ant-table-row-selected {
        background-color: rgba(var(--primary-color-rgb), 0.1);
      }
    }
  }

  :global(.ant-checkbox-wrapper) {
    .ant-checkbox {
      .ant-checkbox-inner {
        border-color: var(--border-color);
        border-radius: 3px;

        &:after {
          border-color: white;
        }
      }

      &.ant-checkbox-checked {
        .ant-checkbox-inner {
          background-color: var(--primary-color);
          border-color: var(--primary-color);
        }
      }
    }
  }
}

.hitCount {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.emptyDocuments {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-tertiary);
}

/* 测试文本输入部分 */
.testingSection {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);

  :global(.ant-form-item) {
    flex: 1;
    margin-bottom: var(--spacing-sm);
    display: flex;
    flex-direction: column;
  }

  :global(.ant-form-item-control) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  :global(.ant-form-item-control-input) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  :global(.ant-form-item-control-input) {
    width: 100%;
    :global(.ant-form-item-control-input-content) {
      width: 100%;
    }
  }
}

.testTextarea {
  flex: 1 !important;
  min-height: 120px !important;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  background-color: white;
  resize: none;
  transition: all var(--transition-normal);

  &:hover,
  &:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-ultralight);
  }

  &::placeholder {
    color: var(--text-tertiary);
  }

  :global(.ant-input) {
    height: 100% !important;
  }
}

.testButton {
  height: 40px;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--primary-hover) 100%
  );
  border: none;
  transition: all var(--transition-normal);
  flex-shrink: 0;

  &:hover:not(:disabled) {
    background: linear-gradient(
      135deg,
      var(--primary-hover) 0%,
      var(--primary-dark) 100%
    );
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
  }

  &:disabled {
    background: var(--neutral-300);
    color: var(--text-disabled);
  }
}

/* 全局样式覆盖 */
:global {
  .ant-card {
    border-radius: var(--border-radius-md);
  }

  .ant-form-item-label > label {
    font-weight: 500;
    color: var(--text-primary);
  }

  .ant-input,
  .ant-input-number,
  .ant-select-selector {
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);

    &:hover,
    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-ultralight);
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .testingControlWrapper {
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
    overflow-y: auto;
  }

  .sectionCard {
    &:nth-child(2) {
      min-height: 100px;
    }

    &:nth-child(3) {
      min-height: 100px;
    }
  }

  .documentSection {
    min-height: 100px;
  }

  .documentName {
    max-width: 240px;
  }

  .testTextarea {
    min-height: 60px !important;
  }
}

/* Dark mode adjustments */
:global(.dark) {
  .testingControlWrapper {
    background-color: var(--neutral-800);

    .historyCard {
      background-color: var(--neutral-800);
    }
  }
}
