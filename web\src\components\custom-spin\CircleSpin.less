// CircleSpin 圆形加载组件样式

// 独立模式（无子组件）
.circle-spin-standalone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  padding: 20px;
}

// 包装模式容器
.circle-spin-container {
  position: relative;
  display: block;
  min-height: 80px;
}

// 被包装的内容
.circle-spin-content {
  opacity: 0.5;
  pointer-events: none;
  user-select: none;
  transition: opacity 0.3s ease;
}

// 遮罩层
.circle-spin-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--spin-overlay-bg, rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(2px);
  z-index: 100;
  min-height: 32px;
}

// 动画指示器容器
.circle-spin-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
}

// CSS圆环Loading动画 - 基于用户提供的确切代码
.loading-circle {
  display: block;
}

.loading-circle .outerCircle {
  display: block;
  position: absolute;
  margin: 0 auto;
  border-radius: 50%;

  // 第一个样式（会被第二个覆盖，但保留作为基础）
  border-top: 7px solid #1890ff;
  border-bottom: 7px solid #1890ff;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  box-shadow: 0 0 20px #1890ff;

  animation: cwSpin 0.8s linear 0.2s infinite;
}

// 第二个样式规则（按照用户CSS，这会覆盖上面的）
.loading-circle .outerCircle {
  box-shadow: none;
  border-top: 7px solid transparent;
  border-bottom: 7px solid #1890ff;
  border-left: 7px solid #1890ff;
  border-right: 7px solid transparent;
  animation: cwSpin 0.8s linear 0.2s infinite;
}

// 不同尺寸的圆环
.circle-spin-indicator.size-small .loading-circle {
  width: 32px;
  height: 32px;
}

.circle-spin-indicator.size-small .outerCircle {
  width: 32px;
  height: 32px;
  border-width: 3px;
  // 覆盖样式
  border-top: 3px solid transparent;
  border-bottom: 3px solid #1890ff;
  border-left: 3px solid #1890ff;
  border-right: 3px solid transparent;
  box-shadow: none;
}

.circle-spin-indicator.size-default .loading-circle {
  width: 48px;
  height: 48px;
}

.circle-spin-indicator.size-default .outerCircle {
  width: 48px;
  height: 48px;
  border-width: 5px;
  // 覆盖样式
  border-top: 5px solid transparent;
  border-bottom: 5px solid #1890ff;
  border-left: 5px solid #1890ff;
  border-right: 5px solid transparent;
  box-shadow: none;
}

.circle-spin-indicator.size-large .loading-circle {
  width: 64px;
  height: 64px;
}

.circle-spin-indicator.size-large .outerCircle {
  width: 64px;
  height: 64px;
  border-width: 7px;
  // 覆盖样式
  border-top: 7px solid transparent;
  border-bottom: 7px solid #1890ff;
  border-left: 7px solid #1890ff;
  border-right: 7px solid transparent;
  box-shadow: none;
}

// 提示文本
.circle-spin-tip {
  color: var(--spin-tip-color, rgba(0, 0, 0, 0.65));
  font-size: 14px;
  text-align: center;
  margin-top: 8px;
  line-height: 1.5;
}

// 旋转动画 - 完全按照用户提供的代码
@-webkit-keyframes cwSpin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}

@-moz-keyframes cwSpin {
  0% {
    -moz-transform: rotate(0deg);
  }
  100% {
    -moz-transform: rotate(360deg);
  }
}

@-ms-keyframes cwSpin {
  0% {
    -ms-transform: rotate(0deg);
  }
  100% {
    -ms-transform: rotate(360deg);
  }
}

@-o-keyframes cwSpin {
  0% {
    -o-transform: rotate(0deg);
  }
  100% {
    -o-transform: rotate(360deg);
  }
}

@keyframes cwSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .circle-spin-overlay {
    background: rgba(0, 0, 0, 0.8);
  }

  .circle-spin-tip {
    color: rgba(255, 255, 255, 0.85);
  }

  .circle-spin-indicator.size-small .outerCircle,
  .circle-spin-indicator.size-default .outerCircle,
  .circle-spin-indicator.size-large .outerCircle {
    border-bottom-color: #40a9ff;
    border-left-color: #40a9ff;
  }

  .loading-circle .outerCircle {
    border-bottom-color: #40a9ff;
    border-left-color: #40a9ff;
  }
}

// 项目主题适配
[data-theme='dark'] {
  .circle-spin-overlay {
    background: rgba(0, 0, 0, 0.8);
  }

  .circle-spin-tip {
    color: rgba(255, 255, 255, 0.85);
  }

  .circle-spin-indicator.size-small .outerCircle,
  .circle-spin-indicator.size-default .outerCircle,
  .circle-spin-indicator.size-large .outerCircle {
    border-bottom-color: #40a9ff;
    border-left-color: #40a9ff;
  }

  .loading-circle .outerCircle {
    border-bottom-color: #40a9ff;
    border-left-color: #40a9ff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .circle-spin-indicator.size-large .loading-circle {
    width: 48px;
    height: 48px;
  }

  .circle-spin-indicator.size-large .outerCircle {
    width: 48px;
    height: 48px;
    border-width: 5px;
    border-top: 5px solid transparent;
    border-bottom: 5px solid #1890ff;
    border-left: 5px solid #1890ff;
    border-right: 5px solid transparent;
  }
}

// 性能优化
.loading-circle,
.outerCircle {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}

// 自定义颜色支持（可通过CSS变量覆盖）
.circle-spin-indicator .outerCircle {
  border-bottom-color: var(--primary-color, #1890ff);
  border-left-color: var(--primary-color, #1890ff);
}
