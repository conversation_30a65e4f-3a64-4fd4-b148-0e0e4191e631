from __future__ import annotations

"""Common prompt builder helpers to be extended by specific agents."""

from typing import List

from .enums import Difficulty, QuestionDistribution


class PromptBuilderBase:
    """Abstract helper providing reusable description utilities."""

    def __init__(self, *, exam_name: str | None = None):
        self.exam_name = exam_name or ""

    # ------------------------------------------------------------------
    # Reusable utilities
    # ------------------------------------------------------------------
    @staticmethod
    def difficulty_desc(difficulty: Difficulty) -> str:
        mapping = {
            Difficulty.LOW: "简单（基础概念理解）",
            Difficulty.MEDIUM: "中等（知识点应用）",
            Difficulty.HIGH: "困难（综合分析）",
        }
        return mapping.get(difficulty, "中等（知识点应用）")

    @staticmethod
    def distribution_desc(dist: QuestionDistribution) -> str:
        parts: List[str] = []
        if dist.single_choice:
            parts.append(f"{dist.single_choice}道单选题")
        if dist.multiple_choice:
            parts.append(f"{dist.multiple_choice}道多选题")
        if dist.fill_blank:
            parts.append(f"{dist.fill_blank}道填空题")
        if dist.true_false:
            parts.append(f"{dist.true_false}道判断题")
        if dist.short_answer:
            parts.append(f"{dist.short_answer}道简答题")
        if dist.ordering:
            parts.append(f"{dist.ordering}道排序题")

        if not parts:
            return "随机生成若干道试题，题型可以包括单选题、多选题、填空题、判断题、简答题等。"

        return "生成试题的类型及数量为：" + "、".join(parts) + "。"
