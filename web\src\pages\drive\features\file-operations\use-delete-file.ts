import { useDeleteFile, useGetFolderId } from '@/hooks/use-drive-request';
import { useCallback } from 'react';

export const useHandleDeleteFile = () => {
  const { deleteFile: removeDocument } = useDeleteFile();
  const parentId = useGetFolderId();

  const handleRemoveFile = useCallback(
    async (fileIds: string[]) => {
      console.log('删除文件请求参数:', { fileIds, parentId });
      const code = await removeDocument({ fileIds, parentId });
      console.log('删除文件响应代码:', code);

      return code;
    },
    [parentId, removeDocument],
  );

  return { handleRemoveFile };
};
