#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import json
import logging
import re
from abc import ABC
from typing import List, Dict, Any

import pandas as pd

from api.db import LLMType
from api.db.services.llm_service import LLMBundle
from api.db.services.drive_service import DriveService
from agent.component.base import ComponentBase, ComponentParamBase


class FileKeywordSearchParam(ComponentParamBase):
    """
    Define the FileKeywordSearch component parameters.
    """

    def __init__(self):
        super().__init__()
        self.llm_id = "deepseek-r1:32b@Ollama"  # 使用本地Ollama模型，避免需要云端API密钥
        self.max_keywords = 5
        self.max_results = 20
        self.temperature = 0.3

    def check(self):
        self.check_empty(self.llm_id, "[FileKeywordSearch] LLM ID")
        self.check_positive_integer(
            self.max_keywords, "[FileKeywordSearch] Max keywords")
        self.check_positive_integer(
            self.max_results, "[FileKeywordSearch] Max results")
        self.check_decimal_float(
            self.temperature, "[FileKeywordSearch] Temperature")


class FileKeywordSearch(ComponentBase, ABC):
    component_name = "FileKeywordSearch"

    def _run(self, history, **kwargs):
        """
        Extract keywords from input sentence and search matching files.
        """
        try:
            # Get input sentence
            sentence = self._get_input_sentence()
            if not sentence:
                return self._output_result([], [], "No input provided")

            # Extract keywords using LLM
            keywords = self._extract_keywords(sentence)
            if not keywords:
                return self._output_result([], [], "No keywords extracted")

            # Search files using keywords
            matched_files = self._search_files(keywords)

            # Format and return results
            message = f"Found {len(matched_files)} files" if matched_files else "No matching files found"
            return self._output_result(keywords, matched_files, message)

        except Exception as e:
            logging.error(
                f"[FileKeywordSearch] Error: {str(e)}", exc_info=True)
            return FileKeywordSearch.be_output(f"Error: {str(e)}")

    def _get_input_sentence(self) -> str:
        """Get and validate input sentence."""
        input_data = self.get_input()
        if "content" not in input_data or input_data.empty:
            return ""

        sentence = " ".join(str(content)
                            for content in input_data["content"]).strip()
        logging.info(f"[FileKeywordSearch] Input: {sentence}")
        return sentence

    def _extract_keywords(self, sentence: str) -> List[Dict[str, Any]]:
        """Extract keywords from sentence using LLM."""
        try:
            tenant_id = self._canvas.get_tenant_id()
            chat_mdl = LLMBundle(tenant_id, LLMType.CHAT, self._param.llm_id)

            prompt = f"""
  你是一名专业的中文文本分析助手，任务是从给定句子中提取 **{self._param.max_keywords} 个关键词**（single token / 最小可独立表达含义的词或词组），并按重要性从高到低排序。请严格遵守以下规则：

  1. 关键词须优先匹配专业领域名词或术语，对于复合词应二次拆分：  
    • 专业名词或术语应优先匹配，如"LKJ设备"、"电务信号工"、"道岔转辙机"等；  
    • 复合词应二次原子化拆分，如"道岔转辙机"可拆分为"道岔"和"转辙机"，但复合词的权重高于原子词；  
    • 不要输出由"、/，空格"连接的多词并列项；  
    • 每个关键词长度 2~6 个汉字，优先输出名词或名词性短语。  

  2. 关键词权重 `weight` 为 1–10 的整数：  
    • 10 = 核心主题，1 = 辅助信息；  
    • 先考虑语义重要性，再考虑词频。  

  3. 过滤无意义/停用词：的、了、和、以及、可以、比如、但是、我们、你们、他们、数字编号等。  

  4. 返回 **纯 JSON 数组**，格式如下（不要输出反引号或其他文字）：  
  [
    {{"keyword": "示例1", "weight": 10}},
    {{"keyword": "示例2", "weight": 9}},
    …
  ]

  句子：{sentence}
  """

            response = chat_mdl.chat(
                prompt,
                [{"role": "user", "content": "请提取句子中的关键词"}],
                {"temperature": self._param.temperature, "max_tokens": 500}
            )

            keywords = self._parse_keywords(response)
            logging.info(f"[FileKeywordSearch] Keywords: {keywords}")
            return keywords

        except Exception as e:
            logging.error(
                f"[FileKeywordSearch] Keyword extraction failed: {str(e)}")
            return self._fallback_keywords(sentence)

    def _parse_keywords(self, response: str) -> List[Dict[str, Any]]:
        """Parse LLM response to extract keywords."""
        try:
            # Clean response
            cleaned = re.sub(r'```json\s*|\s*```', '', response.strip())
            cleaned = re.search(r'\[.*\]', cleaned, re.DOTALL)
            if not cleaned:
                return []

            # Parse JSON
            data = json.loads(cleaned.group())
            keywords = []

            for item in data:
                if isinstance(item, dict) and "keyword" in item:
                    keyword = str(item["keyword"]).strip()
                    weight = float(item.get("weight", 5))
                    if keyword and 1 <= weight <= 10:
                        keywords.append({"keyword": keyword, "weight": weight})

            return sorted(keywords, key=lambda x: x["weight"], reverse=True)[:self._param.max_keywords]

        except Exception as e:
            logging.error(
                f"[FileKeywordSearch] Parse keywords failed: {str(e)}")
            return []

    def _fallback_keywords(self, sentence: str) -> List[Dict[str, Any]]:
        """Fallback keyword extraction using simple text processing."""
        import re

        # Remove punctuation and split by spaces and common Chinese punctuation
        cleaned = re.sub(r'[，。！？、；：""''（）【】\s]+', ' ', sentence)
        words = cleaned.split()

        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一',
                      '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
                      '简单', '介绍', '一下', '常见', '比如', '可以', '但是', '我们', '他们', '她们', '它们'}

        keywords = []

        # Extract individual meaningful segments
        for word in words:
            word = word.strip()
            if len(word) >= 1 and word not in stop_words:
                # For Chinese text, also try to extract 2-4 character segments
                if len(word) >= 4:  # If word is long, try to break it down
                    # Extract common patterns like equipment names
                    if 'LKJ' in word:
                        keywords.append({"keyword": "LKJ", "weight": 8.0})
                    if '设备' in word:
                        keywords.append({"keyword": "设备", "weight": 7.0})
                    if '信号' in word:
                        keywords.append({"keyword": "信号", "weight": 7.0})
                    if '系统' in word:
                        keywords.append({"keyword": "系统", "weight": 6.0})

                    # Also add 2-3 character segments
                    for i in range(len(word) - 1):
                        segment = word[i:i+2]
                        if segment not in stop_words:
                            keywords.append(
                                {"keyword": segment, "weight": 4.0})
                        if i < len(word) - 2:
                            segment3 = word[i:i+3]
                            if segment3 not in stop_words:
                                keywords.append(
                                    {"keyword": segment3, "weight": 5.0})
                else:
                    keywords.append({"keyword": word, "weight": 5.0})

        # Remove duplicates and sort by weight
        seen = set()
        unique_keywords = []
        for kw in keywords:
            if kw["keyword"] not in seen and len(kw["keyword"]) >= 1:
                seen.add(kw["keyword"])
                unique_keywords.append(kw)

        # Sort by weight and limit results
        unique_keywords.sort(key=lambda x: x["weight"], reverse=True)
        return unique_keywords[:self._param.max_keywords]

    def _search_files(self, keywords: List[Dict[str, Any]]) -> List[Dict]:
        """Search files using extracted keywords."""
        if not keywords:
            return []

        try:
            tenant_id = self._canvas.get_tenant_id()
            matched_files = []

            # Get user ID from canvas
            user_id = None
            if hasattr(self._canvas, 'get_user_id'):
                user_id = self._canvas.get_user_id()
            elif hasattr(self._canvas, '_user_id'):
                user_id = self._canvas._user_id

            # If unable to get user_id, use tenant_id as fallback
            if not user_id:
                user_id = tenant_id
                logging.warning(
                    f"[FileKeywordSearch] Could not get user_id, using tenant_id {tenant_id} as fallback")

            # Get .yontenet-drive root folder first so that search is limited to the
            # resource library instead of the whole tenant space
            try:
                drive_root_folder = DriveService.get_drive_root_folder(user_id)
                root_id = drive_root_folder["id"]
            except Exception as e:
                logging.error(
                    f"[FileKeywordSearch] Failed to get .yontenet-drive root folder: {str(e)}")
                return []

            # Collect all descendant folder ids under the .yontenet-drive root. We
            # will use these to constrain the search scope so that only files
            # inside the resource library are returned.
            descendant_folder_ids = self._get_descendant_folder_ids(
                root_id, tenant_id)

            # Search for each keyword using enhanced search
            for kw_data in keywords:
                keyword = kw_data["keyword"]

                # Search files using custom method that includes tags
                try:
                    files = self._search_files_with_tags(
                        tenant_id, descendant_folder_ids, keyword, self._param.max_results
                    )

                    # Add keyword info to each file
                    for file in files:
                        file["matched_keyword"] = keyword
                        file["keyword_weight"] = kw_data["weight"]
                        matched_files.append(file)

                except Exception as e:
                    logging.warning(
                        f"[FileKeywordSearch] Search failed for keyword '{keyword}': {str(e)}")
                    continue

            # Remove duplicates and sort by keyword weight
            unique_files = {}
            for file in matched_files:
                file_id = file["id"]
                if file_id not in unique_files or file["keyword_weight"] > unique_files[file_id]["keyword_weight"]:
                    unique_files[file_id] = file

            result = list(unique_files.values())
            result.sort(key=lambda x: x["keyword_weight"], reverse=True)

            return result[:self._param.max_results]

        except Exception as e:
            logging.error(f"[FileKeywordSearch] File search failed: {str(e)}")
            return []

    def _search_files_with_tags(self, tenant_id: str, folder_ids: set, keyword: str, max_results: int) -> List[Dict]:
        """Search files whose parent folder is within the provided folder_ids set
        and whose tags field contains the keyword. File names are **not** used
        for matching so that we only rely on curated tags, satisfying the strict
        matching requirement.
        """
        try:
            from api.db.db_models import File
            from api.db import FileType

            logging.info(
                f"[FileKeywordSearch] Searching for keyword: '{keyword}'")

            # Some Chinese tags are stored as unicode escape sequences (e.g. "\u89c6\u9891").
            # Attempt to match both the raw keyword and the escaped version.
            escaped_kw = self._to_unicode_escape(keyword)

            search_conditions = (
                (File.tenant_id == tenant_id) &
                (File.type != FileType.FOLDER.value) &  # Exclude folders
                # Only search in allowed folders
                (File.parent_id << list(folder_ids)) &
                (
                    File.tags.contains(keyword) |  # direct match
                    File.tags.contains(escaped_kw)  # escaped match
                )
            )

            files = (File.select()
                     .where(search_conditions)
                     .order_by(File.update_time.desc())
                     .limit(max_results))

            # Execute query and convert to list of dicts
            file_list = list(files.dicts())

            logging.info(
                f"[FileKeywordSearch] Found {len(file_list)} files for keyword '{keyword}'")

            return file_list

        except Exception as e:
            logging.error(
                f"[FileKeywordSearch] Enhanced search failed: {str(e)}")
            # Fallback to original DriveService search
            try:
                # Fall back to DriveService scoped to the .yontenet-drive root
                files, _ = DriveService.get_by_pf_id(
                    tenant_id=tenant_id,
                    pf_id=list(folder_ids)[0],
                    page_number=1,
                    items_per_page=max_results,
                    orderby="update_time",
                    desc=True,
                    keywords=keyword
                )
                return files
            except Exception as fallback_e:
                logging.error(
                    f"[FileKeywordSearch] Fallback search also failed: {str(fallback_e)}")
                return []

    def _to_unicode_escape(self, text: str) -> str:
        """Convert Chinese characters to Unicode escape sequences that match database format."""
        try:
            # Convert each character to \uXXXX format
            result = ""
            for char in text:
                if ord(char) > 127:  # Non-ASCII characters
                    result += f"\\u{ord(char):04x}"
                else:
                    result += char
            return result
        except Exception as e:
            logging.warning(
                f"[FileKeywordSearch] Unicode escape failed for '{text}': {str(e)}")
            return text

    def _output_result(self, keywords: List[Dict], files: List[Dict], message: str) -> pd.DataFrame:
        """Format and output results."""
        result = {
            "keywords": keywords,
            "matched_files": files,
            "total_matches": len(files),
            "message": message
        }

        return FileKeywordSearch.be_output(json.dumps(result, ensure_ascii=False, indent=2))

    def debug(self, **kwargs):
        """Debug method for testing."""
        return self._run([], **kwargs)

    def _get_descendant_folder_ids(self, root_id: str, tenant_id: str) -> set:
        """Breadth-first search to gather all folder ids under the given root
        folder (inclusive). This allows us to restrict search scope to the
        .yontenet-drive directory tree.
        """
        from api.db.db_models import File
        from api.db import FileType

        folder_ids = {root_id}
        queue = [root_id]

        while queue:
            current = queue.pop(0)
            subfolders = (File.select(File.id)
                          .where((File.parent_id == current) &
                                 (File.type == FileType.FOLDER.value) &
                                 (File.tenant_id == tenant_id) &
                                 ~(File.id == current)))
            for folder in subfolders:
                if folder.id not in folder_ids:
                    folder_ids.add(folder.id)
                    queue.append(folder.id)

        return folder_ids
