@import '@/theme/high-tech-theme.less';

.filePreviewModal {
  .high-tech-card();

  :global(.ant-modal-content) {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-hover-shadow);
    backdrop-filter: blur(20px);
    max-height: 90vh;
    overflow: hidden;
  }

  :global(.ant-modal-header) {
    background: var(--background-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    padding: var(--spacing-md) var(--spacing-lg);
  }

  :global(.ant-modal-body) {
    padding: 0;
    background: var(--background-primary);
    max-height: 80vh;
    overflow: hidden;
  }

  :global(.ant-modal-close) {
    top: var(--spacing-md);
    right: var(--spacing-md);
    color: var(--text-secondary);

    &:hover {
      color: var(--primary-color);
    }
  }

  .modalHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .fileName {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--text-primary);
      max-width: 70%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .headerActions {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
  }

  .closeButton,
  .fullscreenButton {
    color: var(--text-secondary);
    border: none;
    background: transparent;

    &:hover {
      color: var(--primary-color);
      background: rgba(var(--primary-color-rgb), 0.1);
    }
  }
}

.previewContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-primary);
  position: relative;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: var(--text-secondary);

  p {
    margin-top: var(--spacing-md);
    font-size: var(--font-size-base);
  }
}

.errorContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50vh;
  color: var(--text-tertiary);

  p {
    font-size: var(--font-size-lg);
  }
}

.videoPlayer {
  background: #000;
  border-radius: var(--border-radius-sm);
  outline: none;

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

.pdfViewer {
  border-radius: var(--border-radius-sm);
  background: #fff;
}

.imagePreview {
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-sm);
  background: #fff;
}

.documentViewer {
  background: var(--background-secondary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);

  // 优化文档内容样式
  :global(p) {
    margin-bottom: var(--spacing-sm);
    line-height: 1.66;
    color: var(--text-primary);
  }

  :global(h1, h2, h3, h4, h5, h6) {
    color: var(--text-primary);
    margin: var(--spacing-md) 0 var(--spacing-sm) 0;
  }

  :global(table) {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-md) 0;

    th,
    td {
      border: 1px solid var(--border-color);
      padding: var(--spacing-xs) var(--spacing-sm);
      text-align: left;
    }

    th {
      background: var(--background-tertiary);
      font-weight: 600;
      color: var(--text-primary);
    }

    td {
      background: var(--background-secondary);
      color: var(--text-primary);
    }
  }

  :global(pre) {
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    overflow-x: auto;
    color: var(--text-primary);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }

  :global(ul, ol) {
    padding-left: var(--spacing-lg);
    color: var(--text-primary);

    li {
      margin-bottom: var(--spacing-xs);
    }
  }

  // Excel表格特殊样式
  :global(.excel-table) {
    font-size: var(--font-size-sm);

    th,
    td {
      min-width: 80px;
      padding: var(--spacing-xs);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .filePreviewModal {
    :global(.ant-modal-content) {
      margin: 0;
      max-height: 100vh;
      border-radius: 0;
    }

    .modalHeader {
      .fileName {
        max-width: 60%;
      }
    }
  }

  .previewContainer {
    .videoPlayer,
    .pdfViewer,
    .imagePreview {
      max-height: 60vh;
    }

    .documentViewer {
      height: 60vh;
      padding: var(--spacing-md);
    }
  }
}

// 全屏模式样式（为将来的全屏功能预留）
.fullscreenMode {
  :global(.ant-modal-content) {
    width: 100vw !important;
    height: 100vh !important;
    max-width: none !important;
    max-height: none !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  .previewContainer {
    height: calc(100vh - 60px);

    .videoPlayer,
    .pdfViewer {
      height: 100%;
      max-height: none;
    }

    .documentViewer {
      height: 100%;
    }
  }
}
