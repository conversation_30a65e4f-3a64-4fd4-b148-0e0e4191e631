#!/usr/bin/env python
"""Import Canvas template JSON into DB CanvasTemplate table.

Usage:
  python scripts/import_canvas_template.py agent/templates/lesson_plan_v2.json --user <user_id>

If --user not provided, defaults to first tenant user (for dev).
"""
from __future__ import annotations
from api.db.db_models import DB
from api.utils import get_uuid
from api.db.services.canvas_service import CanvasTemplateService

import argparse
import json
import sys
import os

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("template_path", help="Path to template JSON file")
    parser.add_argument("--user", dest="user_id",
                        help="owner user_id", default="system")
    args = parser.parse_args()

    if not os.path.exists(args.template_path):
        print("Template file not found")
        return

    with open(args.template_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    tpl_id = get_uuid()
    CanvasTemplateService.save(
        id=tpl_id,
        title=data.get("title", "Lesson Plan V2"),
        avatar="",
        description=data.get("description", ""),
        canvas_type=data.get("canvas_type", "lesson-plan"),
        dsl=data.get("dsl", {}),
    )
    print(f"Imported template as id={tpl_id}")


if __name__ == "__main__":
    main()
