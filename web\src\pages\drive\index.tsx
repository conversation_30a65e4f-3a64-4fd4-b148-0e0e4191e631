import { BulkOperateBar } from '@/components/bulk-operate-bar';
import { FileUploadDialog } from '@/components/file-upload-dialog';
import ListFilterBar from '@/components/list-filter-bar';
import { Button } from '@/components/ui/button';
import { useRowSelection } from '@/hooks/logic-hooks/use-row-selection';
import {
  FileOutlined,
  FileTextOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Button as AntButton, Dropdown, MenuProps } from 'antd';
import { LayoutGrid, TableOfContents } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
// 组件导入
import {
  ResourceBreadcrumb,
  ResourceGridView,
  ResourceListView,
} from './components';

// 对话框导入
import { CreateFolderDialog, MoveDialog } from './dialogs';

// Hooks 导入
import {
  useBulkOperateFile,
  useResourceBreadcrumb,
  useResourceFileList,
} from './hooks';

// 功能特性导入
import {
  useHandleCreateFolder,
  useHandleMoveFile,
  useHandleUploadFile,
} from './features';

import './modal-styles.less';
import './resource.less';

export default function Resource() {
  const { t } = useTranslation();
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('grid');

  const {
    fileUploadVisible,
    hideFileUploadModal,
    showFileUploadModal,
    fileUploadLoading,
    onFileUploadOk,
  } = useHandleUploadFile();

  const {
    folderCreateModalVisible,
    showFolderCreateModal,
    hideFolderCreateModal,
    folderCreateLoading,
    onFolderCreateOk,
  } = useHandleCreateFolder();

  const {
    pagination,
    files,
    total,
    loading,
    setPagination,
    searchString,
    handleInputChange,
  } = useResourceFileList();

  const {
    rowSelection,
    setRowSelection,
    rowSelectionIsEmpty,
    clearRowSelection,
    selectedCount,
  } = useRowSelection();

  const {
    showMoveFileModal,
    moveFileVisible,
    onMoveFileOk,
    hideMoveFileModal,
    moveFileLoading,
  } = useHandleMoveFile({ clearRowSelection });

  const { list } = useBulkOperateFile({
    files,
    rowSelection,
    showMoveFileModal,
    setRowSelection,
  });

  const { breadcrumbItems, currentFolderId } = useResourceBreadcrumb();

  // Clear selected items when navigating to a different folder
  useEffect(() => {
    clearRowSelection();
  }, [currentFolderId, clearRowSelection]);

  const leftPanel = (
    <div className="resource-header">
      <h2 className="resource-title main-page-title">
        {breadcrumbItems.length > 0 ? <ResourceBreadcrumb /> : '资源'}
      </h2>
      <div className="resource-subtitle">
        {!rowSelectionIsEmpty ? (
          <BulkOperateBar list={list} count={selectedCount} compact={true} />
        ) : (
          '管理您的文档和多媒体资源，支持多种文件格式'
        )}
      </div>
    </div>
  );

  const actionItems: MenuProps['items'] = useMemo(() => {
    return [
      {
        key: '1',
        onClick: showFileUploadModal,
        label: (
          <div className="menuItem">
            <FileTextOutlined className="menuIcon" />
            <span className="menuText">{t('fileManager.uploadFile')}</span>
          </div>
        ),
      },
      { type: 'divider' },
      {
        key: '2',
        onClick: showFolderCreateModal,
        label: (
          <div className="menuItem">
            <FileOutlined className="menuIcon" />
            <span className="menuText">{t('fileManager.newFolder')}</span>
          </div>
        ),
      },
    ];
  }, [showFileUploadModal, showFolderCreateModal, t]);

  const rightPanel = (
    <div className="resource-toolbar">
      <div className="view-toggle">
        <Button
          variant={viewMode === 'grid' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setViewMode('grid')}
          className="view-toggle-btn"
          style={{
            backgroundColor:
              viewMode === 'grid' ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
            color:
              viewMode === 'grid'
                ? 'var(--primary-color)'
                : 'var(--text-secondary)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          <LayoutGrid className="h-4 w-4" />
        </Button>
        <Button
          variant={viewMode === 'list' ? 'default' : 'outline'}
          size="sm"
          onClick={() => setViewMode('list')}
          className="view-toggle-btn"
          style={{
            backgroundColor:
              viewMode === 'list' ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
            color:
              viewMode === 'list'
                ? 'var(--primary-color)'
                : 'var(--text-secondary)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          }}
        >
          <TableOfContents className="h-4 w-4" />
        </Button>
      </div>
      <Dropdown
        menu={{ items: actionItems }}
        trigger={['click']}
        overlayClassName="dropdownOverlay"
      >
        <AntButton type="primary" icon={<PlusOutlined />}>
          {t('knowledgeDetails.addFile')}
        </AntButton>
      </Dropdown>
    </div>
  );

  return (
    <section className="resource-page light-theme-override main-content-container">
      <ListFilterBar
        leftPanel={leftPanel}
        searchString={searchString}
        onSearchChange={handleInputChange}
        showFilter={false}
        className="resource-filter-bar"
      >
        {rightPanel}
      </ListFilterBar>

      <div className="resource-content">
        {viewMode === 'grid' ? (
          <ResourceGridView
            files={files}
            total={total}
            pagination={pagination}
            setPagination={setPagination}
            loading={loading}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            showMoveFileModal={showMoveFileModal}
          />
        ) : (
          <ResourceListView
            files={files}
            total={total}
            pagination={pagination}
            setPagination={setPagination}
            loading={loading}
            rowSelection={rowSelection}
            setRowSelection={setRowSelection}
            showMoveFileModal={showMoveFileModal}
          />
        )}
      </div>

      {fileUploadVisible && (
        <FileUploadDialog
          hideModal={hideFileUploadModal}
          onOk={onFileUploadOk}
          loading={fileUploadLoading}
        />
      )}

      {folderCreateModalVisible && (
        <CreateFolderDialog
          loading={folderCreateLoading}
          visible={folderCreateModalVisible}
          hideModal={hideFolderCreateModal}
          onOk={onFolderCreateOk}
        />
      )}

      {moveFileVisible && (
        <MoveDialog
          hideModal={hideMoveFileModal}
          onOk={onMoveFileOk}
          loading={moveFileLoading}
        />
      )}
    </section>
  );
}
