import json
from types import SimpleNamespace

import pandas as pd
import pytest


@pytest.fixture(scope="module")
def client():
    from api.apps import app as flask_app

    flask_app.config["LOGIN_DISABLED"] = True  # bypass login_required
    return flask_app.test_client()


class DummyComponent:
    def __init__(self, *a, **k):
        pass

    def _run(self, **_):
        return pd.DataFrame({"content": ["# 教案示例\n\n正文……"]})


@pytest.fixture(autouse=True)
def patch_components(monkeypatch):
    """Patch heavy components to deterministic stubs."""
    import agent.component.lesson_plan as lp
    monkeypatch.setattr(lp, "LessonPlan", DummyComponent)
    import agent.component.template_retrieval as tr
    monkeypatch.setattr(tr, "TemplateRetrieval", DummyComponent)
    import agent.component.resource_search as rs
    monkeypatch.setattr(rs, "ResourceSearch", DummyComponent)
    # Patch references already imported in agent_app blueprint
    import api.apps.agent_app as agent_app_mod
    monkeypatch.setattr(agent_app_mod, "LessonPlan",
                        DummyComponent, raising=False)
    monkeypatch.setattr(agent_app_mod, "TemplateRetrieval",
                        DummyComponent, raising=False)
    monkeypatch.setattr(agent_app_mod, "ResourceSearch",
                        DummyComponent, raising=False)

    # Patch KnowledgebaseService.get_kb_by_name to return empty list
    import importlib
    kb_mod = importlib.import_module("api.db.services.knowledgebase_service")
    monkeypatch.setattr(kb_mod, "KnowledgebaseService",
                        SimpleNamespace(get_kb_by_name=lambda *a, **k: []))

    # Patch LessonPlanService
    lps_mod = importlib.import_module("api.db.services.lesson_plan_service")
    dummy_lps = SimpleNamespace(
        create_session=lambda *a, **k: "sess-123",
        create_doc=lambda *a, **k: None,
        update_session=lambda *a, **k: None,
    )
    monkeypatch.setattr(lps_mod, "LessonPlanService", dummy_lps)

    # Patch Redis connection used for rate limiting
    class _DummyRedis:
        def __init__(self):
            self.store = {}

        def incr(self, key):
            self.store[key] = self.store.get(key, 0) + 1
            return self.store[key]

        def expire(self, key, ttl):
            pass

    monkeypatch.setattr(agent_app_mod, "REDIS_CONN", _DummyRedis())

    # Patch flask_login.current_user
    import flask_login
    flask_login.utils._request_ctx_stack.top.user = SimpleNamespace(
        id="user-123")

    # Patch tenant utils
    tu_mod = importlib.import_module("api.utils.tenant_utils")
    monkeypatch.setattr(tu_mod, "get_tenant_with_fallback", lambda user_id: (
        True, SimpleNamespace(id="tenant-123")))

    monkeypatch.setattr(agent_app_mod, "current_user",
                        SimpleNamespace(id="user-123"))


HEADER = {"X-Agent-Version": "2"}


def test_start_endpoint_manual(client):
    resp = client.post(
        "/api/v1/lesson-plan-v2/start",
        data={
            "course_name": "测试课程",
            "class_type": "讲授",
            "class_hours": 40,
            "target_audience": "大学生",
            "course_content_type": "manual",
            "course_content": "课程简介"
        },
        headers=HEADER
    )
    assert resp.status_code == 200
    # SSE, check first chunk contains event:init
    first_line = resp.data.splitlines()[0].decode()
    assert first_line.startswith("event:init")


def test_rate_limit(client):
    import api.apps.agent_app as agent_app_mod
    # reset redis counter
    agent_app_mod.REDIS_CONN.store.clear()
    for _ in range(3):
        client.post(
            "/api/v1/lesson-plan-v2/start",
            data={
                "course_name": "c",
                "class_type": "x",
                "class_hours": 30,
                "target_audience": "y",
                "course_content_type": "manual",
                "course_content": "abc"
            }, headers=HEADER)
    # 4th request in same run should exceed hour limit
    resp = client.post(
        "/api/v1/lesson-plan-v2/start",
        data={
            "course_name": "c",
            "class_type": "x",
            "class_hours": 30,
            "target_audience": "y",
            "course_content_type": "manual",
            "course_content": "abc"
        }, headers=HEADER)
    assert resp.status_code == 429
