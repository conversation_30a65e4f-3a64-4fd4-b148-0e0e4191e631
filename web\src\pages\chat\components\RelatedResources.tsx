import { LeafSpin } from '@/components/custom-spin';
import { DEFAULT_LLM_CONFIG } from '@/constants/llm';
import { useFetchUserInfo } from '@/hooks/user-setting-hooks';
import { searchRelatedFiles } from '@/services/agent_service';
import {
  FileExcelOutlined,
  FileImageOutlined,
  FileOutlined,
  FilePdfOutlined,
  FilePptOutlined,
  FileTextOutlined,
  FileWordOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { Button, Card, Empty, Space, Tooltip, Typography } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styles from './RelatedResources.less';

const { Text, Paragraph } = Typography;

export interface ResourceItem {
  id: string;
  name: string;
  type: string;
  size: number;
  matched_keyword?: string;
  keyword_weight?: number;
  create_time: number | string;
  location?: string;
  source: 'api' | 'reference'; // 区分资源来源
}

interface RelatedResourcesProps {
  userQuery?: string; // 用户的查询文本
  referenceDocuments?: Array<{
    doc_id: string;
    doc_name: string;
    url?: string;
  }>; // 来自聊天回复的referenceDocumentList
  onResourceClick?: (resource: ResourceItem) => void;
}

// 获取文件类型对应的图标
const getFileIcon = (type: string, fileName: string) => {
  const lowerType = type.toLowerCase();
  const lowerName = fileName.toLowerCase();

  if (lowerType === 'pdf' || lowerName.endsWith('.pdf')) {
    return <FilePdfOutlined className={styles.fileIcon} />;
  }
  if (
    lowerType === 'document' ||
    lowerName.endsWith('.doc') ||
    lowerName.endsWith('.docx')
  ) {
    return <FileWordOutlined className={styles.fileIcon} />;
  }
  if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) {
    return <FileExcelOutlined className={styles.fileIcon} />;
  }
  if (lowerName.endsWith('.ppt') || lowerName.endsWith('.pptx')) {
    return <FilePptOutlined className={styles.fileIcon} />;
  }
  if (
    lowerType === 'visual' ||
    lowerName.endsWith('.mp4') ||
    lowerName.endsWith('.avi') ||
    lowerName.endsWith('.mov')
  ) {
    return <PlayCircleOutlined className={styles.fileIcon} />;
  }
  if (
    lowerName.endsWith('.jpg') ||
    lowerName.endsWith('.jpeg') ||
    lowerName.endsWith('.png') ||
    lowerName.endsWith('.gif')
  ) {
    return <FileImageOutlined className={styles.fileIcon} />;
  }
  if (lowerName.endsWith('.txt')) {
    return <FileTextOutlined className={styles.fileIcon} />;
  }
  return <FileOutlined className={styles.fileIcon} />;
};

// 获取文件类型对应的颜色
const getFileIconColor = (type: string, fileName: string) => {
  const lowerType = type.toLowerCase();
  const lowerName = fileName.toLowerCase();

  if (lowerType === 'pdf' || lowerName.endsWith('.pdf')) {
    return '#ff4d4f'; // 红色
  }
  if (
    lowerType === 'document' ||
    lowerName.endsWith('.doc') ||
    lowerName.endsWith('.docx')
  ) {
    return '#1890ff'; // 蓝色
  }
  if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) {
    return '#52c41a'; // 绿色
  }
  if (lowerName.endsWith('.ppt') || lowerName.endsWith('.pptx')) {
    return '#fa8c16'; // 橙色
  }
  if (
    lowerType === 'visual' ||
    lowerName.endsWith('.mp4') ||
    lowerName.endsWith('.avi') ||
    lowerName.endsWith('.mov')
  ) {
    return '#722ed1'; // 紫色
  }
  if (
    lowerName.endsWith('.jpg') ||
    lowerName.endsWith('.jpeg') ||
    lowerName.endsWith('.png') ||
    lowerName.endsWith('.gif')
  ) {
    return '#eb2f96'; // 粉色
  }
  return 'var(--primary-color)'; // 使用主题色
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// 格式化日期
const formatDate = (timestamp: number | string): string => {
  const date =
    typeof timestamp === 'number' ? new Date(timestamp) : new Date(timestamp);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

// 截取字符串用于显示
const truncateText = (text: string, maxLength: number = 50): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const RelatedResources: React.FC<RelatedResourcesProps> = ({
  userQuery,
  referenceDocuments = [],
  onResourceClick,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [apiResources, setApiResources] = useState<ResourceItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [lastQuery, setLastQuery] = useState<string>(''); // 记录上次查询，避免重复
  const { data: userInfo } = useFetchUserInfo();

  // 防抖查询 - 使用ref来存储timeout
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // 从API搜索相关资源
  const searchResources = useCallback(
    async (query: string) => {
      if (!query || !query.trim()) return;

      // 避免重复搜索相同的查询
      const currentQuery = query.trim();

      // 如果查询内容与上次相同且仍在加载，则跳过
      if (currentQuery === lastQuery && loading) {
        console.log('[RelatedResources] Same query in progress, skipping...');
        return;
      }

      console.log('[RelatedResources] Searching for:', currentQuery);
      setLoading(true);
      setError(null);
      setLastQuery(currentQuery);

      try {
        const response = await searchRelatedFiles({
          content: currentQuery,
          llm_id: DEFAULT_LLM_CONFIG.FILE_KEYWORD_SEARCH, // 使用统一配置的LLM模型
          max_keywords: 5,
          max_results: 10,
          temperature: 0.1,
        });

        if (response.code === 0 && response.data?.matched_files) {
          const resources: ResourceItem[] = response.data.matched_files.map(
            (file) => ({
              id: file.id,
              name: file.name,
              type: file.type,
              size: file.size,
              matched_keyword: file.matched_keyword,
              keyword_weight: file.keyword_weight,
              create_time: file.create_time,
              location: file.location,
              source: 'api' as const,
            }),
          );
          setApiResources(resources);
          console.log(
            '[RelatedResources] Found',
            resources.length,
            'resources',
          );
        } else {
          setError('搜索相关资源失败');
          setApiResources([]);
        }
      } catch (err) {
        console.error('Error searching related files:', err);
        setError('搜索相关资源失败');
        setApiResources([]);
      } finally {
        setLoading(false);
      }
    },
    [lastQuery, loading],
  );

  // 当用户查询改变时，立即清空资源并展示加载占位，然后防抖调用搜索接口
  useEffect(() => {
    if (userQuery && userQuery.trim()) {
      const currentQuery = userQuery.trim();

      // 若相同查询且当前已有结果或正在加载，则不重复触发
      if (currentQuery === lastQuery) {
        return;
      }

      console.log('[RelatedResources] New query trigger:', currentQuery);

      // 立即进入加载状态并清空现有资源
      setLoading(true);
      setApiResources([]);
      setError(null);

      // 清理之前的定时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // 设置新的防抖定时器
      timeoutRef.current = setTimeout(() => {
        searchResources(currentQuery);
      }, 1000); // 1秒防抖延迟
    } else {
      // 如果没有用户查询，清空API搜索结果
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      setApiResources([]);
      setError(null);
      setLastQuery(''); // 清空上次查询记录
    }
  }, [userQuery, searchResources, lastQuery]);

  // 合并并去重所有资源
  const allResources = useMemo(() => {
    if (loading) return [];

    const resources: ResourceItem[] = [];
    const seenIds = new Set<string>();

    // 添加API搜索的资源
    apiResources.forEach((resource) => {
      if (!seenIds.has(resource.id)) {
        resources.push(resource);
        seenIds.add(resource.id);
      }
    });

    // 添加聊天引用的文档资源
    referenceDocuments.forEach((doc) => {
      if (!seenIds.has(doc.doc_id)) {
        resources.push({
          id: doc.doc_id,
          name: doc.doc_name,
          type: 'document',
          size: 0, // 引用文档没有大小信息
          create_time: Date.now(), // 使用当前时间
          source: 'reference' as const,
        });
        seenIds.add(doc.doc_id);
      }
    });

    return resources;
  }, [apiResources, referenceDocuments, loading]);

  const handleResourceClick = (resource: ResourceItem) => {
    onResourceClick?.(resource);
  };

  const handleRefresh = () => {
    if (userQuery && userQuery.trim()) {
      setLastQuery(''); // 清空查询缓存，强制重新搜索
      searchResources(userQuery.trim());
    }
  };

  // 计算不同来源的资源数量
  const resourceCounts = useMemo(() => {
    if (loading) return { apiCount: 0, referenceCount: 0 };

    const apiCount = allResources.filter((r) => r.source === 'api').length;
    const referenceCount = allResources.filter(
      (r) => r.source === 'reference',
    ).length;
    return { apiCount, referenceCount };
  }, [allResources, loading]);

  return (
    <div className={styles.relatedResources}>
      <div className={styles.sectionHeader}>
        <div className={styles.sectionTitle}>相关资源</div>
        <Button
          type="text"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          disabled={loading || !userQuery}
          className={styles.refreshButton}
          size="small"
        />
      </div>

      {/* 当前问答指示信息 */}
      {userQuery && (
        <div className={styles.currentContext}>
          <div className={styles.contextHeader}>
            <InfoCircleOutlined className={styles.contextIcon} />
            <Text className={styles.contextLabel}>当前问答</Text>
          </div>
          <Tooltip title={userQuery}>
            <Text className={styles.contextText}>
              {truncateText(userQuery, 60)}
            </Text>
          </Tooltip>
          <div className={styles.resourceStats}>
            {resourceCounts.apiCount > 0 && (
              <span className={styles.statItem}>
                搜索匹配: {resourceCounts.apiCount}
              </span>
            )}
            {resourceCounts.referenceCount > 0 && (
              <span className={styles.statItem}>
                聊天引用: {resourceCounts.referenceCount}
              </span>
            )}
          </div>
        </div>
      )}

      <div className={styles.resourceList}>
        <LeafSpin
          spinning={loading}
          style={{ marginTop: 100 }}
          size="small"
          text={t('common.loading')}
        >
          {error ? (
            <div className={styles.errorState}>
              <Text type="danger">{error}</Text>
              <Button type="link" onClick={handleRefresh} size="small">
                重试
              </Button>
            </div>
          ) : allResources.length > 0 ? (
            allResources.map((resource) => (
              <Card
                key={resource.id}
                className={styles.resourceCard}
                hoverable
                onClick={() => handleResourceClick(resource)}
              >
                <Space
                  direction="vertical"
                  size="small"
                  style={{ width: '100%', height: '100%' }}
                >
                  <Space align="start" style={{ width: '100%' }}>
                    <div
                      style={{
                        color: getFileIconColor(resource.type, resource.name),
                      }}
                    >
                      {getFileIcon(resource.type, resource.name)}
                    </div>
                    <div className={styles.contentSection}>
                      <Text
                        strong
                        ellipsis={{ tooltip: resource.name }}
                        className={styles.fileName}
                        style={{ width: '200px' }}
                      >
                        {resource.name}
                      </Text>
                      {resource.matched_keyword && (
                        <Text type="secondary" className={styles.keyword}>
                          关键词: {resource.matched_keyword}
                        </Text>
                      )}
                      {resource.source === 'reference' && (
                        <Text type="secondary" className={styles.sourceTag}>
                          聊天引用
                        </Text>
                      )}
                    </div>
                  </Space>
                  {resource.size > 0 && (
                    <Paragraph
                      ellipsis={{ rows: 1 }}
                      className={styles.details}
                    >
                      {formatFileSize(resource.size)} •{' '}
                      {formatDate(resource.create_time)}
                    </Paragraph>
                  )}
                </Space>
              </Card>
            ))
          ) : (
            !loading && (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  userQuery ? '暂无相关资源' : '滚动聊天记录查看相关资源'
                }
                className={styles.emptyState}
              />
            )
          )}
        </LeafSpin>
      </div>
    </div>
  );
};

export default RelatedResources;
