# 默认领域知识库 Hooks 实现总结

## 概述

为 `knowledge-hooks.ts` 文件添加了获取默认领域知识库（default domain kb）的 hook，用于替代 `useGetKnowledgeSearchParams` 在某些场景下的使用。

## 新增功能

### 1. API 层面的修改

#### `web/src/utils/api.ts`

```typescript
// 新增API路径
get_default_domain_kb: `${api_host}/kb/default_domain_kb`,
```

#### `web/src/services/knowledge-service.ts`

```typescript
// 新增服务方法配置
get_default_domain_kb: {
  url: get_default_domain_kb,
  method: 'get',
},
```

### 2. Hooks 层面的修改

#### `web/src/hooks/knowledge-hooks.ts`

新增了两个 hook：

```typescript
// 获取默认领域知识库详细信息的hook
export const useFetchDefaultDomainKnowledgeBase = () => {
  const { data, isFetching: loading } = useQuery<IKnowledge>({
    queryKey: ['fetchDefaultDomainKnowledgeBase'],
    initialData: {} as IKnowledge,
    gcTime: 0,
    queryFn: async () => {
      const { data } = await kbService.get_default_domain_kb()
      return data?.data ?? {}
    }
  })

  return { data, loading }
}

// 获取默认领域知识库ID的便捷hook
export const useDefaultDomainKnowledgeId = (): string => {
  const { data } = useFetchDefaultDomainKnowledgeBase()
  return data?.id || ''
}
```

### 3. 使用示例

#### `web/src/pages/knowledge-dataset/hooks.ts`

添加了使用示例：

```typescript
/**
 * 使用默认领域知识库的导航示例
 * 这个函数展示了如何使用 useDefaultDomainKnowledgeId 替代 useGetKnowledgeSearchParams
 * 当需要使用默认领域知识库而不是从URL参数获取知识库ID时，可以使用这种方式
 */
export const useNavigateToOtherPageWithDefaultKb = () => {
  const navigate = useNavigate()
  // 使用新的hook获取默认领域知识库ID
  const knowledgeId = useDefaultDomainKnowledgeId()

  const linkToUploadPage = useCallback(() => {
    navigate(`/knowledge/dataset/upload?id=${knowledgeId}`)
  }, [navigate, knowledgeId])

  const toChunk = useCallback(
    (id: string) => {
      navigate(`/knowledge/${KnowledgeRouteKey.Dataset}/chunk?id=${knowledgeId}&doc_id=${id}`)
    },
    [navigate, knowledgeId]
  )

  return { linkToUploadPage, toChunk, knowledgeId }
}
```

## 使用场景

### 替代 `useGetKnowledgeSearchParams` 的场景：

1. **默认知识库操作**：当组件需要操作默认领域知识库而不是基于 URL 参数的知识库时
2. **初始化场景**：在没有明确知识库 ID 的情况下，需要使用默认知识库
3. **全局操作**：需要在应用的多个地方使用同一个默认知识库

### 原有方式 vs 新方式：

```typescript
// 原有方式 - 从URL参数获取
const { knowledgeId } = useGetKnowledgeSearchParams()

// 新方式 - 获取默认领域知识库ID
const knowledgeId = useDefaultDomainKnowledgeId()

// 新方式 - 获取完整的默认领域知识库信息
const { data: defaultKb, loading } = useFetchDefaultDomainKnowledgeBase()
```

## 后端支持

这些 hook 依赖于后端已实现的 `/default_domain_kb` API 端点，该端点：

1. 如果默认领域知识库存在，直接返回其详细信息
2. 如果不存在，会根据配置文件自动创建并返回

## 优势

1. **自动化**：无需手动管理默认知识库的创建和获取
2. **便捷性**：提供简单的 API 获取默认知识库 ID
3. **一致性**：确保整个应用使用同一个默认领域知识库
4. **缓存优化**：使用 React Query 进行数据缓存，提升性能
5. **类型安全**：完整的 TypeScript 类型支持
