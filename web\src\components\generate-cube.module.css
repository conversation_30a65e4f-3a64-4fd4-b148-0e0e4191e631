.container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.box {
  position: relative;
  width: 3em;
  height: 2em;
  transform-style: preserve-3d;
  transform: rotateX(60deg) rotateZ(45deg);
}

.cube {
  position: absolute;
  width: 1em;
  height: 1em;
  animation: move 3s ease-in-out infinite;
  transform-style: preserve-3d;
  box-shadow: 5em 5em 0.3em 0.1em #dbdbdb;
}

.cube::before,
.cube::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.cube::before {
  transform-origin: 100% 100%;
  transform: rotateY(-90deg);
}

.cube::after {
  transform-origin: 0% 100%;
  transform: rotateX(90deg);
}

.cube:nth-of-type(1) {
  animation-delay: -11.25s;
}

.cube:nth-of-type(2) {
  animation-delay: -10.5s;
}

.cube:nth-of-type(3) {
  animation-delay: -9.75s;
}

.cube:nth-of-type(4) {
  animation-delay: -9s;
}

.box:hover .cube {
  animation-play-state: paused;
}

.box:active .cube {
  animation-play-state: running;
}

@keyframes move {
  0%,
  87.5%,
  100% {
    transform: translate(1em, 0em);
  }
  12.5% {
    transform: translate(2em, 0em);
  }
  25% {
    transform: translate(2em, 1em);
  }
  37.5%,
  50% {
    transform: translate(1em, 1em);
  }
  62.5% {
    transform: translate(0em, 1em);
  }
  75% {
    transform: translate(0em, 0em);
  }
}
