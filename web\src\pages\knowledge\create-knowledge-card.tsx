import { DatabaseOutlined, PlusOutlined } from '@ant-design/icons';
import { Card, Flex, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './knowledge-card/index.less';

interface IProps {
  onClick: () => void;
}

const CreateKnowledgeCard = ({ onClick }: IProps) => {
  const { t } = useTranslation('translation', { keyPrefix: 'knowledgeList' });

  return (
    <Card className={styles.card} onClick={onClick}>
      <Flex
        className={styles.container}
        vertical
        justify="center"
        align="center"
        gap="middle"
        style={{
          cursor: 'pointer',
          height: '100%',
        }}
      >
        <Flex
          justify="center"
          align="center"
          style={{
            width: 64,
            height: 64,
            borderRadius: '50%',
            backgroundColor: 'var(--background-tertiary)',
            color: 'var(--primary-color)',
          }}
        >
          <PlusOutlined style={{ fontSize: 28 }} />
        </Flex>

        <Flex vertical align="center" gap="small">
          <Typography.Title level={4} style={{ margin: 0 }}>
            {t('createKnowledgeBase')}
          </Typography.Title>
          <Typography.Text type="secondary" style={{ textAlign: 'center' }}>
            {t('createKnowledgeDescription', '创建知识库，组织文档')}
          </Typography.Text>
        </Flex>

        <Flex
          align="center"
          gap="small"
          style={{ color: 'var(--text-tertiary)' }}
        >
          <DatabaseOutlined />
          <Typography.Text type="secondary">
            {t('getStarted', '点击开始')}
          </Typography.Text>
        </Flex>
      </Flex>
    </Card>
  );
};

export default CreateKnowledgeCard;
