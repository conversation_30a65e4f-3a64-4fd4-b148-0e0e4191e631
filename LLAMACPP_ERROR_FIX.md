# Llama.cpp "Model not authorized" 错误修复指南

## 问题分析

根据服务端日志，错误信息是：
```
LookupError: Model(llama-cpp-embedding) not authorized
```

**根本原因：** 用户还没有在前端正确配置 Llama.cpp 模型，导致数据库中没有对应的租户模型配置记录。

## 解决方案

### 🎯 方案1：前端配置（推荐）

这是**正确且推荐**的解决方案：

1. **确保 RAGFlow 服务正在运行**
2. **打开浏览器，访问 RAGFlow 管理界面**
3. **登录后，进入 "用户设置" -> "模型管理"**
4. **在 "待添加模型" 列表中找到 "Llama.cpp"**
   - 如果没有看到，请检查前端代码是否正确部署
   - 可能需要清理浏览器缓存或重启服务
5. **点击 "添加模型" 按钮**
6. **在弹出的配置对话框中填写：**
   ```
   模型类型: embedding
   模型名称: llama-cpp-embedding
   API Base: http://192.168.3.124:8080
   API Key: dummy
   ```
7. **点击 "确定" 保存配置**
8. **等待配置保存成功的提示**
9. **重新尝试上传文档**

### 🔧 方案2：代码修复（已完成）

我已经修改了后端代码，为 Llama.cpp 添加了自动配置支持：

**修改文件：** `api/db/services/llm_service.py`

**修改内容：** 在第117-125行，将 "Llama.cpp" 添加到自动配置的模型列表中，这样即使用户没有手动配置，系统也会自动提供默认配置。

**重启服务：** 修改代码后需要重启 RAGFlow 服务才能生效。

### 🗄️ 方案3：数据库初始化（高级用户）

如果前端配置有问题，可以手动初始化数据库：

1. **找到 RAGFlow 容器：**
   ```bash
   docker ps | grep ragflow
   ```

2. **将初始化脚本复制到容器：**
   ```bash
   docker cp init_llamacpp_db.py <container_name>:/ragflow/
   ```

3. **在容器内运行脚本：**
   ```bash
   docker exec -it <container_name> python /ragflow/init_llamacpp_db.py
   ```

4. **重启服务：**
   ```bash
   docker restart <container_name>
   ```

## 验证修复

修复后，可以通过以下方式验证：

1. **检查前端模型列表：**
   - 进入 "用户设置" -> "模型管理"
   - 确认 Llama.cpp 出现在已添加模型列表中

2. **测试文档上传：**
   - 创建或编辑知识库
   - 选择 embedding 模型为 "<EMAIL>"
   - 上传测试文档
   - 检查服务端日志是否还有错误

3. **检查 API 连接：**
   ```bash
   curl -X POST http://192.168.3.124:8080/embedding \
     -H "Content-Type: application/json" \
     -d '{"content": "测试连接"}'
   ```

## 常见问题

### Q1: 前端没有显示 Llama.cpp 选项
**A:** 
- 检查前端代码是否正确部署
- 清理浏览器缓存
- 重启 RAGFlow 服务
- 检查 `conf/llm_factories.json` 是否包含 Llama.cpp 配置

### Q2: 配置保存后仍然报错
**A:**
- 检查 API Base URL 是否正确
- 确认 192.168.3.124:8080 服务正在运行
- 检查网络连通性
- 查看详细的服务端日志

### Q3: 模型配置成功但无法连接
**A:**
- 确认 llama.cpp 服务正在运行
- 检查防火墙设置
- 验证 API 端点是否正确响应
- 检查 RAGFlow 容器是否能访问外部网络

## 技术细节

### 错误发生位置
```python
# 文件: api/db/services/llm_service.py
# 行数: 226
model_config = TenantLLMService.get_model_config(
    tenant_id, llm_type, llm_name)
```

### 修复原理
通过在 `get_model_config` 方法中添加 Llama.cpp 的自动配置逻辑，当用户没有手动配置时，系统会自动提供默认配置，避免 "not authorized" 错误。

### 数据流程
1. 用户上传文档 → 
2. 系统查找 embedding 模型配置 → 
3. 在 TenantLLM 表中查找配置 → 
4. 如果没找到，检查是否为内置模型 → 
5. 为 Llama.cpp 提供默认配置 → 
6. 创建 LlamaCppEmbed 实例 → 
7. 调用 embedding API

## 总结

推荐使用**方案1（前端配置）**，这是最标准和用户友好的方式。如果遇到问题，可以结合**方案2（代码修复）**来提供后备支持。

修复完成后，用户就可以正常使用 Llama.cpp embedding 模型进行文档向量化了。
