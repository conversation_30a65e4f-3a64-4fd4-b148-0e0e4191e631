.container {
  display: flex;
  height: 100%;
  width: 100%;

  .sidebarWrapper {
    transition: width 0.3s ease;
    overflow: hidden;
  }

  .hiddenSidebar {
    width: 0 !important;
    min-width: 0 !important;
    max-width: 0 !important;
  }

  .contentWrapper {
    flex: 1;
    overflow-x: auto;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    // padding: 16px 20px 28px 40px;
    display: flex;
    flex-direction: column;
  }

  .content {
    background-color: rgba(255, 255, 255, 0.1);
    // margin-top: 16px;
    flex: 1;
  }
}

.backButtonContainer {
  margin-bottom: 16px;
}

.backButton {
  padding: 0;
  display: flex;
  align-items: center;
  color: var(--text-secondary, #64748b);
  font-size: 14px;
  transition: all 0.2s ease;

  &:hover {
    color: var(--primary-color, #3b82f6);
  }

  &:focus {
    color: var(--primary-color, #3b82f6);
  }

  .anticon {
    margin-right: 8px;
  }
}
