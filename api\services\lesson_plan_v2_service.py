from __future__ import annotations

"""Lesson Plan V2 business logic service.

This module encapsulates all the processing previously implemented directly inside
api/apps/agent_app.py routes.  Each public static method here performs one
specific unit-of-work so that the Flask view functions can stay minimal and
purely concerned with HTTP specifics (URL mapping & authentication).

Single-responsibility is kept by:
* _request_to_param – validation & DTO construction only.
* _handle_file_upload – file IO & async embedding only.
* build_start_response – end-to-end start flow with SSE streaming.
* build_chat_response – iterative chat flow.
* cancel_cleanup – revoke scheduled cleanup job.

The service does NOT depend on Flask decorators or global ``request`` /
``current_user`` objects.  Instead they are passed in from the caller so the
functions are pure and unit-testable.
"""

import json
import logging
import time
import traceback
from typing import Dict, Generator, Optional, Tuple

from flask import Request, Response

from api.utils.api_utils import (
    get_data_error_result,
    get_data_from_request,
    get_json_result,
    server_error_response,
)
from api.utils.tenant_utils import get_tenant_with_fallback
from agent.component.lesson_plan import LessonPlan, LessonPlanParam
from agent.component.template_retrieval import TemplateR<PERSON>rieval, TemplateRetrievalParam
from api.db.services.lesson_plan_service import LessonPlanService
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.user_service import TenantService, UserTenantService
from api.db.services.file_service import FileService
from api.db.services.document_service import DocumentService, _get_or_create_kb_for_user
from api.db.services.file2document_service import File2DocumentService
from api.db.services.llm_service import TenantLLMService, LLMBundle
from api.db import LLMType
from agent.canvas import Canvas

# --------------------------------------------------------------------------------------
# Helper / utility types & functions
# --------------------------------------------------------------------------------------


class SSEStreamBuffer:
    """Control frequency when sending SSE chunks (pure incremental)."""

    def __init__(self, send_interval: float = 1.0) -> None:
        import time  # local import to avoid issues on non-unix OS during build

        self.send_interval = send_interval
        self.last_send_time = 0.0
        self.chunk_id = 0
        self.pending_chunks: list[str] = []

    def add_chunk(self, chunk: str, last_event_id: int = -1):
        import time

        if self.chunk_id <= last_event_id:
            self.chunk_id += 1
            return None

        self.pending_chunks.append(chunk)
        current_time = time.time()
        if current_time - self.last_send_time >= self.send_interval and self.pending_chunks:
            content = "".join(self.pending_chunks)
            self.pending_chunks.clear()
            self.last_send_time = current_time
            event = {
                "id": self.chunk_id,
                "event": "stream",
                "data": json.dumps({"chunk": content}),
            }
            self.chunk_id += 1
            return event
        return None

    def flush(self):
        if not self.pending_chunks:
            return None
        content = "".join(self.pending_chunks)
        self.pending_chunks.clear()
        event = {
            "id": self.chunk_id,
            "event": "stream",
            "data": json.dumps({"chunk": content}),
        }
        self.chunk_id += 1
        return event


class MinimalCanvas(Canvas):
    """Lightweight canvas used by component execution without full agent flow."""

    def __init__(self, tenant_id: str, user_id: str):
        minimal_dsl = json.dumps(
            {
                "components": {
                    "begin_node": {
                        "obj": {"component_name": "Begin", "params": {}},
                        "downstream": ["lesson_plan_node"],
                        "upstream": [],
                        "parent_id": "",
                    },
                    "lesson_plan_node": {
                        "obj": {"component_name": "LessonPlan", "params": {}},
                        "downstream": [],
                        "upstream": ["begin_node"],
                        "parent_id": "",
                    },
                },
                "history": [],
                "messages": [],
                "reference": [],
                "path": [],
                "answer": [],
            }
        )
        super().__init__(dsl=minimal_dsl, tenant_id=tenant_id)
        self._user_id = user_id
        self._tenant_id = tenant_id

    # ------------------------------------------------------------------
    # Canvas overrides
    # ------------------------------------------------------------------
    def load(self):
        self.components = self.dsl["components"]
        self.path = self.dsl.get("path", [])
        self.history = self.dsl.get("history", [])
        self.messages = self.dsl.get("messages", [])
        self.answer = self.dsl.get("answer", [])
        self.reference = self.dsl.get("reference", [])
        self._embed_id = self.dsl.get("embed_id", "")

    def get_user_id(self):  # type: ignore[override]
        return self._user_id

    def get_tenant_id(self):  # type: ignore[override]
        return self._tenant_id


# --------------------------------------------------------------------------------------
# Core service
# --------------------------------------------------------------------------------------

class LessonPlanV2Service:
    """Business logic of lesson-plan-v2 API endpoints extracted from agent_app."""

    # ------------------------------------------------------------------
    # Public API (called from Flask routes)
    # ------------------------------------------------------------------

    @staticmethod
    def build_start_response(req: Request, user) -> Response:
        """Handle POST /lesson-plan-v2/start and build an SSE streaming response."""
        try:
            data, files = LessonPlanV2Service._extract_request_data(req)
            logging.info(
                "[LessonPlanV2] Received data: %s, files: %s",
                list(data.keys()),
                list(files.keys()) if files else [],
            )

            # Validate minimal required fields early
            required_fields = ["course_name", "class_type",
                               "class_hours", "target_audience"]
            for f in required_fields:
                if not data.get(f):
                    return get_json_result(data={}, code=400, message=f"Missing field: {f}")

            # Build LessonPlanParam
            param = LessonPlanParam()
            param.course_name = data.get("course_name", "").strip()
            try:
                param.class_hours = int(data.get("class_hours", 40))
            except (TypeError, ValueError):
                return get_json_result(data={}, code=400, message="class_hours must be an integer")
            param.target_audience = data.get("target_audience", "").strip()
            param.class_type = data.get("class_type", "").strip()
            param.llm_id = data.get("llm_id", "deepseek-r1:32b@Ollama")

            # Handle content source (manual or file)
            course_content_type = data.get("course_content_type", "manual")
            course_file = files.get("course_file") if files else None
            upload_ctx = None  # details about uploaded file / kb for later cleanup
            if course_content_type == "manual" or not course_file:
                param.course_content = data.get("course_content", "")
            else:
                ok, param.material_file_content, upload_ctx = LessonPlanV2Service._handle_file_upload(
                    user.id, course_file
                )
                if not ok:
                    return get_json_result(data={}, code=500, message="文件上传解析失败")

            # Validate param
            try:
                param.check()
            except ValueError as ve:
                return get_json_result(data={}, code=400, message=str(ve))

            # Tenant & Canvas initialisation
            success, tenant = get_tenant_with_fallback(user.id)
            if not success or not tenant:
                return get_data_error_result(message="Unable to determine tenant for user")
            tenant_id = tenant.id
            canvas = MinimalCanvas(tenant_id=tenant_id, user_id=user.id)

            # Persist a session record
            session_dsl = param.as_dict()
            session_dsl["tenant_id"] = tenant_id
            session_id = LessonPlanService.create_session(
                user.id, dsl=session_dsl)

            if upload_ctx:
                # store cleanup job id into session
                LessonPlanService.add_job(
                    session_id, "cleanup", upload_ctx.cleanup_job_id)

            # Template retrieval component (optional)
            tpl_text = LessonPlanV2Service._retrieve_template_text(
                canvas, param.llm_id, user.id)
            param.template_text = tpl_text

            # Build streaming generator
            stream_gen = LessonPlanV2Service._generate_lesson_plan_stream(
                canvas,
                param,
                upload_ctx,
                session_id,
                req.headers.get("Last-Event-ID", ""),
            )

            resp = Response(stream_gen, mimetype="text/event-stream")
            resp.headers["Cache-Control"] = "no-cache"
            resp.headers["X-Accel-Buffering"] = "no"
            return resp

        except Exception as e:
            logging.error("[LessonPlanV2] Fatal error: %s", e, exc_info=True)
            return server_error_response(e)

    @staticmethod
    def build_chat_response(session_id: str, req: Request, user) -> Response:
        """Handle PUT /lesson-plan-v2/<session_id>/chat."""
        body = req.json or {}
        satisfied = body.get("satisfied")
        feedback = body.get("feedback", "")

        from api.db.db_models import LessonPlanSession, LessonPlanDoc

        # Fetch session
        try:
            sess = LessonPlanSession.get(LessonPlanSession.id == session_id)
        except LessonPlanSession.DoesNotExist:
            return get_json_result(data={}, code=404, message="Session not found")

        # If user satisfied just mark done
        if satisfied:
            LessonPlanService.update_session(session_id, status="done")
            LessonPlanDoc.update(satisfied=True).where(
                LessonPlanDoc.session_id == session_id
            ).order_by(LessonPlanDoc.iteration.desc()).execute()
            return get_json_result(data=True)

        # not satisfied -> iterate
        iteration = sess.iteration_count + 1
        LessonPlanService.update_session(session_id, iteration_count=iteration)

        # Rebuild parameters
        param_dict = sess.dsl
        param = LessonPlanParam()
        param.update(param_dict, allow_redundant=True)
        param.feedback = feedback

        # tenant fallback
        tenant_id = sess.dsl.get("tenant_id", "")
        if not tenant_id:
            success, tenant = get_tenant_with_fallback(sess.user_id)
            if not success or not tenant:
                success, tenant = get_tenant_with_fallback(user.id)
            if not success or not tenant:
                return get_json_result(data={}, code=500, message="Unable to determine tenant for user")
            tenant_id = tenant.id

        canvas = MinimalCanvas(tenant_id=tenant_id, user_id=sess.user_id)
        component = LessonPlan(canvas, "lesson_plan_v2", param)

        last_event_id_header = req.headers.get("Last-Event-ID", "")
        try:
            last_event_id_chat = int(
                last_event_id_header) if last_event_id_header else -1
        except ValueError:
            last_event_id_chat = -1

        def sse_stream() -> Generator[str, None, None]:
            yield "id:-10\nevent:generating\ndata:" + json.dumps({"msg": "教案重新生成中..."}) + "\n\n"
            try:
                content = ""
                sse_buffer = SSEStreamBuffer(send_interval=1.0)
                for chunk in component.stream_run(history=[]):
                    if chunk.strip():
                        content += chunk
                        event_data = sse_buffer.add_chunk(
                            chunk, last_event_id_chat)
                        if event_data:
                            yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"
                event_data = sse_buffer.flush()
                if event_data:
                    yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"

                # persist doc
                LessonPlanService.create_doc(
                    session_id, content, iteration=iteration, satisfied=False)
                yield "id:-2\nevent:done\ndata:{}\n\n"
                yield "id:-3\nevent:ask_feedback\ndata:" + json.dumps({"msg": "是否满意？"}) + "\n\n"
            except Exception as e:
                traceback.print_exc()
                yield "event:error\ndata:" + json.dumps({"detail": str(e)}) + "\n\n"

        resp = Response(sse_stream(), mimetype="text/event-stream")
        resp.headers["Cache-Control"] = "no-cache"
        resp.headers["X-Accel-Buffering"] = "no"
        return resp

    @staticmethod
    def cancel_cleanup(session_id: str):
        """Handle POST /lesson-plan-v2/<session_id>/cancel_cleanup."""
        from api.db.services.lesson_plan_service import LessonPlanService
        from api.db.db_models import LessonPlanSession

        try:
            sess = LessonPlanSession.get(LessonPlanSession.id == session_id)
            job_id = sess.jobs.get("cleanup") if sess else None
            if job_id:
                from api.celery_app import celery_app as _celery_app
                try:
                    _celery_app.control.revoke(job_id, terminate=True)
                except Exception:
                    logging.warning("Failed to revoke cleanup task")
            LessonPlanService.update_session(session_id, status="saved")
            return get_json_result(data=True)
        except LessonPlanSession.DoesNotExist:
            return get_json_result(data=False, code=404, message="Session not found")
        except Exception as e:
            logging.error("cancel cleanup error: %s", e, exc_info=True)
            return server_error_response(e)

    # ------------------------------------------------------------------
    # Internals helpers
    # ------------------------------------------------------------------

    class _UploadContext:
        def __init__(self, kb_id: str, doc_id: str, cleanup_job_id: Optional[str]):
            self.kb_id = kb_id
            self.doc_id = doc_id
            self.cleanup_job_id = cleanup_job_id

    @staticmethod
    def _extract_request_data(req: Request) -> Tuple[Dict, Dict]:
        if req.content_type and "application/json" in (req.content_type or ""):
            data = get_data_from_request(req)
            files = {}
        else:
            data = req.form.to_dict()
            files = req.files
        return data, files

    @staticmethod
    def _handle_file_upload(user_id: str, course_file) -> Tuple[bool, str, Optional['_UploadContext']]:
        """Upload file to KB & kick off async embedding. Returns (ok, file_content, ctx)"""
        try:
            kb_id = _get_or_create_kb_for_user(user_id)
            e, kb = KnowledgebaseService.get_by_id(kb_id)
            if not e:
                raise LookupError("Can't find this knowledgebase!")

            err, files = FileService.upload_document(
                kb, [course_file], user_id)
            if err:
                raise Exception("\n".join(err))
            if not files:
                raise Exception("No files uploaded successfully")
            doc_id = files[0][0]["id"]

            # Read local content for generation regardless of embedding
            course_file.seek(0)
            try:
                material_content = course_file.read().decode("utf-8", errors="ignore")
            except Exception:
                course_file.seek(0)
                material_content = course_file.read().decode("gbk", errors="ignore")

            # Start async embedding
            try:
                _, tenant = TenantService.get_by_id(kb.tenant_id)
                doc_dict = files[0][0]
                doc_dict["tenant_id"] = kb.tenant_id
                info = {"run": 1, "progress": 0, "progress_msg": "",
                        "chunk_num": 0, "token_num": 0}
                DocumentService.update_by_id(doc_id, info)
                bucket, name = File2DocumentService.get_storage_address(
                    doc_id=doc_id)
                from api.db.services.task_service import queue_tasks
                queue_tasks(doc_dict, bucket, name, 0)
                logging.info(
                    "Started async embedding task for doc_id: %s", doc_id)
            except Exception as e:
                logging.warning("Failed to start async embedding: %s", e)

            # Schedule cleanup 2h later
            cleanup_job_id = None
            try:
                from api.tasks.lesson_plan_tasks import cleanup_kb_doc
                cleanup_async = cleanup_kb_doc.apply_async(
                    args=[None, kb_id, [doc_id]], countdown=7200)
                cleanup_job_id = cleanup_async.id
                logging.info("Scheduled cleanup task: %s", cleanup_job_id)
            except Exception as e:
                logging.warning("Failed to schedule cleanup task: %s", e)

            return True, material_content, LessonPlanV2Service._UploadContext(kb_id, doc_id, cleanup_job_id)
        except Exception as e:
            logging.error("File upload failed: %s", e, exc_info=True)
            # Best effort cleanup
            try:
                if "doc_id" in locals():
                    doc_id_local = locals()["doc_id"]
                    e, doc_obj = DocumentService.get_by_id(doc_id_local)
                    if e and doc_obj:
                        doc_tenant_id = DocumentService.get_tenant_id(
                            doc_id_local)
                        DocumentService.remove_document(doc_obj, doc_tenant_id)
                        logging.info(
                            "Cleaned up failed document: %s", doc_id_local)
            except Exception as cleanup_err:
                logging.error("Failed to cleanup document: %s", cleanup_err)
            return False, "", None

    @staticmethod
    def _retrieve_template_text(canvas: Canvas, llm_id: str, user_id: str) -> str:
        tpl_text = ""
        try:
            tpl_param = TemplateRetrievalParam()
            tpl_param.top_n = 3
            tpl_kb_name = "tenant-lesson-plan-template"
            kbs = KnowledgebaseService.get_kb_by_name(tpl_kb_name, user_id)
            if kbs:
                tpl_param.tpl_kb_id = kbs[0]["id"]
                tpl_retrieval = TemplateRetrieval(
                    canvas, "tpl_retrieval", tpl_param)
                tpl_df = tpl_retrieval._run(history=[])
                if not tpl_df.empty and "content" in tpl_df.columns:
                    tpl_text = "\n".join(tpl_df["content"].tolist())
        except Exception as e:
            logging.warning("Template retrieval failed: %s", e)
        return tpl_text

    # ------------------------------------------------------------------
    # Generation streaming helper
    # ------------------------------------------------------------------

    @staticmethod
    def _generate_lesson_plan_stream(
        canvas: Canvas,
        param: LessonPlanParam,
        upload_ctx: Optional['_UploadContext'],
        session_id: str,
        last_event_header: str,
    ) -> Generator[str, None, None]:
        lesson_plan_component = LessonPlan(canvas, "lesson_plan_v2", param)
        # parse last event
        try:
            last_event_id = int(last_event_header) if last_event_header else -1
        except ValueError:
            last_event_id = -1

        # Embedding wait generator if uploaded file present
        embedding_generator = None
        if upload_ctx is not None:
            embedding_generator = LessonPlanV2Service._wait_for_embedding(
                upload_ctx.kb_id)

        def _stream():
            yield f"event:init\ndata:{json.dumps({'session_id': session_id})}\n\n"
            if embedding_generator is not None:
                for ev in embedding_generator:
                    yield ev

            # generating main content
            yield f"event:generating\ndata:{json.dumps({'msg': '教案正在生成中...'})}\n\n"
            generated_text = ""
            sse_buffer = SSEStreamBuffer(send_interval=1.0)
            for chunk in lesson_plan_component.stream_run(history=[]):
                if chunk.strip():
                    generated_text += chunk
                    event_data = sse_buffer.add_chunk(chunk, last_event_id)
                    if event_data:
                        yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"
            event_data = sse_buffer.flush()
            if event_data:
                yield f"id:{event_data['id']}\nevent:{event_data['event']}\ndata:{event_data['data']}\n\n"

            # Resource search (sync)
            from agent.component.resource_search import ResourceSearch, ResourceSearchParam
            res_param = ResourceSearchParam()
            res_param.llm_id = param.llm_id
            res_param.max_keywords = 5
            res_param.max_results = 10
            resource_search = ResourceSearch(
                canvas, "resource_search", res_param)
            resource_search._param.debug_inputs = [{"value": generated_text}]
            recommendations = []
            try:
                res_df = resource_search._run(history=[])
                if not res_df.empty and "content" in res_df.columns:
                    recommendations = json.loads(res_df["content"].iloc[0])
            except Exception as e:
                logging.warning("Resource search failed: %s", e)

            LessonPlanService.create_doc(
                session_id, generated_text, iteration=0, satisfied=False, source_files=recommendations)
            yield f"id:-1\nevent:recommendations\ndata:{json.dumps({'resources': recommendations})}\n\n"
            yield "id:-2\nevent:done\ndata:{}\n\n"
            yield "id:-3\nevent:ask_feedback\ndata:" + json.dumps({"msg": "是否满意？"}) + "\n\n"

        return _stream()

    @staticmethod
    def _wait_for_embedding(kb_id: str):
        """Yield SSE events while waiting for file embedding to finish."""
        max_wait_time = 300  # seconds
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            try:
                done, _ = KnowledgebaseService.is_parsed_done(kb_id)
                if done:
                    break
                yield "event:waiting_embedding\ndata:" + json.dumps({"msg": "文件向量化中..."}) + "\n\n"
                time.sleep(2)
            except Exception as e:
                logging.warning("Error checking embedding status: %s", e)
                yield "event:waiting_embedding\ndata:" + json.dumps({"msg": "文件向量化中..."}) + "\n\n"
                time.sleep(2)
        else:
            logging.warning("Embedding wait timeout for kb_id: %s", kb_id)
