import HightLightMarkdown from '@/components/highlight-markdown';
import { getAuthorization } from '@/utils/authorization-util';
import { exportMarkdownToDocx } from '@/utils/markdown-export';
import { markdownToExcelV2 } from '@/utils/markdown-to-excel-utils';
import {
  ArrowLeftOutlined,
  BuildOutlined,
  FileExcelOutlined,
  FileWordOutlined,
} from '@ant-design/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button, Card, Col, Row, Typography, message } from 'antd';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { history } from 'umi';
import { z } from 'zod';
import SmartExamForm from '../form/smart-exam-form';
import { INextOperatorForm } from '../interface';
import styles from './index.less';

const { Title, Paragraph } = Typography;

// Define the form schema
const formSchema = z.object({
  exam_name: z.string().min(1, { message: '名称不能为空' }),
  knowledge_points: z.string().min(1, { message: '知识点不能为空' }),
  question_count: z.number().min(0).max(100).default(10),
  single_choice_count: z.number().min(0).max(50).default(0),
  multiple_choice_count: z.number().min(0).max(50).default(0),
  fill_blank_count: z.number().min(0).max(50).default(0),
  true_false_count: z.number().min(0).max(50).default(0),
  short_answer_count: z.number().min(0).max(50).default(0),
  ordering_count: z.number().min(0).max(50).default(0),
  position_level: z.array(z.string()).optional().default([]),
  difficulty: z.enum(['low', 'medium', 'high']).default('medium'),
  question_bank_file: z.any().optional(),
  llm_id: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

const SmartExamPage: React.FC = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [generatedExam, setGeneratedExam] = useState<string | null>(null);
  const [markdownData, setMarkdownData] = useState<string | null>(null);
  const [fileList, setFileList] = useState<any[]>([]);
  const thinkModeRef = React.useRef(false);

  // 状态机定义
  type Status =
    | 'idle'
    | 'uploading'
    | 'waiting_embedding'
    | 'retrieving_template'
    | 'generating'
    | 'streaming'
    | 'done'
    | 'error';

  const [status, setStatus] = useState<Status>('idle');

  // Initialize form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      exam_name: '',
      knowledge_points: '',
      question_count: 10,
      single_choice_count: 0,
      multiple_choice_count: 0,
      fill_blank_count: 0,
      true_false_count: 0,
      short_answer_count: 0,
      ordering_count: 0,
      position_level: [],
      difficulty: 'medium',
      question_bank_file: undefined,
      llm_id: 'deepseek-r1:32b@Ollama',
    },
  });

  const handleGoBack = () => {
    history.push('/workbench');
  };

  const uploadProps = {
    fileList,
    beforeUpload: (file: any) => {
      setFileList([file]);
      form.setValue('question_bank_file', file);
      return false; // prevent auto-upload
    },
    onRemove: () => {
      setFileList([]);
      form.setValue('question_bank_file', undefined);
    },
  };

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    setGeneratedExam('');
    setStatus('uploading');

    try {
      console.log('Form data:', data);

      const formData = new FormData();
      formData.append('exam_name', data.exam_name);
      formData.append('knowledge_points', data.knowledge_points);
      formData.append('difficulty', data.difficulty);
      formData.append('llm_id', data.llm_id || 'deepseek-r1:32b@Ollama');

      // Question generation method
      formData.append(
        'question_method',
        data.question_count > 0 ? 'random' : 'by_type',
      );

      if (data.question_count > 0) {
        formData.append('question_count', data.question_count.toString());
      } else {
        formData.append(
          'single_choice_count',
          data.single_choice_count.toString(),
        );
        formData.append(
          'multiple_choice_count',
          data.multiple_choice_count.toString(),
        );
        formData.append('fill_blank_count', data.fill_blank_count.toString());
        formData.append('true_false_count', data.true_false_count.toString());
        formData.append(
          'short_answer_count',
          data.short_answer_count.toString(),
        );
        formData.append('ordering_count', data.ordering_count.toString());
      }

      if (data.question_bank_file) {
        formData.append('question_bank_file', data.question_bank_file);
      }

      const resp = await fetch('/v1/agent/smart-exam/start', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          Authorization: getAuthorization(),
        },
      });

      if (!resp.ok) {
        throw new Error(`HTTP ${resp.status}`);
      }

      // Handle SSE streaming response
      const reader = resp.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder('utf-8');
      let buffer = '';
      let accumContent = '';

      const processBuffer = () => {
        let idx;
        while ((idx = buffer.indexOf('\n\n')) >= 0) {
          const rawEvent = buffer.slice(0, idx + 2);
          buffer = buffer.slice(idx + 2);

          let eventName = '';
          let dataStr = '';

          rawEvent
            .trim()
            .split('\n')
            .forEach((line) => {
              if (line.startsWith('event:')) {
                eventName = line.slice(6).trim();
              } else if (line.startsWith('data:')) {
                dataStr += line.slice(5).trim();
              }
            });

          if (!eventName || !dataStr) return;

          try {
            const data = JSON.parse(dataStr);
            console.log(`[SSE] Event: ${eventName}`, data);

            switch (eventName) {
              case 'init':
                console.log('Smart exam generation initialized');
                break;
              case 'waiting_embedding':
                console.log('Waiting for file embedding...');
                setStatus('waiting_embedding');
                break;
              case 'retrieving_template':
                console.log('Retrieving exam templates...');
                setStatus('retrieving_template');
                break;
              case 'generating':
                console.log('Generating smart exam...');
                setStatus('generating');
                break;
              case 'stream':
                setStatus('streaming');
                let chunkText = data.chunk || '';

                // --- Filter <think> blocks streaming ---
                if (thinkModeRef.current) {
                  const closeIdx = chunkText.indexOf('</think>');
                  if (closeIdx !== -1) {
                    // End of think block found
                    chunkText = chunkText.slice(closeIdx + 8);
                    thinkModeRef.current = false;
                  } else {
                    // Still inside think block, ignore whole chunk
                    chunkText = '';
                  }
                }

                // Process any new <think> tags in remaining chunkText
                const startIdx = chunkText.indexOf('<think>');
                if (startIdx !== -1) {
                  const beforeThink = chunkText.slice(0, startIdx);
                  const afterStart = chunkText.slice(startIdx + 7);
                  const endIdx = afterStart.indexOf('</think>');
                  if (endIdx !== -1) {
                    // start and end in same chunk
                    const afterThink = afterStart.slice(endIdx + 8);
                    chunkText = beforeThink + afterThink;
                  } else {
                    // start found but no end, enter think mode
                    thinkModeRef.current = true;
                    chunkText = beforeThink;
                  }
                }

                accumContent += chunkText;
                const visible =
                  accumContent.trim() === ''
                    ? '试题正在生成中...'
                    : accumContent;
                setGeneratedExam(visible || '试题正在生成中...');
                setMarkdownData(visible);
                break;
              case 'done':
                setLoading(false);
                setStatus('done');
                message.success('智能组卷生成成功！');
                break;
              case 'error':
                message.error(data.detail || '服务端错误');
                setLoading(false);
                setStatus('error');
                return;
            }
          } catch (err) {
            console.error(
              'Failed to parse SSE event',
              err,
              'Raw data:',
              dataStr,
            );
          }
        }
      };

      // Stream reading loop
      try {
        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            console.log('[SSE] Stream ended');
            setLoading(false);
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;
          processBuffer();
        }
      } catch (err) {
        console.error('SSE stream reading error', err);
        message.warning('连接中断，请重试');
        setLoading(false);
        setStatus('error');
      }
    } catch (error: any) {
      console.error('Error generating smart exam:', error);
      message.error(error.message || '生成试卷失败，请重试');
      setLoading(false);
      setStatus('error');
    }
  };

  // Cast the form to match INextOperatorForm interface expected by SmartExamForm
  const smartExamForm = form as unknown as INextOperatorForm['form'];

  return (
    <div className={`${styles.smartExamContainer} main-content-container`}>
      <div className={styles.pageHeader}>
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          className={styles.backButton}
        >
          返回
        </Button>
        <div className={styles.pageTitle}>
          <h2 className="main-page-title">智能组卷</h2>
        </div>
        <Paragraph className={styles.pageDescription}>
          基于试题库和知识库，智能组合生成试卷
        </Paragraph>
      </div>

      <Row gutter={24}>
        <Col span={10}>
          <Card className={styles.formCard} title="组卷配置">
            <SmartExamForm form={smartExamForm} uploadProps={uploadProps} />

            <div className={styles.formActions}>
              <Button
                type="primary"
                icon={<BuildOutlined />}
                size="large"
                loading={loading}
                onClick={form.handleSubmit(onSubmit)}
                className={styles.generateButton}
              >
                一键组卷
              </Button>
            </div>
          </Card>
        </Col>

        <Col span={14}>
          <Card
            className={styles.resultCard}
            title="生成结果"
            extra={
              markdownData &&
              markdownData.trim() !== '' && (
                <div className={styles.resultActions}>
                  <Button
                    type="default"
                    icon={<FileWordOutlined />}
                    onClick={() =>
                      markdownData &&
                      exportMarkdownToDocx(
                        markdownData,
                        `智能组卷_${Date.now()}.docx`,
                      )
                    }
                    className={styles.actionButton}
                  >
                    保存Word
                  </Button>
                  <Button
                    type="default"
                    icon={<FileExcelOutlined />}
                    onClick={() =>
                      markdownData &&
                      markdownToExcelV2(markdownData, `智能组卷_${Date.now()}`)
                    }
                    className={styles.actionButton}
                  >
                    保存Excel
                  </Button>
                </div>
              )
            }
          >
            {markdownData && markdownData.trim() !== '' ? (
              <div className={styles.resultContent}>
                <HightLightMarkdown>{markdownData}</HightLightMarkdown>
              </div>
            ) : (
              <div className={styles.emptyResult}>
                {loading ? (
                  <div className={styles.loadingState}>
                    <div className={styles.statusInfo}>
                      {status === 'uploading' && '📤 正在上传试题文件...'}
                      {status === 'waiting_embedding' &&
                        '🔄 试题文件向量化中...'}
                      {status === 'retrieving_template' &&
                        '🔍 试卷模板检索中...'}
                      {status === 'generating' && '✍️ 智能组卷生成中...'}
                      {status === 'streaming' && '📝 试卷内容流式输出中...'}
                    </div>
                    <div className={styles.tip}>
                      请耐心等待，生成过程可能需要几分钟
                    </div>
                  </div>
                ) : (
                  <div className={styles.placeholder}>
                    <BuildOutlined className={styles.placeholderIcon} />
                    <p>完成左侧表单配置后，点击"一键组卷"按钮生成试卷</p>
                  </div>
                )}
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SmartExamPage;
