# 教案生成功能错误修复总结（方案二：模板结构优化）

## 错误描述

**错误信息：** `AttributeError: 'str' object has no attribute 'get'`

**错误位置：** `agent/component/base.py:417` 在 `get_dependent_components()` 方法中

**触发场景：** 用户通过前端请求生成教案时，Canvas 系统在处理组件依赖关系时抛出异常

## 根本原因分析

### 问题起因

错误的根本原因是 `get_dependent_components()` 方法假设 `self._param.query` 中的所有元素都是字典对象，但实际上不同组件的 `query` 参数有不同的数据类型：

1. **Begin 组件**：`query` 是字典列表，用于定义输入参数

   ```json
   "query": [
     {"key": "course_name", "name": "课程名称", "value": ""},
     {"key": "class_hours", "name": "课时", "value": "40"}
   ]
   ```

2. **Retrieval 组件**：`query` 是字符串，用于检索查询
   ```json
   "query": "教案模板 {Begin:Start@course_name} {Begin:Start@target_audience}"
   ```

### 错误触发流程

1. 前端发送教案生成请求到 `/v1/agent/lesson_plan/generate`
2. 系统加载 `generate_lesson_plan.json` 模板
3. Canvas 通过 `load()` 方法初始化所有组件
4. 运行时调用 `get_dependent_components()` 检查组件依赖
5. 方法尝试对字符串类型的 `query` 调用 `.get()` 方法
6. 抛出 `AttributeError`

## 修复方案（不修改核心代码）

### 方案选择原因

- **避免修改核心文件**：不对 `agent/component/base.py` 进行任何修改
- **保持系统稳定性**：通过调整模板结构解决问题，风险更低
- **维护简单性**：模板修改比代码修改更容易维护和回滚

### 具体修改

#### 修改 DSL 模板结构

**文件：** `agent/templates/generate_lesson_plan.json`

**修改策略：** 使用 Template 组件替代 Retrieval 组件的字符串 query 参数

**修改前的工作流：**

```
Begin:Start → Retrieval:TemplateRetrieval → Generate:LessonPlan
```

**修改后的工作流：**

```
Begin:Start → Template:QueryBuilder → Retrieval:TemplateRetrieval → Generate:LessonPlan
```

#### 详细修改内容

1. **添加 Template:QueryBuilder 组件**

   ```json
   "Template:QueryBuilder": {
     "obj": {
       "component_name": "Template",
       "params": {
         "content": "教案模板 {Begin:Start@course_name} {Begin:Start@target_audience}",
         "parameters": []
       }
     },
     "downstream": ["Retrieval:TemplateRetrieval"]
   }
   ```

2. **修改 Begin 组件的下游连接**

   ```json
   "Begin:Start": {
     "downstream": ["Template:QueryBuilder"]  // 原来是直接连接到 Retrieval
   }
   ```

3. **移除 Retrieval 组件的 query 参数**
   ```json
   "Retrieval:TemplateRetrieval": {
     "obj": {
       "component_name": "Retrieval",
       "params": {
         "kb_ids": ["6fdc95de3bb711f08a02428c2b71dd13"],
         // 移除了 "query": "教案模板 {Begin:Start@course_name} {Begin:Start@target_audience}"
         "top_n": 5,
         "similarity_threshold": 0.3,
         "keywords_similarity_weight": 0.7,
         "empty_response": "未找到相关教案模板，将使用默认模板格式生成教案。"
       }
     }
   }
   ```

### 修复原理

1. **Template 组件**：负责根据 Begin 组件的输出生成检索查询字符串
2. **Retrieval 组件**：通过 `get_input()` 方法从上游组件（Template）获取查询字符串
3. **避免类型冲突**：Retrieval 组件不再有字符串类型的 query 参数，避免了类型检查问题

## 修复效果

### 解决的问题

- ✅ 修复了 `AttributeError: 'str' object has no attribute 'get'` 错误
- ✅ 保持了教案生成功能的完整性
- ✅ 维护了检索功能的有效性
- ✅ 没有修改任何核心代码文件

### 功能验证

修复后的工作流程：

1. Begin 组件收集用户输入（课程名称、授课对象等）
2. Template 组件根据用户输入生成检索查询："教案模板 [课程名称] [授课对象]"
3. Retrieval 组件使用生成的查询在知识库中检索相关模板
4. Generate 组件基于检索结果和用户输入生成教案
5. 最终格式化输出

### 优势

1. **安全性高**：不涉及核心代码修改，降低引入新 bug 的风险
2. **维护简单**：模板文件的修改更直观，容易理解和维护
3. **功能完整**：保持了所有原有功能，包括动态查询生成
4. **扩展性好**：Template 组件可以进行更复杂的查询生成逻辑

## 验证方法

### 测试步骤

1. 通过前端发送教案生成请求
2. 验证 Template 组件能否正确生成查询字符串
3. 验证 Retrieval 组件能否正常检索模板
4. 验证最终教案生成结果的质量

### 预期结果

- 不再出现 `AttributeError` 错误
- 教案生成功能正常工作
- 检索到的模板内容符合预期
- 生成的教案质量保持原有水平

## 总结

这个修复方案通过调整 DSL 模板结构，巧妙地避免了 query 参数的类型冲突问题。相比修改核心代码，这种方案：

1. **风险更低**：不影响系统的核心逻辑
2. **维护更容易**：模板修改比代码修改更直观
3. **扩展性更好**：为未来的功能扩展提供了更好的架构基础
4. **兼容性更强**：不影响其他功能模块

这种解决方案体现了"在架构层面解决问题"的设计思想，通过合理的组件组合和数据流设计，从根源上避免了类型冲突问题。
