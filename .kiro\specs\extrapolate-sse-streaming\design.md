# Design Document

## Overview

This design refactors the ExtrapolateService to implement robust Server-Sent Events (SSE) streaming capability, following the proven patterns established in LessonPlanV2Service. The refactoring will enhance user experience by providing real-time feedback during question generation while maintaining consistency across the codebase.

## Architecture

### Current State Analysis

The current ExtrapolateService has basic streaming but lacks:

- Proper SSE event formatting and buffering
- Embedding progress feedback for uploaded files
- Reconnection support via Last-Event-ID
- Consistent error handling patterns
- Think block filtering for clean output

### Target Architecture

The refactored service will adopt the LessonPlanV2Service architecture pattern:

```
ExtrapolateService
├── SSEStreamBuffer (time-based chunk buffering)
├── MinimalCanvas (lightweight canvas for component execution)
├── _UploadContext (file upload metadata tracking)
├── Stream Generation Pipeline
│   ├── File Upload & Embedding (if applicable)
│   ├── Content Generation with SSE Events
│   └── Cleanup Scheduling
└── Error Handling & Logging
```

## Components and Interfaces

### 1. SSEStreamBuffer Integration

**Purpose:** Replace current manual SSE formatting with proven buffering system

**Implementation:**

- Import `SSEStreamBuffer` from `lesson_plan_v2_service.py`
- Configure 1-second send interval for optimal user experience
- Handle Last-Event-ID for reconnection support

**Interface:**

```python
class SSEStreamBuffer:
    def __init__(self, send_interval: float = 1.0)
    def add_chunk(self, chunk: str, last_event_id: int = -1) -> dict | None
    def flush() -> dict | None
```

### 2. Enhanced Stream Generation

**Purpose:** Implement comprehensive streaming pipeline with proper event sequencing

**Event Sequence:**

1. `init` - Initialize streaming session
2. `waiting_embedding` - File embedding progress (if file uploaded)
3. `generating` - Content generation status
4. `stream` - Incremental content chunks
5. `done` - Generation completion
6. `error` - Error conditions

**Think Block Filtering:**

- Implement the same think block filtering logic as frontend
- Filter `<think>...</think>` blocks from streaming output
- Maintain state across chunk boundaries

### 3. File Upload Context Management

**Purpose:** Standardize file handling and cleanup scheduling

**\_UploadContext Class:**

```python
class _UploadContext:
    def __init__(self, kb_id: str, doc_id: str, cleanup_job_id: Optional[str])
    kb_id: str          # Knowledge base ID for cleanup
    doc_id: str         # Document ID for cleanup
    cleanup_job_id: str # Celery task ID for cleanup cancellation
```

**Upload Flow:**

1. Parse file content using existing `SampleFileParser`
2. Upload to knowledge base and start embedding
3. Schedule 2-hour cleanup task
4. Return upload context for tracking

### 4. MinimalCanvas Integration

**Purpose:** Use lightweight canvas for component execution consistency

**Implementation:**

- Import `MinimalCanvas` from `lesson_plan_v2_service.py`
- Initialize with tenant_id and user_id
- Provide consistent component execution environment

### 5. Enhanced Error Handling

**Purpose:** Implement comprehensive error handling with proper SSE error events

**Error Categories:**

- Validation errors (return HTTP 400 before streaming)
- File upload errors (return HTTP 500 before streaming)
- Generation errors (send SSE error events during streaming)
- Cleanup errors (log warnings, don't affect user experience)

## Data Models

### Enhanced ExtrapolateParam

No changes required - current model is sufficient.

### ExtrapolateContext Enhancement

Add fields for streaming context:

```python
@dataclass
class ExtrapolateContext:
    # ... existing fields ...

    # New streaming-related fields
    session_id: Optional[str] = None
    upload_context: Optional[_UploadContext] = None
```

## Error Handling

### Pre-Streaming Errors

**File Upload Errors:**

- Return HTTP 500 with JSON error response
- Log detailed error information
- Attempt cleanup of partial uploads

**Validation Errors:**

- Return HTTP 400 with JSON error response
- Include specific validation failure details

### Streaming Errors

**Generation Errors:**

- Send SSE error event with error details
- Log full exception information
- Terminate stream gracefully

**Embedding Timeout:**

- Log warning but continue with available content
- Don't block generation process

## Testing Strategy

### Unit Tests

1. **SSEStreamBuffer Integration Tests**

   - Test chunk buffering and timing
   - Test Last-Event-ID handling
   - Test event formatting

2. **File Upload Tests**

   - Test successful upload and embedding
   - Test upload failure scenarios
   - Test cleanup scheduling

3. **Stream Generation Tests**
   - Test complete streaming pipeline
   - Test error conditions during streaming
   - Test think block filtering

### Integration Tests

1. **End-to-End Streaming Tests**

   - Test complete request-to-response flow
   - Test file upload with streaming
   - Test reconnection scenarios

2. **Error Handling Tests**
   - Test various error conditions
   - Test error event generation
   - Test cleanup behavior

### Performance Tests

1. **Streaming Performance**
   - Test streaming latency and throughput
   - Test memory usage during long generations
   - Test concurrent streaming sessions

## Implementation Phases

### Phase 1: Core Streaming Infrastructure

- Import and integrate SSEStreamBuffer
- Implement MinimalCanvas usage
- Add \_UploadContext class

### Phase 2: Stream Generation Pipeline

- Refactor \_generate_stream method
- Implement proper event sequencing
- Add think block filtering

### Phase 3: File Upload Enhancement

- Enhance \_handle_file_upload method
- Add embedding progress events
- Implement cleanup scheduling

### Phase 4: Error Handling & Testing

- Implement comprehensive error handling
- Add logging and monitoring
- Create comprehensive test suite

## Migration Considerations

### Backward Compatibility

The refactoring maintains API compatibility:

- Same endpoint URLs and request formats
- Same response content (enhanced with better streaming)
- Existing frontend code will work without changes

### Deployment Strategy

1. Deploy backend changes first
2. Frontend can immediately benefit from improved streaming
3. No database schema changes required
4. No configuration changes required

## Monitoring and Observability

### Logging Enhancements

- Add structured logging for streaming events
- Log performance metrics (generation time, chunk counts)
- Log error details for troubleshooting

### Metrics

- Track streaming session durations
- Monitor file upload success rates
- Track embedding completion times
- Monitor cleanup job execution
