/* High-Tech Modern UI Theme Variables */
/* This file contains global design tokens for the application */

/* Color System */
:root {
  /* Primary colors */
  --primary-color: #3b82f6;
  --primary-color-rgb: 59, 130, 246;
  --primary-hover: #2563eb;
  --primary-light: #60a5fa;
  --primary-ultralight: rgba(59, 130, 246, 0.1);
  --primary-dark: #1d4ed8;

  /* Accent colors */
  --accent-color: #10b981;
  --accent-hover: #059669;
  --accent-light: #a7f3d0;

  /* Neutral colors */
  --neutral-50: #f9fafb;
  --neutral-100: #f6f6f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* Semantic colors */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* Background colors */
  --background-primary: var(--neutral-50);
  --background-secondary: var(--neutral-100);
  --background-tertiary: var(--neutral-200);
  --card-background: white;

  --background-menu-selected: #0000000f;

  /* Text colors */
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-tertiary: var(--neutral-500);
  --text-disabled: var(--neutral-400);
  --text-on-primary: white;

  /* Border colors */
  --border-color: #0000000f;
  --border-color-dark: var(--neutral-300);

  /* Effects */
  --glow-color: rgba(59, 130, 246, 0.2);
  --glow-border: rgba(37, 99, 235, 0.25);
  --glass-background: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.12);
  --dark-glass-background: rgba(15, 23, 42, 0.7);
  --dark-glass-border: rgba(30, 41, 59, 0.3);
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
  --card-hover-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --dropdown-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* Search Input Colors */
  --search-input-bg: #f3f4f6;
  --search-input-border: transparent;
  --search-input-focus-bg: #f3f4f6;
  --search-input-focus-border: transparent;
  --search-input-icon-color: #9ca3af;
  --search-input-text-color: #374151;
  --search-input-placeholder-color: #9ca3af;

  /* Border radius */
  --border-radius-sm: 4px;
  --border-radius-md: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-pill: 9999px;
  --border-width: 1px;

  /* Layout */
  --header-height: 64px;
  --sidebar-width: 180px;
  --sidebar-collapsed-width: 80px;
  --sidebar-item-size: 72px;

  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  /* Typography */
  --font-family: ui-sans-serif, -apple-system, system-ui, Segoe UI, Helvetica,
    Apple Color Emoji, Arial, sans-serif, Segoe UI Emoji, Segoe UI Symbol;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

html,
body {
  font-family: var(--font-family);
}

* {
  font-family: var(--font-family);
}

#root {
  overflow: hidden;
}

.main-content-container {
  padding: 24px;
}

/* Dark mode */
.dark {
  /* Background colors */
  --background-primary: var(--neutral-900);
  --background-secondary: var(--neutral-800);
  --background-tertiary: var(--neutral-700);
  --card-background: var(--neutral-800);

  /* Text colors */
  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-300);
  --text-tertiary: var(--neutral-400);
  --text-disabled: var(--neutral-600);

  /* Border colors */
  --border-color: var(--neutral-700);
  --border-color-dark: var(--neutral-600);

  /* Search Input Colors for Dark Mode */
  --search-input-bg: #374151;
  --search-input-border: transparent;
  --search-input-focus-bg: #374151;
  --search-input-focus-border: transparent;
  --search-input-icon-color: #9ca3af;
  --search-input-text-color: #f9fafb;
  --search-input-placeholder-color: #9ca3af;

  /* Effects */
  --card-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.2);
  --card-hover-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3),
    0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --dropdown-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3),
    0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Mixins */
.glass-effect() {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark-glass-effect() {
  background: rgba(31, 41, 55, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.glow-effect() {
  box-shadow: 0 0 15px var(--glow-color);
  border: 1px solid var(--glow-border);
}

.hover-transform() {
  transition: transform var(--transition-normal);

  &:hover {
    transform: translateY(-2px);
  }
}

.gradient-text(@start-color: var(--primary-color), @end-color: #38bdf8) {
  background: linear-gradient(90deg, @start-color 0%, @end-color 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.responsive-container() {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* High-Tech Card Styling */
.high-tech-card {
  background: var(--card-background);
  border-radius: var(--border-radius-md);
  box-shadow: var(--card-shadow);
  transition: all var(--transition-normal);

  &:hover {
    box-shadow: var(--card-hover-shadow);
  }

  &.glow {
    .glow-effect();
  }

  &.glass {
    .glass-effect();
  }
}

/* Flat Buttons */
.flat-button {
  background: var(--primary-color);
  color: var(--text-on-primary);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: 500;
  transition: all var(--transition-fast);

  &:hover {
    background: var(--primary-hover);
  }

  &.secondary {
    background: var(--neutral-100);
    color: var(--text-primary);

    &:hover {
      background: var(--neutral-200);
    }
  }

  &.ghost {
    background: transparent;
    color: var(--primary-color);

    &:hover {
      background: var(--primary-ultralight);
    }
  }
}

/* App Card Styling */
.app-card {
  .high-tech-card();
  .hover-transform();
  padding: var(--spacing-md);
  height: 100%;
  display: flex;
  flex-direction: column;

  .app-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
  }

  .app-title {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
  }

  .app-description {
    color: var(--text-secondary);
    flex-grow: 1;
  }
}

/* Modern Layout Components */
.high-tech-layout {
  background: var(--background-secondary);
  min-height: 100vh;

  .high-tech-header {
    background: var(--background-primary);
    height: var(--header-height);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    position: fixed;
    width: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-md);
  }

  .high-tech-sidebar {
    background: var(--background-primary);
    width: var(--sidebar-width);
    border-right: var(--border-width) solid var(--border-color);
    height: calc(100vh - var(--header-height));
    position: fixed;
    top: var(--header-height);
    left: 0;
    overflow-y: auto;
    transition: width var(--transition-normal);

    &.collapsed {
      width: var(--sidebar-collapsed-width);
    }
  }

  .high-tech-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: var(--spacing-lg);
    min-height: calc(100vh - var(--header-height));
    transition: margin-left var(--transition-normal);

    &.sidebar-collapsed {
      margin-left: var(--sidebar-collapsed-width);
    }
  }
}

&::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

&::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--border-radius-pill);
}

&::-webkit-scrollbar-track {
  background: transparent;
}

&::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

&::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.3);
  }
}

/* Main Page Title Styling */
.main-page-title {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  margin-bottom: var(--spacing-sm);
}

/* Direct class for modern search input */
:global(.modern-search-input-field) {
  background: #f3f4f6 !important;
  border: 1px solid rgba(0, 0, 0, 0) !important;
  border-radius: 8px !important;
  transition: border-color 0.2s ease !important;
  padding: 8px 12px !important;
  box-shadow: none !important;

  &:hover {
    background: #f3f4f6 !important;
    border: 1px solid rgba(0, 0, 0, 0) !important;
    box-shadow: none !important;
  }

  &:focus,
  &:focus-within,
  &.ant-input-affix-wrapper-focused {
    background: #f3f4f6 !important;
    border: 1px solid #3b82f6 !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

:global(.modern-search-input) {
  span.ant-input-affix-wrapper,
  。ant-input-affix-wrapper {
    background: #f3f4f6 !important;
    background-color: #f3f4f6 !important;
    border: 1px solid rgba(0, 0, 0, 0) !important;
    border-width: 1px !important;
    border-color: rgba(0, 0, 0, 0) !important;
    box-shadow: none !important;
    border-radius: 8px !important;
    transition: border-color 0.2s ease !important;

    &:hover {
      background: #f3f4f6 !important;
      background-color: #f3f4f6 !important;
      border: 1px solid rgba(0, 0, 0, 0) !important;
      border-width: 1px !important;
      border-color: rgba(0, 0, 0, 0) !important;
      box-shadow: none !important;
      outline: none !important;
    }

    &:focus,
    &:focus-within,
    &:active,
    &.ant-input-affix-wrapper-focused,
    &.ant-input-affix-wrapper-borderless {
      background: #f3f4f6 !important;
      background-color: #f3f4f6 !important;
      border: 1px solid #3b82f6 !important;
      border-width: 1px !important;
      border-color: #3b82f6 !important;
      box-shadow: none !important;
      outline: none !important;
    }

    input.ant-input,
    .ant-input {
      background: transparent !important;
      background-color: transparent !important;
      border: none !important;
      border-width: 0 !important;
      box-shadow: none !important;
      outline: none !important;

      &:hover,
      &:focus,
      &:active {
        background: transparent !important;
        background-color: transparent !important;
        border: none !important;
        border-width: 0 !important;
        box-shadow: none !important;
        outline: none !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .high-tech-layout {
    .high-tech-sidebar {
      width: 0;

      &.expanded {
        width: 100%;
      }
    }

    .high-tech-content {
      margin-left: 0;
    }
  }
}
