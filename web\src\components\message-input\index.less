.messageInputWrapper {
  margin: 0;
  padding: 16px 20px;
  border: 1px solid #e5e7eb;
  border-radius: 24px;
  background-color: #ffffff;
  transition: all 0.2s ease;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: flex-end;
  gap: 12px;

  &:hover {
    border-color: #d1d5db;
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.08),
      0 2px 4px rgba(0, 0, 0, 0.04);
  }

  &:focus-within {
    border-color: #d1d5db;
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.06);
  }

  // 隐藏原有样式
  :global(.ant-input-affix-wrapper) {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    border-radius: 0;
  }

  // 文本输入框样式
  :global(.ant-input) {
    background-color: transparent !important;
    font-size: 14px !important;
    line-height: 1.66 !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    resize: none;

    &::placeholder {
      color: #9ca3af;
    }

    &:focus {
      border: none !important;
      box-shadow: none !important;
      outline: none !important;
    }
  }

  // 左侧按钮样式
  .leftActions {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  // 输入区域
  .inputArea {
    flex: 1;
    min-height: 80px;

    :global(.ant-input) {
      min-height: 80px;
    }
  }

  // 右侧按钮组
  .rightActions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
  }

  // 通用按钮样式
  .actionButton {
    padding: 8px;
    border: none;
    background: transparent;
    color: #6b7280;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }

    &.canvasButton,
    &.canvas-button {
      color: #1890ff;
      font-weight: 500;
      padding: 6px 12px;
      font-size: 14px;

      &:hover {
        background-color: #e6f7ff;
        color: #1890ff;
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.documentCard {
  :global(.ant-card-body) {
    padding: 10px;
    position: relative;
    width: 100%;
  }
}

.listWrapper {
  padding: 0 10px;
  overflow: auto;
  max-height: 170px;
  width: 100%;
}

.inputWrapper {
  border-radius: 8px;
}

.deleteIcon {
  position: absolute;
  right: -4px;
  top: -4px;
  color: #d92d20;
}
