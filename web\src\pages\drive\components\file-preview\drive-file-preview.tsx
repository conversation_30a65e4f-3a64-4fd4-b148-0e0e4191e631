import driveService from '@/services/drive-service';
import { CloseOutlined } from '@ant-design/icons';
import { Button, Modal, Spin, message } from 'antd';
import mammoth from 'mammoth';
import React, { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';
import { DriveFilePreviewFile } from '../../hooks/use-file-preview';
import styles from './drive-file-preview.less';

export interface DriveFilePreviewProps {
  visible: boolean;
  onClose: () => void;
  file: DriveFilePreviewFile | null;
}

const DriveFilePreview: React.FC<DriveFilePreviewProps> = ({
  visible,
  onClose,
  file,
}) => {
  const [loading, setLoading] = useState(false);
  const [content, setContent] = useState<string | null>(null);
  const [previewType, setPreviewType] = useState<
    'video' | 'pdf' | 'document' | 'excel' | 'image' | 'text' | 'unsupported'
  >('unsupported');
  const [blobUrl, setBlobUrl] = useState<string>('');

  // 获取文件类型
  const getFileType = (fileName: string, type: string) => {
    const lowerName = fileName.toLowerCase();
    const lowerType = type.toLowerCase();

    if (
      lowerType === 'visual' ||
      lowerName.endsWith('.mp4') ||
      lowerName.endsWith('.avi') ||
      lowerName.endsWith('.mov') ||
      lowerName.endsWith('.webm')
    ) {
      return 'video';
    }
    if (lowerType === 'pdf' || lowerName.endsWith('.pdf')) {
      return 'pdf';
    }
    if (
      lowerType === 'document' ||
      lowerName.endsWith('.doc') ||
      lowerName.endsWith('.docx')
    ) {
      return 'document';
    }
    if (lowerName.endsWith('.xls') || lowerName.endsWith('.xlsx')) {
      return 'excel';
    }
    if (
      lowerName.endsWith('.jpg') ||
      lowerName.endsWith('.jpeg') ||
      lowerName.endsWith('.png') ||
      lowerName.endsWith('.gif') ||
      lowerName.endsWith('.webp')
    ) {
      return 'image';
    }
    if (lowerName.endsWith('.txt') || lowerName.endsWith('.md')) {
      return 'text';
    }
    return 'unsupported';
  };

  // 下载文件并创建 Blob URL
  const downloadFileBlob = async (fileId: string) => {
    try {
      setLoading(true);
      const { data } = await driveService.getFile(fileId);
      const url = window.URL.createObjectURL(data);
      setBlobUrl(url);
      return data;
    } catch (error) {
      console.error('Error downloading file:', error);
      message.error('无法下载文件进行预览');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 处理Word文档预览
  const previewWordDocument = async (blob: Blob) => {
    try {
      setLoading(true);
      const arrayBuffer = await blob.arrayBuffer();
      const result = await mammoth.convertToHtml({ arrayBuffer });
      setContent(result.value);
    } catch (error) {
      console.error('Error previewing Word document:', error);
      message.error('无法预览Word文档');
      setContent('<p>无法预览此文档</p>');
    } finally {
      setLoading(false);
    }
  };

  // 处理Excel文件预览
  const previewExcelFile = async (blob: Blob) => {
    try {
      setLoading(true);
      const arrayBuffer = await blob.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });

      // 取第一个工作表
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];
      const htmlContent = XLSX.utils.sheet_to_html(worksheet);

      setContent(htmlContent);
    } catch (error) {
      console.error('Error previewing Excel file:', error);
      message.error('无法预览Excel文件');
      setContent('<p>无法预览此文件</p>');
    } finally {
      setLoading(false);
    }
  };

  // 处理文本文件预览
  const previewTextFile = async (blob: Blob) => {
    try {
      setLoading(true);
      const text = await blob.text();
      setContent(
        `<pre style="white-space: pre-wrap; font-family: monospace;">${text}</pre>`,
      );
    } catch (error) {
      console.error('Error previewing text file:', error);
      message.error('无法预览文本文件');
      setContent('<p>无法预览此文件</p>');
    } finally {
      setLoading(false);
    }
  };

  // 重置状态
  const resetState = () => {
    setContent(null);
    setPreviewType('unsupported');
    setLoading(false);
    if (blobUrl) {
      window.URL.revokeObjectURL(blobUrl);
      setBlobUrl('');
    }
  };

  // 处理文件预览
  useEffect(() => {
    if (!visible || !file) {
      resetState();
      return;
    }

    const fileType = getFileType(file.name, file.type);
    setPreviewType(fileType);

    const previewFile = async () => {
      try {
        const blob = await downloadFileBlob(file.location);

        switch (fileType) {
          case 'video':
          case 'pdf':
          case 'image':
            // 这些类型使用 blob URL，已经在 downloadFileBlob 中设置
            break;
          case 'document':
            await previewWordDocument(blob);
            break;
          case 'excel':
            await previewExcelFile(blob);
            break;
          case 'text':
            await previewTextFile(blob);
            break;
          default:
            setContent(null);
            message.warning('暂不支持此文件格式的预览');
        }
      } catch (error) {
        // Error already handled in downloadFileBlob
      }
    };

    previewFile();

    // 清理函数
    return () => {
      if (blobUrl) {
        window.URL.revokeObjectURL(blobUrl);
      }
    };
  }, [visible, file]);

  // 组件卸载时清理 blob URL
  useEffect(() => {
    return () => {
      if (blobUrl) {
        window.URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  // 渲染预览内容
  const renderPreviewContent = () => {
    if (loading) {
      return (
        <div className={styles.loadingContainer}>
          <Spin size="large" />
          <p>正在加载文件...</p>
        </div>
      );
    }

    if (!blobUrl && !content) {
      return (
        <div className={styles.errorContainer}>
          <p>无法预览此文件</p>
        </div>
      );
    }

    switch (previewType) {
      case 'video':
        return (
          <video
            controls
            className={styles.videoPlayer}
            src={blobUrl}
            style={{
              width: '100%',
              height: '100%',
              maxHeight: 'calc(90vh - 60px - 32px)',
            }}
          >
            您的浏览器不支持视频播放
          </video>
        );

      case 'pdf':
        return (
          <iframe
            src={blobUrl}
            className={styles.pdfViewer}
            style={{
              width: '100%',
              height: 'calc(90vh - 60px - 32px)',
              border: 'none',
            }}
            title="PDF预览"
          />
        );

      case 'image':
        return (
          <img
            src={blobUrl}
            alt={file?.name}
            className={styles.imagePreview}
            style={{
              maxWidth: '100%',
              maxHeight: 'calc(90vh - 60px - 32px)',
              objectFit: 'contain',
            }}
          />
        );

      case 'document':
      case 'excel':
      case 'text':
        return (
          <div
            className={styles.documentViewer}
            dangerouslySetInnerHTML={{ __html: content || '' }}
            style={{
              height: 'calc(90vh - 60px - 32px)',
              overflow: 'auto',
              padding: '20px',
            }}
          />
        );

      default:
        return (
          <div className={styles.errorContainer}>
            <p>暂不支持此文件格式的预览</p>
          </div>
        );
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width="90vw"
      style={{ top: '5vh', height: '90vh' }}
      className={styles.filePreviewModal}
      destroyOnClose
      maskStyle={{ backgroundColor: 'rgba(0, 0, 0, 0.8)' }}
      closeIcon={
        <Button
          type="text"
          icon={<CloseOutlined />}
          className={styles.closeButton}
        />
      }
      title={
        <div className={styles.modalHeader}>
          <span className={styles.fileName}>{file?.name}</span>
        </div>
      }
    >
      <div className={styles.previewContainer}>{renderPreviewContent()}</div>
    </Modal>
  );
};

export default DriveFilePreview;
