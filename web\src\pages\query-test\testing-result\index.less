@import '@/theme/high-tech-theme.less';

.testingResultWrapper {
  padding: 16px;
  background: var(--background-color);
  border-radius: 8px;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .resultHeader {
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-md);

    .resultIcon {
      color: #3b82f6;
    }

    .resultTitle {
      margin-bottom: 0;
      font-weight: 600;
      .gradient-text();
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .resultStats {
      margin-left: var(--spacing-md);
      font-size: 0.9rem;
    }
  }

  .resultContainer {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .messagesContainer {
      flex: 1;
      overflow-y: auto;
      padding: 8px 0;

      // 消息项之间的间距
      :global(.ant-list-item) {
        padding: 12px 0;
        border-bottom: none;
      }

      // 覆盖MessageItem的默认样式
      :global(.message-item) {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .emptyState {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      text-align: center;
      color: var(--text-color-secondary);
      height: 300px;

      .emptyIcon {
        font-size: 32px;
        color: var(--text-color-tertiary);
        opacity: 0.6;
      }
    }
  }
}

// 响应式样式
@media (max-width: 768px) {
  .testingResultWrapper {
    padding: 12px;

    .resultContainer {
      .messagesContainer {
        padding: 4px 0;

        :global(.message-item) {
          margin-bottom: 12px;
        }
      }
    }
  }
}

.imagePreview {
  display: block;
  max-width: 45vw;
  max-height: 40vh;
  border-radius: var(--border-radius-md);
}

/* Dark mode adjustments */
:global(.dark) {
  .testingResultWrapper {
    background-color: var(--neutral-800);

    .selectFilesCollapse {
      :global(.ant-collapse-header) {
        background-color: var(--neutral-700);
      }

      :global(.ant-collapse-content) {
        background-color: var(--neutral-800);
      }
    }

    .similarityCircle {
      background-color: rgba(59, 130, 246, 0.2);
    }

    .chunkCard {
      background-color: var(--neutral-800);

      :global(.ant-card-head) {
        background-color: var(--neutral-700);
      }
    }
  }
}
