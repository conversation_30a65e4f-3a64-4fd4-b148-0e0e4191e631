# 邀请成员功能实现文档

## 功能概述

为系统设置的组成员管理页面实现了「邀请成员」功能，允许用户通过搜索现有用户并向其发送租户邀请。

## 实现架构

### 后端实现

#### 1. 用户搜索 API (`api/apps/user_app.py`)

**新增端点**: `GET /v1/user/search`

**功能特性**:

- 支持按用户名或邮箱模糊搜索
- 分页支持（默认每页 10 条，最大 50 条）
- 只返回状态为有效的用户
- 过滤敏感信息，只返回必要字段

**请求参数**:

- `keyword`: 搜索关键词（可选）
- `page`: 页码（默认 1）
- `page_size`: 每页数量（默认 10，最大 50）

**响应格式**:

```json
{
  "code": 0,
  "data": {
    "users": [
      {
        "id": "user_id",
        "nickname": "用户名",
        "email": "<EMAIL>",
        "avatar": "头像URL"
      }
    ],
    "total": 100
  }
}
```

#### 2. 用户服务层扩展 (`api/db/services/user_service.py`)

**新增方法**:

- `search_users(keyword, page, page_size)`: 搜索用户
- `count_users(keyword)`: 统计匹配用户数量

**实现特点**:

- 使用 Peewee ORM 的 `contains` 方法进行模糊匹配
- 支持在用户名和邮箱字段中同时搜索
- 按用户名排序确保结果一致性
- 分页查询优化性能

### 前端实现

#### 1. API 服务层 (`web/src/utils/api.ts` & `web/src/services/user-service.ts`)

**新增 API 端点**:

- `search_users`: 用户搜索接口

**新增服务函数**:

- `searchUsers(params)`: 搜索用户
- `inviteTenantUser(tenantId, email)`: 邀请用户加入租户

#### 2. React Hooks (`web/src/hooks/system-setting-hooks.tsx`)

**新增 Hooks**:

- `useSearchUsers(keyword)`: 用户搜索 Hook
- `useInviteTenantUser()`: 邀请用户 Hook

**功能特性**:

- 自动缓存搜索结果
- 只有关键词长度大于 0 时才执行搜索
- 邀请成功后自动刷新用户列表
- 统一的错误处理和加载状态

#### 3. 邀请成员弹窗组件 (`web/src/pages/system-setting/group-member/invite-member-modal.tsx`)

**核心功能**:

- 实时搜索用户（防抖处理，300ms 延迟）
- 用户列表展示（头像、用户名、邮箱）
- 一键邀请功能
- 搜索状态和结果统计

**用户体验优化**:

- 至少输入 2 个字符才开始搜索
- 搜索加载状态指示
- 空状态提示和引导
- 搜索结果数量统计
- 邀请按钮加载状态
- 已在租户中的用户邀请按钮自动禁用
- 当前登录用户邀请按钮自动禁用
- 不可邀请用户显示相应标签（"已在租户中"或"当前用户"）

**界面设计**:

- 600px 宽度的模态框
- 大尺寸搜索框
- 可滚动的用户列表（最大高度 400px）
- 用户头像和信息展示
- 邀请按钮带图标

#### 4. 主组件集成 (`web/src/pages/system-setting/group-member/index.tsx`)

**集成要点**:

- 添加邀请成员弹窗状态管理
- 邀请按钮在未选择租户时禁用
- 传递租户信息和用户列表到邀请弹窗
- 弹窗关闭时重置状态
- 智能识别不可邀请用户（租户成员、当前用户）并禁用邀请

## 逻辑优化

### 用户排除逻辑

1. **租户成员排除**: 已经属于当前租户的用户不可再次邀请
2. **当前用户排除**: 当前登录用户不可邀请自己
3. **状态标识**: 不同排除原因显示不同的标签文字
4. **统一判断**: 使用 `excludedUserEmails` 集合统一管理所有不可邀请的用户

### 用户界面反馈

- **按钮状态**: 不可邀请用户的按钮自动禁用并显示"不可邀请"
- **原因标签**: 显示具体的排除原因（"已在租户中"、"当前用户"）
- **视觉区分**: 通过灰色标签和禁用按钮清晰区分可邀请和不可邀请用户

## 技术特点

### 性能优化

1. **防抖搜索**: 300ms 延迟避免频繁请求
2. **分页查询**: 后端限制每页最大 50 条记录
3. **React Query 缓存**: 自动缓存搜索结果
4. **条件查询**: 只有关键词存在时才执行搜索

### 用户体验

1. **实时搜索**: 输入即搜索，无需点击按钮
2. **加载状态**: 搜索和邀请过程都有加载指示
3. **错误处理**: 统一的错误提示机制
4. **空状态处理**: 友好的空状态提示和引导

### 安全性

1. **数据过滤**: 后端只返回必要的用户信息
2. **权限控制**: 基于现有的租户权限体系
3. **输入验证**: 前后端都有相应的参数验证
4. **重复邀请防护**: 自动检测并禁用已在租户中的用户邀请
5. **自我邀请防护**: 自动检测并禁用当前登录用户的邀请按钮

## 使用流程

1. **选择租户**: 在左侧树中选择要邀请成员的租户
2. **点击邀请**: 点击"邀请成员"按钮打开搜索弹窗
3. **搜索用户**: 输入用户名或邮箱进行搜索
4. **发送邀请**: 点击用户旁边的"邀请"按钮发送邀请
5. **自动刷新**: 邀请成功后自动刷新用户列表

## 扩展性考虑

1. **搜索优化**: 可以添加更多搜索字段或高级搜索功能
2. **批量邀请**: 可以扩展为支持批量选择和邀请
3. **邀请状态**: 可以添加邀请状态跟踪和管理
4. **权限细化**: 可以根据用户角色限制邀请权限

## 依赖关系

- **前端依赖**: React Query, Ant Design, ahooks
- **后端依赖**: 现有的用户和租户服务
- **数据库**: 使用现有的用户表和租户关系表

## 测试建议

1. **搜索功能测试**: 测试各种搜索关键词和边界情况
2. **权限测试**: 验证不同角色用户的邀请权限
3. **性能测试**: 测试大量用户数据下的搜索性能
4. **用户体验测试**: 验证加载状态和错误处理的用户体验
