import { get, post } from '@/utils/request';

export interface AIQuestionGenerateParams {
  exam_scene: string;
  knowledge_points: string;
  objective: string;
  random_questions_enabled: boolean;
  question_count?: number;
  by_question_type_enabled: boolean;
  single_choice_count?: number;
  multiple_choice_count?: number;
  fill_blank_count?: number;
  true_false_count?: number;
  short_answer_count?: number;
  ordering_count?: number;
  other_requirements?: string;
  llm_id: string;
  exam_kb_id?: string;
  kb_ids?: string[]; // For other knowledge bases
  // Add any other parameters the backend agent expects
}

export interface SmartExamGenerateParams {
  exam_name: string;
  knowledge_points: string;
  difficulty: 'low' | 'medium' | 'high';
  question_method: 'random' | 'by_type';
  question_count?: number;
  single_choice_count?: number;
  multiple_choice_count?: number;
  fill_blank_count?: number;
  true_false_count?: number;
  short_answer_count?: number;
  ordering_count?: number;
  llm_id: string;
  question_bank_file?: File;
}

export interface AIQuestionGenerateResponse {
  code: number;
  data: {
    generated_questions: string;
  };
  message: string;
}

/**
 * Calls the AI Question Generation agent.
 */
export const generateAIQuestions = async (
  params: AIQuestionGenerateParams,
): Promise<AIQuestionGenerateResponse> => {
  console.log('Calling AI Question API with params:', params);
  try {
    const endpoint = `/v1/agent/ai_question/generate`;
    console.log(`Sending request to endpoint: ${endpoint}`);

    const { data: response } = await post(endpoint, params);

    // Ensure response has the expected structure
    if (response && typeof response === 'object') {
      // Check for the expected structure
      if (response.code !== undefined && response.data !== undefined) {
        console.log('Response data:', response.data);

        // Validate generated_questions exists in data
        if (response.data.generated_questions) {
          console.log(
            'Generated questions length:',
            response.data.generated_questions.length,
          );
        } else {
          console.warn('No generated_questions found in response data');
        }

        return response as AIQuestionGenerateResponse;
      } else {
        console.error('Unexpected response structure:', response);
        throw new Error('Unexpected response structure from AI Question API');
      }
    } else {
      console.error('Invalid response format:', response);
      throw new Error('Invalid response format from AI Question API');
    }
  } catch (error) {
    console.error('Error in generateAIQuestions API call:', error);
    throw error;
  }
};

// You might have other agent-related API calls here

export interface ExtrapolateGenerateParams {
  question_type: 'same' | 'custom';
  custom_question_type?: string;
  structure: 'same' | 'custom';
  custom_structure?: string;
  knowledge_points: 'same_context' | 'custom';
  custom_knowledge_points?: string;
  difficulty: 'same' | 'basic' | 'medium' | 'hard';
  quantity: number;
  sample_file?: any;
  llm_id: string;
}

export interface ExtrapolateGenerateResponse {
  code: number;
  data: {
    generated_questions: string;
  };
  message: string;
}

/**
 * Calls the Extrapolate (举一反三) Generation agent.
 */
export const generateExtrapolatedQuestions = async (
  params: ExtrapolateGenerateParams | FormData,
): Promise<ExtrapolateGenerateResponse> => {
  console.log('Calling Extrapolate API with params:', params);
  try {
    const endpoint = `/v1/agent/extrapolate/generate`;
    console.log(`Sending request to endpoint: ${endpoint}`);

    // Import getAuthorization function to get auth header
    const { getAuthorization } = await import('@/utils/authorization-util');

    // For now, we'll use a simpler approach with native fetch for FormData
    // This bypasses the complex request processing
    let response;
    if (params instanceof FormData) {
      const result = await fetch(endpoint, {
        method: 'POST',
        body: params,
        credentials: 'include',
        headers: {
          // Add authorization header manually for FormData requests
          Authorization: getAuthorization(),
        },
      });

      if (!result.ok) {
        throw new Error(`HTTP ${result.status}: ${result.statusText}`);
      }

      const data = await result.json();
      response = data; // 直接使用返回的数据，不要额外包装
    } else {
      // For regular JSON params, use the existing post method
      const { data } = await post(endpoint, params);
      response = data;
    }

    // Ensure response has the expected structure
    if (response && typeof response === 'object') {
      // Check for the expected structure (same as AI Question API)
      if (response.code !== undefined && response.data !== undefined) {
        console.log('Response data:', response.data);

        // Validate generated_questions exists in data
        if (response.data.generated_questions) {
          console.log(
            'Generated questions length:',
            response.data.generated_questions.length,
          );
        } else {
          console.warn('No generated_questions found in response data');
        }

        return response as ExtrapolateGenerateResponse;
      } else {
        console.error('Unexpected response structure:', response);
        throw new Error('Unexpected response structure from Extrapolate API');
      }
    } else {
      console.error('Invalid response format:', response);
      throw new Error('Invalid response format from Extrapolate API');
    }
  } catch (error) {
    console.error('Error in generateExtrapolatedQuestions API call:', error);
    throw error;
  }
};

// Lesson Plan Generation interfaces and API
export interface LessonPlanGenerateParams {
  course_name: string;
  lesson_type: string;
  lesson_duration: number;
  target_audience: string;
  course_content: string;
  other_requirements?: string;
  course_file?: any;
  llm_id: string;
}

export interface LessonPlanGenerateResponse {
  code: number;
  data: {
    generated_lesson_plan: string;
  };
  message: string;
}

/**
 * Calls the LessonPlan (教案生成) Generation agent.
 */
export const generateLessonPlan = async (
  params: LessonPlanGenerateParams | FormData,
): Promise<LessonPlanGenerateResponse> => {
  console.log('Calling LessonPlan API with params:', params);
  try {
    const endpoint = `/v1/agent/lesson_plan/generate`;
    console.log(`Sending request to endpoint: ${endpoint}`);

    // Import getAuthorization function to get auth header
    const { getAuthorization } = await import('@/utils/authorization-util');

    // For now, we'll use a simpler approach with native fetch for FormData
    // This bypasses the complex request processing
    let response;
    if (params instanceof FormData) {
      const result = await fetch(endpoint, {
        method: 'POST',
        body: params,
        credentials: 'include',
        headers: {
          // Add authorization header manually for FormData requests
          Authorization: getAuthorization(),
        },
      });

      if (!result.ok) {
        throw new Error(`HTTP ${result.status}: ${result.statusText}`);
      }

      const data = await result.json();
      response = data; // 直接使用返回的数据，不要额外包装
    } else {
      // For regular JSON params, use the existing post method
      const { data } = await post(endpoint, params);
      response = data;
    }

    // Ensure response has the expected structure
    if (response && typeof response === 'object') {
      // Check for the expected structure
      if (response.code !== undefined && response.data !== undefined) {
        console.log('Response data:', response.data);

        // Validate generated_lesson_plan exists in data
        if (response.data.generated_lesson_plan) {
          console.log(
            'Generated lesson plan length:',
            response.data.generated_lesson_plan.length,
          );
        } else {
          console.warn('No generated_lesson_plan found in response data');
        }

        return response as LessonPlanGenerateResponse;
      } else {
        console.error('Unexpected response structure:', response);
        throw new Error('Unexpected response structure from LessonPlan API');
      }
    } else {
      console.error('Invalid response format:', response);
      throw new Error('Invalid response format from LessonPlan API');
    }
  } catch (error) {
    console.error('Error in generateLessonPlan API call:', error);
    throw error;
  }
};

// File Keyword Search interfaces and API
export interface FileKeywordSearchParams {
  content: string;
  llm_id: string;
  max_keywords?: number;
  max_results?: number;
  temperature?: number;
}

export interface FileKeywordSearchResponse {
  code: number;
  data: {
    keywords: Array<{
      keyword: string;
      weight: number;
    }>;
    matched_files: Array<{
      id: string;
      name: string;
      matched_keyword: string;
      keyword_weight: number;
      size: number;
      type: string;
      create_time: string;
      parent_id?: string;
      location?: string;
    }>;
    total_matches: number;
    message: string;
  };
  message: string;
}

/**
 * Calls the FileKeywordSearch agent to find related files.
 */
export const searchRelatedFiles = async (
  params: FileKeywordSearchParams,
): Promise<FileKeywordSearchResponse> => {
  console.log('Calling FileKeywordSearch API with params:', params);
  try {
    const endpoint = `/v1/agent/file_keyword_search/search`;
    console.log(`Sending request to endpoint: ${endpoint}`);

    const { data } = await post(endpoint, params);
    const response = data;

    // Ensure response has the expected structure
    if (response && typeof response === 'object') {
      if (response.code !== undefined && response.data !== undefined) {
        console.log('FileKeywordSearch response data:', response.data);

        return response as FileKeywordSearchResponse;
      } else {
        console.error('Unexpected response structure:', response);
        throw new Error(
          'Unexpected response structure from FileKeywordSearch API',
        );
      }
    } else {
      console.error('Invalid response format:', response);
      throw new Error('Invalid response format from FileKeywordSearch API');
    }
  } catch (error) {
    console.error('Error in searchRelatedFiles API call:', error);
    throw error;
  }
};

// V3 System Configuration interfaces
export interface V3SystemConfig {
  endpoint: string;
  token: string;
}

export interface V3SystemConfigResponse {
  code: number;
  data: V3SystemConfig;
  message: string;
}

/**
 * Get V3 system configuration including endpoint URL and token
 */
export const getV3SystemConfig = async (): Promise<V3SystemConfigResponse> => {
  console.log('Fetching V3 system configuration');
  try {
    const endpoint = `/v1/api/v3_system/config`;
    console.log(`Sending request to endpoint: ${endpoint}`);

    const { data: response } = await get(endpoint);

    // Ensure response has the expected structure
    if (response && typeof response === 'object') {
      if (response.code !== undefined && response.data !== undefined) {
        console.log('V3 system config response:', response.data);
        return response as V3SystemConfigResponse;
      } else {
        console.error('Unexpected response structure:', response);
        throw new Error(
          'Unexpected response structure from V3 system config API',
        );
      }
    } else {
      console.error('Invalid response format:', response);
      throw new Error('Invalid response format from V3 system config API');
    }
  } catch (error) {
    console.error('Error in getV3SystemConfig API call:', error);
    throw error;
  }
};

// -----------------------------
// Streaming (SSE) helpers – v2 endpoints
// -----------------------------

export const startAIQuestionStream = (
  params: Record<string, any>,
): EventSource => {
  // backend expects json; we stringify and attach as query param payload to keep GET compatibility
  // For simplicity we send POST with fetch+ReadableStream outside; here we use EventSource with encoded params.
  const qs = encodeURIComponent(JSON.stringify(params));
  const url = `/v1/ai-question/start?payload=${qs}`;
  return new EventSource(url, { withCredentials: true } as any);
};

export const startExtrapolateStream = (formData: FormData): EventSource => {
  // For file upload we cannot use EventSource directly; placeholder for future implementation.
  throw new Error(
    'startExtrapolateStream not implemented – use fetch streaming via useSSE',
  );
};
