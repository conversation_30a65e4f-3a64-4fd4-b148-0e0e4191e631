import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { IFile } from '@/interfaces/database/file-manager';
import { cn } from '@/lib/utils';
import { getExtension } from '@/utils/document-util';
import {
  ArrowDownToLine,
  Eye,
  FolderInput,
  FolderPen,
  MoreVertical,
  Tag,
  Trash2,
} from 'lucide-react';
import { MouseEvent, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { isFolderType, isSupportedPreviewType } from '../../utils';

// Ant Design filled icons for file types
import {
  AudioFilled,
  FileExcelFilled,
  FileFilled,
  FileImageFilled,
  FileMarkdownFilled,
  FilePdfFilled,
  FilePptFilled,
  FileTextFilled,
  FileWordFilled,
  FolderFilled,
  VideoCameraFilled,
} from '@ant-design/icons';

interface ResourceCardProps {
  file: IFile;
  selected: boolean;
  onSelect: (selected: boolean) => void;
  onClick: () => void;
  onDoubleClick?: () => void;
  showMoveFileModal: (ids: string[]) => void;
  showEditTagsModal?: (file: IFile) => void;
  showRenameModal?: (file: IFile) => void;
  showDeleteModal?: (file: IFile) => void;
  showPreviewModal?: (file: IFile) => void;
  onDownload?: (file: IFile) => void;
}

// TagsPopover 组件用于显示完整的标签列表
function TagsPopover({ tags }: { tags: string[] }) {
  const [open, setOpen] = useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <span
          className="inline-block px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 rounded cursor-pointer hover:bg-gray-200 transition-colors"
          onMouseEnter={() => setOpen(true)}
          onMouseLeave={() => setOpen(false)}
        >
          +{tags.length - 2}
        </span>
      </PopoverTrigger>
      <PopoverContent
        className="max-w-xs p-3 z-[9999]"
        align="start"
        side="top"
        sideOffset={5}
        onMouseEnter={() => setOpen(true)}
        onMouseLeave={() => setOpen(false)}
      >
        <div className="text-xs text-gray-600 mb-2 font-medium">
          全部标签 ({tags.length})
        </div>
        <div className="flex gap-1 flex-wrap">
          {tags.map((tag: string, index: number) => (
            <span
              key={index}
              className="inline-block px-1.5 py-0.5 text-xs rounded"
              style={{
                backgroundColor: '#f1f5f9',
                color: '#64748b',
                border: '1px solid #e2e8f0',
                fontSize: '12px',
                fontWeight: '500',
                padding: '2px 8px',
              }}
            >
              {tag}
            </span>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
}

export function ResourceCard({
  file,
  selected,
  onSelect,
  onClick,
  onDoubleClick,
  showMoveFileModal,
  showEditTagsModal,
  showRenameModal,
  showDeleteModal,
  showPreviewModal,
  onDownload,
}: ResourceCardProps) {
  const { t } = useTranslation('translation', {
    keyPrefix: 'fileManager',
  });

  const isFolder = isFolderType(file.type);
  const extension = isFolder ? 'folder' : getExtension(file.name);
  const canPreview = !isFolder && isSupportedPreviewType(file.name, file.type);

  const handleCheckboxChange = (checked: boolean) => {
    onSelect(checked);
  };

  const handleMoreClick = (e: MouseEvent) => {
    e.stopPropagation();
  };

  const handleActionClick = (action: string, e: MouseEvent) => {
    e.stopPropagation();
    switch (action) {
      case 'move':
        showMoveFileModal([file.id]);
        break;
      case 'rename':
        showRenameModal?.(file);
        break;
      case 'edit_tags':
        showEditTagsModal?.(file);
        break;
      case 'delete':
        showDeleteModal?.(file);
        break;
      case 'download':
        onDownload?.(file);
        break;
      case 'preview':
        showPreviewModal?.(file);
        break;
    }
  };

  const getFileIcon = () => {
    if (isFolder) {
      return <FolderFilled style={{ fontSize: 48, color: '#4096ff' }} />;
    }

    switch (extension.toLowerCase()) {
      case 'doc':
      case 'docx':
        return <FileWordFilled style={{ fontSize: 48, color: '#1e88e5' }} />;
      case 'xls':
      case 'xlsx':
      case 'csv':
        return <FileExcelFilled style={{ fontSize: 48, color: '#43a047' }} />;
      case 'ppt':
      case 'pptx':
        return <FilePptFilled style={{ fontSize: 48, color: '#e64a19' }} />;
      case 'pdf':
        return <FilePdfFilled style={{ fontSize: 48, color: '#d32f2f' }} />;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'mkv':
        return <VideoCameraFilled style={{ fontSize: 48, color: '#ff9800' }} />;
      case 'mp3':
      case 'wav':
      case 'aac':
      case 'flac':
        return <AudioFilled style={{ fontSize: 48, color: '#7c4dff' }} />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
      case 'bmp':
      case 'svg':
        return <FileImageFilled style={{ fontSize: 48, color: '#ff9800' }} />;
      case 'md':
      case 'markdown':
        return (
          <FileMarkdownFilled style={{ fontSize: 48, color: '#607d8b' }} />
        );
      case 'txt':
        return <FileTextFilled style={{ fontSize: 48, color: '#546e7a' }} />;
      default:
        return <FileFilled style={{ fontSize: 48, color: '#90a4ae' }} />;
    }
  };

  return (
    <div
      className={cn(
        'resource-card',
        selected && 'resource-card-selected',
        isFolder && 'resource-card-folder',
      )}
      onClick={onClick}
      onDoubleClick={onDoubleClick}
    >
      <div className="resource-card-header">
        <Checkbox
          checked={selected}
          onCheckedChange={handleCheckboxChange}
          onClick={(e) => e.stopPropagation()}
          className="resource-card-checkbox"
        />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="resource-card-menu"
              onClick={handleMoreClick}
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            {canPreview && (
              <DropdownMenuItem
                onClick={(e) => handleActionClick('preview', e)}
              >
                <Eye className="h-4 w-4 mr-2" />
                {t('preview')}
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={(e) => handleActionClick('rename', e)}>
              <FolderPen className="h-4 w-4 mr-2" />
              {t('rename', { keyPrefix: 'common' })}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => handleActionClick('edit_tags', e)}
            >
              <Tag className="h-4 w-4 mr-2" />
              {t('editTags')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={(e) => handleActionClick('move', e)}>
              <FolderInput className="h-4 w-4 mr-2" />
              {t('move', { keyPrefix: 'common' })}
            </DropdownMenuItem>
            {!isFolder && (
              <DropdownMenuItem
                onClick={(e) => handleActionClick('download', e)}
              >
                <ArrowDownToLine className="h-4 w-4 mr-2" />
                {t('download', { keyPrefix: 'common' })}
              </DropdownMenuItem>
            )}
            <DropdownMenuItem
              onClick={(e) => handleActionClick('delete', e)}
              className="text-red-600"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {t('delete', { keyPrefix: 'common' })}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div className="resource-card-icon">{getFileIcon()}</div>

      <div className="resource-card-content">
        {isFolder ? (
          <TooltipProvider delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className={cn(
                    'resource-card-title truncate cursor-pointer',
                    'folder-title',
                  )}
                >
                  {file.name}
                </div>
              </TooltipTrigger>
              <TooltipContent>{file.name}</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ) : (
          <div className={cn('resource-card-title file-title')}>
            {file.name}
          </div>
        )}
        {!isFolder && file.tags && file.tags.length > 0 && (
          <div className="resource-card-tags">
            {file.tags.slice(0, 2).map((tag, index) => (
              <span
                key={index}
                className="inline-block px-1.5 py-0.5 text-xs bg-blue-100 text-blue-800 rounded mr-1 mb-1"
              >
                {tag}
              </span>
            ))}
            {file.tags.length > 2 && <TagsPopover tags={file.tags} />}
          </div>
        )}
      </div>
    </div>
  );
}
