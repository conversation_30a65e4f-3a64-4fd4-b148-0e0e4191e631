from __future__ import annotations

"""Domain models & data transfer objects for Smart Exam refactor.

Separated to standalone module so that they can be imported by components,
service layer and tests without pulling heavy runtime dependencies.
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import List, Dict, Any, Sequence, Optional
from pydantic import BaseModel, Field, validator, root_validator


class Difficulty(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class QuestionMethod(str, Enum):
    RANDOM = "random"
    BY_TYPE = "by_type"


@dataclass(frozen=True)
class QuestionDistribution:
    """Number of questions per type."""

    single_choice: int = 0
    multiple_choice: int = 0
    fill_blank: int = 0
    true_false: int = 0
    short_answer: int = 0
    ordering: int = 0

    @property
    def total(self) -> int:
        return (
            self.single_choice
            + self.multiple_choice
            + self.fill_blank
            + self.true_false
            + self.short_answer
            + self.ordering
        )


@dataclass(frozen=True)
class RetrieveResult:
    """Light-weight container for retrieval outcome."""

    content: str
    chunks: Sequence[Dict[str, Any]]

    def is_empty(self) -> bool:
        return not self.content


@dataclass
class ExamContext:
    """Aggregated context required for prompt building & exam generation."""

    exam_name: str
    knowledge_points: str
    difficulty: Difficulty = Difficulty.MEDIUM
    question_method: QuestionMethod = QuestionMethod.RANDOM

    # Distribution info (required when method == BY_TYPE but also useful for RANDOM)
    distribution: QuestionDistribution = field(
        default_factory=QuestionDistribution)

    # Content sources (after retrieval)
    user_content: str = ""
    preset_content: str = ""
    template_text: str = ""

    # Misc meta
    tenant_id: str | None = None
    user_id: str | None = None

    def total_questions(self) -> int:
        if self.question_method == QuestionMethod.RANDOM:
            # For RANDOM we rely on distribution.total if set, else fallback to default 10
            return self.distribution.total or 10
        return self.distribution.total


# ------------------------------------------------------------------
# Pydantic parameter model (Step-2)
# ------------------------------------------------------------------


class SmartExamParam(BaseModel):
    """Validated parameters for Smart Exam generation (external API level)."""

    exam_name: str = Field(..., description="试卷名称")
    knowledge_points: str = Field(..., description="考察知识点")
    difficulty: Difficulty = Field(Difficulty.MEDIUM, description="难度等级")

    llm_id: str = Field("deepseek-r1:32b@Ollama", description="LLM 模型 ID")

    # Question generation method & counts
    question_method: QuestionMethod = Field(QuestionMethod.RANDOM)
    question_count: int = Field(
        10, ge=1, description="随机生成题目数量，当 question_method == RANDOM 时")

    # 分类型出题计数 (when by_type)
    single_choice_count: int = Field(0, ge=0)
    multiple_choice_count: int = Field(0, ge=0)
    fill_blank_count: int = Field(0, ge=0)
    true_false_count: int = Field(0, ge=0)
    short_answer_count: int = Field(0, ge=0)
    ordering_count: int = Field(0, ge=0)

    # Knowledge base / retrieval config
    user_kb_id: Optional[str] = None
    preset_kb_id: Optional[str] = None
    user_kb_top_n: int = Field(15, ge=1)
    preset_kb_top_n: int = Field(15, ge=1)
    similarity_threshold: float = Field(0.1, ge=0.0, le=1.0)

    # frontend uploaded content / template
    question_bank_file_content: str = ""
    template_text: str = ""

    stream: bool = True

    class Config:
        validate_assignment = True
        use_enum_values = True

    # ------------------ validators ------------------

    @root_validator(skip_on_failure=True)
    def _check_counts(cls, values):
        method = values.get("question_method")
        if method == QuestionMethod.RANDOM:
            if values.get("question_count", 0) <= 0:
                raise ValueError(
                    "question_count must be > 0 when method is random")
        else:
            total = sum(
                values.get(f, 0)
                for f in [
                    "single_choice_count",
                    "multiple_choice_count",
                    "fill_blank_count",
                    "true_false_count",
                    "short_answer_count",
                    "ordering_count",
                ]
            )
            if total <= 0:
                raise ValueError(
                    "At least one question type count must be > 0 when method is by_type")
        return values


# ------------------------------------------------------------------
# Legacy adapter – keep external behaviours unchanged.
# ------------------------------------------------------------------


class SmartExamParamLegacyAdapter:
    """Helper to convert legacy dict / Flask form data into SmartExamParam.

    If conversion失败，则抛出 Pydantic ValidationError，由调用方捕获并 fallback
    to the old `agent.component.smart_exam.SmartExamParam` if necessary.
    """

    @staticmethod
    def from_request_data(data: dict) -> "SmartExamParam":
        # Sanitize numeric fields (Flask form gives str)
        int_fields = [
            "question_count",
            "single_choice_count",
            "multiple_choice_count",
            "fill_blank_count",
            "true_false_count",
            "short_answer_count",
            "ordering_count",
            "user_kb_top_n",
            "preset_kb_top_n",
        ]
        float_fields = ["similarity_threshold"]

        sanitized = data.copy()
        for f in int_fields:
            if f in sanitized and sanitized[f] != "":
                try:
                    sanitized[f] = int(sanitized[f])
                except (TypeError, ValueError):
                    pass  # let pydantic raise error
        for f in float_fields:
            if f in sanitized and sanitized[f] != "":
                try:
                    sanitized[f] = float(sanitized[f])
                except (TypeError, ValueError):
                    pass

        # Pydantic will validate and coerce enums via str values
        return SmartExamParam(**sanitized)
