import { SettingOutlined, TeamOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'umi';
import GroupMember from './group-member';
import ModelSetting from './model-setting';

import styles from './index.less';

const SystemSetting = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeKey, setActiveKey] = useState('group-member');

  useEffect(() => {
    // 根据当前路径设置活跃tab
    if (location.pathname.includes('group-member')) {
      setActiveKey('group-member');
    } else if (location.pathname.includes('model-setting')) {
      setActiveKey('model-setting');
    }
  }, [location.pathname]);

  const handleTabChange = (key: string) => {
    setActiveKey(key);
    navigate(`/system-setting/${key}`);
  };

  const tabItems = [
    {
      key: 'group-member',
      label: (
        <span>
          <TeamOutlined />
          团队成员
        </span>
      ),
      children: <GroupMember />,
    },
    {
      key: 'model-setting',
      label: (
        <span>
          <SettingOutlined />
          模型设置
        </span>
      ),
      children: <ModelSetting />,
    },
  ];

  return (
    <div className={styles.settingWrapper}>
      <Tabs
        activeKey={activeKey}
        onChange={handleTabChange}
        items={tabItems}
        size="large"
        className={styles.systemTabs}
      />
    </div>
  );
};

export default SystemSetting;
