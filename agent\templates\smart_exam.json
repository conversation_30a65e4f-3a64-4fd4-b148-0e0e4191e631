{"title": "Smart Exam Generation (Streaming)", "description": "使用用户上传试题文件(50%) + 内置试题知识库(50%)生成流式智能组卷。", "canvas_type": "smart-exam", "dsl": {"components": {"Begin:Entry": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "您好！我是智能组卷助手，请提供试卷信息和试题文件。", "query": [{"key": "exam_name", "name": "试卷名称", "value": ""}, {"key": "knowledge_points", "name": "考察知识点", "value": ""}, {"key": "difficulty", "name": "难度等级", "value": "medium"}, {"key": "question_method", "name": "出题方式", "value": "random"}, {"key": "question_count", "name": "试题数量", "value": "10"}, {"key": "question_bank_file_content", "name": "试题文件内容", "value": ""}]}}, "downstream": ["FileProcessor:Proc"]}, "FileProcessor:Proc": {"obj": {"component_name": "FileProcessor", "params": {}}, "downstream": ["SmartExam:Generate"]}, "SmartExam:Generate": {"obj": {"component_name": "SmartExam", "params": {"llm_id": "deepseek-r1:32b@Ollama", "user_kb_top_n": 15, "preset_kb_top_n": 15, "similarity_threshold": 0.2}}, "downstream": ["Answer:Final"]}, "Answer:Final": {"obj": {"component_name": "Answer", "params": {"format": "markdown"}}, "upstream": ["SmartExam:Generate"]}}}}