import { useFetchFileList } from '@/hooks/use-drive-request';
import { useCallback } from 'react';

export const useResourceFileList = () => {
  const result = useFetchFileList();

  // 重写 setPagination 函数以保持与 resource 页面一致的 API
  const setPagination = useCallback(
    ({ page, pageSize }: { page: number; pageSize?: number }) => {
      result.setPagination({ page, pageSize: pageSize || 10 });
    },
    [result.setPagination],
  );

  return {
    ...result,
    setPagination,
  };
};
