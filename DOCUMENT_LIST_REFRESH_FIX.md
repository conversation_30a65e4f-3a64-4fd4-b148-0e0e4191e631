# 文档列表刷新问题修复总结

## 问题描述

在 `knowledge-dataset` 页面中，当用户上传文件后，文件已经成功上传到服务器，但是文档列表表格没有及时刷新显示最新上传的文件。

## 问题根因

问题的根本原因是 **React Query 缓存键不匹配**：

1. **文档列表查询键**: `useFetchDefaultDocumentList` 使用的查询键是 `['fetchDefaultDocumentList', searchString, pagination]`
2. **文档操作后的刷新键**: 各种文档操作（如上传、删除、重命名等）使用的刷新键是 `['fetchDocumentList']`

由于查询键不匹配，导致 `queryClient.invalidateQueries()` 无法正确刷新对应的查询缓存。

## 修复方案

在所有文档操作的 hooks 中，添加对 `['fetchDefaultDocumentList']` 查询键的刷新。

## 修复的 Hooks

### 1. `useUploadNextDocument` - 文档上传

```typescript
if (code === 0 || code === 500) {
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
}
```

### 2. `useNextWebCrawl` - 网页爬取

```typescript
if (code === 0) {
  message.success(i18n.t('message.uploaded'))
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
}
```

### 3. `useSetNextDocumentStatus` - 文档状态修改

```typescript
if (data.code === 0) {
  message.success(i18n.t('message.modified'))
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({ queryKey: ['fetchDefaultDocumentList'] })
}
```

### 4. `useRunNextDocument` - 文档解析/运行

```typescript
// 立即刷新文档列表以显示状态变更
queryClient.invalidateQueries({
  queryKey: ['fetchDocumentList']
})
queryClient.invalidateQueries({
  queryKey: ['fetchDefaultDocumentList']
})

// ... 执行操作后 ...

if (code === 0) {
  // 操作成功后再次刷新
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
  message.success(i18n.t('message.operated'))
}
```

### 5. `useRemoveNextDocument` - 文档删除

```typescript
if (data.code === 0) {
  message.success(i18n.t('message.deleted'))
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
}
```

### 6. `useCreateNextDocument` - 创建文档

```typescript
if (page === 1) {
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
}
```

### 7. `useSetNextDocumentParser` - 文档解析器修改

```typescript
if (data.code === 0) {
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
  message.success(i18n.t('message.modified'))
}
```

### 8. `useSaveNextDocumentName` - 文档重命名

```typescript
if (data.code === 0) {
  message.success(i18n.t('message.renamed'))
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
}
```

### 9. `useSetDocumentMeta` - 文档元数据设置

```typescript
if (data?.code === 0) {
  // 刷新文档列表
  queryClient.invalidateQueries({ queryKey: ['fetchDocumentList'] })
  queryClient.invalidateQueries({
    queryKey: ['fetchDefaultDocumentList']
  })
  message.success(i18n.t('message.modified'))
}
```

## 修复效果

修复后的效果：

1. ✅ **文件上传后立即刷新**: 用户上传文件后，表格会立即显示新上传的文件
2. ✅ **文档操作后及时更新**: 所有文档操作（删除、重命名、状态修改等）后，列表都会及时刷新
3. ✅ **保持用户体验一致**: 用户无需手动刷新页面就能看到最新的文档状态
4. ✅ **支持分页和搜索**: 在不同分页和搜索条件下，刷新机制都能正常工作

## 技术要点

1. **React Query 缓存键匹配**: 确保 `invalidateQueries` 使用的键与 `useQuery` 的键匹配
2. **多键刷新**: 同时刷新 `fetchDocumentList` 和 `fetchDefaultDocumentList` 以支持不同场景
3. **即时反馈**: 在操作前后都进行刷新，确保用户能及时看到状态变化
4. **错误处理**: 即使在部分失败的情况下（如 code 500）也会刷新列表

## 预防措施

为了避免类似问题再次发生：

1. **统一查询键命名**: 建议在项目中建立查询键的命名规范
2. **创建刷新工具函数**: 可以创建统一的文档列表刷新函数
3. **代码审查**: 在添加新的文档操作时，确保包含正确的缓存刷新逻辑
