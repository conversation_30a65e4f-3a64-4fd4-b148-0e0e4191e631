import { parseMarkdownQuestions } from './markdown-to-excel-utils';

// 测试最新格式的markdown内容（没有**包围的难度和答案）
const latestFormatTestContent = `
### 铁路信号工安全操作规程考试试题

---

#### **多选题**

**1. 在进行铁路信号设备维护作业前，必须执行哪些准备工作？（   ）**  
难度：易  
正确答案：A,B,C,D  
A. 确认作业区域已停电并挂接地线  
B. 检查工具和仪表是否符合安全要求  
C. 穿戴好防护服、绝缘鞋和安全帽  
D. 设置好警示标志和防护措施  

---

**2. 在铁路信号工操作中，哪些行为属于严重违规？（   ）**  
难度：难  
正确答案：A,C,D  
A. 未断电情况下进行设备内部作业  
B. 使用不合格的工具或仪表  
C. 擅自修改设备参数或程序  
D. 未按照规程检查设备状态  

---

#### **判断题**

**5. 在铁路信号工操作中，可以使用金属工具敲击设备内部的电气元件。（   ）**  
难度：难  
正确答案：错  
解析：禁止使用金属工具直接接触电气元件，以免造成短路或触电事故。
`;

// 测试新格式的markdown内容
const newFormatTestContent = `
### 考试场景：铁路信号工操作考试  
**出题目标：考察铁路信号工的操作熟练度**  

---

#### 题目 1 （单选题）  
在铁路信号工安全操作规程中，以下哪一项是进行设备维护前的必要步骤？  
A. 直接开始作业  
B. 检查工具是否齐全  
C. 立即切断电源并悬挂警示标志  
D. 通知相邻车站  

**正确答案：C**  
**难易度：中**  
**解析：在进行设备维护前，必须先切断电源并悬挂警示标志以确保安全。**

---

#### 题目 2 （多选题）  
根据铁路信号工的安全操作规程，以下哪些行为是被严格禁止的？  
A. 在未断电的情况下接触高压设备  
B. 操作未经检查的工具和设备  
C. 单独一人进行高空作业  
D. 使用不符合规格的保险丝  

**正确答案：A,B,C,D**  
**难易度：难**  
**解析：以上行为均违反安全操作规程，可能导致严重事故。**

---

#### 题目 3 （判断题）  
在铁路信号设备维护过程中，可以使用潮湿的手触碰电器元件。  
（   ）  

**正确答案：错误**  
**难易度：中**  
**解析：潮湿的手触碰电器元件会增加触电风险，必须避免。**
`;

// 旧格式的测试内容（用于对比）
const oldFormatTestContent = `
### 铁路信号工安全操作规程考题

#### 1. 多选题
**在进行铁路信号设备维护作业前，必须完成哪些准备工作？（   ）**
- **难易度：中**
- **正确答案：A,B,C,D**
- A. 检查工具是否齐全且状态良好
- B. 确认设备电源已断开并采取防触电措施
- C. 设置好安全防护信号，确保作业区域无列车通过
- D. 佩戴必要的劳动保护用品

**答案解析：** 这些准备工作是铁路信号工在进行设备维护时必须遵守的基本安全操作规程，确保作业人员的安全和设备的正常运行。
`;

export const testMarkdownCompatibility = () => {
  console.log('开始测试markdown解析兼容性...');

  console.log('\n=== 测试最新格式（您提供的示例）===');
  const latestQuestions = parseMarkdownQuestions(latestFormatTestContent);
  console.log(`最新格式解析结果: ${latestQuestions.length} 道题目`);

  latestQuestions.forEach((q, index) => {
    console.log(`题目 ${index + 1}:`, {
      题型: q.题型,
      题目内容: q.题目内容.slice(0, 50) + '...',
      难易度: q.难易度,
      正确答案: q.正确答案,
      选项A: q.答案A ? q.答案A.slice(0, 30) + '...' : '无',
      解析: q.解析 ? q.解析.slice(0, 50) + '...' : '无',
    });
  });

  console.log('\n=== 测试新格式 ===');
  const newQuestions = parseMarkdownQuestions(newFormatTestContent);
  console.log(`新格式解析结果: ${newQuestions.length} 道题目`);

  newQuestions.forEach((q, index) => {
    console.log(`题目 ${index + 1}:`, {
      题型: q.题型,
      题目内容: q.题目内容.slice(0, 50) + '...',
      难易度: q.难易度,
      正确答案: q.正确答案,
      选项A: q.答案A ? q.答案A.slice(0, 30) + '...' : '无',
      解析: q.解析 ? q.解析.slice(0, 50) + '...' : '无',
    });
  });

  console.log('\n=== 测试旧格式 ===');
  const oldQuestions = parseMarkdownQuestions(oldFormatTestContent);
  console.log(`旧格式解析结果: ${oldQuestions.length} 道题目`);

  oldQuestions.forEach((q, index) => {
    console.log(`题目 ${index + 1}:`, {
      题型: q.题型,
      题目内容: q.题目内容.slice(0, 50) + '...',
      难易度: q.难易度,
      正确答案: q.正确答案,
      选项A: q.答案A ? q.答案A.slice(0, 30) + '...' : '无',
      解析: q.解析 ? q.解析.slice(0, 50) + '...' : '无',
    });
  });

  return {
    latestFormat: latestQuestions,
    newFormat: newQuestions,
    oldFormat: oldQuestions,
  };
};

// 在浏览器环境中添加到window对象以便调试
if (typeof window !== 'undefined') {
  (window as any).testMarkdownCompatibility = testMarkdownCompatibility;
}
