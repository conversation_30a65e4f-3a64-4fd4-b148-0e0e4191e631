import { DocumentParserType } from '@/constants/knowledge';
import { useTranslate } from '@/hooks/common-hooks';
import { normFile } from '@/utils/file-util';
import {
  PlusOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Divider,
  Form,
  Input,
  Radio,
  Space,
  Typography,
  Upload,
} from 'antd';
import { FormInstance } from 'antd/lib';
import { useEffect, useMemo, useState } from 'react';
import {
  useFetchKnowledgeConfigurationOnMount,
  useSubmitKnowledgeConfiguration,
} from '../hooks';
import { AudioConfiguration } from './audio';
import { BookConfiguration } from './book';
import { EmailConfiguration } from './email';
import { KnowledgeGraphConfiguration } from './knowledge-graph';
import { LawsConfiguration } from './laws';
import { ManualConfiguration } from './manual';
import { NaiveConfiguration } from './naive';
import { OneConfiguration } from './one';
import { PaperConfiguration } from './paper';
import { PictureConfiguration } from './picture';
import { PresentationConfiguration } from './presentation';
import { QAConfiguration } from './qa';
import { ResumeConfiguration } from './resume';
import { TableConfiguration } from './table';
import { TagConfiguration } from './tag';

import styles from '../index.less';

const { Title, Paragraph } = Typography;

const ConfigurationComponentMap = {
  [DocumentParserType.Naive]: NaiveConfiguration,
  [DocumentParserType.Qa]: QAConfiguration,
  [DocumentParserType.Resume]: ResumeConfiguration,
  [DocumentParserType.Manual]: ManualConfiguration,
  [DocumentParserType.Table]: TableConfiguration,
  [DocumentParserType.Paper]: PaperConfiguration,
  [DocumentParserType.Book]: BookConfiguration,
  [DocumentParserType.Laws]: LawsConfiguration,
  [DocumentParserType.Presentation]: PresentationConfiguration,
  [DocumentParserType.Picture]: PictureConfiguration,
  [DocumentParserType.One]: OneConfiguration,
  [DocumentParserType.Audio]: AudioConfiguration,
  [DocumentParserType.Email]: EmailConfiguration,
  [DocumentParserType.Tag]: TagConfiguration,
  [DocumentParserType.KnowledgeGraph]: KnowledgeGraphConfiguration,
};

function EmptyComponent() {
  return null;
}

export const ConfigurationForm = ({ form }: { form: FormInstance }) => {
  const { submitKnowledgeConfiguration, submitLoading, navigateToDataset } =
    useSubmitKnowledgeConfiguration(form);
  const { t } = useTranslate('knowledgeConfiguration');

  const [finalParserId, setFinalParserId] = useState<DocumentParserType>();
  const knowledgeDetails = useFetchKnowledgeConfigurationOnMount(form);
  const parserId: DocumentParserType = Form.useWatch('parser_id', form);
  const ConfigurationComponent = useMemo(() => {
    return finalParserId
      ? ConfigurationComponentMap[finalParserId]
      : EmptyComponent;
  }, [finalParserId]);

  useEffect(() => {
    setFinalParserId(parserId);
  }, [parserId]);

  useEffect(() => {
    setFinalParserId(knowledgeDetails.parser_id as DocumentParserType);
  }, [knowledgeDetails.parser_id]);

  return (
    <div className={styles.configContainer}>
      <Title level={3} className={styles.configTitle}>
        <SettingOutlined />{' '}
        {t('configurationTitle') || 'Knowledge Base Configuration'}
      </Title>
      <Paragraph className={styles.configDescription}>
        {t('configurationDescription') ||
          'Configure your knowledge base settings below.'}
      </Paragraph>

      <Card className={styles.configCard}>
        <Form
          form={form}
          name="validateOnly"
          layout="vertical"
          autoComplete="off"
          requiredMark="optional"
        >
          <Form.Item
            name="name"
            label={t('name')}
            rules={[{ required: true }]}
            className={styles.formItem}
          >
            <Input
              placeholder={t('namePlaceholder') || 'Enter knowledge base name'}
              className={styles.formInput}
            />
          </Form.Item>

          <Form.Item
            name="avatar"
            label={t('photo')}
            valuePropName="fileList"
            getValueFromEvent={normFile}
            className={styles.formItem}
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
              showUploadList={{ showPreviewIcon: false, showRemoveIcon: false }}
            >
              <button style={{ border: 0, background: 'none' }} type="button">
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>{t('upload')}</div>
              </button>
            </Upload>
          </Form.Item>

          <Form.Item
            name="description"
            label={t('description')}
            className={styles.formItem}
          >
            <Input.TextArea
              placeholder={t('descriptionPlaceholder') || 'Enter a description'}
              className={styles.formTextarea}
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>

          <Form.Item
            name="permission"
            label={t('permissions')}
            tooltip={t('permissionsTip')}
            rules={[{ required: true }]}
            className={styles.formItem}
          >
            <Radio.Group className={styles.permissionGroup}>
              <Radio.Button value="me" className={styles.permissionButton}>
                <UserOutlined /> {t('me')}
              </Radio.Button>
              <Radio.Button value="team" className={styles.permissionButton}>
                <TeamOutlined /> {t('team')}
              </Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Divider className={styles.divider} />

          <ConfigurationComponent />

          <Form.Item className={styles.submitItem}>
            <div className={styles.buttonWrapper}>
              <Space size="middle">
                <Button
                  size="large"
                  onClick={navigateToDataset}
                  className={styles.cancelButton}
                >
                  {t('cancel')}
                </Button>
                <Button
                  type="primary"
                  size="large"
                  loading={submitLoading}
                  onClick={submitKnowledgeConfiguration}
                  className={styles.saveButton}
                >
                  {t('save')}
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};
