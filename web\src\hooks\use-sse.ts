import { useCallback, useEffect, useRef, useState } from 'react';

interface SSEOptions {
  endpoint: string;
  payload?: any; // JSON or FormData
  method?: 'POST' | 'GET';
}

interface SSEState {
  data: string;
  error: string | null;
  loading: boolean;
  start: (bodyOverride?: any) => void;
  stop: () => void;
}

// Very light-weight SSE hook that works with fetch streaming (POST supported)
export const useSSE = (options: SSEOptions): SSEState => {
  const { endpoint, payload, method = 'POST' } = options;
  const controllerRef = useRef<AbortController | null>(null);
  const [data, setData] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const stop = useCallback(() => {
    controllerRef.current?.abort();
    controllerRef.current = null;
    setLoading(false);
  }, []);

  const start = useCallback(
    async (bodyOverride?: any) => {
      setData('');
      setError(null);
      setLoading(true);
      const ctrl = new AbortController();
      controllerRef.current = ctrl;

      const body = bodyOverride ?? payload;
      const fetchInit: RequestInit = {
        method,
        signal: ctrl.signal,
        headers: {},
      };
      if (method === 'POST' && body) {
        if (body instanceof FormData) {
          fetchInit.body = body;
        } else {
          fetchInit.body = JSON.stringify(body);
          (fetchInit.headers as any)['Content-Type'] = 'application/json';
        }
      }

      // attach auth header if util exists
      try {
        // dynamic import to avoid circular deps in SSR
        const { getAuthorization } = await import('@/utils/authorization-util');
        (fetchInit.headers as any)['Authorization'] = getAuthorization();
      } catch (_) {
        /* no-op */
      }

      fetch(endpoint, fetchInit)
        .then((res) => {
          if (!res.body) throw new Error('No response body');
          const reader = res.body.getReader();
          const decoder = new TextDecoder('utf-8');
          const read = (): any => {
            reader
              .read()
              .then(({ value, done }) => {
                if (done) {
                  setLoading(false);
                  return;
                }
                const chunk = decoder.decode(value || new Uint8Array(), {
                  stream: true,
                });
                setData((prev) => prev + chunk);
                read();
              })
              .catch((e) => {
                setError(e.message || 'stream error');
                setLoading(false);
              });
          };
          read();
        })
        .catch((e) => {
          if (ctrl.signal.aborted) return;
          setError(e.message || 'network error');
          setLoading(false);
        });
    },
    [endpoint, payload, method],
  );

  useEffect(() => {
    return () => {
      stop();
    };
  }, [stop]);

  return { data, error, loading, start, stop };
};
