/* Drive Modal 高科技样式定义 */
@import '@/theme/high-tech-theme.less';

/* 基础Modal样式类 */
.ht-modal-content {
  /* 背景和边框 */
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(250, 252, 255, 0.98) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 16px;

  /* 阴影效果 */
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.08),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(59, 130, 246, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);

  /* 动画效果 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* 消除默认样式 */
  border-width: 1px;
  border-style: solid;
  padding: 0;

  /* 响应式处理 */
  max-width: min(90vw, 500px);
  min-width: 400px;

  @media (max-width: 768px) {
    min-width: 320px;
    max-width: 95vw;
    border-radius: 12px;
  }
}

/* Modal头部样式 */
.ht-modal-header {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.05) 0%,
    rgba(147, 197, 253, 0.08) 100%
  );
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
  position: relative;

  /* 标题样式 */
  .ht-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.025em;
    display: flex;
    align-items: center;
    gap: 8px;

    /* 图标样式 */
    svg {
      width: 20px;
      height: 20px;
      color: var(--primary-color);
    }
  }

  @media (max-width: 768px) {
    padding: 16px 20px;
    border-radius: 12px 12px 0 0;

    .ht-modal-title {
      font-size: 16px;
    }
  }
}

/* Modal内容区域样式 */
.ht-modal-body {
  padding: 24px;

  /* 表单样式 */
  .form-group {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  /* 标签样式 */
  label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
    letter-spacing: -0.01em;
  }

  /* 输入框样式 */
  input,
  textarea,
  select {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    color: var(--text-primary);
    transition: all 0.2s ease;

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      background: white;
    }

    &::placeholder {
      color: var(--text-tertiary);
    }
  }

  /* 特殊组件样式 */
  .file-uploader,
  .tag-editor,
  .tree-select {
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;

    &:focus-within {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  @media (max-width: 768px) {
    padding: 20px;
  }
}

/* Modal底部按钮区域样式 */
.ht-modal-footer {
  background: rgba(249, 250, 251, 0.8);
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0 0 16px 16px;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  justify-content: flex-end;

  @media (max-width: 768px) {
    padding: 16px 20px;
    border-radius: 0 0 12px 12px;
    flex-direction: column-reverse;

    button {
      width: 100%;
    }
  }
}

/* 高科技按钮样式 */
.ht-modal-btn {
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  letter-spacing: -0.01em;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  min-width: 80px;
  height: 40px;

  /* 图标样式 */
  svg {
    width: 16px;
    height: 16px;
  }

  /* 主要按钮样式 */
  &.primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);

    &:hover:not(:disabled) {
      background: linear-gradient(
        135deg,
        var(--primary-hover) 0%,
        #1d4ed8 100%
      );
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
    }

    /* 危险操作按钮（删除等） */
    &.bg-red-600 {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;

      &:hover:not(:disabled) {
        background: linear-gradient(
          135deg,
          #b91c1c 0%,
          #991b1b 100%
        ) !important;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
      }
    }
  }

  /* 次要按钮样式 */
  &.secondary {
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-secondary);
    border: 1px solid rgba(59, 130, 246, 0.2);

    &:hover:not(:disabled) {
      background: rgba(59, 130, 246, 0.05);
      border-color: var(--primary-color);
      color: var(--primary-color);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.15);
    }

    &:active {
      transform: translateY(0);
    }
  }

  /* 禁用状态 */
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
  }

  /* 加载状态 */
  &.loading {
    position: relative;
    color: transparent;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 16px;
      height: 16px;
      border: 2px solid currentColor;
      border-top: 2px solid transparent;
      border-radius: 50%;
      animation: spin 0.8s linear infinite;
      color: inherit;
    }
  }

  /* ButtonLoading 组件特殊处理 */
  &:has(.lucide-loader-2) {
    position: relative;

    .lucide-loader-2 {
      animation: spin 0.8s linear infinite;
    }
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Modal遮罩样式 */
.ht-modal-overlay {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* 关闭按钮样式 */
.ht-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    transform: scale(1.05);
  }

  svg {
    width: 16px;
    height: 16px;
  }

  @media (max-width: 768px) {
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;

    svg {
      width: 14px;
      height: 14px;
    }
  }
}

/* 深色模式适配 */
.dark {
  .ht-modal-content {
    background: linear-gradient(
      135deg,
      rgba(31, 41, 55, 0.95) 0%,
      rgba(17, 24, 39, 0.98) 100%
    );
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .ht-modal-header {
    background: linear-gradient(
      135deg,
      rgba(59, 130, 246, 0.1) 0%,
      rgba(147, 197, 253, 0.15) 100%
    );
    border-bottom-color: rgba(59, 130, 246, 0.2);
  }

  .ht-modal-body {
    input,
    textarea,
    select {
      background: rgba(31, 41, 55, 0.8);
      border-color: rgba(59, 130, 246, 0.3);
      color: var(--text-primary);

      &:focus {
        background: rgba(31, 41, 55, 1);
      }
    }
  }

  .ht-modal-footer {
    background: rgba(17, 24, 39, 0.8);
    border-top-color: rgba(59, 130, 246, 0.2);
  }

  .ht-modal-btn.secondary {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(59, 130, 246, 0.3);

    &:hover:not(:disabled) {
      background: rgba(59, 130, 246, 0.1);
    }
  }

  .ht-modal-close {
    background: rgba(31, 41, 55, 0.8);
  }
}

/* 全局样式覆盖 - 确保第三方组件也应用样式 */
.ht-modal-content {
  /* 文件上传组件 */
  :global(.dropzone) {
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.8) !important;

    &:focus-within,
    &:hover {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }

  /* Ant Design 组件样式覆盖 */
  :global(.ant-select) {
    .ant-select-selector {
      border: 1px solid rgba(59, 130, 246, 0.2) !important;
      border-radius: 8px !important;
      background: rgba(255, 255, 255, 0.8) !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }

  /* Tree Select 样式 */
  :global(.ant-tree-select) {
    .ant-select-selector {
      border: 1px solid rgba(59, 130, 246, 0.2) !important;
      border-radius: 8px !important;
      background: rgba(255, 255, 255, 0.8) !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }

  /* 输入框全局样式 */
  :global(.ant-input) {
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.8) !important;

    &:focus,
    &:focus-within {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }

  /* 标签组件样式 */
  :global(.ant-tag) {
    border-radius: 6px !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
    background: rgba(59, 130, 246, 0.1) !important;
    color: var(--primary-color) !important;
  }
}
