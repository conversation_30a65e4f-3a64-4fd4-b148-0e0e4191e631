// LeafSpin 旋转点加载组件样式 - 基于用户提供的CSS

// 独立模式（无子组件）
.leaf-spin-standalone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  padding: 20px;
}

// 包装模式容器
.leaf-spin-container {
  position: relative;
  display: block;
  min-height: 80px;
}

// 被包装的内容
.leaf-spin-content {
  opacity: 0.5;
  pointer-events: none;
  user-select: none;
  transition: opacity 0.3s ease;
}

// 遮罩层
.leaf-spin-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--spin-overlay-bg, rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(2px);
  z-index: 100;
  min-height: 100px;
  opacity: 0.9;
}

// 动画指示器容器
.leaf-spin-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  position: relative;
}

// 加载内容容器
.loading-content {
  position: relative;
  display: block;
}

// 旋转点容器 - 基于用户提供的CSS
.loading-dots-container {
  position: relative;
  display: block;
}

// 加载文本 - 基于用户提供的CSS
.loading-text {
  width: 100px;
  height: 30px;
  position: absolute;
  left: 50%;
  margin-left: -50px;
  top: 50%;
  margin-top: 36px;
  color: #999;
  font-size: 14px;
  text-align: center;
}

// 基础点样式 - 基于用户提供的CSS
.loading-dots-container > div {
  content: '';
  position: absolute;
  background: #1890ff;
  border-radius: 50%;
  animation-duration: 2s;
  animation-iteration-count: infinite;
  animation-timing-function: cubic-bezier(0.5, 0, 0.5, 1);
}

// c1点 - 左上角
.loading-dots-container > .c1 {
  animation-name: spin-a;
}

// c2点 - 右上角
.loading-dots-container > .c2 {
  animation-name: spin-b;
}

// c3点 - 右下角
.loading-dots-container > .c3 {
  animation-name: spin-c;
}

// c4点 - 左下角
.loading-dots-container > .c4 {
  animation-name: spin-d;
}

// 不同尺寸适配
.leaf-spin-indicator.size-small {
  .loading-dots-container {
    width: 40px;
    height: 40px;
  }

  .loading-dots-container > div {
    width: 10px;
    height: 10px;
    top: 7px;
    left: 7px;
    transform-origin: 13px 13px;
  }

  .loading-dots-container > .c2 {
    top: 7px;
    left: auto;
    right: 7px;
    transform-origin: -3px 13px;
  }

  .loading-dots-container > .c3 {
    top: auto;
    left: auto;
    right: 7px;
    bottom: 7px;
    transform-origin: -3px -3px;
  }

  .loading-dots-container > .c4 {
    top: auto;
    bottom: 7px;
    transform-origin: 13px -3px;
  }

  .loading-text {
    margin-top: 25px;
    font-size: 12px;
  }
}

.leaf-spin-indicator.size-default {
  .loading-dots-container {
    width: 60px;
    height: 60px;
  }

  .loading-dots-container > div {
    width: 16px;
    height: 16px;
    top: 10px;
    left: 10px;
    transform-origin: 20px 20px;
  }

  .loading-dots-container > .c2 {
    top: 10px;
    left: auto;
    right: 10px;
    transform-origin: -4px 20px;
  }

  .loading-dots-container > .c3 {
    top: auto;
    left: auto;
    right: 10px;
    bottom: 10px;
    transform-origin: -4px -4px;
  }

  .loading-dots-container > .c4 {
    top: auto;
    bottom: 10px;
    transform-origin: 20px -4px;
  }

  .loading-text {
    margin-top: 36px;
    font-size: 14px;
  }
}

.leaf-spin-indicator.size-large {
  .loading-dots-container {
    width: 80px;
    height: 80px;
  }

  .loading-dots-container > div {
    width: 20px;
    height: 20px;
    top: 13px;
    left: 13px;
    transform-origin: 27px 27px;
  }

  .loading-dots-container > .c2 {
    top: 13px;
    left: auto;
    right: 13px;
    transform-origin: -7px 27px;
  }

  .loading-dots-container > .c3 {
    top: auto;
    left: auto;
    right: 13px;
    bottom: 13px;
    transform-origin: -7px -7px;
  }

  .loading-dots-container > .c4 {
    top: auto;
    bottom: 13px;
    transform-origin: 27px -7px;
  }

  .loading-text {
    margin-top: 46px;
    font-size: 14px;
  }
}

// 提示文本
.leaf-spin-tip {
  color: var(--spin-tip-color, rgba(0, 0, 0, 0.65));
  font-size: 12px;
  text-align: center;
  margin-top: 16px;
  line-height: 1.5;
  width: 100px;
  height: 30px;
  position: relative;
}

// 旋转动画 - 完全基于用户提供的keyframes
@keyframes spin-a {
  0% {
    transform: rotate(90deg);
  }
  0% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spin-b {
  0% {
    transform: rotate(90deg);
  }
  25% {
    transform: rotate(90deg);
  }
  25% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spin-c {
  0% {
    transform: rotate(90deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  50% {
    transform: rotate(270deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes spin-d {
  0% {
    transform: rotate(90deg);
  }
  25% {
    transform: rotate(90deg);
  }
  50% {
    transform: rotate(180deg);
  }
  75% {
    transform: rotate(270deg);
  }
  75% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  .leaf-spin-overlay {
    background: rgba(22, 23, 29, 0.9);
  }

  .leaf-spin-tip {
    color: #999;
  }

  .loading-dots-container > div {
    background: #40a9ff;
  }

  .loading-text {
    color: #999;
  }
}

// 项目主题适配
[data-theme='dark'] {
  .leaf-spin-overlay {
    background: rgba(22, 23, 29, 0.9);
  }

  .leaf-spin-tip {
    color: #999;
  }

  .loading-dots-container > div {
    background: #40a9ff;
  }

  .loading-text {
    color: #999;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .leaf-spin-indicator.size-large {
    .loading-dots-container {
      width: 60px;
      height: 60px;
    }

    .loading-dots-container > div {
      width: 16px;
      height: 16px;
      top: 10px;
      left: 10px;
      transform-origin: 20px 20px;
    }

    .loading-dots-container > .c2 {
      top: 10px;
      left: auto;
      right: 10px;
      transform-origin: -4px 20px;
    }

    .loading-dots-container > .c3 {
      top: auto;
      left: auto;
      right: 10px;
      bottom: 10px;
      transform-origin: -4px -4px;
    }

    .loading-dots-container > .c4 {
      top: auto;
      bottom: 10px;
      transform-origin: 20px -4px;
    }
  }
}

// 性能优化
.loading-dots-container,
.loading-dots-container > div {
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
}

// 自定义颜色支持（可通过CSS变量覆盖）
.loading-dots-container > div {
  background: var(--primary-color, #1890ff);
}

// 暗色主题下的自定义颜色
[data-theme='dark'] .loading-dots-container > div {
  background: var(--primary-color, #40a9ff);
}

@media (prefers-color-scheme: dark) {
  .loading-dots-container > div {
    background: var(--primary-color, #40a9ff);
  }
}
