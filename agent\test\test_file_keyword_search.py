#!/usr/bin/env python3
"""
Test script for FileKeywordSearch component
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent.component.file_keyword_search import <PERSON><PERSON>eywordSearch, FileKeywordSearchParam
from agent.canvas import Canvas
import pandas as pd


def test_file_keyword_search():
    """Test FileKeywordSearch component"""
    
    # Create component parameters
    param = FileKeywordSearchParam()
    param.llm_id = "deepseek-chat"  # 使用你系统中可用的LLM ID
    param.max_keywords = 5
    param.max_results = 10
    param.temperature = 0.3
    
    # Create canvas (mock)
    canvas = Canvas({"tenant_id": "test_tenant"}, "test_user")
    
    # Create component
    component = FileKeywordSearch(param, canvas, "test_component")
    
    # Prepare test input
    test_input = pd.DataFrame({
        "content": ["帮我找一些关于机器学习和人工智能的文档"]
    })
    
    # Set input
    component.set_input(test_input)
    
    # Run component
    try:
        result = component.debug()
        print("=== FileKeywordSearch Test Result ===")
        print(result)
        print("=== Test Completed Successfully ===")
        return True
    except Exception as e:
        print(f"=== Test Failed ===")
        print(f"Error: {str(e)}")
        return False


if __name__ == "__main__":
    success = test_file_keyword_search()
    sys.exit(0 if success else 1) 