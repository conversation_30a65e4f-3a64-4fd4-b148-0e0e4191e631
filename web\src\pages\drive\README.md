# Drive 页面目录结构

本目录已按照功能职责重新组织，采用模块化的目录结构。

## 目录结构

```
web/src/pages/drive/
├── index.tsx                    # 主页面入口
├── resource.less               # 样式文件
├── exports.ts                  # 统一导出文件
├── components/                 # UI 组件
│   ├── index.ts               # 组件导出
│   ├── views/                 # 视图组件
│   │   ├── resource-grid-view.tsx    # 网格视图
│   │   └── resource-list-view.tsx    # 列表视图
│   ├── cards/                 # 卡片组件
│   │   ├── resource-card.tsx         # 资源卡片
│   │   └── action-cell.tsx          # 操作单元格
│   ├── navigation/            # 导航组件
│   │   └── resource-breadcrumb.tsx  # 面包屑导航
│   └── cells/                 # 单元格组件
│       └── knowledge-cell.tsx       # 知识库单元格
├── dialogs/                   # 对话框组件
│   ├── index.ts              # 对话框导出
│   ├── create-folder/        # 创建文件夹对话框
│   │   ├── index.tsx
│   │   └── create-folder-form.tsx
│   ├── edit-tags/            # 编辑标签对话框
│   │   ├── index.ts
│   │   ├── edit-tags-dialog.tsx
│   │   └── use-edit-tags.ts
│   ├── move-dialog.tsx       # 移动文件对话框
│   └── link-to-dataset-dialog.tsx  # 链接到数据集对话框
├── hooks/                     # 共享 Hooks
│   ├── index.ts              # Hooks 导出
│   ├── hooks.ts              # 通用 hooks
│   ├── use-resource-file-list.ts    # 资源文件列表
│   ├── use-resource-navigation.ts  # 资源导航
│   └── use-bulk-operate-file.tsx   # 批量操作文件
├── features/                  # 功能特性
│   ├── index.ts              # 功能导出
│   └── file-operations/      # 文件操作功能
│       ├── use-upload-file.ts      # 上传文件
│       ├── use-delete-file.ts      # 删除文件
│       ├── use-move-file.ts        # 移动文件
│       └── use-create-folder.ts    # 创建文件夹
└── utils/                     # 工具函数
    └── index.ts              # 工具函数导出
```

## 设计原则

1. **按功能聚类**: 将相关功能的文件组织在同一目录下
2. **职责分离**: 组件、hooks、工具函数等分别放在不同目录
3. **模块化导出**: 每个目录都有 index.ts 文件统一导出
4. **层次清晰**: 目录层次不超过 3 层，保持结构清晰

## 导入方式

### 推荐的导入方式

```typescript
// 从统一导出文件导入
import {
  ResourceGridView,
  useResourceFileList,
  EditTagsDialog,
} from './exports';

// 或者从具体模块导入
import { ResourceGridView } from './components';
import { useResourceFileList } from './hooks';
import { EditTagsDialog } from './dialogs';
```

### 避免的导入方式

```typescript
// 避免深层路径导入
import { ResourceGridView } from './components/views/resource-grid-view';
```

## 文件职责

- **components/**: 纯 UI 组件，负责渲染和用户交互
- **dialogs/**: 对话框组件及其相关逻辑
- **hooks/**: 可复用的状态逻辑和副作用
- **features/**: 特定功能的业务逻辑
- **utils/**: 纯函数工具，无副作用
