{"title": "Lesson Plan V2 (Streaming)", "description": "使用 FileProcessor + TemplateRetrieval + LessonPlanV2 + ResourceSearch 生成流式教案。", "canvas_type": "lesson-plan", "dsl": {"components": {"Begin:Entry": {"obj": {"component_name": "<PERSON><PERSON>", "params": {"prologue": "您好！我是教案生成助手 V2，请输入课程信息。", "query": [{"key": "course_name", "name": "课程名称", "value": ""}, {"key": "class_type", "name": "课型", "value": ""}, {"key": "class_hours", "name": "课时(分钟)", "value": "40"}, {"key": "target_audience", "name": "授课对象", "value": ""}, {"key": "course_content", "name": "课程内容", "value": ""}, {"key": "material_file_content", "name": "课程材料内容", "value": ""}, {"key": "other_requirements", "name": "其他要求", "value": ""}]}}, "downstream": ["FileProcessor:Proc"]}, "FileProcessor:Proc": {"obj": {"component_name": "FileProcessor", "params": {}}, "downstream": ["TemplateRetrieval:Tpl"]}, "TemplateRetrieval:Tpl": {"obj": {"component_name": "TemplateRetrieval", "params": {"tpl_kb_id": "tenant-lesson-plan-template", "top_n": 3}}, "downstream": ["LessonPlanV2:Generate"]}, "LessonPlanV2:Generate": {"obj": {"component_name": "LessonPlan", "params": {"llm_id": "deepseek-r1:32b@Ollama", "max_tokens": 8192}}, "downstream": ["Answer:Final", "ResourceSearch:RS"]}, "ResourceSearch:RS": {"obj": {"component_name": "ResourceSearch", "params": {"llm_id": "deepseek-r1:32b@Ollama", "max_keywords": 5, "max_results": 10}}, "upstream": ["LessonPlanV2:Generate"], "downstream": []}, "Answer:Final": {"obj": {"component_name": "Answer", "params": {"format": "markdown"}}, "upstream": ["LessonPlanV2:Generate"]}}}}