from __future__ import annotations

import logging
from typing import List

from agent.component.retrieval import Retrieval, RetrievalParam


class TemplateRetrievalParam(RetrievalParam):
    """Parameters for TemplateRetrieval component.

    In addition to all the RetrievalParam fields, users can specify
    `tpl_kb_id` to explicitly set the knowledge-base that stores lesson-plan
    template chunks. If this value is not provided, `kb_ids` MUST be
    pre-populated manually before execution, otherwise the component will
    return empty output.
    """

    def __init__(self):
        super().__init__()
        self.tpl_kb_id: str = ""  # 默认留空，由服务端注入

    def check(self):
        # tpl_kb_id 为空时，要求调用方自行配置 kb_ids
        if not self.tpl_kb_id and not self.kb_ids:
            raise ValueError(
                "Either `tpl_kb_id` or `kb_ids` must be provided for TemplateRetrieval")
        super().check()


class TemplateRetrieval(Retrieval):
    """Retrieve template fragments from a dedicated template knowledge base.

    This class is a *very thin* wrapper around the generic Retrieval component
    – it only guarantees that the knowledge-base ID used for retrieval is the
    template KB declared in the parameters (`tpl_kb_id`). All ranking /
    similarity logic is fully delegated to the parent Retrieval component.
    """

    component_name = "TemplateRetrieval"

    def _run(self, history, **kwargs):
        # Ensure kb_ids is correctly set before delegating to parent logic.
        if not self._param.kb_ids:
            if getattr(self._param, "tpl_kb_id", ""):
                self._param.kb_ids = [self._param.tpl_kb_id]
            else:
                logging.warning(
                    "TemplateRetrieval invoked without kb_ids – returning empty DataFrame")
                return self.be_output("")

        return super()._run(history, **kwargs)
