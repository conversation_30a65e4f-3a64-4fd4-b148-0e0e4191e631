#!/usr/bin/env python3
"""
检查和修复 Llama.cpp 模型配置问题
"""

import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_database_config():
    """检查数据库中的模型配置"""
    
    print("=" * 60)
    print("检查 Llama.cpp 模型配置")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from api.db.services.llm_service import TenantLLMService, LLMService, LLMFactoriesService
        from api.db import LLMType
        
        print("\n1. 检查 LLM 工厂配置...")
        
        # 检查 LLMFactories 表
        factories = LLMFactoriesService.get_all()
        llamacpp_factory = None
        for factory in factories:
            if factory.name == "Llama.cpp":
                llamacpp_factory = factory
                break
        
        if llamacpp_factory:
            print("✓ Llama.cpp 工厂已在数据库中注册")
            print(f"  - 名称: {llamacpp_factory.name}")
            print(f"  - 标签: {llamacpp_factory.tags}")
            print(f"  - 状态: {llamacpp_factory.status}")
        else:
            print("✗ Llama.cpp 工厂未在数据库中找到")
            print("  请运行数据库初始化脚本")
            return False
        
        print("\n2. 检查 LLM 模型配置...")
        
        # 检查 LLM 表
        llms = LLMService.query(fid="Llama.cpp")
        if llms:
            print(f"✓ 找到 {len(llms)} 个 Llama.cpp 模型:")
            for llm in llms:
                print(f"  - {llm.llm_name} ({llm.model_type})")
        else:
            print("✗ 未找到 Llama.cpp 模型配置")
            return False
        
        print("\n3. 检查租户配置...")
        
        # 检查所有租户的配置
        tenant_llms = TenantLLMService.query(llm_factory="Llama.cpp")
        if tenant_llms:
            print(f"✓ 找到 {len(tenant_llms)} 个租户的 Llama.cpp 配置:")
            for tenant_llm in tenant_llms:
                print(f"  - 租户: {tenant_llm.tenant_id}")
                print(f"    模型: {tenant_llm.llm_name}")
                print(f"    类型: {tenant_llm.model_type}")
                print(f"    API Base: {tenant_llm.api_base}")
                print(f"    有API Key: {'是' if tenant_llm.api_key else '否'}")
        else:
            print("⚠ 未找到任何租户的 Llama.cpp 配置")
            print("  这意味着用户还没有在前端配置 Llama.cpp 模型")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def provide_solution():
    """提供解决方案"""
    
    print("\n" + "=" * 60)
    print("解决方案")
    print("=" * 60)
    
    print("""
根据错误分析，问题是用户还没有在前端正确配置 Llama.cpp 模型。

请按照以下步骤操作：

1. 【重要】确保 RAGFlow 服务正在运行
2. 打开浏览器，访问 RAGFlow 管理界面
3. 登录后，进入 "用户设置" -> "模型管理"
4. 在 "待添加模型" 列表中找到 "Llama.cpp"
5. 点击 "添加模型" 按钮
6. 在弹出的对话框中填写：
   - 模型类型: embedding
   - 模型名称: llama-cpp-embedding
   - API Base: http://192.168.3.124:8080
   - API Key: dummy (任意值)
7. 点击 "确定" 保存配置
8. 等待配置保存成功
9. 重新尝试上传文档

如果前端没有显示 Llama.cpp 选项，请检查：
- 前端代码是否正确部署
- 浏览器缓存是否需要清理
- 服务是否需要重启

如果仍有问题，可以尝试手动添加配置（高级用户）：
""")

def manual_fix_config():
    """手动修复配置（高级选项）"""
    
    print("\n" + "=" * 60)
    print("手动修复配置（高级选项）")
    print("=" * 60)
    
    try:
        from api.db.services.llm_service import TenantLLMService
        from api.db.services.user_service import TenantService
        
        # 获取所有租户
        tenants = TenantService.get_all()
        
        if not tenants:
            print("未找到任何租户")
            return False
        
        print(f"找到 {len(tenants)} 个租户")
        
        for tenant in tenants:
            print(f"\n为租户 {tenant.id} 添加 Llama.cpp 配置...")
            
            # 检查是否已存在配置
            existing = TenantLLMService.query(
                tenant_id=tenant.id,
                llm_factory="Llama.cpp",
                llm_name="llama-cpp-embedding"
            )
            
            if existing:
                print("  配置已存在，跳过")
                continue
            
            # 创建配置
            config = {
                "tenant_id": tenant.id,
                "llm_factory": "Llama.cpp",
                "model_type": "embedding",
                "llm_name": "llama-cpp-embedding",
                "api_key": "dummy",
                "api_base": "http://192.168.3.124:8080",
                "max_tokens": 8192,
                "used_tokens": 0
            }
            
            try:
                TenantLLMService.save(**config)
                print("  ✓ 配置添加成功")
            except Exception as e:
                print(f"  ✗ 配置添加失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"手动修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("检查 Llama.cpp 模型配置问题...")
    
    # 检查数据库配置
    db_ok = check_database_config()
    
    # 提供解决方案
    provide_solution()
    
    # 询问是否需要手动修复
    if not db_ok:
        print("\n是否尝试手动修复配置？(y/N): ", end="")
        try:
            response = input().strip().lower()
            if response in ['y', 'yes']:
                manual_fix_config()
        except KeyboardInterrupt:
            print("\n操作取消")
    
    print("\n检查完成！")
