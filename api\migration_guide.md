# Dialog Tenant ID Migration Guide

## 问题描述

在创建 dialog 时，系统错误地使用了 `user_id` 作为 `tenant_id` 存储在 dialog 表中，导致权限检查失败。

## 修复内容

1. **代码修复**: 修改了 `dialog_app.py` 中的创建逻辑，确保使用正确的 `tenant_id`
2. **数据迁移**: 提供了迁移脚本来修复现有的错误数据

## 迁移步骤

### 1. 备份数据库

在运行迁移脚本之前，请务必备份您的数据库：

```bash
# 如果使用 MySQL
mysqldump -u username -p database_name > backup.sql

# 如果使用 PostgreSQL
pg_dump database_name > backup.sql
```

### 2. 运行迁移脚本

```bash
cd api
python utils/migrate_dialog_tenant.py
```

### 3. 验证迁移结果

迁移脚本会输出以下信息：

- 检查的 dialog 数量
- 更新的 dialog 数量
- 发生的错误数量

### 4. 测试权限

迁移完成后，测试以下接口：

- `/conversation/list?dialog_id=<dialog_id>`
- `/dialog/get?dialog_id=<dialog_id>`

## 技术细节

### 修复的接口

1. `POST /dialog/set` - 创建新 dialog 时使用正确的 tenant_id
2. `GET /dialog/list` - 查询和创建默认 dialog 时使用正确的 tenant_id
3. `GET /dialog/get` - 添加了权限检查

### 权限检查逻辑

现在系统会检查用户是否属于拥有该 dialog 的租户，且具有以下角色之一：

- OWNER
- ADMIN
- NORMAL

### 迁移逻辑

迁移脚本会：

1. 查找所有有效的 dialog 记录
2. 检查 dialog 的 tenant_id 是否实际上是 user_id
3. 从 user_tenant 表中查找用户的实际 tenant_id
4. 更新 dialog 表中的 tenant_id

## 注意事项

- 迁移脚本是幂等的，可以安全地多次运行
- 如果用户属于多个租户，脚本会选择第一个租户
- 迁移前请确保应用程序已停止运行
- 迁移过程中会输出详细的日志信息

## 回滚方案

如果迁移出现问题，可以从备份恢复数据库：

```bash
# 如果使用 MySQL
mysql -u username -p database_name < backup.sql

# 如果使用 PostgreSQL
psql database_name < backup.sql
```
