from __future__ import annotations

"""LLM generation wrapper for Extrapolate."""

import logging
from typing import Generator

from api.utils.errors import redact
from .models import ExtrapolateContext
from .prompt_builder import ExtrapolatePromptBuilder


class ExtrapolateGenerator:
    """Generate variant questions using upstream LLM."""

    def __init__(self, llm_bundle, stream: bool = True):
        self.llm_bundle = llm_bundle
        self.stream = stream

    def build_prompt(self, ctx: ExtrapolateContext) -> str:
        prompt = ExtrapolatePromptBuilder(ctx).build()
        logging.debug("[ExtrapolateGenerator] prompt=%s", redact(prompt))
        return prompt

    def stream_generate(self, ctx: ExtrapolateContext, llm_config: dict | None = None) -> Generator[str, None, None]:
        msg = self.build_prompt(ctx)
        llm_config = llm_config or {
            "max_tokens": 8192, "temperature": 0.3, "stream": True}

        if self.stream and hasattr(self.llm_bundle, "chat_streamly"):
            last = ""
            for chunk in self.llm_bundle.chat_streamly(msg, [], llm_config):
                if isinstance(chunk, int):
                    continue
                if not isinstance(chunk, str):
                    chunk = str(chunk)
                delta = chunk[len(last):]
                if delta:
                    yield delta
                    last = chunk
        else:
            response = self.llm_bundle.chat(msg, [], llm_config)
            if isinstance(response, tuple):
                response = response[0]
            yield str(response)
