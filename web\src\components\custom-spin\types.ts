// 自定义Spin组件库类型定义

import React from 'react';

// 通用的Spin组件Props接口
export interface SpinProps {
  /** 是否为加载中状态 */
  spinning?: boolean;
  /** 当作为包裹元素时，可以自定义描述文案 */
  tip?: string;
  /** 组件大小，可选值为 small default large */
  size?: 'small' | 'default' | 'large';
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 被包裹的子元素 */
  children?: React.ReactNode;
}

// CircleSpin组件Props (继承自SpinProps)
export interface CircleSpinProps extends SpinProps {}

// LeafSpin组件Props (继承自SpinProps)
export interface LeafSpinProps extends SpinProps {
  /** 自定义加载文本，如果不传入则不显示文本 */
  text?: string;
}

// Spin组件类型枚举
export type SpinType = 'circle' | 'leaf';

// 扩展属性接口 (未来可能需要的特殊属性)
export interface ExtendedSpinProps extends SpinProps {
  /** 动画类型 */
  type?: SpinType;
  /** 自定义颜色 */
  color?: string;
  /** 动画速度 */
  speed?: 'slow' | 'normal' | 'fast';
}

// 主题相关类型
export interface SpinTheme {
  primaryColor: string;
  overlayBg: string;
  tipColor: string;
}

// 导出默认主题
export const defaultSpinTheme: SpinTheme = {
  primaryColor: '#1890ff',
  overlayBg: 'rgba(255, 255, 255, 0.8)',
  tipColor: 'rgba(0, 0, 0, 0.65)',
};

// 导出暗色主题
export const darkSpinTheme: SpinTheme = {
  primaryColor: '#40a9ff',
  overlayBg: 'rgba(0, 0, 0, 0.8)',
  tipColor: 'rgba(255, 255, 255, 0.85)',
};
