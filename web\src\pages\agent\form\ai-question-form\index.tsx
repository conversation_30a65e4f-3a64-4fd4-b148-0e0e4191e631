import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DocumentParserType } from '@/constants/knowledge';
import { useFetchKnowledgeList } from '@/hooks/knowledge-hooks';
import { useFetchLlmList } from '@/hooks/llm-hooks';
import { UserOutlined } from '@ant-design/icons';
import {
  Avatar as AntAvatar,
  InputNumber as AntdInputNumber,
  Card,
  Col,
  Radio,
  Row,
  Space,
} from 'antd';
import { useTranslation } from 'react-i18next';
import { INextOperatorForm } from '../../interface';
import styles from './index.less';

// Custom InputNumber component that is compatible with the form
const InputNumber = ({ value, onChange, ...props }: any) => {
  return <AntdInputNumber value={value} onChange={onChange} {...props} />;
};

const AIQuestionForm = ({ form }: INextOperatorForm) => {
  const { t } = useTranslation();
  const { list: knowledgeList } = useFetchKnowledgeList(true);
  const { list: llmList } = useFetchLlmList();

  const knowledgeOptions = knowledgeList
    .filter((x) => x.parser_id !== DocumentParserType.Tag)
    .map((x) => ({
      label: (
        <Space>
          <AntAvatar size={20} icon={<UserOutlined />} src={x.avatar} />
          {x.name}
        </Space>
      ),
      value: x.id,
    }));

  const llmOptions = llmList
    ?.filter((llm: any) => llm.llm_type === 'CHAT')
    ?.map((llm: any) => ({
      label: llm.name,
      value: llm.id,
    }));

  return (
    <Form {...form}>
      <form
        className="space-y-6"
        onSubmit={(e) => {
          e.preventDefault();
        }}
      >
        {/* 场景 - Scene/Environment */}
        <FormField
          control={form.control}
          name="exam_scene"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('aiQuestion.sceneTip')}>
                {t('aiQuestion.scene', '出题场景')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'aiQuestion.scenePlaceholder',
                    '请输入出题场景，如：铁路信号工安全操作规程',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 内容及知识点 - Content and Knowledge Points */}
        <FormField
          control={form.control}
          name="knowledge_points"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('aiQuestion.knowledgePointsTip')}>
                {t('aiQuestion.knowledgePoints', '内容及知识点')}
              </FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'aiQuestion.knowledgePointsPlaceholder',
                    '请输入内容及知识点',
                  )}
                  rows={6}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* 出题目标 - Question Generation Objective */}
        <FormField
          control={form.control}
          name="objective"
          render={({ field }) => (
            <FormItem>
              <FormLabel tooltip={t('aiQuestion.objectiveTip')}>
                {t('aiQuestion.objective', '出题目标')}
              </FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="bg-white"
                  placeholder={t(
                    'aiQuestion.objectivePlaceholder',
                    '请输入出题目标，如：检验信号工对安全操作规程的掌握程度',
                  )}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Question Generation Type */}
        <div className={styles.questionTypeSection}>
          <FormField
            control={form.control}
            name="question_generation_type"
            defaultValue={'random'}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('aiQuestion.generationType', '出题方式')}
                </FormLabel>
                <FormControl>
                  <div className={styles.radioSelector}>
                    <Radio.Group
                      value={field.value}
                      onChange={field.onChange}
                      className={styles.radioGroup}
                    >
                      <Radio value="random" className={styles.radioOption}>
                        {t('aiQuestion.randomQuestions', '随机出题')}
                      </Radio>
                      <Radio value="byType" className={styles.radioOption}>
                        {t('aiQuestion.byQuestionType', '按题型出题')}
                      </Radio>
                    </Radio.Group>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Question Count - Only shown when random questions is selected */}
        {form.watch('question_generation_type') === 'random' && (
          <div className={styles.questionTypeSection}>
            <FormField
              control={form.control}
              name="question_count"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('aiQuestion.questionCount', '试题数量')}
                  </FormLabel>
                  <FormControl>
                    <InputNumber
                      value={field.value}
                      onChange={field.onChange}
                      min={1}
                      max={100}
                      className={styles.numberInput}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {/* Question Types - Only shown when byType is selected */}
        {form.watch('question_generation_type') === 'byType' && (
          <div className={styles.questionTypeSection}>
            <Card className={styles.questionTypeCard}>
              <Row gutter={[16, 16]}>
                {/* 单选题 - Single Choice */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="single_choice_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('aiQuestion.singleChoice', '单选题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 多选题 - Multiple Choice */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="multiple_choice_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('aiQuestion.multipleChoice', '多选题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 填空题 - Fill in the Blank */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="fill_blank_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('aiQuestion.fillBlank', '填空题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 判断题 - True/False */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="true_false_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('aiQuestion.trueFalse', '判断题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 简答题 - Short Answer */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="short_answer_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('aiQuestion.shortAnswer', '简答题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>

                {/* 排序题 - Ordering */}
                <Col span={8}>
                  <FormField
                    control={form.control}
                    name="ordering_count"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('aiQuestion.ordering', '排序题')}
                        </FormLabel>
                        <FormControl>
                          <InputNumber
                            value={field.value}
                            onChange={field.onChange}
                            min={0}
                            max={50}
                            className={styles.numberInput}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </Col>
              </Row>
            </Card>
          </div>
        )}
      </form>
    </Form>
  );
};

export default AIQuestionForm;
