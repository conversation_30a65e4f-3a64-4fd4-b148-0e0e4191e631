from __future__ import annotations

"""Time-based chunk buffer for Server-Sent Events (generic version).

This module is copied from `agent.smart_exam.streamer` so that other exam-related
agents can reuse it without depending on SmartExam internals.
"""

import json
import time
from typing import Generator, Iterable


class Streamer:
    """Buffer incremental chunks and generate SSE-compliant strings."""

    def __init__(self, send_interval: float = 1.0):
        self.send_interval = send_interval
        self._last_send_time = 0.0
        self._chunk_id = 0
        self._pending: list[str] = []

    # ------------------------------------------------------------
    # Public helpers
    # ------------------------------------------------------------
    def add_chunk(self, chunk: str) -> str | None:  # noqa: C901
        """Add a piece of content; maybe return SSE payload if sending triggered."""
        if not chunk:
            return None

        self._pending.append(chunk)
        now = time.time()
        if now - self._last_send_time >= self.send_interval:
            data = "".join(self._pending)
            self._pending.clear()
            self._last_send_time = now
            payload = self._format_event(data)
            return payload
        return None

    def flush(self) -> str | None:
        if not self._pending:
            return None
        data = "".join(self._pending)
        self._pending.clear()
        return self._format_event(data)

    # ------------------------------------------------------------
    # Private utils
    # ------------------------------------------------------------
    def _format_event(self, data: str) -> str:
        payload = {
            "id": self._chunk_id,
            "event": "stream",
            "data": json.dumps({"chunk": data}),
        }
        self._chunk_id += 1
        return f"id:{payload['id']}\nevent:{payload['event']}\ndata:{payload['data']}\n\n"

    # ------------------------------------------------------------
    # Helper to stream from an underlying generator
    # ------------------------------------------------------------
    def wrap_generator(self, source: Iterable[str]) -> Generator[str, None, None]:
        for chunk in source:
            payload = self.add_chunk(chunk)
            if payload:
                yield payload
        flush_payload = self.flush()
        if flush_payload:
            yield flush_payload
