import { SearchOutlined } from '@ant-design/icons';
import { But<PERSON>, Divider, Input, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import DOMPurify from 'dompurify';
import { Settings2 } from 'lucide-react';
import React, { useMemo, useState } from 'react';
import { history } from 'umi';

import { useTranslate } from '@/hooks/common-hooks';
import { useSelectChunkMethodList } from '@/pages/data-training/knowledge/components/knowledge-setting/hooks';

import styles from './index.less';

interface IChunkMethodRow {
  id: string;
  name: string;
  formats: string[];
  scene: string;
}

const ChunkMethodMeta: Record<
  string,
  { name: string; formats: string[]; scene: string }
> = {
  naive: {
    name: '通用切片',
    formats: ['DOCX', 'PDF', 'TXT', 'PPT', 'CSV', 'JSON', 'EML'],
    scene: '支持最丰富的文档格式',
  },
  qa: {
    name: '问答',
    formats: ['Excel', 'CSV', 'TXT'],
    scene: 'FAQ / 问答知识库',
  },
  resume: {
    name: '简历',
    formats: ['DOCX', 'PDF', 'TXT'],
    scene: '结构化简历搜索',
  },
  manual: {
    name: '手册',
    formats: ['PDF'],
    scene: '技术/产品手册',
  },
  table: {
    name: '表格数据',
    formats: ['XLSX', 'CSV', 'TXT'],
    scene: '结构化数据表',
  },
  paper: {
    name: '学术论文',
    formats: ['PDF'],
    scene: '论文章节总结',
  },
  book: {
    name: '书籍',
    formats: ['DOCX', 'PDF', 'TXT'],
    scene: '长篇书籍',
  },
  laws: {
    name: '法律条文',
    formats: ['DOCX', 'PDF', 'TXT'],
    scene: '法律条文解析',
  },
  presentation: {
    name: '演示文稿',
    formats: ['PPT', 'PDF'],
    scene: '每页分块',
  },
  picture: {
    name: '图片',
    formats: ['IMAGE'],
    scene: 'OCR/视觉描述',
  },
  one: {
    name: '整页',
    formats: ['DOCX', 'EXCEL', 'PDF', 'TXT'],
    scene: '全文上下文分析',
  },
  knowledge_graph: {
    name: '知识图谱',
    formats: [
      'DOCX',
      'EXCEL',
      'PPT',
      'IMAGE',
      'PDF',
      'TXT',
      'MD',
      'JSON',
      'EML',
    ],
    scene: '图谱/思维导图提取',
  },
  tag: {
    name: '标签集',
    formats: ['XLSX', 'CSV', 'TXT'],
    scene: '标签匹配',
  },
};

const stripHtml = (html: string = '') => {
  return DOMPurify.sanitize(html).replace(/<[^>]+>/g, '');
};

const TrainingMethodPage: React.FC = () => {
  const parserList = useSelectChunkMethodList();
  const { t } = useTranslate('knowledgeConfiguration');

  /** 拼装表格数据 */
  const tableData: IChunkMethodRow[] = useMemo(
    () =>
      parserList.map((item) => {
        const meta = ChunkMethodMeta[item.value] ?? {
          name: item.label,
          formats: '-',
          scene: '-',
        };
        return {
          id: item.value,
          name: meta.name,
          formats: meta.formats,
          scene: meta.scene,
        };
      }),
    [parserList],
  );

  const [searchValue, setSearchValue] = useState('');
  const filteredData = useMemo(() => {
    if (!searchValue) return tableData;
    return tableData.filter((x) =>
      x.name.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [searchValue, tableData]);

  const columns: ColumnsType<IChunkMethodRow> = [
    {
      title: '序号',
      width: 80,
      align: 'center',
      render: (_, __, index) => (
        <span className={styles.indexNumber}>{index + 1}</span>
      ),
    },
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '支持格式',
      dataIndex: 'formats',
      width: 300,
      render: (formats: string[]) => (
        <div className={styles.formatsCell}>
          {formats.map((format, index) => (
            <span key={index} className={styles.formatTag}>
              {format}
            </span>
          ))}
        </div>
      ),
    },
    {
      title: '适合场景',
      dataIndex: 'scene',
      ellipsis: true,
    },
    {
      title: '操作',
      width: 70,
      align: 'center',
      render: (_, record) => (
        <Button
          type="text"
          icon={<Settings2 size={18} />}
          className={styles.actionButton}
          onClick={() => history.push(`/training-method/detail/${record.id}`)}
        />
      ),
    },
  ];

  return (
    <div className={`${styles.pageWrapper} main-content-container`}>
      <h2 className="main-page-title">训练设置</h2>
      <p className={styles.pageDescription}>
        查看并了解系统支持的各类分块（切片）方法。
      </p>
      <Divider className={styles.divider} />
      <div className={`${styles.toolbarRight} search-input-container`}>
        <Input
          placeholder="搜索切片方法..."
          prefix={<SearchOutlined />}
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          className={styles.searchInput}
          allowClear
        />
      </div>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={filteredData}
        className={styles.trainingTable}
        size="middle"
        pagination={{
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ['10', '20', '50'],
        }}
      />
    </div>
  );
};

export default TrainingMethodPage;
