import { useTestChunkRetrieval } from '@/hooks/knowledge-hooks';
import { ExperimentOutlined } from '@ant-design/icons';
import { Flex, Form, Typography } from 'antd';
import TestingControl from './testing-control';
import TestingResult from './testing-result';

import styles from './index.less';

const { Title } = Typography;

const KnowledgeTesting = () => {
  const [form] = Form.useForm();
  const { testChunk } = useTestChunkRetrieval();

  const handleTesting = async (documentIds: string[] = []) => {
    const values = await form.validateFields();
    testChunk({
      ...values,
      doc_ids: Array.isArray(documentIds) ? documentIds : [],
      vector_similarity_weight: 1 - values.vector_similarity_weight,
    });
  };

  return (
    <div className={`${styles.testingContainer} main-content-container`}>
      <Title level={3} className={styles.sectionTitle}>
        <ExperimentOutlined /> Knowledge Testing
      </Title>

      <Flex className={styles.testingWrapper} gap={16}>
        <TestingControl form={form} handleTesting={handleTesting} />
        <TestingResult handleTesting={handleTesting} />
      </Flex>
    </div>
  );
};

export default KnowledgeTesting;
