@import '@/theme/high-tech-theme.less';

.workbenchContainer {
  width: 100%;
  margin: 0 auto;
}

.heroSection {
  margin-bottom: var(--spacing-xl);

  .heroTitle {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(90deg, var(--primary-color) 0%, #38bdf8 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
  }

  .heroSubtitle {
    font-size: 16px;
    color: var(--text-secondary);
    max-width: 600px;
  }
}

.sectionDivider {
  margin: var(--spacing-lg) 0;
  border-color: var(--border-color);
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);

  .sectionTitle {
    margin: 0;
    font-weight: 600;
    color: var(--text-primary);
  }

  .sectionTools {
    display: flex;
    gap: var(--spacing-md);
  }

  .viewAllLink {
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
    transition: color var(--transition-normal);

    &:hover {
      color: var(--primary-hover);
    }
  }
}

// Category Tabs Styles
.mainTabsContainer {
  margin-bottom: var(--spacing-xl);
}

.mainTabs {
  margin-bottom: var(--spacing-lg);

  :global(.ant-tabs-nav) {
    margin-bottom: var(--spacing-md);
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: #e5e7eb;
    }
  }

  :global(.ant-tabs-nav-list) {
    gap: var(--spacing-lg);
  }

  :global(.ant-tabs-tab) {
    padding: var(--spacing-sm) 0;
    margin: 0;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-secondary);
    opacity: 1;
    transition: all var(--transition-normal);
    position: relative;

    &:hover {
      color: var(--primary-color);
    }
  }

  :global(.ant-tabs-tab-active) {
    font-weight: 600;

    :global(.ant-tabs-tab-btn) {
      color: var(--primary-color) !important;
    }
  }

  :global(.ant-tabs-ink-bar) {
    height: 2px;
    background: var(--primary-color);
    border-radius: 2px;
  }
}

.appGridContainer {
  padding: var(--spacing-lg);
  background-color: white;
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.appCard {
  .high-tech-card();
  padding: var(--spacing-lg);
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  width: 100%;
  background-color: white;
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-lg);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(
      90deg,
      var(--primary-color),
      var(--accent-color)
    );
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--card-hover-shadow);
  }

  .appIcon {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--primary-ultralight);
    color: var(--primary-color);
  }

  .appTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
  }

  .appDescription {
    color: var(--text-secondary);
    flex-grow: 1;
    font-size: 0.9rem;
  }
}

.statsSection {
  margin-bottom: var(--spacing-xl);

  .statCard {
    .high-tech-card();
    padding: var(--spacing-lg);
    text-align: center;

    .statValue {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--primary-color);
      margin-bottom: var(--spacing-xs);
    }

    .statLabel {
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
  }
}

.recentActivitySection {
  margin-bottom: var(--spacing-xl);

  .activityCard {
    .high-tech-card();
    padding: var(--spacing-lg);

    :global(.ant-timeline-item-content) {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .timeLabel {
      color: var(--text-tertiary);
      font-size: 0.85rem;
    }
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .heroSection {
    .heroTitle {
      font-size: 24px;
    }

    .heroSubtitle {
      font-size: 16px;
    }
  }

  .sectionHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);

    .sectionTools {
      margin-top: var(--spacing-xs);
    }
  }

  .mainTabs {
    :global(.ant-tabs-nav) {
      overflow-x: auto;
      white-space: nowrap;
    }

    :global(.ant-tabs-tab) {
      padding: var(--spacing-sm) var(--spacing-md);
      font-size: var(--font-size-md);
    }
  }
}

/* Dark mode enhancements */
:global(.dark) {
  .appCard {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);

    .appIcon {
      background-color: rgba(59, 130, 246, 0.2);
    }

    &:hover {
      border-color: var(--primary-color);
    }
  }

  .heroSection {
    .heroTitle {
      background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
      -webkit-background-clip: text;
      background-clip: text;
    }
  }

  .mainTabs {
    :global(.ant-tabs-nav) {
      background-color: transparent;

      &::before {
        background-color: var(--neutral-700);
      }
    }

    :global(.ant-tabs-tab) {
      color: var(--text-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }

    :global(.ant-tabs-tab-active) {
      :global(.ant-tabs-tab-btn) {
        color: var(--primary-color) !important;
      }
    }

    :global(.ant-tabs-ink-bar) {
      background: var(--primary-color);
    }
  }

  .appGridContainer {
    background-color: var(--neutral-800);
    border-color: var(--neutral-700);
  }
}
