import {
  useDefaultDomainKnowledgeId,
  useTestChunkRetrieval,
} from '@/hooks/knowledge-hooks';
import { Form } from 'antd';
import { useState } from 'react';
import TestingControl from './testing-control';
import TestingResult from './testing-result';

import styles from './index.less';

const RetrievalTestPage: React.FC = () => {
  const [form] = Form.useForm();
  const { testChunk } = useTestChunkRetrieval();
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  const KNOWLEDGE_BASE_ID = useDefaultDomainKnowledgeId();
  const handleTesting = async (documentIds?: string[]) => {
    const values = await form.validateFields();
    const docIds = documentIds ?? selectedDocumentIds;
    testChunk({
      ...values,
      kb_id: KNOWLEDGE_BASE_ID,
      doc_ids: Array.isArray(docIds) ? docIds : [],
      vector_similarity_weight: 1 - values.vector_similarity_weight,
      highlight: true, // 添加高亮支持
    });
  };

  // 包装 setSelectedDocumentIds 以匹配组件期望的类型
  const handleSetSelectedDocumentIds = (ids: string[]) => {
    setSelectedDocumentIds(ids);
  };

  return (
    <div className={`${styles.testingContainer} main-content-container`}>
      <h2 className="main-page-title">检索测试</h2>

      <div className={styles.testingWrapper}>
        {/* 左侧控制面板 */}
        <div className={styles.leftPanel}>
          <TestingControl
            form={form}
            handleTesting={handleTesting}
            selectedDocumentIds={selectedDocumentIds}
            setSelectedDocumentIds={handleSetSelectedDocumentIds}
          />
        </div>

        {/* 右侧结果区域 */}
        <div className={styles.rightPanel}>
          <TestingResult
            handleTesting={handleTesting}
            selectedDocumentIds={selectedDocumentIds}
          />
        </div>
      </div>
    </div>
  );
};

export default RetrievalTestPage;
