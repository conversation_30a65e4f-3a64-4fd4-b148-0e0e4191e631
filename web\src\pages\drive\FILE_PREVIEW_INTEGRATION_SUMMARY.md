# Drive 页面文件预览功能集成总结

## 概述

成功为 Drive 页面集成了文件预览功能，将现有的 `FilePreview.tsx` 组件适配到 Drive 页面的架构中。

## 实现的功能

### 1. 文件预览组件

- **DriveFilePreview** (`web/src/pages/drive/components/file-preview/drive-file-preview.tsx`)
  - 基于原有 `FilePreview.tsx` 组件重构
  - 适配 Drive API (使用 `/api/drive/get/{fileId}` 接口)
  - 支持多种文件格式预览：
    - 图片：jpg, jpeg, png, gif, webp
    - 文档：pdf, doc, docx, txt, md
    - 表格：xls, xlsx
    - 视频：mp4, avi, mov, webm

### 2. 预览触发逻辑

- **useFilePreview Hook** (`web/src/pages/drive/hooks/use-file-preview.ts`)
  - 管理预览模态框状态
  - 处理文件数据转换（IFile → DriveFilePreviewFile）
  - 提供显示/隐藏预览的方法

### 3. 文件类型判断

- **isSupportedPreviewType** 工具函数 (`web/src/pages/drive/utils/index.ts`)
  - 根据文件名和类型判断是否支持预览
  - 支持扩展名和MIME类型检测

### 4. UI集成

- **ResourceCard** 组件更新

  - 添加预览菜单项
  - 只在支持的文件类型显示预览选项
  - 添加 `showPreviewModal` 回调

- **ActionCell** 组件更新

  - 添加预览按钮（眼睛图标）
  - 移除原有的 `NewDocumentLink` 依赖
  - 使用 Drive 自己的预览系统

- **ResourceGridView** 和 **ResourceListView** 更新
  - 集成 `useFilePreview` hook
  - 添加 `DriveFilePreview` 组件
  - 传递预览回调到子组件

## 文件结构

```
web/src/pages/drive/
├── components/
│   ├── file-preview/
│   │   ├── index.ts                          # 组件导出
│   │   ├── drive-file-preview.tsx           # Drive专用预览组件
│   │   └── drive-file-preview.less          # 预览组件样式
│   ├── cards/
│   │   ├── resource-card.tsx                # 更新：添加预览功能
│   │   └── action-cell.tsx                  # 更新：添加预览按钮
│   └── views/
│       ├── resource-grid-view.tsx           # 更新：集成预览
│       └── resource-list-view.tsx           # 更新：集成预览
├── hooks/
│   ├── use-file-preview.ts                  # 新增：预览状态管理
│   └── index.ts                             # 更新：导出预览hook
└── utils/
    └── index.ts                             # 更新：添加文件类型判断
```

## 技术特点

### 1. 模块化设计

- 遵循 Drive 页面的模块化架构
- 独立的预览组件和hooks
- 清晰的职责分离

### 2. API适配

- 使用 Drive 专用的文件获取接口
- 正确处理文件ID和路径映射
- 兼容现有的权限控制

### 3. 用户体验

- 高科技玻璃效果样式
- 响应式设计支持
- 加载状态和错误处理
- 支持全屏预览（预留功能）

### 4. 类型安全

- 完整的 TypeScript 类型定义
- 接口兼容性保证
- 编译时错误检查

## 支持的文件格式

| 类型 | 格式                      | 预览方式               |
| ---- | ------------------------- | ---------------------- |
| 图片 | jpg, jpeg, png, gif, webp | 直接显示               |
| 文档 | pdf                       | iframe 预览            |
| 文档 | doc, docx                 | mammoth.js 转换为HTML  |
| 表格 | xls, xlsx                 | xlsx.js 转换为HTML表格 |
| 文本 | txt, md                   | 纯文本显示             |
| 视频 | mp4, avi, mov, webm       | HTML5 video 播放器     |

## 使用方式

### 网格视图

1. 点击文件卡片右上角的菜单按钮
2. 选择"预览"选项
3. 支持预览的文件类型会显示预览选项

### 列表视图

1. 点击操作栏中的眼睛图标
2. 直接触发文件预览
3. 只在支持的文件类型显示预览按钮

## 样式特性

- 使用高科技主题样式
- 支持暗色/亮色主题切换
- 玻璃效果和模糊背景
- 响应式布局适配移动端
- 优化的文档内容样式

## 多语言支持

- 复用现有的 `preview` 翻译key
- 支持所有已配置的语言
- 错误消息本地化

## 兼容性

- 与现有 Drive 功能完全兼容
- 不影响其他页面的文件预览
- 保持API接口一致性
- 支持现有的权限和安全控制

## 后续改进建议

1. **全屏预览功能**

   - 实现真正的全屏模式
   - 添加键盘快捷键支持

2. **预览缓存**

   - 避免重复加载相同文件
   - 提升用户体验

3. **更多文件格式支持**

   - 添加更多Office格式支持
   - 支持代码文件语法高亮

4. **性能优化**
   - 大文件预览优化
   - 懒加载和虚拟滚动

## 总结

文件预览功能已成功集成到 Drive 页面，提供了完整的文件预览体验。该实现遵循了项目的架构模式，具备良好的可维护性和扩展性，为用户提供了便捷的文件查看功能。
