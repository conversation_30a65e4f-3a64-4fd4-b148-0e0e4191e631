import FilePreview from '@/components/file-preview/FilePreview';
import HightLightMarkdown from '@/components/highlight-markdown';
import { getAuthorization } from '@/utils/authorization-util';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  ScheduleOutlined,
} from '@ant-design/icons';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Card,
  Col,
  Input,
  Row,
  Space,
  Typography,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { history } from 'umi';
import { z } from 'zod';
import LessonPlanV2Form from '../form/lesson-plan-v2-form';
import { INextOperatorForm } from '../interface';
import RecommendedResources, {
  ResourceItem,
} from '../lesson-plan/components/RecommendedResources';
import styles from './index.less';

import { exportMarkdownToDocx } from '@/utils/markdown-export';
import { flushSync } from 'react-dom';

const { Title, Paragraph } = Typography;

/* ---------------------------------- Schema --------------------------------- */
const formSchema = z
  .object({
    course_name: z.string().min(1, { message: '课程名称不能为空' }),
    class_type: z.string().min(1, { message: '课型不能为空' }),
    class_hours: z.number().min(1, { message: '课时不能为空' }),
    target_audience: z.string().min(1, { message: '受众群体不能为空' }),
    course_content: z.string().optional(),
    course_file: z.any().optional(),
    llm_id: z.string().optional(),
  })
  .refine(
    (data) => {
      return (
        (data.course_content && data.course_content.trim() !== '') ||
        data.course_file
      );
    },
    {
      message: '请输入课程内容或上传课程材料文件',
      path: ['course_content'],
    },
  );

type FormValues = z.infer<typeof formSchema>;

/* -------------------------------- Component -------------------------------- */
const LessonPlanV2Page: React.FC = () => {
  /* ------------------------------ Form state ----------------------------- */
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      course_name: '',
      class_type: '',
      class_hours: 40,
      target_audience: '',
      course_content: '',
      course_file: undefined,
      llm_id: 'deepseek-r1:32b@Ollama',
    },
  });

  const [fileList, setFileList] = useState<any[]>([]);

  /* ----------------------------- Runtime state ---------------------------- */
  const [loading, setLoading] = useState(false);
  const [generatedLessonPlan, setGeneratedLessonPlan] = useState<string>('');
  const [thinkingProcess, setThinkingProcess] = useState<string>('');
  const [formalContent, setFormalContent] = useState<string>('');
  const [resources, setResources] = useState<ResourceItem[]>([]);
  const [sessionId, setSessionId] = useState<string>('');

  // 用于跟踪当前是否在思考过程中
  const [isInThinking, setIsInThinking] = useState(false);

  /* ------------------------- File preview state -------------------------- */
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewFile, setPreviewFile] = useState<{
    name: string;
    type: string;
    location: string;
    size: number;
  } | null>(null);

  /* ------------------------------ SSE logic ------------------------------ */
  // 状态机定义
  type Status =
    | 'idle'
    | 'uploading'
    | 'waiting_embedding'
    | 'retrieving_template'
    | 'generating'
    | 'streaming'
    | 'done'
    | 'ask_feedback'
    | 'error';

  const [status, setStatus] = useState<Status>('idle');

  const handleGoBack = () => {
    history.push('/workbench');
  };

  const uploadProps = {
    fileList,
    beforeUpload: (file: any) => {
      setFileList([file]);
      form.setValue('course_file', file);
      return false; // prevent auto-upload
    },
    onRemove: () => {
      setFileList([]);
      form.setValue('course_file', undefined);
    },
  };

  /* ----------------------------- Submit form ----------------------------- */
  const onSubmit = async (values: FormValues) => {
    setLoading(true);
    setGeneratedLessonPlan('');
    setThinkingProcess('');
    setFormalContent('');
    setResources([]);
    setFeedbackVisible(false);
    setShowFeedbackInput(false);
    setExportEnabled(false);
    setStatus('uploading');

    try {
      const formData = new FormData();
      formData.append('course_name', values.course_name);
      formData.append('class_type', values.class_type);
      formData.append('class_hours', values.class_hours.toString());
      formData.append('target_audience', values.target_audience);
      formData.append('llm_id', 'deepseek-r1:32b@Ollama');

      if (values.course_content && values.course_content.trim() !== '') {
        formData.append('course_content_type', 'manual');
        formData.append('course_content', values.course_content);
      } else if (values.course_file) {
        formData.append('course_content_type', 'file');
        formData.append('course_file', values.course_file);
      }

      const resp = await fetch('/v1/agent/lesson-plan-v2/start', {
        method: 'POST',
        body: formData,
        credentials: 'include',
        headers: {
          Authorization: getAuthorization(),
        },
      });

      if (!resp.ok) {
        throw new Error(`HTTP ${resp.status}`);
      }

      // 直接从 POST 响应中读取 SSE 流，不要创建新的 EventSource
      const reader = resp.body?.getReader();
      if (!reader) {
        throw new Error('Response body is not readable');
      }

      const decoder = new TextDecoder('utf-8');
      let buffer = '';

      const processBuffer = () => {
        let idx;
        while ((idx = buffer.indexOf('\n\n')) >= 0) {
          const rawEvent = buffer.slice(0, idx + 2);
          buffer = buffer.slice(idx + 2);

          let eventId = '';
          let eventName = '';
          let dataStr = '';

          rawEvent
            .trim()
            .split('\n')
            .forEach((line) => {
              if (line.startsWith('id:')) {
                eventId = line.slice(3).trim();
              } else if (line.startsWith('event:')) {
                eventName = line.slice(6).trim();
              } else if (line.startsWith('data:')) {
                dataStr += line.slice(5).trim();
              }
            });

          if (!eventName || !dataStr) return;

          try {
            const data = JSON.parse(dataStr);
            console.log(`[SSE] Event: ${eventName}`, data); // 添加调试日志

            switch (eventName) {
              case 'init':
                setSessionId(data.session_id);
                break;
              case 'waiting_embedding':
                setStatus('waiting_embedding');
                break;
              case 'retrieving_template':
                setStatus('retrieving_template');
                break;
              case 'generating':
                setStatus('generating');
                break;
              case 'stream':
                // 检查是否有replace标志
                const shouldReplace = data.replace === true;
                flushSync(() => {
                  setStatus('streaming');
                  parseContentChunk(data.chunk || '');
                });
                break;
              case 'recommendations':
                setResources(
                  Array.isArray(data.resources) ? data.resources : [],
                );
                break;
              case 'done':
                setLoading(false);
                setExportEnabled(true);
                setStatus('done');
                break;
              case 'ask_feedback':
                setFeedbackVisible(true);
                setStatus('ask_feedback');
                break;
              case 'error':
                message.error(data.detail || '服务端错误');
                setLoading(false);
                setStatus('error');
                return; // 停止处理
            }
          } catch (err) {
            console.error(
              'Failed to parse SSE event',
              err,
              'Raw data:',
              dataStr,
            );
          }
        }
      };

      // 使用循环而不是递归来避免调用栈过深
      const startPump = async () => {
        try {
          while (true) {
            const { value, done } = await reader.read();
            if (done) {
              console.log('[SSE] Stream ended');
              // 确保 loading 状态被重置
              setLoading(false);
              // 如果没有收到 done 事件，设置状态为 done
              if (status !== 'done' && status !== 'error') {
                setStatus('done');
              }
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            console.log('[SSE] Received chunk:', chunk); // 添加调试日志
            processBuffer();
          }
        } catch (err) {
          console.error('SSE stream reading error', err);
          message.warning('连接中断，请重试');
          setLoading(false);
          setStatus('error');
        } finally {
          // 确保无论如何都重置 loading 状态
          setLoading(false);
        }
      };

      await startPump();
    } catch (err: any) {
      console.error('LessonPlanV2 generation failed', err);
      message.error(err.message || '生成教案失败');
      setLoading(false);
      setStatus('error');
    }
  };

  /* --------------------- Export Markdown to Docx -------------------- */
  const handleExport = async () => {
    if (!formalContent) return;
    const fileName = `教案_${new Date().toISOString().slice(0, 10)}.docx`;
    exportMarkdownToDocx(formalContent, fileName);

    // 告知后端用户已保存，取消清理任务
    if (sessionId) {
      try {
        fetch(`/v1/agent/lesson-plan-v2/${sessionId}/cancel_cleanup`, {
          method: 'POST',
          credentials: 'include',
          headers: {
            Authorization: getAuthorization(),
          },
        });
      } catch (_) {
        /* ignore */
      }
    }
  };

  /* ------------------------- Resource click -------------------------- */
  const handleResourceClick = (resource: ResourceItem) => {
    setPreviewFile({
      name: resource.name,
      type: resource.type,
      location: resource.location,
      size: resource.size,
    });
    setPreviewVisible(true);
  };

  const handleClosePreview = () => {
    setPreviewVisible(false);
    setPreviewFile(null);
  };

  /* ---------------------------- Cleanup ------------------------------ */
  useEffect(() => {
    return () => {
      // closeEventSource();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /* ---------------------------- Render ------------------------------- */
  const lessonPlanForm = form as unknown as INextOperatorForm['form'];

  /* ---------------------- Feedback interaction state --------------------- */
  const [feedbackVisible, setFeedbackVisible] = useState(false);
  const [showFeedbackInput, setShowFeedbackInput] = useState(false);
  const [feedbackText, setFeedbackText] = useState('');
  const [submittingFeedback, setSubmittingFeedback] = useState(false);
  const [exportEnabled, setExportEnabled] = useState(false);

  /* ------------------------- 滚动到底部的引用 -------------------------- */
  const markdownSectionRef = React.useRef<HTMLDivElement>(null);

  /* --------------------------- Feedback APIs ---------------------------- */
  const putFeedback = async (payload: Record<string, any>) => {
    if (!sessionId) {
      message.error('会话 ID 缺失，无法提交反馈');
      return;
    }
    setSubmittingFeedback(true);
    setStatus('generating');
    try {
      const resp = await fetch(`/v1/agent/lesson-plan-v2/${sessionId}/chat`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: getAuthorization(),
        },
        body: JSON.stringify(payload),
        credentials: 'include',
      });

      if (!resp.ok) {
        throw new Error(`HTTP ${resp.status}`);
      }

      message.success('反馈已提交');
      setFeedbackVisible(false);
      setShowFeedbackInput(false);
      setFeedbackText('');
      setStatus('done');

      // 若用户选择不满意，后端将流式返回新的生成结果
      // 这里使用 ReadableStream 解析 SSE
      if (payload.feedback) {
        setGeneratedLessonPlan('');
        setThinkingProcess('');
        setFormalContent('');
        setResources([]);
        setExportEnabled(false);
        setLoading(true);

        const reader = resp.body?.getReader();
        if (!reader) return;
        const decoder = new TextDecoder('utf-8');
        let buffer = '';

        const processBuffer = () => {
          let idx;
          while ((idx = buffer.indexOf('\n\n')) >= 0) {
            const rawEvent = buffer.slice(0, idx + 2);
            buffer = buffer.slice(idx + 2);

            let eventName = '';
            let dataStr = '';
            rawEvent
              .trim()
              .split('\n')
              .forEach((line) => {
                if (line.startsWith('event:')) {
                  eventName = line.slice(6).trim();
                } else if (line.startsWith('data:')) {
                  dataStr += line.slice(5).trim();
                }
              });

            if (!eventName) return;
            try {
              const data = JSON.parse(dataStr);
              console.log(`[SSE Feedback] Event: ${eventName}`, data); // 添加调试日志

              if (eventName === 'stream') {
                flushSync(() => {
                  parseContentChunk(data.chunk || '');
                });
              } else if (eventName === 'recommendations') {
                setResources(
                  Array.isArray(data.resources) ? data.resources : [],
                );
              } else if (eventName === 'done') {
                setLoading(false);
                setExportEnabled(true);
              } else if (eventName === 'ask_feedback') {
                setFeedbackVisible(true);
              } else if (eventName === 'error') {
                message.error(data.detail || '生成错误');
                setLoading(false);
              }
            } catch (err) {
              console.error(
                'Failed to parse feedback SSE event',
                err,
                'Raw data:',
                dataStr,
              );
            }
          }
        };

        // 使用循环而不是递归
        const startFeedbackPump = async () => {
          try {
            while (true) {
              const { value, done } = await reader.read();
              if (done) {
                console.log('[SSE Feedback] Stream ended');
                // 确保 loading 状态被重置
                setLoading(false);
                break;
              }

              const chunk = decoder.decode(value, { stream: true });
              buffer += chunk;
              console.log('[SSE Feedback] Received chunk:', chunk);
              processBuffer();
            }
          } catch (err) {
            console.error('Feedback SSE stream reading error', err);
            setLoading(false);
          } finally {
            // 确保无论如何都重置 loading 状态
            setLoading(false);
          }
        };

        await startFeedbackPump();
      }
    } catch (err: any) {
      console.error('提交反馈失败', err);
      message.error(err.message || '提交反馈失败');
      setStatus('error');
    } finally {
      setSubmittingFeedback(false);
    }
  };

  const handleSatisfied = () => {
    putFeedback({ satisfied: true });
  };

  const handleDissatisfied = () => {
    if (!feedbackText.trim()) {
      message.warning('请填写反馈信息');
      return;
    }
    putFeedback({ feedback: feedbackText });
  };

  /* ------------------------- 内容解析工具函数 -------------------------- */
  const parseContentChunk = (chunk: string) => {
    // 纯追加模式：因为服务端发送的是增量数据
    const newContent = generatedLessonPlan + chunk;
    setGeneratedLessonPlan(newContent);

    // 分析完整内容，更新思考过程和正式内容
    // 使用 lastIndexOf 找到最后一个 <think> 标签
    const thinkStartIndex = newContent.lastIndexOf('<think>');
    const thinkEndIndex = newContent.indexOf('</think>', thinkStartIndex);

    if (thinkStartIndex !== -1) {
      if (thinkEndIndex !== -1) {
        // 找到完整的思考过程
        const thinking = newContent.substring(
          thinkStartIndex + 7,
          thinkEndIndex,
        );
        const formal = newContent.substring(thinkEndIndex + 8);

        flushSync(() => {
          setThinkingProcess(thinking);
          setFormalContent(formal);
          setIsInThinking(false);
        });
        // 滚动到底部
        setTimeout(() => {
          if (markdownSectionRef.current) {
            markdownSectionRef.current.scrollTop =
              markdownSectionRef.current.scrollHeight;
          }
        }, 0);
      } else {
        // 还在思考过程中
        const thinking = newContent.substring(thinkStartIndex + 7);

        flushSync(() => {
          setThinkingProcess(thinking);
          setFormalContent('');
          setIsInThinking(true);
        });
        // 滚动到底部
        setTimeout(() => {
          if (markdownSectionRef.current) {
            markdownSectionRef.current.scrollTop =
              markdownSectionRef.current.scrollHeight;
          }
        }, 0);
      }
    } else {
      // 没有思考过程，全部作为正式内容
      flushSync(() => {
        setThinkingProcess('');
        setFormalContent(newContent);
        setIsInThinking(false);
      });
      // 滚动到底部
      setTimeout(() => {
        if (markdownSectionRef.current) {
          markdownSectionRef.current.scrollTop =
            markdownSectionRef.current.scrollHeight;
        }
      }, 0);
    }
  };

  return (
    <div className={`${styles.lessonPlanContainer} main-content-container`}>
      {/* Header */}
      <div className={styles.pageHeader}>
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={handleGoBack}
          className={styles.backButton}
        >
          返回
        </Button>
        <div className={styles.pageTitle}>
          <h2 className="main-page-title">教案生成</h2>
        </div>
        <Paragraph className={styles.pageDescription}>
          基于知识库，智能生成教学教案
        </Paragraph>
      </div>

      <Row gutter={24}>
        {/* Form column */}
        <Col span={10}>
          <Card className={styles.formCard} title="教案生成配置">
            <LessonPlanV2Form form={lessonPlanForm} uploadProps={uploadProps} />
            <div className={styles.formActions}>
              <Button
                type="primary"
                icon={<ScheduleOutlined />}
                size="large"
                loading={loading}
                onClick={form.handleSubmit(onSubmit)}
                className={styles.generateButton}
              >
                开始生成
              </Button>
            </div>
          </Card>
        </Col>

        {/* Result column */}
        <Col span={14}>
          <Card
            className={styles.resultCard}
            title="生成结果"
            extra={
              formalContent && (
                <Button
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleExport}
                  disabled={!exportEnabled}
                >
                  导出 Word
                </Button>
              )
            }
          >
            {thinkingProcess || formalContent ? (
              <div className={styles.resultContent}>
                <div
                  className={styles.markdownSection}
                  ref={markdownSectionRef}
                >
                  {/* 思考过程显示 */}
                  {thinkingProcess && (
                    <div className={styles.thinkingProcess}>
                      {thinkingProcess}
                    </div>
                  )}

                  {/* 正式内容显示 */}
                  <div className={styles.formalContent}>
                    <HightLightMarkdown>{formalContent}</HightLightMarkdown>
                  </div>
                </div>
                <div className={styles.sourcesSection}>
                  <RecommendedResources
                    resources={resources}
                    onResourceClick={handleResourceClick}
                  />

                  {/* Feedback Section */}
                  {feedbackVisible && (
                    <div className={styles.feedbackSection}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div>对本次教案是否满意？</div>
                        <Space>
                          <Button
                            type="primary"
                            onClick={handleSatisfied}
                            loading={submittingFeedback}
                          >
                            满意
                          </Button>
                          <Button
                            onClick={() => setShowFeedbackInput(true)}
                            danger
                          >
                            不满意
                          </Button>
                        </Space>

                        {showFeedbackInput && (
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <Input.TextArea
                              rows={4}
                              placeholder="请描述不满意的原因或希望改进的方向"
                              value={feedbackText}
                              onChange={(e) => setFeedbackText(e.target.value)}
                            />
                            <Button
                              type="primary"
                              onClick={handleDissatisfied}
                              loading={submittingFeedback}
                            >
                              提交反馈并重新生成
                            </Button>
                          </Space>
                        )}
                      </Space>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className={styles.emptyResult}>
                {loading ? (
                  <div className={styles.loadingState}>
                    <div className={styles.statusInfo}>
                      {status === 'uploading' && '📤 正在上传文件...'}
                      {status === 'waiting_embedding' && '🔄 文件向量化中...'}
                      {status === 'retrieving_template' && '🔍 模板检索中...'}
                      {status === 'generating' && '✍️ 教案生成中...'}
                      {status === 'streaming' && '📝 教案内容流式输出中...'}
                    </div>
                    <div className={styles.tip}>
                      请耐心等待，生成过程可能需要几分钟
                    </div>
                  </div>
                ) : (
                  <div className={styles.placeholder}>
                    <ScheduleOutlined className={styles.placeholderIcon} />
                    <p>填写左侧表单并点击"开始生成"以创建教案</p>
                  </div>
                )}
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* File preview */}
      <FilePreview
        visible={previewVisible}
        onClose={handleClosePreview}
        file={previewFile}
      />
    </div>
  );
};

export default LessonPlanV2Page;
